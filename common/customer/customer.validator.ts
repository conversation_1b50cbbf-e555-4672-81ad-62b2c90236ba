import dayjs from 'dayjs';
import { z } from 'zod';

import { SortOrder } from '../shared/shared.constants';
import { account_id } from '../validators/account';
import { CustomerGender, CustomerType } from './customer.constants';

const created_by = z.string();
const updated_by = z.string();
const created_proxied_by = z.string().optional();
const updated_proxied_by = z.string().optional();
const updated_at = z.date().optional();

const first_name = z.string().max(100).optional();
const last_name = z.string().max(100).optional();
const middle_name = z.string().max(100).optional();
const company_name = z.string().max(100).optional();
const nickname = z.string().max(100).optional();
const website = z.string().max(100).optional();
const email = z.string().email().optional();
const phone = z.string().max(12).optional();
const dob = z
  .string()
  .refine((v) => dayjs(v).isValid())
  .transform((v) => dayjs(v).toISOString())
  .optional();
const gender = z.enum([CustomerGender.male, CustomerGender.female]).optional();
const start_date = z
  .string()
  .refine((v) => dayjs(v).isValid())
  .transform((v) => dayjs(v).toISOString())
  .optional();
const end_date = z
  .string()
  .refine((v) => dayjs(v).isValid())
  .transform((v) => dayjs(v).toISOString())
  .optional();
const address = z
  .object({
    street: z.string().nullable().optional(),
    street2: z.string().nullable().optional(),
    city: z.string().nullable().optional(),
    geo_state: z.string().nullable().optional(),
    zipcode: z.string().nullable().optional(),
    country: z.string().nullable().optional(),
  })
  .optional();
const group_id = z.string().optional();

const type = z.enum([CustomerType.individual, CustomerType.group]).optional();

export const createCustomerSchema = z.object({
  account_id,
  created_by,
  created_proxied_by,
  first_name,
  middle_name,
  last_name,
  address,
  company_name,
  nickname,
  website,
  email,
  phone,
  gender,
  dob,
  start_date,
  end_date,
  type,
  group_id,
});

export type CreateCustomerDTO = z.infer<typeof createCustomerSchema>;

export const getCustomerSchema = z.object({
  account_id,
  sort: z.enum([SortOrder.ASC, SortOrder.DESC]).optional(),
  orderBy: z.string().optional(),
  q: z.string().optional(),
});
export type GetCustomerDTO = z.infer<typeof getCustomerSchema>;

export const config = z.object({
  overrideFields: z.array(z.string()).optional(),
});
export const updateCustomerSchema = z.object({
  id: z.number(),
  first_name,
  middle_name,
  last_name,
  address,
  company_name,
  nickname,
  website,
  email,
  phone,
  gender,
  dob,
  start_date,
  end_date,
  group_id,
  type,
  updated_by,
  updated_proxied_by,
  updated_at,
  account_id,
  config: config.optional(),
});
export type UpdateCustomerDTO = z.infer<typeof updateCustomerSchema>;

export const deleteCustomerSchema = z.object({
  ids: z.array(z.number()),
  account_id,
});
export type DeleteCustomerDTO = z.infer<typeof deleteCustomerSchema>;

const ids = z.array(z.number());
export const bulkEditCustomerSchema = z.object({
  ids,
  type,
  account_id,
  updated_at,
  updated_by,
  updated_proxied_by,
});
export type BulkEditCustomerDTO = z.infer<typeof bulkEditCustomerSchema>;
export const getCustomersByNamesSchema = z.object({
  first_names: z.array(z.string()),
  last_names: z.array(z.string()),
  company_names: z.array(z.string()),
  account_id,
});

export type GetCustomersByNamesDTO = z.infer<typeof getCustomersByNamesSchema>;
