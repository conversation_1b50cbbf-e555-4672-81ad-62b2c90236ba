import type { CustomerType } from './customer.constants';

export type CreateCustomerFromPolicies = Omit<Customer, 'str_id' | 'id'> & {
  policy_str_ids: string[];
  customer_id?: number;
  clientId: string; // Only use for client side to keep track of ui state
  dedupe_group_ids?: string[];
  policies_ids?: string[]; // Only use for client side to show data
  dedupe_customers_names?: string[];
  internal_id?: string;
};

export type CreateCustomerFromPoliciesPostData = Omit<
  CreateCustomerFromPolicies,
  'clientId' | 'dedupe_group_ids'
>;

export type Customer = {
  id: number;
  str_id: string;
  first_name?: string;
  last_name?: string;
  company_name?: string;
  group_id?: string;
  type: CustomerType;
};
