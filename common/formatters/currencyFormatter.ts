import BigNumber from 'bignumber.js';

/**
 * Formats a small percentage value with a specified precision, ensuring correct rounding.
 *
 * This function first rounds the input value to a higher precision if necessary,
 * then rounds it again to the desired number of decimal places using `BigNumber.ROUND_HALF_UP`.
 * This is useful for displaying small percentage values where precision and rounding are important.
 *
 * @param value - The percentage value to format, as a `BigNumber`.
 * @param options.precision - The number of decimal places to round the value to.
 * @returns The formatted percentage value as a string, rounded to the specified precision.
 */
const formatSmallPercentageWithPrecision = (
  value: BigNumber,
  options: { precision: number }
): string => {
  const maxDecimalPlaces = options.precision;
  const totalValueDecimalPlaces = value.decimalPlaces() as number;

  let intermediateValue = BigNumber(value.toString());

  /**
   * Step A: Round to a higher precision first.
   * For example, given 9.1444445 turns it:
   *  - into '9.2' with precision 1
   *  - into '9.15' with precision 2
   *  - into '9.145' with precision 3.
   * */
  for (
    let decimalPlaces = totalValueDecimalPlaces;
    decimalPlaces > maxDecimalPlaces;
    decimalPlaces--
  ) {
    intermediateValue = intermediateValue.dp(
      decimalPlaces,
      BigNumber.ROUND_HALF_UP
    );
  }

  const finalValue = intermediateValue.toFixed(
    maxDecimalPlaces,
    BigNumber.ROUND_HALF_UP
  );

  return finalValue;
};

export { formatSmallPercentageWithPrecision };
