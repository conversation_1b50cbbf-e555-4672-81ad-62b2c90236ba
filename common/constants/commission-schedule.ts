export const PaymentDateBasis = {
  EFFECTIVE_DATE: 'effective_date',
  FIRST_PAYMENT_DATE: 'first_payment_date',
} as const;

export type paymentDateBasis =
  (typeof PaymentDateBasis)[keyof typeof PaymentDateBasis];

export const PremiumAmountBasis = {
  POLICY_TARGET_PREMIUM: 'policy_target_premium',
  POLICY_ANNUALIZED_REVENUE: 'policy_annualized_revenue',
  COMMISSION_PREMIUM_AMOUNT_12: 'commission_premium_amount_12',
} as const;
export type premiumAmountBasis =
  (typeof PremiumAmountBasis)[keyof typeof PremiumAmountBasis];

export const CommissionScheduleType = {
  NINE_MONTH: 'nine_month',
  SIX_MONTH: 'six_month',
  CUSTOM: 'custom',
} as const;
export type CommissionScheduleTypes =
  (typeof CommissionScheduleType)[keyof typeof CommissionScheduleType];
