export const DEFAULT_PROMPT = `
Extract the following information from the given pdf:
    1-Find the statement date mentioned in the text and convert it to this format MM/DD/YYYY.
    2-Locate the data entries containing policy number which is Unique identifier of insurance policy (it might not exist and it might be called customer no.), commission which is the Commission received according to the carrier commission statement amount and customer name by identifying the lines that have the required information.
    3-Extract the following with the data entries if exists: Agent name, Carrier/MGA, Paying entity, Premium amount, Commission rate, Effective date, Period date, Transaction type, Product type, Product name, Compensation type, Statement number, Agent id and Group number.
    4-For any field that doesn't exist and is not 0, use null.

Format the output as follows in correct JSON format without any extra chars:
    1-For each valid data entry extract the following statement_date , policy_number, commission_amount, customer_name, agent_name, carrier_name, paying_entity, premium_amount, commission_rate, effective_date, period_date, transaction_type, product_name, product_type, compensation_type, statement_number, agent_id and group_number.
    2-Convert all dates to this format MM/DD/YYYY.
    3-If the period has 2 dates choose the end date.

***Do not add '''json ''' to the output***
***If any field is written once in the document but it goes with multiple rows, you should add to all rows in the json output.***
`;

export enum AIModel {
  CHATGPT = 'chatgpt',
  GROK = 'grok',
  CLAUDE = 'claude',
  GEMINI = 'gemini',
  LLAMA_INDEX = 'llamaindex',
  MISTRAL = 'mistral',
}

export const AI_MODEL_LABELS = {
  [AIModel.CHATGPT]: 'OpenAI',
  [AIModel.GROK]: 'Grok',
  [AIModel.CLAUDE]: 'Claude',
  [AIModel.GEMINI]: 'Gemini',
  [AIModel.LLAMA_INDEX]: 'Llama',
  [AIModel.MISTRAL]: 'Mistral',
};
