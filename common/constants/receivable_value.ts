import { TransactionParty } from 'common/globalTypes';

export const ReceivableValueMap = {
  [TransactionParty.AGENCY]: {
    label: 'Agency receivable',
    rate_field: 'receivable_value_agency_rate',
  },
  [TransactionParty.AGENT]: {
    label: 'Agent receivable',
    rate_field: 'receivable_value_agent_rate',
  },
  [TransactionParty.POLICY]: {
    label: 'Override receivable',
    rate_field: 'receivable_value_override_rate',
  },
};
