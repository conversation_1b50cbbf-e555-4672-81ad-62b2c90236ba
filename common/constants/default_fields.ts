export const DEFAULT_PAGE_SETTING_FIELDS = {
  // All fields commissions page
  commissions: [
    'policy_id',
    'advanced_commission_amount',
    'commission_amount',
    'commission_paid_amount',
    'customer_name',
    'invoice_date',
    'payment_date',
    'processing_date',
    'agent_name',
    'statement_number',
    'transaction_type',
    'writing_carrier_name',
    'premium_type',
    'carrier_name',
    'agent_id',
    'premium_amount',
    'expected_result',
    'commission_rate',
    'carrier_rate',
    'accounting_transaction_details',
    'effective_date',
    'product_type',
    'product_sub_type',
    'product_name',
    'product_option_name',
    'fees',
    'group_id',
    'internal_id',
    'period_date',
    'status',
    'compensation_type',
    'commissionable_premium_amount',
    'account_type',
    'issue_age',
    'customer_paid_premium_amount',
    'bill_mode',
    'geo_state',
    'split_percentage',
    'group_name',
    'payment_mode',
    'aggregation_id',
    'member_count',
    'commission_basis',
    'standardized_customer_name',
    'contacts',
    'agent_commissions',
    'agent_commissions_v2',
    'comp_calc',
    'comp_calc_log',
    'comp_calc_status',
    'agent_commissions_log',
    'flags',
    'agent_commissions_status',
    'agent_commissions_status2',
    'notes',
    'tags',
    'document_id',
    'processing_status',
    'children_data',
    'payment_status',
    'agent_payout_rate',
    'agent_payout_rate_override',
    'agent_commission_payout_rate',
    'report_data_id',
    'reconciliation_method',
  ],
  reconciliation: [
    'policy_id',
    'compensation_type',
    'report_str_id',
    'writing_carrier_name',
    'carrier_name',
    'customer_name',
    'product_type',
    'product_sub_type',
    'product_name',
    'effective_date',
    'premium_amount',
    'commissionable_premium_amount',
    'commissions_expected',
    'cancellation_date',
    'reinstatement_date',
    'agent_name',
    'contacts',
    'amount_paid',
    'amount_paid_commissionable_premium_amount_pct',
    'amount_paid_premium_amount_pct',
    'balance',
    'policy_status',
    'issue_age',
    'group_id',
    'internal_id',
    'notes',
    'reconciled',
    'commission_amount_monthly',
    'customer_paid_premium_amount',
    'agent_commission_amount',
    'agent_commission_amount_pct',
    'report_data',
    'log',
    'statements',
    'report_data_id',
    'statement_str_ids',
  ],
};
