import { z } from 'zod';

export const userCoreSchema = z.object({
  emailAddress: z
    .string()
    .min(1, { message: 'Email is required' })
    .email({ message: 'Please enter a valid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters long' })
    .regex(/[A-Z]/, {
      message:
        'Password must contain at least one uppercase letter and one non-alphanumeric character',
    })
    .regex(/[^a-zA-Z0-9]/, {
      message:
        'Password must contain at least one uppercase letter and one non-alphanumeric character',
    }),
});

export const signUpSchema = userCoreSchema
  .extend({
    confirmEmailAddress: z
      .string()
      .min(1, { message: 'Please confirm your email' }),
    confirmPassword: z
      .string()
      .min(1, { message: 'Please confirm your password' }),
  })
  .refine((data) => data.emailAddress === data.confirmEmailAddress, {
    message: "Emails don't match. Please validate your email.",
    path: ['confirmEmailAddress'],
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match. Please validate your password.",
    path: ['confirmPassword'],
  });
