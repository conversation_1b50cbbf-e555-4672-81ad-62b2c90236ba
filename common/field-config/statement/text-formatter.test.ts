import { describe, it, expect, beforeEach } from 'vitest';
import type { Contact } from 'common/Formatter';

import { StatementTextFormatter } from './text-formatter';

const mockContact = {
  id: 1,
  str_id: 'contact-1',
  first_name: '<PERSON>',
  last_name: '<PERSON>',
  email: '<EMAIL>',
  account_id: 'account-1',
};

const mockCompany = {
  id: 1,
  str_id: 'company-1',
  company_name: 'Test Insurance Company',
  account_id: 'account-1',
  alias_list: [],
  created_at: '2024-01-01T00:00:00.000Z',
};

const mockRow = {
  report: {
    policy_id: 'POL123456',
    aggregation_id: 'AGG789',
    str_id: 'report-1',
    agent_payout_rate_override: { 'contact-1': 0.05, 'contact-2': 0.03 },
    contacts_split: { 'contact-1': 60, 'contact-2': 40 },
  },
  contacts: ['contact-1', 'contact-2'],
  agentCommissionContacts: {
    'contact-1': '<PERSON>',
    'contact-2': '<PERSON>',
  },
  compCalcContactMapById: {
    '1': { name: '<PERSON>', type: 'agent' },
    '2': { name: 'John Doe', type: 'agent' },
  },
};

describe('StatementTextFormatter', () => {
  let formatter: StatementTextFormatter;

  beforeEach(() => {
    formatter = new StatementTextFormatter({
      account_id: 'test-account',
      timezone: 'UTC',
    });
  });

  describe('PolicyIdTextFormatter', () => {
    it('Given a valid policy ID string, should format it correctly', () => {
      expect(formatter.policyIdTextFormatter('POL123456')).toBe('POL123456');
    });

    it('Given a numeric policy ID, should convert to string format', () => {
      expect(formatter.policyIdTextFormatter(123456)).toBe('123456');
    });

    it('Given null policy ID, should return empty string', () => {
      expect(formatter.policyIdTextFormatter(null)).toBe('');
    });

    it('Given undefined policy ID, should return empty string', () => {
      expect(formatter.policyIdTextFormatter(undefined)).toBe('');
    });
  });

  describe('AggregationIdTextFormatter', () => {
    it('Given row with existing aggregation_id, should return the aggregation_id value', () => {
      expect(
        formatter.aggregationIdTextFormatter('AGG789', {
          report: { policy_id: 'POL123456', aggregation_id: 'AGG789' },
        })
      ).toBe('AGG789');
    });

    it('Given row with policy_id but no aggregation_id value, should return formatted aggregation_id with asterisk', () => {
      expect(
        formatter.aggregationIdTextFormatter('', {
          report: { policy_id: 'POL123456', aggregation_id: 'AGG789' },
        })
      ).toBe('AGG789*');
    });

    it('Given row with no aggregation_id and no policy_id, should return empty string', () => {
      expect(
        formatter.aggregationIdTextFormatter('', {
          report: { policy_id: '', aggregation_id: '' },
        })
      ).toBe('');
    });
  });

  describe('TagsTextFormatter', () => {
    it('Given an array of tags, should format as comma separated values', () => {
      expect(formatter.tagsTextFormatter(['tag1', 'tag2', 'tag3'])).toBe(
        'tag1, tag2, tag3'
      );
    });

    it('Given string tags format, should return original string value', () => {
      expect(formatter.tagsTextFormatter('tag1,tag2,tag3')).toBe(
        'tag1,tag2,tag3'
      );
    });

    it('Given empty tags array, should return empty string', () => {
      expect(formatter.tagsTextFormatter([])).toBe('');
    });

    it('Given single tag value, should return the tag value', () => {
      expect(formatter.tagsTextFormatter('single-tag')).toBe('single-tag');
    });
  });

  describe('ContactsTextFormatter', () => {
    it('Given contacts array with valid contact collection, should format with contact names', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      expect(
        formatter.contactsTextFormatter(['contact-1', 'contact-2'], mockRow, {
          allDynamicSelectMap,
        })
      ).toContain('Jane Smith');
    });

    it('Given contacts array with missing contact in collection, should return not found message', () => {
      const allDynamicSelectMap = {
        contacts: new Map(),
      };
      expect(
        formatter.contactsTextFormatter(['missing-contact'], mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('missing-contact (not found in Fintary)');
    });
  });

  describe('DocumentIdTextFormatter', () => {
    it('Given document ID with valid document collection, should return document filename', () => {
      const allDynamicSelectMap = {
        documents: new Map([
          [
            'doc-1',
            {
              filename: 'test-document.pdf',
              file_path: '/path/test-document.pdf',
            },
          ],
        ]),
      };
      expect(
        formatter.documentIdTextFormatter('doc-1', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('test-document.pdf');
    });

    it('Given document ID with missing document in collection, should return not found message', () => {
      const allDynamicSelectMap = {
        documents: new Map(),
      };
      expect(
        formatter.documentIdTextFormatter('missing-doc', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('missing-doc (not found in Fintary)');
    });

    it('Given document ID with no collection data, should return original value', () => {
      expect(formatter.documentIdTextFormatter('doc-1', mockRow, {})).toBe(
        'doc-1'
      );
    });
  });

  describe('AgentCommissionsTextFormatter', () => {
    it('Given agent commissions with valid contact collection, should format with contact names and amounts', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          [
            'contact-2',
            {
              ...mockContact,
              id: 2,
              str_id: 'contact-2',
              first_name: 'John',
              last_name: 'Doe',
            },
          ],
        ]),
      };
      const commissions = { 'contact-1': 1000, 'contact-2': 500, total: 1500 };
      const result = formatter.agentCommissionsTextFormatter(
        commissions,
        mockRow,
        {
          allDynamicSelectMap,
        }
      );
      expect(result).toContain('Jane Smith: $1,000.00');
      expect(result).toContain('John Doe: $500.00');
    });

    it('Given null commissions value, should return empty string', () => {
      const allDynamicSelectMap = {
        contacts: new Map(),
      };
      expect(
        formatter.agentCommissionsTextFormatter(null, mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('');
    });

    it('Given commissions with no contacts collection, should return empty string', () => {
      const commissions = { 'contact-1': 1000 };
      expect(
        formatter.agentCommissionsTextFormatter(commissions, mockRow, {})
      ).toBe('');
    });
  });

  describe('AgentCommissionsLogTextFormatter', () => {
    it('Given agent commissions log data, should format the log entries', () => {
      const log = {
        'contact-1': [{ message: 'Commission calculated' }],
        'contact-2': [{ message: 'Commission paid' }],
      };
      const result = formatter.agentCommissionsLogTextFormatter(log, mockRow);
      expect(result).toBeDefined();
    });

    it('Given empty log object, should handle gracefully', () => {
      const result = formatter.agentCommissionsLogTextFormatter({}, mockRow);
      expect(result).toBeDefined();
    });
  });

  describe('CompCalcTextFormatter', () => {
    it('Given comp calc data with valid contact collection, should format with contact names and amounts', () => {
      const allDynamicSelectMap = {
        contacts: new Map<string | number, Contact>([
          [mockContact.str_id, mockContact],
          [mockContact.id, mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
          [2, { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      const compCalc = { '1': 1000, total: 1500 };
      const result = formatter.compCalcTextFormatter(compCalc, mockRow, {
        allDynamicSelectMap,
      });
      expect(result).toContain('Jane Smith: $1,000.00');
    });

    it('Given null comp calc value, should return empty string', () => {
      expect(formatter.compCalcTextFormatter(null, mockRow, {})).toBe('');
    });
  });

  describe('CompCalcLogTextFormatter', () => {
    it('Given comp calc log data, should format the log entries', () => {
      const log = {
        '1': [{ message: 'Comp calculated' }],
        '2': [{ message: 'Comp paid' }],
      };
      const result = formatter.compCalcLogTextFormatter(log, mockRow);
      expect(result).toBeDefined();
    });
  });

  describe('CompCalcStatusTextFormatter', () => {
    it('Given comp calc status with valid contact collection, should format with contact names and status values', () => {
      const allDynamicSelectMap = {
        contacts: new Map<string | number, Contact>([
          [mockContact.str_id, mockContact],
          [mockContact.id, mockContact],
          [
            'contact-2',
            {
              ...mockContact,
              id: 2,
              str_id: 'contact-2',
              first_name: 'John',
              last_name: 'Doe',
            },
          ],
          [
            2,
            {
              ...mockContact,
              id: 2,
              str_id: 'contact-2',
              first_name: 'John',
              last_name: 'Doe',
            },
          ],
        ]),
      };
      const status = { '1': 56, '2': 44, total: 100 };
      const result = formatter.compCalcStatusTextFormatter(status, mockRow, {
        allDynamicSelectMap,
      });
      expect(result).toContain('Jane Smith: 56');
      expect(result).toContain('John Doe: 44');
    });

    it('Given null status value, should return empty string', () => {
      expect(formatter.compCalcStatusTextFormatter(null, mockRow, {})).toBe('');
    });
  });

  describe('AgentPayoutRateTextFormatter', () => {
    it('Given agent payout rates with valid contact collection, should format with contact names and rate percentages', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          [
            'contact-2',
            {
              ...mockContact,
              id: 2,
              str_id: 'contact-2',
              first_name: 'John',
              last_name: 'Doe',
            },
          ],
        ]),
      };
      const rates = { 'contact-1': 0.05, 'contact-2': 0.03, total: 0.04 };
      const result = formatter.agentPayoutRateTextFormatter(rates, mockRow, {
        allDynamicSelectMap,
      });
      expect(result).toContain('Jane Smith: 0.050%');
      expect(result).toContain('John Doe: 0.030%');
    });

    it('Given null rates value, should return empty string', () => {
      const allDynamicSelectMap = {
        contacts: new Map(),
      };
      expect(
        formatter.agentPayoutRateTextFormatter(null, mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('');
    });
  });

  describe('AgentPayoutRateOverrideTextFormatter', () => {
    it('Given agent payout rate override with valid contact collection, should format with contact names and override percentages', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          [
            'contact-2',
            {
              ...mockContact,
              id: 2,
              str_id: 'contact-2',
              first_name: 'John',
              last_name: 'Doe',
            },
          ],
        ]),
      };
      const overrideRates = { 'contact-1': 0.05 };
      const result = formatter.agentPayoutRateOverrideTextFormatter(
        overrideRates,
        mockRow,
        {
          allDynamicSelectMap,
        }
      );
      expect(result).toContain('Jane Smith: 0.05%');
    });

    it('Given null override value with report override rates, should use report override rates', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          [
            'contact-2',
            {
              ...mockContact,
              id: 2,
              str_id: 'contact-2',
              first_name: 'John',
              last_name: 'Doe',
            },
          ],
        ]),
      };
      const result = formatter.agentPayoutRateOverrideTextFormatter(
        null,
        mockRow,
        {
          allDynamicSelectMap,
        }
      );
      expect(result).toContain('Jane Smith: 0.05%');
      expect(result).toContain('John Doe: 0.03%');
    });

    it('Given override rates with no contacts collection, should return empty string', () => {
      const overrideRates = { 'contact-1': 0.05 };
      expect(
        formatter.agentPayoutRateOverrideTextFormatter(
          overrideRates,
          mockRow,
          {}
        )
      ).toBe('');
    });
  });

  describe('AgentCommissionPayoutRateTextFormatter', () => {
    it('Given agent commission payout rates with valid contact collection, should format with contact names and rate percentages', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      const rates = { 'contact-1': 0.05, 'contact-2': 0.03, total: 0.04 };
      const result = formatter.agentCommissionPayoutRateTextFormatter(
        rates,
        mockRow,
        {
          allDynamicSelectMap,
        }
      );
      expect(result).toContain('Jane Smith: 0.050%');
      expect(result).toContain('Jane Smith: 0.030%');
    });

    it('Given null rates value, should return empty string', () => {
      const allDynamicSelectMap = {
        contacts: new Map(),
      };
      expect(
        formatter.agentCommissionPayoutRateTextFormatter(null, mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('');
    });
  });

  describe('AgentCommissionsStatus2TextFormatter', () => {
    it('Given agent commissions status with valid contact collection, should format with contact names and status values', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      const status = { 'contact-1': 'completed', 'contact-2': 'pending' };
      const result = formatter.agentCommissionsStatus2TextFormatter(
        status,
        mockRow,
        {
          allDynamicSelectMap,
        }
      );
      expect(result).toContain('Jane Smith: completed');
      expect(result).toContain('Jane Smith: pending');
    });

    it('Given null status value, should return empty string', () => {
      expect(
        formatter.agentCommissionsStatus2TextFormatter(null, mockRow, {})
      ).toBe('');
    });
  });

  describe('ChildrenDataTextFormatter', () => {
    it('Given children data array with str_id properties, should format as comma separated values', () => {
      const childrenData = [{ str_id: 'child-1' }, { str_id: 'child-2' }];
      expect(formatter.childrenDataTextFormatter(childrenData)).toBe(
        'child-1\nchild-2'
      );
    });

    it('Given empty children data array, should return empty string', () => {
      expect(formatter.childrenDataTextFormatter([])).toBe('');
    });
  });

  describe('ReportDataIdTextFormatter', () => {
    it('Given report data with existing str_id and value, should return the value', () => {
      expect(
        formatter.reportDataIdTextFormatter('report-1', {
          report: { str_id: 'report-1' },
        })
      ).toBe('report-1');
    });

    it('Given report data with no value, should return empty string', () => {
      expect(
        formatter.reportDataIdTextFormatter('', {
          report: { str_id: 'report-1' },
        })
      ).toBe('');
    });
  });

  describe('ReconciliationMethodTextFormatter', () => {
    it('Given reconciliation method value "0", should return "Manual"', () => {
      expect(
        formatter.reconciliationMethodTextFormatter('0', mockRow, {})
      ).toBe('Manual');
    });

    it('Given reconciliation method with valid reconcilers collection, should return reconciler name', () => {
      const allDynamicSelectMap = {
        reconcilers: new Map([['1', { name: 'Auto Reconciler' }]]),
      };
      expect(
        formatter.reconciliationMethodTextFormatter('1', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('1 (not found in Fintary)');
    });

    it('Given reconciliation method with missing reconciler in collection, should return not found message', () => {
      const allDynamicSelectMap = {
        reconcilers: new Map(),
      };
      expect(
        formatter.reconciliationMethodTextFormatter('999', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('999 (not found in Fintary)');
    });

    it('Given reconciliation method with no collection data, should return original value', () => {
      expect(
        formatter.reconciliationMethodTextFormatter('unknown', mockRow, {})
      ).toBe('unknown');
    });
  });

  describe('FlagsTextFormatter', () => {
    it('Given flags object with key-value pairs, should format as newline separated key-value pairs', () => {
      const flags = { flag1: 'value1', flag2: 'value2' };
      expect(formatter.flagsTextFormatter(flags)).toBe(
        'flag1: value1\nflag2: value2'
      );
    });

    it('Given null flags value, should return empty string', () => {
      expect(formatter.flagsTextFormatter(null)).toBe('');
    });

    it('Given empty flags object, should return empty string', () => {
      expect(formatter.flagsTextFormatter({})).toBe('');
    });
  });

  describe('FlagsLogTextFormatter', () => {
    it('Given flags log with message properties, should format messages as newline separated values', () => {
      const flagsLog = {
        flag1: { message: 'Error occurred' },
        flag2: { message: 'Warning issued' },
      };
      expect(formatter.flagsLogTextFormatter(flagsLog)).toBe(
        'Error occurred\nWarning issued'
      );
    });

    it('Given flags log without message properties, should format values as newline separated values', () => {
      const flagsLog = {
        flag1: 'simple value',
        flag2: { message: 'Warning issued' },
      };
      expect(formatter.flagsLogTextFormatter(flagsLog)).toBe(
        'simple value\nWarning issued'
      );
    });

    it('Given null flags log value, should return empty string', () => {
      expect(formatter.flagsLogTextFormatter(null)).toBe('');
    });
  });

  describe('WritingCarrierNameTextFormatter', () => {
    it('Given writing carrier name with valid companies collection, should return company name', () => {
      const allDynamicSelectMap = {
        companies: new Map([['Test Insurance Company', mockCompany]]),
      };
      expect(
        formatter.writingCarrierNameTextFormatter(
          'Test Insurance Company',
          mockRow,
          { allDynamicSelectMap }
        )
      ).toBe('Test Insurance Company');
    });

    it('Given writing carrier name with missing company in collection, should return not found message', () => {
      const allDynamicSelectMap = {
        companies: new Map(),
      };
      expect(
        formatter.writingCarrierNameTextFormatter('Missing Company', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('Missing Company (not found in Fintary)');
    });

    it('Given writing carrier name with no collection data, should return original value', () => {
      expect(
        formatter.writingCarrierNameTextFormatter('Test Company', mockRow, {})
      ).toBe('Test Company');
    });
  });

  describe('GeoStateTextFormatter', () => {
    it('Given valid state ID, should return state label with ID', () => {
      expect(formatter.geoStateTextFormatter('CA')).toBe('California (CA)');
    });

    it('Given invalid state ID, should return original value', () => {
      expect(formatter.geoStateTextFormatter('INVALID')).toBe('INVALID');
    });

    it('Given empty state value, should return empty string', () => {
      expect(formatter.geoStateTextFormatter('')).toBe('');
    });
  });

  describe('SplitPercentageTextFormatter', () => {
    it('Given split percentage value with contacts split data, should format with percentage sign', () => {
      expect(
        formatter.splitPercentageTextFormatter('0.05', {
          report: { contacts_split: { 'contact-1': 0.05 } },
          contacts: ['contact-1'],
        })
      ).toBe('0.05%');
    });

    it('Given no split percentage value with single contact, should return formatted percentage with asterisk', () => {
      expect(
        formatter.splitPercentageTextFormatter('', {
          report: { contacts_split: { 'contact-1': 0.05 } },
          contacts: ['contact-1'],
        })
      ).toBe('0.05%*');
    });

    it('Given no split percentage value with multiple contacts, should return empty string', () => {
      expect(
        formatter.splitPercentageTextFormatter('', {
          report: { contacts_split: { 'contact-1': 0.05, 'contact-2': 0.03 } },
          contacts: ['contact-1', 'contact-2'],
        })
      ).toBe('');
    });

    it('Given no contacts data, should return empty string', () => {
      expect(
        formatter.splitPercentageTextFormatter('', {
          report: { contacts_split: {} },
          contacts: [],
        })
      ).toBe('');
    });
  });
});
