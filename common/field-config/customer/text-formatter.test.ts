import { describe, it, expect, beforeEach } from 'vitest';

import { CustomerTextFormatter } from './text-formatter';

const mockFullAddress = {
  street: '123 Main Street',
  city: 'New York',
  geo_state: 'NY',
  zipcode: '10001',
};

const mockPartialAddress = {
  street: '456 Oak Avenue',
  city: 'Los Angeles',
  geo_state: '',
  zipcode: '90210',
};

const mockMinimalAddress = {
  street: '',
  city: 'Chicago',
  geo_state: '',
  zipcode: '',
};

const mockEmptyAddress = {
  street: '',
  city: '',
  geo_state: '',
  zipcode: '',
};

const mockReportData = [
  { id: 1, name: 'Report 1' },
  { id: 2, name: 'Report 2' },
  { id: 3, name: 'Report 3' },
];

describe('CustomerTextFormatter', () => {
  let formatter: CustomerTextFormatter;

  beforeEach(() => {
    formatter = new CustomerTextFormatter();
  });

  describe('addressTextFormatter', () => {
    it('Should format full address with all fields', () => {
      expect(formatter.addressTextFormatter(mockFullAddress)).toBe(
        '123 Main Street, New York, NY, 10001'
      );
    });

    it('Should format partial address excluding empty fields', () => {
      expect(formatter.addressTextFormatter(mockPartialAddress)).toBe(
        '456 Oak Avenue, Los Angeles, 90210'
      );
    });

    it('Should format address with only one field', () => {
      expect(formatter.addressTextFormatter(mockMinimalAddress)).toBe(
        'Chicago'
      );
    });

    it('Should return empty string for address with all empty fields', () => {
      expect(formatter.addressTextFormatter(mockEmptyAddress)).toBe('');
    });

    it('Should return empty string for null address', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.addressTextFormatter(null as any)).toBe('');
    });

    it('Should return empty string for undefined address', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.addressTextFormatter(undefined as any)).toBe('');
    });

    it('Should handle address with only street', () => {
      const streetOnlyAddress = {
        street: '789 Pine Street',
        city: '',
        geo_state: '',
        zipcode: '',
      };
      expect(formatter.addressTextFormatter(streetOnlyAddress)).toBe(
        '789 Pine Street'
      );
    });

    it('Should handle address with only city and state', () => {
      const cityStateAddress = {
        street: '',
        city: 'San Francisco',
        geo_state: 'CA',
        zipcode: '',
      };
      expect(formatter.addressTextFormatter(cityStateAddress)).toBe(
        'San Francisco, CA'
      );
    });

    it('Should handle address with only zipcode', () => {
      const zipcodeOnlyAddress = {
        street: '',
        city: '',
        geo_state: '',
        zipcode: '12345',
      };
      expect(formatter.addressTextFormatter(zipcodeOnlyAddress)).toBe('12345');
    });

    it('Should handle address with mixed empty and filled fields', () => {
      const mixedAddress = {
        street: '100 Test St',
        city: '',
        geo_state: 'TX',
        zipcode: '75001',
      };
      expect(formatter.addressTextFormatter(mixedAddress)).toBe(
        '100 Test St, TX, 75001'
      );
    });
  });

  describe('reportDataTextFormatter', () => {
    it('Should return length of array with data', () => {
      expect(formatter.reportDataTextFormatter(mockReportData)).toBe(3);
    });

    it('Should return empty string for empty array', () => {
      expect(formatter.reportDataTextFormatter([])).toBe('');
    });

    it('Should return empty string for null', () => {
      expect(formatter.reportDataTextFormatter(null)).toBe('');
    });

    it('Should return empty string for undefined', () => {
      expect(formatter.reportDataTextFormatter(undefined)).toBe('');
    });

    it('Should return length for array with single item', () => {
      const singleItemArray = [{ id: 1, name: 'Single Report' }];
      expect(formatter.reportDataTextFormatter(singleItemArray)).toBe(1);
    });

    it('Should return length for array with different types of items', () => {
      const mixedArray = [
        'string',
        123,
        { object: 'value' },
        null,
        undefined,
        true,
      ];
      expect(formatter.reportDataTextFormatter(mixedArray)).toBe(6);
    });

    it('Should return length for array with nested arrays', () => {
      const nestedArray = [[1, 2, 3], [4, 5], [6]];
      expect(formatter.reportDataTextFormatter(nestedArray)).toBe(3);
    });

    it('Should return 0 for falsy values that are not arrays', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.reportDataTextFormatter(false as any)).toBe('');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.reportDataTextFormatter(0 as any)).toBe('');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.reportDataTextFormatter('' as any)).toBe('');
    });
  });
});
