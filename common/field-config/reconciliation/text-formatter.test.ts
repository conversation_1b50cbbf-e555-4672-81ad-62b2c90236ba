import { describe, it, expect, beforeEach, vi } from 'vitest';

import { ReconciliationTextFormatter } from './text-formatter';

const mockContactsSplitData = {
  contactsSplitInfo: ['Agent A: 60%', 'Agent B: 40%'],
};

const mockContactsData = {
  contactNames: ['<PERSON>', '<PERSON>', '<PERSON>'],
};

const mockAmountPaidData = {
  amount_paid: {
    amount_paid: 1500.5,
  },
};

const mockNestedAmountPaidData = {
  amount_paid: {
    amount_paid: {
      amount_paid: 2000,
    },
  },
  agent_commission_amount: 300,
};

const mockStatementsData = [
  {
    document: {
      filename: 'statement-01-2024.pdf',
    },
  },
  {
    document: {
      filename: 'statement-02-2024.pdf',
    },
  },
  {
    // Missing document
  },
];

const mockReportData = {
  report_data: {
    document: {
      filename: 'report-march-2024.pdf',
    },
  },
};

const mockStatementStrIds = {
  statement_str_ids: ['stmt_001', 'stmt_002', 'stmt_003'],
};

const mockReconciliationRowWithParent = {
  report_data_parent: {
    policy_id: 'parent_policy_123',
    str_id: 'parent_str_123',
  },
};

const mockReconciliationRowWithChildren = {
  children_report_data_ids: [
    {
      policy_id: 'child_policy_001',
      str_id: 'child_str_001',
    },
    {
      policy_id: 'child_policy_002',
      str_id: 'child_str_002',
    },
  ],
};

const mockCommissionAmountMonthlyData = {
  agent_001: {
    commission_amount_monthly: 1200.75,
  },
  agent_002: {
    commission_amount_monthly: 800.25,
  },
  agent_003: {
    commission_amount_monthly: 0,
  },
};

const mockProfitCalculationData = {
  amount_paid: {
    amount_paid: {
      amount_paid: 5000,
    },
  },
  agent_commission_amount: 1000,
  commissionable_premium_amount: '10000',
};

describe('ReconciliationTextFormatter', () => {
  let formatter: ReconciliationTextFormatter;

  beforeEach(() => {
    formatter = new ReconciliationTextFormatter({
      account_id: 'test-account',
      timezone: 'UTC',
    });
    vi.clearAllMocks();
  });

  describe('contactsSplitTextFormatter', () => {
    it('Should format contacts split info as newline separated values', () => {
      const result = formatter.contactsSplitTextFormatter(
        {},
        mockContactsSplitData
      );
      expect(result).toBe('Agent A: 60%\nAgent B: 40%');
    });

    it('Should return empty string when contactsSplitInfo is undefined', () => {
      const result = formatter.contactsSplitTextFormatter({}, {});
      expect(result).toBe('');
    });

    it('Should return empty string when contactsSplitInfo is empty array', () => {
      const result = formatter.contactsSplitTextFormatter(
        {},
        { contactsSplitInfo: [] }
      );
      expect(result).toBe('');
    });

    it('Should handle single contact split info', () => {
      const singleContactData = { contactsSplitInfo: ['Agent A: 100%'] };
      const result = formatter.contactsSplitTextFormatter(
        {},
        singleContactData
      );
      expect(result).toBe('Agent A: 100%');
    });
  });

  describe('contactsTextFormatter', () => {
    it('Should format contact names as comma separated values', () => {
      const result = formatter.contactsTextFormatter([], mockContactsData);
      expect(result).toBe('John Doe, Jane Smith, Bob Johnson');
    });

    it('Should return empty string when contactNames is undefined', () => {
      const result = formatter.contactsTextFormatter([], {});
      expect(result).toBe('');
    });

    it('Should return empty string when contactNames is empty array', () => {
      const result = formatter.contactsTextFormatter([], { contactNames: [] });
      expect(result).toBe('');
    });

    it('Should handle single contact name', () => {
      const singleContactData = { contactNames: ['John Doe'] };
      const result = formatter.contactsTextFormatter([], singleContactData);
      expect(result).toBe('John Doe');
    });
  });

  describe('balanceTextFormatter', () => {
    it('Should format numeric balance value as currency', () => {
      const result = formatter.balanceTextFormatter(1234.56);
      expect(result).toBe('$1,234.56');
    });

    it('Should format string balance value as currency', () => {
      const result = formatter.balanceTextFormatter('2500.75');
      expect(result).toBe('$2,500.75');
    });

    it('Should return empty string for null value', () => {
      const result = formatter.balanceTextFormatter(null);
      expect(result).toBe('');
    });

    it('Should return empty string for undefined value', () => {
      const result = formatter.balanceTextFormatter(undefined);
      expect(result).toBe('');
    });

    it('Should handle zero value', () => {
      const result = formatter.balanceTextFormatter(0);
      expect(result).toBe('$0.00');
    });

    it('Should handle negative value', () => {
      const result = formatter.balanceTextFormatter(-500.25);
      expect(result).toBe('-$500.25');
    });
  });

  describe('amountPaidTextFormatter', () => {
    it('Should format nested amount_paid value as currency', () => {
      const result = formatter.amountPaidTextFormatter(mockAmountPaidData);
      expect(result).toBe('$1,500.50');
    });

    it('Should return empty string for undefined input', () => {
      const result = formatter.amountPaidTextFormatter(undefined);
      expect(result).toBe('');
    });

    it('Should return empty string when amount_paid is missing', () => {
      const result = formatter.amountPaidTextFormatter({});
      expect(result).toBe('');
    });

    it('Should return empty string when nested amount_paid is missing', () => {
      const incompleteData = { amount_paid: {} };
      const result = formatter.amountPaidTextFormatter(incompleteData);
      expect(result).toBe('');
    });

    it('Should handle string amount_paid value', () => {
      const stringAmountData = {
        amount_paid: {
          amount_paid: '750.25',
        },
      };
      const result = formatter.amountPaidTextFormatter(stringAmountData);
      expect(result).toBe('$750.25');
    });
  });

  describe('profitTextFormatter', () => {
    it('Should calculate and format profit when data is valid', () => {
      const result = formatter.profitTextFormatter(
        null,
        mockNestedAmountPaidData
      );
      expect(result).toBe('$1,700.00');
    });

    it('Should return empty string when profit calculation returns undefined', () => {
      const invalidData = {
        amount_paid: { amount_paid: { amount_paid: NaN } },
        agent_commission_amount: 300,
      };
      const result = formatter.profitTextFormatter(null, invalidData);
      expect(result).toBe('');
    });

    it('Should return empty string when rowData is undefined', () => {
      // This will cause an error in the actual implementation, but let's test what should happen
      expect(() => {
        formatter.profitTextFormatter(null, undefined);
      }).toThrow();
    });

    it('Should return empty string for zero profit', () => {
      const zeroData = {
        amount_paid: { amount_paid: { amount_paid: 1000 } },
        agent_commission_amount: 1000,
      };
      const result = formatter.profitTextFormatter(null, zeroData);
      expect(result).toBe(''); // Zero profit returns empty string
    });

    it('Should handle negative profit', () => {
      const negativeData = {
        amount_paid: { amount_paid: { amount_paid: 500 } },
        agent_commission_amount: 800,
      };
      const result = formatter.profitTextFormatter(null, negativeData);
      expect(result).toBe('-$300.00');
    });
  });

  describe('profitPercentageTextFormatter', () => {
    it('Should calculate and format profit percentage when data is valid', () => {
      const result = formatter.profitPercentageTextFormatter(
        null,
        mockProfitCalculationData
      );
      expect(result).toBe('4000%');
    });

    it('Should return empty string when profit percentage calculation returns undefined', () => {
      const invalidData = {
        amount_paid: { amount_paid: { amount_paid: NaN } },
        agent_commission_amount: 300,
        commissionable_premium_amount: '1000',
      };
      const result = formatter.profitPercentageTextFormatter(null, invalidData);
      expect(result).toBe('');
    });

    it('Should return empty string when rowData is undefined', () => {
      // This will cause an error in the actual implementation, but let's test what should happen
      expect(() => {
        formatter.profitPercentageTextFormatter(null, undefined);
      }).toThrow();
    });

    it('Should return empty string for zero percentage', () => {
      const zeroData = {
        amount_paid: { amount_paid: { amount_paid: 1000 } },
        agent_commission_amount: 1000,
        commissionable_premium_amount: '5000',
      };
      const result = formatter.profitPercentageTextFormatter(null, zeroData);
      expect(result).toBe(''); // Zero percentage returns empty string
    });
  });

  describe('statementsTextFormatter', () => {
    it('Should format statements array with document filenames', () => {
      const result = formatter.statementsTextFormatter(mockStatementsData);
      expect(result).toBe(
        'statement-01-2024.pdf\nstatement-02-2024.pdf\nDocument not available'
      );
    });

    it('Should return empty string for empty array', () => {
      const result = formatter.statementsTextFormatter([]);
      expect(result).toBe('');
    });

    it('Should return empty string for undefined input', () => {
      const result = formatter.statementsTextFormatter(undefined);
      expect(result).toBe('');
    });

    it('Should handle statements with missing documents', () => {
      const statementsWithMissingDocs = [
        { document: { filename: 'valid.pdf' } },
        {},
        { document: null },
      ];
      const result = formatter.statementsTextFormatter(
        statementsWithMissingDocs
      );
      expect(result).toBe(
        'valid.pdf\nDocument not available\nDocument not available'
      );
    });
  });

  describe('reportDataIdTextFormatter', () => {
    it('Should return report document filename', () => {
      const result = formatter.reportDataIdTextFormatter(null, mockReportData);
      expect(result).toBe('report-march-2024.pdf');
    });

    it('Should return empty string when report_data is missing', () => {
      const result = formatter.reportDataIdTextFormatter(null, {});
      expect(result).toBe('');
    });

    it('Should return empty string when document is missing', () => {
      const incompleteData = { report_data: {} };
      const result = formatter.reportDataIdTextFormatter(null, incompleteData);
      expect(result).toBe('');
    });

    it('Should return empty string when filename is missing', () => {
      const incompleteData = { report_data: { document: {} } };
      const result = formatter.reportDataIdTextFormatter(null, incompleteData);
      expect(result).toBe('');
    });
  });

  describe('statementStrIdsTextFormatter', () => {
    it('Should format statement string IDs as newline separated values', () => {
      const result = formatter.statementStrIdsTextFormatter(
        null,
        mockStatementStrIds
      );
      expect(result).toBe('stmt_001\nstmt_002\nstmt_003');
    });

    it('Should return empty string when statement_str_ids is undefined', () => {
      const result = formatter.statementStrIdsTextFormatter(null, {});
      expect(result).toBe('');
    });

    it('Should return empty string when statement_str_ids is empty array', () => {
      const result = formatter.statementStrIdsTextFormatter(null, {
        statement_str_ids: [],
      });
      expect(result).toBe('');
    });

    it('Should handle single statement string ID', () => {
      const singleStmtData = { statement_str_ids: ['stmt_001'] };
      const result = formatter.statementStrIdsTextFormatter(
        null,
        singleStmtData
      );
      expect(result).toBe('stmt_001');
    });
  });

  describe('policyIdTextFormatter', () => {
    it('Should return policy ID with asterisk for parent report data', () => {
      const result = formatter.policyIdTextFormatter(
        'POL123',
        mockReconciliationRowWithParent
      );
      expect(result).toBe('POL123*');
    });

    it('Should return policy ID with folder icon for children report data', () => {
      const result = formatter.policyIdTextFormatter(
        'POL456',
        mockReconciliationRowWithChildren
      );
      expect(result).toBe('POL456 🗂️');
    });

    it('Should return policy ID as-is for regular data', () => {
      const result = formatter.policyIdTextFormatter('POL789', {});
      expect(result).toBe('POL789');
    });

    it('Should handle numeric policy ID', () => {
      const result = formatter.policyIdTextFormatter(123456, {});
      expect(result).toBe(123456);
    });

    it('Should prioritize parent over children when both exist', () => {
      const dataWithBoth = {
        report_data_parent: {
          policy_id: 'parent_policy_999',
          str_id: 'parent_str_999',
        },
        children_report_data_ids: [
          {
            policy_id: 'child_policy_001',
            str_id: 'child_str_001',
          },
        ],
      };
      const result = formatter.policyIdTextFormatter('POL999', dataWithBoth);
      expect(result).toBe('POL999*');
    });
  });

  describe('commissionAmountMonthlyTextFormatter', () => {
    it('Should format commission amount monthly data as key-value pairs', () => {
      const result = formatter.commissionAmountMonthlyTextFormatter(
        mockCommissionAmountMonthlyData
      );
      expect(result).toBe(
        'agent_001: $1,200.75\nagent_002: $800.25\nagent_003: $0.00'
      );
    });

    it('Should return empty string for undefined input', () => {
      const result = formatter.commissionAmountMonthlyTextFormatter(undefined);
      expect(result).toBe('');
    });

    it('Should return empty string for null input', () => {
      const result = formatter.commissionAmountMonthlyTextFormatter(null);
      expect(result).toBe('');
    });

    it('Should return empty string for empty object', () => {
      const result = formatter.commissionAmountMonthlyTextFormatter({});
      expect(result).toBe('');
    });

    it('Should handle missing commission_amount_monthly values', () => {
      const incompleteData = {
        agent_001: {},
        agent_002: { commission_amount_monthly: 500 },
      };
      const result =
        formatter.commissionAmountMonthlyTextFormatter(incompleteData);
      expect(result).toBe('agent_001: \nagent_002: $500.00');
    });
  });

  describe('agentCommissionAmountPctTextFormatter', () => {
    it('Should format percentage value', () => {
      const result = formatter.agentCommissionAmountPctTextFormatter(0.25);
      expect(result).toBe('25.0%'); // 0.25 * 100 = 25%
    });

    it('Should return empty string for NaN value', () => {
      const result = formatter.agentCommissionAmountPctTextFormatter(NaN);
      expect(result).toBe('');
    });

    it('Should format zero percentage', () => {
      const result = formatter.agentCommissionAmountPctTextFormatter(0);
      expect(result).toBe('0.00%');
    });

    it('Should format negative percentage', () => {
      const result = formatter.agentCommissionAmountPctTextFormatter(-0.15);
      expect(result).toBe('-15.0%'); // -0.15 * 100 = -15%
    });

    it('Should handle string input that converts to number', () => {
      const result = formatter.agentCommissionAmountPctTextFormatter('0.50');
      expect(result).toBe('0.50'); // String is returned as-is
    });
  });
});
