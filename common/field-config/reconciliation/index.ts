import { RECONCILIATION_VERSION } from 'common/constants/reconciliation';
import { FieldTypes } from 'common/constants';
import UILabels from 'common/constants/UILabels';
import { SystemRoles } from 'common/globalTypes';
import Formatter from 'common/Formatter';
import {
  formatCurrency,
  formatDate,
} from 'common/helpers/DataTransformation/formatter';

import { ReconciliationTextFormatter } from './text-formatter';

const getReconciliationFields = ({
  mode,
  role,
  options,
}: {
  mode: 'insurance' | 'default';
  role: SystemRoles | null;
  options: { version?: string; account_id?: string };
}) => {
  const strings = new UILabels(mode);
  const textFormatter = new ReconciliationTextFormatter({
    account_id: options?.account_id,
  });

  return {
    table: 'reconciliation_data',
    label: 'Reconciliation',
    fields: {
      profit: {
        enabled: true,
        label: 'Profit',
        sort: false,
        type: FieldTypes.TABLE_CELL_CUSTOM,
        textFormatter: textFormatter.profitTextFormatter,
      },
      profit_percentage: {
        enabled: true,
        label: '% profit',
        sort: false,
        type: FieldTypes.TABLE_CELL_CUSTOM,
        textFormatter: textFormatter.profitPercentageTextFormatter,
      },
      policy_id: {
        label: strings.getLabel('reconciliation', 'salesId'),
        bulkEdit: true,
        enabled: true,
        copyable: true,
        readOnly: true,
        textFormatter: textFormatter.policyIdTextFormatter,
      },
      compensation_type: {
        label: 'Compensation type',
        enabled: true,
        readOnly: true,
      },
      report_str_id: {
        label: 'Linked policy',
        enabled: true,
      },
      writing_carrier_name: {
        bulkEdit: true,
        label: strings.getLabel('reconciliation', 'entity'),
        enabled: mode === 'insurance',
        global: true,
        readOnly: true,
      },
      carrier_name: {
        label: strings.getLabel('reconciliation', 'payingEntity'),
        bulkEdit: true,
        enabled: true,
        global: true,
        readOnly: true,
      },
      customer_name: {
        bulkEdit: true,
        label: strings.getLabel('reconciliation', 'customerName'),
        enabled: true,
        readOnly: true,
      },
      product_type: {
        label: 'Product type',
        bulkEdit: true,
        enabled: true,
        readOnly: true,
      },
      product_sub_type: {
        label: 'Product sub type',
        enabled: true,
        readOnly: true,
      },
      product_name: {
        label: 'Product name',
        bulkEdit: true,
        enabled: true,
        readOnly: true,
      },
      effective_date: {
        bulkEdit: true,
        label: strings.getLabel('reconciliation', 'startDate'),
        enabled: true,
        type: 'date',
        readOnly: true,
        textFormatter: formatDate,
      },
      processing_date: {
        label: strings.getLabel('cash', 'processingDate'),
        matches: ['processing date', 'processingDate'],
        enabled: options?.version === RECONCILIATION_VERSION.V2,
        type: FieldTypes.DATE,
        textFormatter: formatDate,
      },
      premium_amount: {
        label: strings.getLabel('reconciliation', 'annualizedRevenue'),
        enabled: mode === 'insurance',
        type: FieldTypes.CURRENCY,
        readOnly: true,
        textFormatter: formatCurrency,
      },
      commissionable_premium_amount: {
        label: 'Target premium',
        matches: ['target premium', 'commissionable premium'],
        enabled: true,
        type: 'currency',
        readOnly: true,
        textFormatter: formatCurrency,
      },
      commissions_expected: {
        label: 'Commissions due',
        description:
          'Commission amount due. Calculated from commission schedules when available.',
        matches: [
          'agency commission total',
          'commissions expected',
          'commission expected',
        ],
        enabled: mode === 'insurance',
        type: FieldTypes.CURRENCY,
        infoIcon: true,
        readOnly: true,
        textFormatter: formatCurrency,
      },
      cancellation_date: {
        label: 'Cancellation date',
        enabled: mode === 'insurance',
        type: FieldTypes.DATE,
        defaultTableHidden: true,
        readOnly: true,
        textFormatter: formatDate,
      },
      reinstatement_date: {
        label: 'Reinstatement date',
        enabled: mode === 'insurance',
        type: FieldTypes.DATE,
        defaultTableHidden: true,
        readOnly: true,
        textFormatter: formatDate,
      },
      agent_name: {
        bulkEdit: true,
        label: 'Agent name',
        enabled: mode === 'insurance',
        defaultTableHidden: true,
        readOnly: true,
      },
      contacts: {
        bulkEdit: true,
        label: 'Agents',
        enabled: mode === 'insurance',
        type: FieldTypes.DYNAMIC_SELECT,
        multiple: true,
        table: 'contacts',
        readOnly: true,
        textFormatter: textFormatter.contactsTextFormatter,
      },
      amount_paid: {
        label: strings.getLabel('reconciliation', 'cashPaid'),
        id2: 'amountPaid',
        description:
          'Commission amount paid as recorded from commission statements',
        infoIcon: true,
        enabled: true,
        type: FieldTypes.CURRENCY,
        readOnly: true,
        textFormatter: textFormatter.amountPaidTextFormatter,
      },
      amount_paid_commissionable_premium_amount_pct: {
        label: '% Target premium',
        enabled: true,
        type: FieldTypes.PERCENTAGE,
        readOnly: true,
        textFormatter: Formatter.percentageWithPrecision,
      },
      amount_paid_premium_amount_pct: {
        label: '% Premium',
        enabled: true,
        type: FieldTypes.PERCENTAGE,
        readOnly: true,
        textFormatter: Formatter.percentageWithPrecision,
      },
      balance: {
        // Type: 'computed',
        label: strings.getLabel('reconciliation', 'balance'),
        enabled: true,
        type: FieldTypes.CURRENCY,
        compute: (v: {
          commissions_expected?: number;
          amount_paid?: number;
        }) => {
          return (v.commissions_expected ?? 0) - (v.amount_paid ?? 0);
        },
        readOnly: true,
        textFormatter: formatCurrency,
      },
      policy_status: {
        type: 'field',
        fieldId: 'policy_status',
        label: strings.getLabel('reconciliation', 'status'),
        infoIcon: false,
        enabled: true,
        // Visible: false,
        readOnly: true,
        bulkEdit: true,
      },
      issue_age: {
        label: 'Issue age',
        enabled: true,
        matches: ['issue age'],
        type: 'integer',
        readOnly: true,
      },
      group_id: {
        label: 'Group id',
        enabled: true,
        readOnly: true,
      },
      internal_id: {
        bulkEdit: true,
        label: 'Internal id',
        enabled: true,
        readOnly: true,
      },
      notes: {
        type: 'field',
        fieldId: 'notes',
        label: 'Notes',
        visible: true,
        enabled: true,
      },
      reconciled: {
        label: strings.getLabel('reconciliation', 'reconciliationStatus'),
        readOnly: true,
        enabled: true,
      },
      commission_amount_monthly: {
        visible: false,
        enabled: true,
        type: FieldTypes.CURRENCY,
        readOnly: true,
        label: 'Commission amount monthly',
        textFormatter: textFormatter.commissionAmountMonthlyTextFormatter,
      },
      customer_paid_premium_amount: {
        label: 'Customer paid premium',
        enabled: true,
        matches: ['Basis', 'customer paid premium'],
        textFormatter: formatCurrency,
      },
      agent_commission_amount: {
        label: 'Agent commission',
        enabled: true,
        textFormatter: formatCurrency,
      },
      agent_commission_amount_pct: {
        label: '% Agent commission',
        enabled: true,
        textFormatter: textFormatter.agentCommissionAmountPctTextFormatter,
      },
      report_data: {
        label: 'Agent split',
        table: 'contacts',
        enabled: true,
        textFormatter: textFormatter.contactsSplitTextFormatter,
      },
      log: {
        type: 'field',
        fieldId: 'log',
        label: 'Log 🔒',
        visible: true,
        enabled: role === SystemRoles.ADMIN,
        readOnly: true,
      },
      statements: {
        type: 'field',
        label: 'Statement documents',
        enabled: true,
        visible: true,
        readOnly: true,
        textFormatter: textFormatter.statementsTextFormatter,
      },
      report_data_id: {
        visible: true,
        label: 'Policy document',
        type: 'field',
        enabled: true,
        readOnly: true,
        textFormatter: textFormatter.reportDataIdTextFormatter,
      },
      statement_str_ids: {
        type: 'field',
        fieldId: 'statement_str_ids',
        label: 'Statements 🔒',
        enabled: role === SystemRoles.ADMIN,
        readOnly: true,
        textFormatter: textFormatter.statementStrIdsTextFormatter,
      },
      payment_mode: {
        label: 'Payment mode',
        matches: ['payment mode'],
        enabled: options?.version === RECONCILIATION_VERSION.V2,
        readOnly: true,
        type: FieldTypes.TEXT,
      },
    },
  };
};

export default getReconciliationFields;
