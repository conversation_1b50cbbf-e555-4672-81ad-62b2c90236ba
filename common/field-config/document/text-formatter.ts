import { formatCurrency } from 'common/helpers/formatCurrency';
import dayjs from 'dayjs';
import {
  DocumentTypeLabels,
  type DocumentTypes,
} from 'common/constants/documents';
import { numberOrDefault } from 'common/helpers';
import {
  DocumentStatuses,
  DocumentStatusesLabels,
  ProcessMethod,
} from 'common/globalTypes';
import CommonFormatter from 'common/Formatter';
import type { Document } from 'common/documents/documents.types';
import type { Company } from 'common/types/companies';
import BigNumber from 'bignumber.js';
import BaseTextFormatter from 'common/field-config/shared/base/base-text-formatter';
import { DATE_TIME_FORMATS, TIMEZONE } from 'common/constants/date-time';

export class DocumentTextFormatter extends BaseTextFormatter {
  timezone?: string;

  constructor(private additionalConfig?: { timezone?: string }) {
    super();
    this.timezone = this.additionalConfig?.timezone;
  }

  filenameTextFormatter = (filename: string, row?: Document | null) => {
    if (!filename) return '--';

    const hasOverrideFile = !!row?.override_filename;
    let result = filename;

    if (hasOverrideFile) {
      result += ` (Override: ${row.override_filename})`;
    }

    return result;
  };

  typeTextFormatter = (v: DocumentTypes) => {
    return DocumentTypeLabels[v] || v || '';
  };

  companiesTextFormatter = (val: Partial<Company>) => {
    if (!val?.company_name) return '';
    return val.company_name;
  };

  statementDataTextFormatter = (val: Document['statement_data']) => {
    if (!val) return '0';

    const groupedCountInfoStrList: string[] = [];
    const groupedCommissionInfoStrList: string[] = [];

    if (val.groupedCountInfo) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.entries(val.groupedCountInfo).forEach(([key, value]) => {
        if (key !== 'NO_STATUS') {
          groupedCountInfoStrList.push(`${key}: ${value}`);
        }
      });
    }

    if (val.groupedCommissionInfo) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.entries(val.groupedCommissionInfo).forEach(([key, value]) => {
        if (key !== 'NO_STATUS') {
          groupedCommissionInfoStrList.push(`${key}: ${formatCurrency(value)}`);
        }
      });
    }

    let result = '';

    if (val.total_count) {
      result += `${+val.total_count}`;
      if (groupedCountInfoStrList.length > 0) {
        result += ` (${groupedCountInfoStrList.toString()})`;
      }
    }

    if (val.total_commission) {
      if (result) result += '\n';
      result += `${formatCurrency(val.total_commission)}`;
      if (groupedCommissionInfoStrList.length > 0) {
        result += ` (${groupedCommissionInfoStrList.join(', ')})`;
      }
    }

    return result || '0';
  };

  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  statementAmountTextFormatter = (val: string, rowData: Document) => {
    const commissionTotalCell = commissionTotalCellTextFormatter(rowData);
    const { text, icon } = commissionTotalCell;
    const result = [text];

    if (icon) {
      result.unshift(icon);
    }
    return result.join(' ');
  };

  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  bankTotalAmountTextFormatter = (v: number, row: Document) => {
    const bankTotalAmount = numberOrDefault(row.bank_total_amount, null, {
      toFixed: 2,
    });

    if (!bankTotalAmount) return '';
    return `Bank total: ${formatCurrency(bankTotalAmount)}`;
  };

  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  createdByTextFormatter = (v: string, row: Document) => {
    const list = row.imports;
    // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (list && list.length) {
      const target = list[0];
      return formatCurrency(target?.summed_total_amount ?? 0) || '';
    }
    return '';
  };

  statementMonthTextFormatter = (s: string) => {
    if (!s) return '';
    return CommonFormatter.dateTimeWithTimezone(
      s,
      TIMEZONE.UTC,
      DATE_TIME_FORMATS.MM_YYYY
    );
  };

  statusTextFormatter = (val: string, row: Document) => {
    if (
      val === DocumentStatuses.PROCESSED &&
      row.process_method === ProcessMethod.AUTO
    ) {
      return 'Processed✨';
    }
    return DocumentStatusesLabels[val] || val || '';
  };

  methodTextFormatter = (val: string) => {
    return val || '';
  };

  syncIdTextFormatter = (val: string) => {
    return val ? 'Yes' : 'No';
  };

  importedAtTextFormatter = (s: string, row: Document) => {
    if (!s) return '';

    const uploadToImportTime = dayjs(s).diff(
      dayjs(row.created_at),
      'milliseconds'
    );
    const uploadedInRes = `${CommonFormatter.duration(uploadToImportTime, { truncate: 'seconds' })}`;
    return `${CommonFormatter.dateTimeWithTimezone(s, this.timezone, DATE_TIME_FORMATS.US_DATE_TIME_FORMAT)}\n(${uploadedInRes})`;
  };

  createdAtTextFormatter = (s: string) => {
    if (!s) return '';
    return CommonFormatter.dateTimeWithTimezone(
      s,
      this.timezone,
      DATE_TIME_FORMATS.US_DATE_TIME_FORMAT
    );
  };
}

export const commissionTotalCellTextFormatter = (rowData: Document) => {
  const bankAmount = new BigNumber(rowData.bank_total_amount).dp(2);
  const statementAmount = new BigNumber(rowData.statement_amount).dp(2);
  const totalCommissionAmount = new BigNumber(
    rowData.statement_data?.total_commission
  ).dp(2);

  if (rowData.status !== DocumentStatuses.PROCESSED) {
    if (!statementAmount.isNaN()) {
      return {
        text: `Statement amount: ${formatCurrency(statementAmount.toNumber())}`,
        icon: null,
      };
    }
    return { text: '', icon: null };
  }

  const allAmountMatch =
    !totalCommissionAmount.isNaN() &&
    totalCommissionAmount.isEqualTo(statementAmount) &&
    totalCommissionAmount.isEqualTo(bankAmount);

  const totalCommissionAndStatementMatch =
    !totalCommissionAmount.isNaN() &&
    totalCommissionAmount.isEqualTo(statementAmount) &&
    bankAmount.isNaN();

  const totalCommissionAndBankMatch =
    !totalCommissionAmount.isNaN() &&
    totalCommissionAmount.isEqualTo(bankAmount) &&
    statementAmount.isNaN();

  if (
    allAmountMatch ||
    totalCommissionAndStatementMatch ||
    totalCommissionAndBankMatch
  ) {
    return {
      text: `✅ ${formatCurrency(totalCommissionAmount.toNumber())}`,
    };
  }

  const noAmountsAvailable =
    statementAmount.isNaN() &&
    bankAmount.isNaN() &&
    totalCommissionAmount.isNaN();

  if (noAmountsAvailable) {
    return { text: '', icon: null };
  }

  const contentItems: string[] = [];
  let missingAmounts = 3;
  let icon = '❌';

  if (!totalCommissionAmount.isNaN()) {
    missingAmounts--;
    contentItems.push(
      `Commissions: ${formatCurrency(totalCommissionAmount.toNumber())}`
    );
  }

  if (!statementAmount.isNaN()) {
    missingAmounts--;
    contentItems.push(
      `Statement amount: ${formatCurrency(statementAmount.toNumber())}`
    );
  }

  if (!bankAmount.isNaN()) {
    missingAmounts--;
    contentItems.push(`Bank amount: ${formatCurrency(bankAmount.toNumber())}`);
  }

  if (missingAmounts === 2) icon = 'ℹ️';

  return {
    text: contentItems.join('\n'),
    icon,
  };
};
