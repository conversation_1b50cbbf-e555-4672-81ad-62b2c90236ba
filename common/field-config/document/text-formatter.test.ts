import { describe, it, expect, beforeEach } from 'vitest';
import { DocumentTypeLabels, DocumentTypes } from 'common/constants/documents';
import { DocumentStatuses, ProcessMethod } from 'common/globalTypes';

import {
  DocumentTextFormatter,
  commissionTotalCellTextFormatter,
} from './text-formatter';

const mockDocument = {
  id: 31325,
  str_id: 'KcG5WHe-CkxzIgjJD8pbY',
  created_at: '2025-06-17T02:21:38.168Z',
  created_by: null,
  bank_total_amount: null,
  check_date: null,
  company_str_id: '0e6e2aefd91248ad8e39',
  deposit_date: null,
  file_hash: 'cbe5a93faacb480ceda0625c7eb28f7ae57c767ed6eca0e0c15949bdfba4eb47',
  file_path:
    'uploads/yJ3D_Uy-R4qRl8_KPP4Ha/yYNsOhZAu5bC5oqeMv0Cr-<PERSON> 05-24-2025 $1,455.93.pdf',
  file_type: 'spreadsheet',
  filename: '<PERSON> 05-24-2025 $1,455.93.pdf',
  import_id: null,
  imported_at: null,
  mapping: null,
  method: '',
  notes: null,
  override_file_path: null,
  override_filename: null,
  override_mapping: null,
  processor: '',
  profile_str_id: null,
  prompt: null,
  status: DocumentStatuses.PROCESSING,
  sync_id: '1975f07213d8f8f6-3-John Hancock 05-24-2025 $1,455.93.pdf',
  sync_worker: null,
  tag: null,
  type: DocumentTypes.STATEMENT,
  statement_amount: '1455.93',
  statement_month: null,
  payment_method: null,
  validations: {
    type: true,
    company_str_id: true,
    statement_amount: true,
  },
  upload_source: 'email',
  process_method: null,
  report_data: [],
  imports: [],
  companies: {
    id: 62599,
    str_id: '0e6e2aefd91248ad8e39',
    company_name: 'John Hancock',
    account_id: 'yJ3D_Uy-R4qRl8_KPP4Ha',
    alias_list: [],
    created_at: '2024-12-17T04:23:46.480Z',
  },
  extractions: [],
  created_by_user: null,
  statement_data: {
    groupedCountInfo: {},
    groupedCommissionInfo: {},
    total_commission: 0,
    total_count: 0,
  },
  updated_by_user: null,
  calc_company_name: 'John Hancock',
  calc_records: '',
  calc_commission_total: 'Statement amount: $1,455.93',
};

const mockProcessedDocument = {
  ...mockDocument,
  id: 31128,
  str_id: 'FQtI62gHO60BS6UEIQonJ',
  status: DocumentStatuses.PROCESSED,
  type: DocumentTypes.REPORT,
  statement_amount: null,
  import_id: 'oWDs6rljetGQFxSAtRmPD',
  imported_at: '2025-06-09T17:11:56.383Z',
  mapping: '1ShXbTwpNaX8yk3Nt8I86',
  method: 'mapping',
  process_method: ProcessMethod.MANUAL,
  statement_data: {
    groupedCountInfo: {},
    groupedCommissionInfo: {},
    total_commission: 0,
    total_count: 45,
  },
  created_by_user: {
    first_name: 'Kristin',
    last_name: 'Schorn',
    id: 92,
    str_id: '4Vhj2A97uMm14IyEWD4mQ',
  },
};

const mockDocumentWithOverride = {
  ...mockDocument,
  override_filename: 'override-file.pdf',
};

const mockDocumentWithImports = {
  ...mockDocument,
  imports: [
    {
      id: 19079,
      str_id: 'oG1MBEJFk88wzCh5W_y9N',
      status: 'Success',
      summed_total_amount: 1500,
      statement_total_amount: null,
    },
  ],
};

describe('DocumentTextFormatter', () => {
  let formatter: DocumentTextFormatter;

  beforeEach(() => {
    formatter = new DocumentTextFormatter({ timezone: 'UTC' });
  });

  describe('FilenameTextFormatter', () => {
    it('Should return "--" for empty filename', () => {
      expect(
        formatter.filenameTextFormatter('', {
          str_id: 'str_id',
          filename: '',
        })
      ).toBe('--');
    });

    it('Should return filename with override info', () => {
      expect(
        formatter.filenameTextFormatter(
          mockDocumentWithOverride.filename,
          mockDocumentWithOverride
        )
      ).toBe(
        'John Hancock 05-24-2025 $1,455.93.pdf (Override: override-file.pdf)'
      );
    });

    it('Should return filename without override info', () => {
      expect(
        formatter.filenameTextFormatter(mockDocument.filename, mockDocument)
      ).toBe('John Hancock 05-24-2025 $1,455.93.pdf');
    });

    it('Should handle null override filename', () => {
      const docWithNullOverride = { ...mockDocument, override_filename: null };
      expect(
        formatter.filenameTextFormatter(
          mockDocument.filename,
          docWithNullOverride
        )
      ).toBe('John Hancock 05-24-2025 $1,455.93.pdf');
    });
  });

  describe('TypeTextFormatter', () => {
    it('Should return document type label for statement', () => {
      expect(formatter.typeTextFormatter(DocumentTypes.STATEMENT)).toBe(
        DocumentTypeLabels.statement
      );
    });

    it('Should return document type label for report', () => {
      expect(formatter.typeTextFormatter(DocumentTypes.REPORT)).toBe(
        DocumentTypeLabels.report
      );
    });

    it('Should return empty string for unknown type', () => {
      expect(formatter.typeTextFormatter('unknown' as DocumentTypes)).toBe(
        'unknown'
      );
    });
  });

  describe('CompaniesTextFormatter', () => {
    it('Should return company name', () => {
      expect(formatter.companiesTextFormatter(mockDocument.companies)).toBe(
        'John Hancock'
      );
    });

    it('Should return empty string for empty company', () => {
      expect(formatter.companiesTextFormatter(null)).toBe('');
    });

    it('Should return empty string for company without name', () => {
      const companyWithoutName = {
        ...mockDocument.companies,
        company_name: null,
      };
      expect(formatter.companiesTextFormatter(companyWithoutName)).toBe('');
    });
  });

  describe('StatementDataTextFormatter', () => {
    it('Should format statement data with total count', () => {
      const statementData = {
        total_count: 10,
        total_commission: 1000,
      };
      expect(formatter.statementDataTextFormatter(statementData)).toBe(
        '10\n$1,000.00'
      );
    });

    it('Should format statement data with grouped info', () => {
      const statementData = {
        total_count: 10,
        total_commission: 1000,
        groupedCountInfo: { PENDING: 5, APPROVED: 5 },
        groupedCommissionInfo: { PENDING: 500, APPROVED: 500 },
      };
      expect(formatter.statementDataTextFormatter(statementData)).toBe(
        '10 (PENDING: 5,APPROVED: 5)\n$1,000.00 (PENDING: $500.00, APPROVED: $500.00)'
      );
    });

    it('Should return "0" for empty data', () => {
      expect(formatter.statementDataTextFormatter(null)).toBe('0');
    });

    it('Should format real statement data from mock', () => {
      expect(
        formatter.statementDataTextFormatter(mockDocument.statement_data)
      ).toBe('0');
    });

    it('Should format processed document statement data', () => {
      expect(
        formatter.statementDataTextFormatter(
          mockProcessedDocument.statement_data
        )
      ).toBe('45');
    });
  });

  describe('StatementAmountTextFormatter', () => {
    it('Should format commission total for processing status', () => {
      expect(
        formatter.statementAmountTextFormatter(
          mockDocument.statement_amount,
          mockDocument
        )
      ).toBe('Statement amount: $1,455.93');
    });

    it('Should format commission total for processed status', () => {
      const processedDoc = {
        ...mockDocument,
        status: DocumentStatuses.PROCESSED,
      };
      expect(
        formatter.statementAmountTextFormatter(
          processedDoc.statement_amount,
          processedDoc
        )
      ).toBe('❌ Commissions: $0.00\nStatement amount: $1,455.93');
    });

    it('Should return empty string for null amount', () => {
      const docWithoutStatementAmount = {
        ...mockDocument,
        statement_amount: null,
      };
      expect(
        formatter.statementAmountTextFormatter(null, docWithoutStatementAmount)
      ).toBe('');
    });
  });

  describe('BankTotalAmountTextFormatter', () => {
    it('Should format bank total amount', () => {
      const docWithBankAmount = { ...mockDocument, bank_total_amount: 2000 };
      expect(
        formatter.bankTotalAmountTextFormatter(
          docWithBankAmount.bank_total_amount,
          docWithBankAmount
        )
      ).toBe('Bank total: $2,000.00');
    });

    it('Should return empty string for empty amount', () => {
      expect(
        formatter.bankTotalAmountTextFormatter(
          mockDocument.bank_total_amount,
          mockDocument
        )
      ).toBe('');
    });

    it('Should format zero amount', () => {
      const docWithZeroAmount = { ...mockDocument, bank_total_amount: 0 };
      expect(
        formatter.bankTotalAmountTextFormatter(
          docWithZeroAmount.bank_total_amount,
          docWithZeroAmount
        )
      ).toBe('');
    });
  });

  describe('CreatedByTextFormatter', () => {
    it('Should return imported total from imports', () => {
      expect(
        formatter.createdByTextFormatter('', mockDocumentWithImports)
      ).toBe('$1,500.00');
    });

    it('Should return empty string for empty imports', () => {
      expect(formatter.createdByTextFormatter('', mockDocument)).toBe('');
    });

    it('Should return empty string for imports without summed_total_amount', () => {
      const docWithEmptyImports = { ...mockDocument, imports: [{}] };
      expect(formatter.createdByTextFormatter('', docWithEmptyImports)).toBe(
        '$0.00'
      );
    });
  });

  describe('StatementMonthTextFormatter', () => {
    it('Should format statement month', () => {
      const docWithStatementMonth = {
        ...mockDocument,
        statement_month: '2023-12',
      };
      expect(
        formatter.statementMonthTextFormatter(
          docWithStatementMonth.statement_month
        )
      ).toBe('12/2023');
    });

    it('Should return empty string for empty month', () => {
      expect(
        formatter.statementMonthTextFormatter(mockDocument.statement_month)
      ).toBe('');
    });

    it('Should return first month of year if only year is specified', () => {
      expect(formatter.statementMonthTextFormatter('2023')).toBe('01/2023');
    });
  });

  describe('StatusTextFormatter', () => {
    it('Should return status label for new', () => {
      const newDoc = { ...mockDocument, status: DocumentStatuses.NEW };
      expect(formatter.statusTextFormatter(newDoc.status, newDoc)).toBe('New');
    });

    it('Should return status label for processing', () => {
      expect(
        formatter.statusTextFormatter(mockDocument.status, mockDocument)
      ).toBe('Processing');
    });

    it('Should return status label for pending review', () => {
      const pendingReviewDoc = {
        ...mockDocument,
        status: DocumentStatuses.PENDING_REVIEW,
      };
      expect(
        formatter.statusTextFormatter(pendingReviewDoc.status, pendingReviewDoc)
      ).toBe('Pending review');
    });

    it('Should return processed with sparkle for auto processed', () => {
      const autoProcessedDoc = {
        ...mockDocument,
        status: DocumentStatuses.PROCESSED,
        process_method: ProcessMethod.AUTO,
      };
      expect(
        formatter.statusTextFormatter(autoProcessedDoc.status, autoProcessedDoc)
      ).toBe('Processed✨');
    });

    it('Should return original value for unknown status', () => {
      const unknownStatusDoc = { ...mockDocument, status: 'unknown' };
      expect(
        formatter.statusTextFormatter(unknownStatusDoc.status, unknownStatusDoc)
      ).toBe('unknown');
    });
  });

  describe('MethodTextFormatter', () => {
    it('Should return method value', () => {
      expect(formatter.methodTextFormatter(ProcessMethod.MANUAL)).toBe(
        'manual'
      );
    });

    it('Should return empty string for null method', () => {
      expect(formatter.methodTextFormatter(mockDocument.method)).toBe('');
    });

    it('Should return method value from mock document', () => {
      const docWithMethod = { ...mockDocument, method: 'mapping' };
      expect(formatter.methodTextFormatter(docWithMethod.method)).toBe(
        'mapping'
      );
    });
  });

  describe('SyncIdTextFormatter', () => {
    it('Should return "Yes" for synced', () => {
      expect(formatter.syncIdTextFormatter('true')).toBe('Yes');
    });

    it('Should return "No" for null sync status', () => {
      expect(formatter.syncIdTextFormatter(null)).toBe('No');
    });

    it('Should return "No" for empty sync status', () => {
      expect(formatter.syncIdTextFormatter('')).toBe('No');
    });

    it('Should return "Yes" for truthy values', () => {
      expect(formatter.syncIdTextFormatter('any-truthy-value')).toBe('Yes');
    });
  });

  describe('ImportedAtTextFormatter', () => {
    it('Should format imported at date', () => {
      expect(
        formatter.importedAtTextFormatter(
          mockProcessedDocument.imported_at,
          mockProcessedDocument
        )
      ).toContain('6/9/2025, 5:11PM');
    });

    it('Should return empty string for empty date', () => {
      expect(
        formatter.importedAtTextFormatter(
          mockDocument.imported_at,
          mockDocument
        )
      ).toBe('');
    });
  });

  describe('CreatedAtTextFormatter', () => {
    it('Should format created at date', () => {
      expect(formatter.createdAtTextFormatter(mockDocument.created_at)).toBe(
        '6/17/2025, 2:21AM'
      );
    });

    it('Should return empty string for empty date', () => {
      expect(formatter.createdAtTextFormatter(null)).toBe('');
    });
  });
});

describe('CommissionTotalCellTextFormatter', () => {
  it('Should return "Match" when all amounts are equal', () => {
    const row = {
      bank_total_amount: 1000,
      statement_amount: '1000',
      statement_data: { total_commission: 1000 },
      status: DocumentStatuses.PROCESSED,
      str_id: 'str_id',
      filename: 'filename',
    };
    const result = commissionTotalCellTextFormatter(row);
    expect(result.text).toContain('✅ $1,000.00');
  });

  it('Should return "Mismatch" when amounts are different', () => {
    const row = {
      bank_total_amount: 1000,
      statement_amount: '1200',
      statement_data: { total_commission: 1000 },
      status: DocumentStatuses.PROCESSED,
      str_id: 'str_id',
      filename: 'filename',
    };
    const result = commissionTotalCellTextFormatter(row);
    expect(result.text).toContain('Commissions: $1,000.00');
    expect(result.text).toContain('Statement amount: $1,200.00');
    expect(result.text).toContain('Bank amount: $1,000.00');
  });

  it('Should return statement amount for non-processed status', () => {
    const row = {
      statement_amount: '1000',
      status: DocumentStatuses.NEW,
      str_id: 'str_id',
      filename: 'filename',
    };
    const result = commissionTotalCellTextFormatter(row);
    expect(result.text).toContain('Statement amount: $1,000.00');
  });

  it('Should return empty text when no amounts available', () => {
    const row = {
      status: DocumentStatuses.NEW,
      str_id: 'str_id',
      filename: 'filename',
    };
    const result = commissionTotalCellTextFormatter(row);
    expect(result.text).toBe('');
  });

  it('Should handle real document data', () => {
    const result = commissionTotalCellTextFormatter(mockDocument);
    expect(result.text).toContain('Statement amount: $1,455.93');
  });
});
