import { describe, it, expect, beforeEach } from 'vitest';
import { CustomerType } from 'common/customer/customer.constants';

import { ReportTextFormatter } from './text-formatter';

const mockCustomer = {
  type: CustomerType.individual,
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  company_name: 'Test Company',
};

const mockCompanyCustomer = {
  type: 'company' as const,
  first_name: '<PERSON>',
  last_name: '<PERSON><PERSON>',
  company_name: 'Test Company',
};

const mockCompany = {
  id: 1,
  str_id: 'company-1',
  company_name: 'Test Insurance Company',
  account_id: 'account-1',
  alias_list: [],
  created_at: '2024-01-01T00:00:00.000Z',
};

const mockContact = {
  id: 1,
  str_id: 'contact-1',
  first_name: '<PERSON>',
  last_name: '<PERSON>',
  email: '<EMAIL>',
  account_id: 'account-1',
};

const mockRow = {
  customer: mockCustomer,
  writing_carrier_name: 'Test Insurance Company',
  contacts: ['contact-1', 'contact-2'],
  contacts_split: { 'contact-1': 60, 'contact-2': 40 },
  agent_payout_rate_override: { 'contact-1': 0.05, 'contact-2': 0.03 },
  statement_data: [{ str_id: 'stmt-1' }, { str_id: 'stmt-2' }],
  children_report_data: [{ str_id: 'child-1' }, { str_id: 'child-2' }],
  tags: 'tag1,tag2,tag3',
  tags_array: ['tag1', 'tag2', 'tag3'],
  aggregation_primary: true,
  aggregation_primary_false: false,
};

describe('ReportTextFormatter', () => {
  let formatter: ReportTextFormatter;

  beforeEach(() => {
    formatter = new ReportTextFormatter({
      account_id: 'test-account',
      timezone: 'UTC',
    });
  });

  describe('CustomerNameTextFormatter', () => {
    it('Should format individual customer name', () => {
      expect(
        formatter.customerNameTextFormatter('', {
          customer: mockCustomer,
        })
      ).toBe('John Doe');
    });

    it('Should format company customer name', () => {
      expect(
        formatter.customerNameTextFormatter('', {
          customer: mockCompanyCustomer,
        })
      ).toBe('Test Company');
    });

    it('Should return original value when no customer data', () => {
      expect(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        formatter.customerNameTextFormatter('Original Value', {} as any)
      ).toBe('Original Value');
    });

    it('Should handle null customer', () => {
      expect(
        formatter.customerNameTextFormatter('Original Value', {
          customer: null,
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any)
      ).toBe('Original Value');
    });
  });

  describe('PolicyIdTextFormatter', () => {
    it('Should format policy ID', () => {
      expect(formatter.policyIdTextFormatter('POL123456')).toBe('POL123456');
    });

    it('Should handle numeric policy ID', () => {
      expect(formatter.policyIdTextFormatter(123456)).toBe('123456');
    });

    it('Should handle null policy ID', () => {
      expect(formatter.policyIdTextFormatter(null)).toBe('');
    });

    it('Should handle undefined policy ID', () => {
      expect(formatter.policyIdTextFormatter(undefined)).toBe('');
    });
  });

  describe('EffectiveDateTextFormatter', () => {
    it('Should format effective date', () => {
      expect(formatter.effectiveDateTextFormatter('2024-01-15')).toBe(
        '01/15/2024'
      );
    });

    it('Should return null for null date', () => {
      expect(formatter.effectiveDateTextFormatter(null)).toBeNull();
    });

    it('Should return undefined for undefined date', () => {
      expect(formatter.effectiveDateTextFormatter(undefined)).toBeNull();
    });
  });

  describe('SignedDateTextFormatter', () => {
    it('Should format signed date', () => {
      expect(formatter.signedDateTextFormatter('2024-01-15')).toBe(
        '01/15/2024'
      );
    });

    it('Should return null for null date', () => {
      expect(formatter.signedDateTextFormatter(null)).toBeNull();
    });
  });

  describe('PolicyDateTextFormatter', () => {
    it('Should format policy date', () => {
      expect(formatter.policyDateTextFormatter('2024-01-15')).toBe(
        '01/15/2024'
      );
    });

    it('Should return null for null date', () => {
      expect(formatter.policyDateTextFormatter(null)).toBeNull();
    });
  });

  describe('WritingCarrierNameTextFormatter', () => {
    it('Should return company name from collection', () => {
      const allDynamicSelectMap = {
        companies: new Map([['Test Insurance Company', mockCompany]]),
      };
      expect(
        formatter.writingCarrierNameTextFormatter(
          'Test Insurance Company',
          mockRow,
          { allDynamicSelectMap }
        )
      ).toBe('Test Insurance Company');
    });

    it('Should return not found message for missing company', () => {
      const allDynamicSelectMap = {
        companies: new Map(),
      };
      expect(
        formatter.writingCarrierNameTextFormatter('Missing Company', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('Missing Company (not found in Fintary)');
    });

    it('Should return original value when no collection data', () => {
      expect(
        formatter.writingCarrierNameTextFormatter('Test Company', mockRow, {})
      ).toBe('Test Company');
    });
  });

  describe('WritingCarrierIdTextFormatter', () => {
    it('Should return company ID from collection', () => {
      const allDynamicSelectMap = {
        companies: new Map([['Test Insurance Company', mockCompany]]),
      };
      expect(
        formatter.writingCarrierIdTextFormatter(
          'company-1',
          { writing_carrier_name: 'Test Insurance Company' },
          { allDynamicSelectMap }
        )
      ).toBe(1);
    });

    it('Should return not found message for missing company', () => {
      const allDynamicSelectMap = {
        companies: new Map(),
      };
      expect(
        formatter.writingCarrierIdTextFormatter(
          'company-1',
          { writing_carrier_name: 'Missing Company' },
          { allDynamicSelectMap }
        )
      ).toBe('Missing Company (not found in Fintary)');
    });

    it('Should return empty string when no writing carrier name', () => {
      const allDynamicSelectMap = {
        companies: new Map(),
      };
      expect(
        formatter.writingCarrierIdTextFormatter(
          'company-1',
          { writing_carrier_name: '' },
          { allDynamicSelectMap }
        )
      ).toBe('');
    });
  });

  describe('PremiumAmountTextFormatter', () => {
    it('Should format premium amount', () => {
      expect(formatter.premiumAmountTextFormatter(1500.5)).toBe('$1,500.50');
    });

    it('Should format zero amount', () => {
      expect(formatter.premiumAmountTextFormatter(0)).toBe('$0.00');
    });

    it('Should handle string amount', () => {
      expect(formatter.premiumAmountTextFormatter('1500.50')).toBe('$1,500.50');
    });
  });

  describe('ProductNameTextFormatter', () => {
    it('Should return product name from collection', () => {
      const allDynamicSelectMap = {
        'companies/products': new Map([
          ['product-1', { product_name: 'Life Insurance' }],
        ]),
      };
      expect(
        formatter.productNameTextFormatter('product-1', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('Life Insurance');
    });

    it('Should return not found message for missing product', () => {
      const allDynamicSelectMap = {
        'companies/products': new Map(),
      };
      expect(
        formatter.productNameTextFormatter('missing-product', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('missing-product (not found in Fintary)');
    });
  });

  describe('CancellationDateTextFormatter', () => {
    it('Should format cancellation date', () => {
      expect(formatter.cancellationDateTextFormatter('2024-01-15')).toBe(
        '01/15/2024'
      );
    });

    it('Should return null for null date', () => {
      expect(formatter.cancellationDateTextFormatter(null)).toBeNull();
    });
  });

  describe('ReinstatementDateTextFormatter', () => {
    it('Should format reinstatement date', () => {
      expect(formatter.reinstatementDateTextFormatter('2024-01-15')).toBe(
        '01/15/2024'
      );
    });

    it('Should return null for null date', () => {
      expect(formatter.reinstatementDateTextFormatter(null)).toBeNull();
    });
  });

  describe('CommissionsExpectedTextFormatter', () => {
    it('Should format commissions expected', () => {
      expect(formatter.commissionsExpectedTextFormatter('1000.50')).toBe(
        '$1,000.50'
      );
    });

    it('Should format zero amount', () => {
      expect(formatter.commissionsExpectedTextFormatter('0')).toBe('$0.00');
    });
  });

  describe('SplitPercentageTextFormatter', () => {
    it('Should format split percentage', () => {
      expect(formatter.splitPercentageTextFormatter('0.05')).toBe('0.05%');
    });

    it('Should format zero percentage', () => {
      expect(formatter.splitPercentageTextFormatter('0')).toBe('0.00%');
    });
  });

  describe('CommissionablePremiumAmountTextFormatter', () => {
    it('Should format commissionable premium amount', () => {
      expect(
        formatter.commissionablePremiumAmountTextFormatter('2000.75')
      ).toBe('$2,000.75');
    });

    it('Should format zero amount', () => {
      expect(formatter.commissionablePremiumAmountTextFormatter('0')).toBe(
        '$0.00'
      );
    });
  });

  describe('IssueAgeTextFormatter', () => {
    it('Should return issue age as string', () => {
      expect(formatter.issueAgeTextFormatter('35')).toBe('35');
    });

    it('Should handle numeric issue age', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.issueAgeTextFormatter(35 as any)).toBe(35);
    });
  });

  describe('CustomerPaidPremiumAmountTextFormatter', () => {
    it('Should format customer paid premium amount', () => {
      expect(formatter.customerPaidPremiumAmountTextFormatter('1800.25')).toBe(
        '$1,800.25'
      );
    });

    it('Should format zero amount', () => {
      expect(formatter.customerPaidPremiumAmountTextFormatter('0')).toBe(
        '$0.00'
      );
    });
  });

  describe('AggregationPrimaryTextFormatter', () => {
    it('Should return "Yes" for true value', () => {
      expect(formatter.aggregationPrimaryTextFormatter(true)).toBe('Yes');
    });

    it('Should return "No" for false value', () => {
      expect(formatter.aggregationPrimaryTextFormatter(false)).toBe('No');
    });

    it('Should return "No" for null value', () => {
      expect(formatter.aggregationPrimaryTextFormatter(null)).toBe('No');
    });
  });

  describe('StatementDataTextFormatter', () => {
    it('Should format statement data array', () => {
      const statementData = [{ str_id: 'stmt-1' }, { str_id: 'stmt-2' }];
      expect(formatter.statementDataTextFormatter(statementData)).toBe(
        'stmt-1\nstmt-2'
      );
    });

    it('Should return original value for non-array', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.statementDataTextFormatter('not-array' as any)).toBe(
        'not-array'
      );
    });

    it('Should return original value for empty array', () => {
      expect(formatter.statementDataTextFormatter([])).toStrictEqual([]);
    });
  });

  describe('ChildrenReportDataTextFormatter', () => {
    it('Should format children report data array', () => {
      const childrenData = [{ str_id: 'child-1' }, { str_id: 'child-2' }];
      expect(formatter.childrenReportDataTextFormatter(childrenData)).toBe(
        'child-1\nchild-2'
      );
    });

    it('Should return original value for non-array', () => {
      expect(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        formatter.childrenReportDataTextFormatter('not-array' as any)
      ).toBe('not-array');
    });

    it('Should return original value for empty array', () => {
      expect(formatter.childrenReportDataTextFormatter([])).toStrictEqual([]);
    });
  });

  describe('TagsTextFormatter', () => {
    it('Should format comma-separated tags string', () => {
      expect(formatter.tagsTextFormatter('tag1,tag2,tag3')).toBe(
        'tag1, tag2, tag3'
      );
    });

    it('Should format tags array', () => {
      expect(formatter.tagsTextFormatter(['tag1', 'tag2', 'tag3'])).toBe(
        'tag1, tag2, tag3'
      );
    });

    it('Should return empty string for empty array', () => {
      expect(formatter.tagsTextFormatter([])).toBe('');
    });

    it('Should handle single tag', () => {
      expect(formatter.tagsTextFormatter('single-tag')).toBe('single-tag');
    });

    it('Should handle empty string', () => {
      expect(formatter.tagsTextFormatter('')).toBe('');
    });
  });

  describe('DocumentIdTextFormatter', () => {
    it('Should return document filename from collection', () => {
      const allDynamicSelectMap = {
        documents: new Map([['doc-1', { filename: 'test-document.pdf' }]]),
      };
      expect(
        formatter.documentIdTextFormatter('doc-1', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('test-document.pdf');
    });

    it('Should return not found message for missing document', () => {
      const allDynamicSelectMap = {
        documents: new Map(),
      };
      expect(
        formatter.documentIdTextFormatter('missing-doc', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('missing-doc (not found in Fintary)');
    });
  });

  describe('DocumentRelationshipIdTextFormatter', () => {
    it('Should return document ID from row', () => {
      expect(
        formatter.documentRelationshipIdTextFormatter('rel-1', {
          document_id: 'doc-1',
        })
      ).toBe('doc-1');
    });

    it('Should return empty string when no document ID', () => {
      expect(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        formatter.documentRelationshipIdTextFormatter('rel-1', {} as any)
      ).toBe('');
    });
  });

  describe('FirstPaymentDateTextFormatter', () => {
    it('Should format first payment date', () => {
      expect(formatter.firstPaymentDateTextFormatter('2024-01-15')).toBe(
        '01/15/2024'
      );
    });

    it('Should return null for null date', () => {
      expect(formatter.firstPaymentDateTextFormatter(null)).toBeNull();
    });
  });

  describe('FirstProcessedDateTextFormatter', () => {
    it('Should format first processed date', () => {
      expect(formatter.firstProcessedDateTextFormatter('2024-01-15')).toBe(
        '01/15/2024'
      );
    });

    it('Should return null for null date', () => {
      expect(formatter.firstProcessedDateTextFormatter(null)).toBeNull();
    });
  });

  describe('CommissionProfileIdTextFormatter', () => {
    it('Should return profile name from collection', () => {
      const allDynamicSelectMap = {
        'commission-profiles': new Map([
          ['profile-1', { name: 'Standard Profile' }],
        ]),
      };
      expect(
        formatter.commissionProfileIdTextFormatter('profile-1', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('Standard Profile');
    });

    it('Should return not found message for missing profile', () => {
      const allDynamicSelectMap = {
        'commission-profiles': new Map(),
      };
      expect(
        formatter.commissionProfileIdTextFormatter('missing-profile', mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('missing-profile (not found in Fintary)');
    });
  });

  describe('AgentPayoutRateOverrideTextFormatter', () => {
    it('Should format agent payout rate override', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      const overrideRates = { 'contact-1': 0.05, 'contact-2': 0.03 };
      expect(
        formatter.agentPayoutRateOverrideTextFormatter(overrideRates, mockRow, {
          allDynamicSelectMap,
        })
      ).toContain('Jane Smith: 0.05%');
    });

    it('Should return empty string for null override rates', () => {
      const allDynamicSelectMap = {
        contacts: new Map(),
      };
      expect(
        formatter.agentPayoutRateOverrideTextFormatter(null, mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('');
    });

    it('Should return empty string when no contacts collection', () => {
      const overrideRates = { 'contact-1': 0.05 };
      expect(
        formatter.agentPayoutRateOverrideTextFormatter(
          overrideRates,
          mockRow,
          {}
        )
      ).toBe('');
    });
  });

  describe('ContactsTextFormatter', () => {
    it('Should format contacts array', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      expect(
        formatter.contactsTextFormatter(['contact-1', 'contact-2'], mockRow, {
          allDynamicSelectMap,
        })
      ).toContain('Jane Smith');
    });

    it('Should return original value for non-array contacts', () => {
      expect(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        formatter.contactsTextFormatter('single-contact' as any, mockRow, {})
      ).toBe('single-contact');
    });

    it('Should return null for null contacts', () => {
      expect(formatter.contactsTextFormatter(null, mockRow, {})).toBeNull();
    });
  });

  describe('ContactsIdTextFormatter', () => {
    it('Should format contacts IDs', () => {
      expect(
        formatter.contactsIdTextFormatter('contact-1', {
          contacts: ['contact-1', 'contact-2'],
        })
      ).toBe('contact-1\ncontact-2');
    });

    it('Should return empty string when no contacts', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(formatter.contactsIdTextFormatter('contact-1', {} as any)).toBe(
        ''
      );
    });
  });

  describe('ContactsSplitTextFormatter', () => {
    it('Should format contacts split with valid percentages', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      const split = { 'contact-1': 60, 'contact-2': 40 };
      const result = formatter.contactsSplitTextFormatter(split, mockRow, {
        allDynamicSelectMap,
      });
      expect(result).toContain('Jane Smith: 60%');
      expect(result).toContain('Jane Smith: 40%');
    });

    it('Should add total warning when percentages do not equal 100', () => {
      const allDynamicSelectMap = {
        contacts: new Map([
          ['contact-1', mockContact],
          ['contact-2', { ...mockContact, id: 2, str_id: 'contact-2' }],
        ]),
      };
      const split = { 'contact-1': 60, 'contact-2': 30 };
      const result = formatter.contactsSplitTextFormatter(split, mockRow, {
        allDynamicSelectMap,
      });
      expect(result).toContain('Total should be 100');
    });

    it('Should return empty string for null split', () => {
      const allDynamicSelectMap = {
        contacts: new Map(),
      };
      expect(
        formatter.contactsSplitTextFormatter(null, mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('');
    });

    it('Should return empty string for empty split object', () => {
      const allDynamicSelectMap = {
        contacts: new Map(),
      };
      expect(
        formatter.contactsSplitTextFormatter({}, mockRow, {
          allDynamicSelectMap,
        })
      ).toBe('');
    });
  });
});
