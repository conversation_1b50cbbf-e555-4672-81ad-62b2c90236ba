import type { TransactionParty } from 'common/globalTypes';
import { FieldTypes } from 'common/constants';
import { ReceivableValueMap } from 'common/constants/receivable_value';

export const buildReceivableValueFilter = (party: TransactionParty) => {
  const label = ReceivableValueMap[party].label;
  const rate_field = ReceivableValueMap[party].rate_field;

  return {
    label,
    enabled: true,
    required: false,
    type: FieldTypes.CUSTOM,
    textFormatter: (
      fields: { value: string; str_id: string },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      row: Record<string, any>
    ) => {
      const rowParsed = row || {};
      if (Array.isArray(fields)) {
        return fields.map((field) => field.value).join('\n');
      }
      const { value } = fields || rowParsed[rate_field] || {};
      return value;
    },
  };
};
