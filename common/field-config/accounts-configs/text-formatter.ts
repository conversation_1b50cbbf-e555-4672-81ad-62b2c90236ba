import { AccountConfigsTypesOptions } from 'common/constants/accounts_configs';

import BaseTextFormatter from '../shared/base/base-text-formatter';

export class AccountConfigsTextFormatter extends BaseTextFormatter {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {
    super();
  }

  valueTextFormatter = (value: JSON) => {
    return JSON.stringify(value);
  };

  typeTextFormatter = (value: string) => {
    return (
      AccountConfigsTypesOptions.find((option) => option.id === value)?.label ??
      value
    );
  };
}
