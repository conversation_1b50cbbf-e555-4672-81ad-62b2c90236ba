import { FieldTypes } from 'common/constants';

import { AccountConfigsTextFormatter } from './text-formatter';

export const getAccountConfigsFieldConfig = () => {
  const textFormatter = new AccountConfigsTextFormatter();

  return {
    label: 'Account configurations',
    table: 'accounts/configs',
    fields: {
      type: {
        label: 'Type',
        required: true,
        type: FieldTypes.SELECT,
        enabled: true,
        textFormatter: textFormatter.typeTextFormatter,
      },
      value: {
        label: 'Configuration',
        type: FieldTypes.CUSTOM,
        enabled: true,
        textFormatter: textFormatter.valueTextFormatter,
      },
      notes: {
        label: 'Notes',
        type: FieldTypes.TEXT,
        enabled: true,
      },
    },
  };
};
