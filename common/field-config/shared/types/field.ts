import type { Mapping } from './processor';

export type FieldConfig = {
  label: string;
  enabled?: boolean;
  allowedInBody?: boolean; // Controls whether field can be included in request body for add/edit operations
  readOnly?: boolean;
  hidden?: boolean;
  bulkEdit?: boolean;
  isRelationalField?: boolean;
  exportOnly?: boolean;
  textFormatter?: (
    v?: string | object | boolean | number,
    row?: unknown,
    args?: TextFormatterArgs // Passed dynamic select values
  ) => string | number;
  ref?: string;
};

export type TextFormatterArgs = {
  allDynamicSelectMap?: Record<string, Map<string | number, unknown>>;
  companyExportMappings?: Mapping[]; // Passed mappings for companies export
};

// TODO: remove all any types
export type RenderFieldConfig = {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  normalizer?: (value: any) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  formatter?: (value: any, collectionVals?: any[] | any) => any;
  dynamicFormatter?: (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    value: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    collectionVals?: any[] | any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    row?: any
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  validator?: (value: any) => boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  tableFormatter?: (value: any, row?: any) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  optionFormatter?: (value: any) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  optionValuer?: (value: any) => any;
  render?: (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    value: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    row?: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    setter?: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dynamicSelect?: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dynamicSelectData?: any
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getWidth?: (data: any) => number;
};

export interface DynamicSelectConfig {
  table: string;
  queryParamName: string;
  queryParamValue: (string | number)[];
  collectDataFields: string[];
  dataExtractors?: {
    [field: string]: (
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      rows: any,
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      totals?: { [key: string]: any }
    ) => (string | number)[];
  };
  mapKey?: string | string[];
}
