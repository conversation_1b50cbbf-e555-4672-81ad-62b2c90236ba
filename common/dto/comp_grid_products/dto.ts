import { z } from 'zod';
import { toArray } from 'common/helpers/toArray';

export const CompGridProductCreateDTOSchema = z.object({
  comp_grid_id: z.coerce
    .number()
    .min(1, { message: 'Comp grid must be specified' }),
  name: z.string().min(1, { message: 'Name cannot be empty' }),
  notes: z.string().optional().nullable(),
  comp_grid_product_ids: z
    .array(z.object({ id: z.coerce.number() }))
    .optional()
    .nullable(),
  company_product_ids: z
    .union([
      z.array(
        z.union([
          z.coerce.number(),
          z.string().transform(Number),
          z.object({ id: z.coerce.number() }),
        ])
      ),
      z.string().transform((str) => {
        try {
          return JSON.parse(str);
        } catch {
          return [];
        }
      }),
    ])
    .optional()
    .nullable(),
  type: z.string().optional().nullable(),
});

export const BulkCompGridProductCreateDTOSchema = z.object({
  data: CompGridProductCreateDTOSchema.array(),
});

export type BulkCompGridProductCreateDTO = z.infer<
  typeof BulkCompGridProductCreateDTOSchema
>;
export type CompGridProductCreateDTO = z.infer<
  typeof CompGridProductCreateDTOSchema
>;

export const CompGridProductUpdateDTOSchema =
  CompGridProductCreateDTOSchema.extend({
    id: z.number(),
  });

export type CompGridProductUpdateDTO = z.infer<
  typeof CompGridProductUpdateDTOSchema
>;

export const CompGridProductDeleteDTOSchema = z.object({
  ids: z.union([z.number(), z.array(z.number())]).transform(toArray),
});

export type CompGridProductDeleteDTO = z.infer<
  typeof CompGridProductDeleteDTOSchema
>;
