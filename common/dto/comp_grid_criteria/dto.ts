import { z } from 'zod';
import { toArray } from 'common/helpers/toArray';

export const DataRangeDTOSchema = z
  .object({
    id: z.coerce.number().optional(),
    start_date: z.coerce.date(),
    end_date: z.coerce.date().optional().nullable(),
  })
  .required({
    start_date: true,
  });

export const CompGridCriteriaCreateDTOSchema = z.object({
  comp_grid_id: z.coerce.number(),
  company_id: z.coerce.number().optional().nullable(),
  compensation_type: z.string().optional().nullable(),
  compensation_type_alternative: z.string().optional().nullable(),
  grid_product_id: z.coerce.number().optional().nullable(),
  issue_age_start: z.coerce.number().optional().nullable(),
  issue_age_end: z.coerce.number().optional().nullable(),
  premium_min: z.coerce.number().optional().nullable(),
  premium_max: z.coerce.number().optional().nullable(),
  payment_mode: z.coerce.string().optional().nullable(),
  policy_year_start: z.coerce.number().optional().nullable(),
  policy_year_end: z.coerce.number().optional().nullable(),
  transaction_type: z.string().optional().nullable(),
  filter_date_field: z.string().optional().nullable(),
  date_ranges: z.array(DataRangeDTOSchema).optional().nullable(),
});

export type CompGridCriteriaDTO = z.infer<
  typeof CompGridCriteriaCreateDTOSchema
> & { id: number };

export const BulkCompGridCriteriaCreateDTOSchema = z.object({
  data: CompGridCriteriaCreateDTOSchema.array(),
});
export type BulkCompGridCriteriaCreateDTO = z.infer<
  typeof BulkCompGridCriteriaCreateDTOSchema
>;
export type CompGridCriteriaCreateDTO = z.infer<
  typeof CompGridCriteriaCreateDTOSchema
>;

export const CompGridCriteriaUpdateDTOSchema =
  CompGridCriteriaCreateDTOSchema.extend({
    id: z.number(),
  });

export type CompGridCriteriaUpdateDTO = z.infer<
  typeof CompGridCriteriaUpdateDTOSchema
>;

export const CompGridCriteriaDeleteDTOSchema = z.object({
  ids: z.union([z.number(), z.array(z.number())]).transform(toArray),
});

export type CompGridCriteriaDeleteDTO = z.infer<
  typeof CompGridCriteriaDeleteDTOSchema
>;
