import { z } from 'zod';
import { preprocessBoolean } from 'common/helpers/zod';

// Converting KeyConfig class to Zod schema
export const KeyConfigSchema = z.object({
  field: z.string(),
  params: z
    .union([z.string(), z.record(z.any())])
    .nullable()
    .optional(),
  transformers: z.array(z.string()),
});

export type KeyConfig = z.infer<typeof KeyConfigSchema>;

// Enum for ReconcilerMethodTypes
export enum ReconcilerMethodTypes {
  SIMILARITY_CONFIG = 'similarity-config',
  SIMILARITY_CUSTOM = 'similarity-custom',
  KEY_CONFIG = 'key-config',
  KEY_CUSTOM = 'key-custom',
}

// Converting ReconcilationCreateDTO to Zod
export const ReconcilationCreateDTOSchema = z
  .object({
    name: z.string().min(1, { message: 'Reconciler name cannot be empty' }),
    method_type: z.nativeEnum(ReconcilerMethodTypes, {
      errorMap: () => ({
        message: 'Invalid Matcher configuration method type',
      }),
    }),
    notes: z.string().optional().nullable(),
    key_condition: z
      .union([z.string(), z.array(z.string())])
      .optional()
      .nullable(),
    key_config_report: z
      .union([z.string(), z.array(KeyConfigSchema)])
      .optional()
      .nullable(),
    key_config_statement: z
      .union([z.string(), z.array(KeyConfigSchema)])
      .optional()
      .nullable(),
    similarity_config: z
      .union([z.string(), z.array(z.any())])
      .optional()
      .nullable(),
  })
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  .superRefine((data: any, ctx) => {
    // Validate key_config fields only when method_type is KEY_CONFIG or KEY_CUSTOM
    if (
      data.method_type === ReconcilerMethodTypes.KEY_CONFIG ||
      data.method_type === ReconcilerMethodTypes.KEY_CUSTOM
    ) {
      // Validate key_config_report
      if (!data.key_config_report) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Policy report key can not be empty',
          path: ['key_config_report'],
        });
      } else if (
        (typeof data.key_config_report === 'string' &&
          data.key_config_report.trim() === '') ||
        (Array.isArray(data.key_config_report) &&
          data.key_config_report.length === 0)
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Policy report key can not be empty',
          path: ['key_config_report'],
        });
      }

      // Validate key_config_statement
      if (!data.key_config_statement) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Commission statement key can not be empty',
          path: ['key_config_statement'],
        });
      } else if (
        (typeof data.key_config_statement === 'string' &&
          data.key_config_statement.trim() === '') ||
        (Array.isArray(data.key_config_statement) &&
          data.key_config_statement.length === 0)
      ) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Commission statement key can not be empty',
          path: ['key_config_statement'],
        });
      }
    }

    // Validate similarity_config field only when method_type is SIMILARITY_CONFIG or SIMILARITY_CUSTOM
    if (
      (data.method_type === ReconcilerMethodTypes.SIMILARITY_CONFIG ||
        data.method_type === ReconcilerMethodTypes.SIMILARITY_CUSTOM) &&
      !data?.similarity_config?.length
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Similarity config can not be empty',
        path: ['similarity_config'],
      });
    }
  });

export type ReconcilationCreateDTO = z.infer<
  typeof ReconcilationCreateDTOSchema
>;

// Converting ReconcilerUpdateDTO to Zod
export const ReconcilerUpdateDTOSchema = z
  .object({
    ...ReconcilationCreateDTOSchema._def.schema.shape,
    id: z.number(),
  })
  .superRefine((data) => {
    // Apply the same refinement as in ReconcilationCreateDTOSchema
    ReconcilationCreateDTOSchema.parse(data);
  });

export type ReconcilerUpdateDTO = z.infer<typeof ReconcilerUpdateDTOSchema>;

// Converting ReconcilerDeleteDTO to Zod
export const ReconcilerDeleteDTOSchema = z.object({
  ids: z.array(z.number()),
});

export type ReconcilerDeleteDTO = z.infer<typeof ReconcilerDeleteDTOSchema>;

// ReconcileDTOSchema already exists in Zod form, so keeping it
export const ReconcileDTOSchema = z
  .object({
    documentId: z.string().nullable(),
    flowId: z.coerce.number().nullable(),
    taskId: z.string().nullable(),
    worker: z.string().nullable(),
    forceFullRun: preprocessBoolean(z.boolean()).default(false).nullable(),
    isSync: preprocessBoolean(z.boolean()).default(false).nullable(),
    assignOneAgent: preprocessBoolean(z.boolean()).default(false).nullable(),
    redo: preprocessBoolean(z.boolean()).default(false).nullable(),
    testRun: preprocessBoolean(z.boolean()).default(false).nullable(),
    disableClearCommissionCalcs: preprocessBoolean(z.boolean())
      .default(false)
      .nullable(),
    disableUpdatePaidApprovedContacts: preprocessBoolean(z.boolean())
      .default(false)
      .nullable(),
    isManual: preprocessBoolean(z.boolean())
      .default(false)
      .nullable()
      .describe('Manual reconciliation'),
    policyId: z
      .number()
      .nullable()
      .describe('The policy needs to be reconciled'),
    statementIds: z
      .array(z.number())
      .nullable()
      .describe('The statements needs to be reconciled to the policy'),
  })
  .partial();

export type ReconcileDTO = z.infer<typeof ReconcileDTOSchema>;
