import { z } from 'zod';
import { CompReportGroupTypes, CompReportViewTypes } from 'common/globalTypes';

export type CompReportGroupType =
  | CompReportGroupTypes.NONE
  | CompReportGroupTypes.POLICY_GROUP;
export type CompReportViewType =
  | CompReportViewTypes.PRODUCER_VIEW
  | CompReportViewTypes.ADMIN_VIEW;

const groupingSchema = z.enum([
  CompReportGroupTypes.NONE,
  CompReportGroupTypes.POLICY_GROUP,
]) as unknown as z.ZodEnum<
  [CompReportGroupTypes.NONE, CompReportGroupTypes.POLICY_GROUP]
>;

const viewSchema = z.enum([
  CompReportViewTypes.PRODUCER_VIEW,
  CompReportViewTypes.ADMIN_VIEW,
]) as unknown as z.ZodE<PERSON><
  [CompReportViewTypes.PRODUCER_VIEW, CompReportViewTypes.ADMIN_VIEW]
>;

export const GetCompReportSchema = z.object({
  report_str_id: z.string(),
  grouping: groupingSchema,
  view: viewSchema,
  q: z.string().optional(),
  page: z.coerce.number().min(0).optional(),
  limit: z.coerce.number().min(50).optional(),
});

export type GetCompReportDto = {
  report_str_id: string;
  grouping: CompReportGroupType;
  view: CompReportViewType;
  q?: string;
  page?: number;
  limit?: number;
};
