import { z } from 'zod';

export const ReceivableSchema = z
  .object({
    report_id: z.number().nullable().optional(),
    report_ids: z.array(z.number()).nullable().optional(),
    statement_id: z.number().nullable().optional(),
  })
  .refine((data) => data.report_id || data.statement_id || data.report_ids, {
    message:
      'At least one of report_id or report_ids or statement_id is required',
  });

export type ReceivableDTO = z.infer<typeof ReceivableSchema>;
