import { z } from 'zod';
import { preprocessBoolean } from 'common/helpers/zod';
import { toArray } from 'common/helpers/toArray';

export const CompGridLevelCreateDTOSchema = z.object({
  name: z.string().nullable().optional(),
  transaction_id: z.coerce.number().nullable().optional(),
  comp_grid_id: z.coerce.number().nullable().optional(),
  effective_date: z.coerce.date().optional().nullable(),
  date: z.coerce.date().optional().nullable(),
  amount: z.coerce.number().optional().nullable(),
  logs: z.string().optional().nullable(),
  party: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  rate: z.coerce.number().optional().nullable(),
  status: z.string().optional().nullable(),
  contact_id: z.coerce.number().optional().nullable(),
  report_id: z.coerce.number().optional().nullable(),
  type: z
    .union([z.string(), preprocessBoolean(z.boolean())])
    .optional()
    .nullable(),
});

export const BulkCompGridLevelCreateDTOSchema = z.object({
  data: CompGridLevelCreateDTOSchema.array(),
});
export type BulkCompGridLevelCreateDTO = z.infer<
  typeof BulkCompGridLevelCreateDTOSchema
>;
export type CompGridLevelCreateDTO = z.infer<
  typeof CompGridLevelCreateDTOSchema
>;

export const CompGridLevelUpdateDTOSchema = CompGridLevelCreateDTOSchema.extend(
  {
    id: z.number(),
  }
);

export type CompGridLevelUpdateDTO = z.infer<
  typeof CompGridLevelUpdateDTOSchema
>;

export const CompGridLevelDeleteDTOSchema = z.object({
  ids: z.union([z.number(), z.array(z.number())]).transform(toArray),
});

export type CompGridLevelDeleteDTO = z.infer<
  typeof CompGridLevelDeleteDTOSchema
>;
