import { nanoid } from 'nanoid';
import { z } from 'zod';
import { extendZod<PERSON>ith<PERSON>penApi } from 'zod-openapi';

extendZodWithOpenApi(z);

const DocumentTypeEnum = z.enum(['commission', 'policy', 'compgrid'], {
  message: 'Invalid document type',
});

const fileType = z.object({
  size: z
    .number()
    .max(50 * 1024 * 1024, { message: 'File must be 50MB or less' }),
  mimetype: z.enum(
    [
      'image/jpeg',
      'image/png',
      'application/pdf',
      'text/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/vnd.oasis.opendocument.spreadsheet',
      'application/vnd.ms-excel.sheet.macroEnabled.12',
    ],
    {
      required_error: 'A file is required',
      message:
        'Invalid file type. Only JPEG, PNG, PDF, CSV, Excel/XLSX are allowed.',
    }
  ),
  hash: z.string(),
});

export const DocumentUploadSchema = z
  .object({
    type: DocumentTypeEnum,
    file: fileType.openapi({
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      type: 'file' as any,
      description: 'File to upload',
    }),
    company_name: z
      .string()
      .max(100, { message: 'Company name must be less than 100 characters' })
      .openapi({
        description: 'Name of the carrier / company',
        example: 'Company name',
      }),
    company_id: z
      .string()
      .max(36, { message: 'Company id must be less than 36 characters' })
      .openapi({
        description:
          'This is the identifier(which is stored in the sync_id of company entity) for the company synced from third party systems',
        example: '1234',
      }),
    bank_total_amount: z.coerce.number().openapi({
      description: 'Bank deposit amount for commission statements',
      example: 1000,
    }),
    statement_amount: z.coerce.number().openapi({
      description: 'Statement amount for commission statements',
      example: 1000,
    }),
    check_date: z.coerce.date().openapi({
      description: 'Date of the check',
      type: 'string',
      format: 'date',
    }),
    deposit_date: z.coerce.date().openapi({
      description: 'Date of the deposit',
      type: 'string',
      format: 'date',
    }),
    filename: z.string().openapi({
      description: 'Filename of the file',
      example: 'filename.pdf',
    }),
    notes: z.string().openapi({
      description: 'Notes for the document',
      example: '',
    }),
    sync_id: z.string().openapi({
      description:
        'Unique id for document. Currently unused, but allows for referencing file in the future.',
      example: nanoid(),
    }),
  })
  .partial()
  .openapi({
    ref: 'DocumentUploadSchema',
    description: 'A document uploaded to the server.',
    required: ['file', 'type'],
  });

export type DocumentUploadParams = z.infer<typeof DocumentUploadSchema>;

export const DocumentUploadResponseSchema = z.object({
  id: z.number(),
  str_id: z.string(),
  sync_id: z.string().nullable(),
});
