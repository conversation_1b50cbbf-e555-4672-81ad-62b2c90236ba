import { z } from 'zod';

export const ReceivableCalcSchema = z.object({
  effective_date_start: z.string().optional(),
  effective_date_end: z.string().optional(),
  transaction_type: z.array(z.string()).optional(),
  group_name: z.array(z.string()).optional(),
  agent_name: z.array(z.string()).optional(),
  contacts: z.array(z.string()).optional(),
  document_id: z.array(z.string()).optional(),
  policy_status: z.array(z.string()).optional(),
  product_name: z.array(z.string()).optional(),
  product_type: z.array(z.string()).optional(),
  writing_carrier_name: z.array(z.string()).optional(),
  account_type: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
});

export type ReceivableCalcRequest = z.infer<typeof ReceivableCalcSchema>;
