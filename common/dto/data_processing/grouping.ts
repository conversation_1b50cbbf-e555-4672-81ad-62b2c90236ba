import { z } from 'zod';
import { preprocessBoolean } from 'common/helpers/zod';

import { GroupingCalculationMethod } from './interfaces';

export const GroupingActions = ['group', 'dedupe'] as const;
export const GroupingDTOSchema = z
  .object({
    isSync: preprocessBoolean(z.boolean()).nullable().optional(),
    policyDataPriorityField: z.string().nullable().optional(),
    taskId: z.string().nullable().optional(),
    worker: z.string().nullable().optional(),
    excludeNonEditable: preprocessBoolean(z.boolean()).nullable().default(true),
    // At least two statement ids are required
    statementIds: z.array(z.number()).nullable().optional(),
    masterId: z.number().nullable().optional(),
    // At least two report ids are required
    reportIds: z
      .array(z.number())
      .min(2, { message: 'At least two policies are required' })
      .nullable()
      .optional(),
    useVirtualRecords: preprocessBoolean(z.boolean())
      .default(false)
      .nullable()
      .optional(),
    incrementalDedupe: preprocessBoolean(z.boolean())
      .default(false)
      .nullable()
      .optional(),
    calculationMethod: z
      .nativeEnum(GroupingCalculationMethod)
      .nullable()
      .optional(),
    advancedRatePercent: z.number().min(0).max(100).nullable().optional(),
    isManual: preprocessBoolean(z.boolean()).nullable().optional(),
    actions: z.array(z.enum(GroupingActions)).optional(),
    startDate: z.string().nullable().optional(),
    endDate: z.string().nullable().optional(),
  })
  .superRefine((data, ctx) => {
    if (
      !data.useVirtualRecords &&
      data.statementIds &&
      data.statementIds.length < 2
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.too_small,
        minimum: 2,
        type: 'array',
        inclusive: true,
        message: 'At least two statements are required',
        path: ['statementIds'],
      });
    }
  });

export type GroupingDTO = z.infer<typeof GroupingDTOSchema>;

export const UngroupingDTOSchema = z.object({
  statementIds: z
    .array(z.number())
    .min(1, { message: 'At least one statement is required' }),
});

export type UngroupingDTO = z.infer<typeof UngroupingDTOSchema>;

export const BulkUngroupingDTOSchema = z.object({
  startDate: z.coerce.date().nullable(),
  endDate: z.coerce.date().nullable(),
  carriers: z.array(z.string()).optional(),
  isSync: preprocessBoolean(z.boolean()).nullable().optional(),
});

export type BulkUngroupingDTO = z.infer<typeof BulkUngroupingDTOSchema>;
