import { z } from 'zod';
import { preprocessBoolean } from 'common/helpers/zod';

export const CommissionCalcSchema = z.object({
  id: z.coerce.number().optional().nullable(),
  ids: z.array(z.number()).optional().nullable(),
  useGroupedCommissions: preprocessBoolean(z.boolean()).optional().nullable(),
  documentId: z.string().optional().nullable(),
  documentIds: z
    .union([z.string(), z.array(z.string())])
    .optional()
    .nullable(),
  startDate: z.union([z.coerce.date(), z.undefined()]).nullable().optional(),
  endDate: z.union([z.coerce.date(), z.undefined()]).nullable().optional(),
  policyId: z.string().optional().nullable(),
  payingEntity: z.string().optional().nullable(),
  isSync: preprocessBoolean(z.boolean()).optional().nullable().default(false),
  onlyGetProfilesRates: preprocessBoolean(z.boolean())
    .optional()
    .nullable()
    .default(false),
  statementIds: z.array(z.number()).optional().nullable(),
  master_str_id: z.string().optional().nullable(),
  contactIds: z.array(z.string()).optional().nullable(),
  worker: z.string().optional().nullable(),
  regressionTestMode: preprocessBoolean(z.boolean())
    .optional()
    .nullable()
    .default(false),
  regressionAccount: z.string().optional().nullable(),
});

export type CommissionCalcDTO = z.infer<typeof CommissionCalcSchema>;

export const CalculateCommissionsDTOSchema = z.object({
  policy_ids: z.array(z.number()),
  account_id: z.string(),
  clear: preprocessBoolean(z.boolean()).optional().nullable().default(false),
  worker: z.string().optional().nullable(),
});

export type CalculateCommissionsDTO = z.infer<
  typeof CalculateCommissionsDTOSchema
>;

export const CalculateAgentCommissionsDTOSchema = z.object({
  agent_ids: z.array(z.number()),
  account_id: z.string(),
  clear: preprocessBoolean(z.boolean()).optional().nullable().default(false),
  worker: z.string().optional().nullable(),
});

export type CalculateAgentCommissionsDTO = z.infer<
  typeof CalculateAgentCommissionsDTOSchema
>;
