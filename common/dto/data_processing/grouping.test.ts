import { describe, expect, it } from 'vitest';

import {
  GroupingDTOSchema,
  UngroupingDTOSchema,
  GroupingActions,
} from './grouping';

describe('GroupingDTOSchema', () => {
  it('should validate when all required fields are provided', () => {
    const validData = {
      statementIds: [1, 2, 3],
      actions: GroupingActions as readonly string[],
    };

    const result = GroupingDTOSchema.parse(validData);
    expect(result.statementIds).toEqual([1, 2, 3]);
    expect(result.actions).toEqual(GroupingActions);
  });

  it('set default values for optional fields', () => {
    const validData = {};
    const result = GroupingDTOSchema.parse(validData);
    expect(result.excludeNonEditable).toBe(true);
  });

  describe('excludeNonEditable', () => {
    it('should accept false value', () => {
      const validData = {
        statementIds: [1, 2],
        excludeNonEditable: false,
      };

      const result = GroupingDTOSchema.parse(validData);
      expect(result.excludeNonEditable).toBe(false);
    });
  });

  describe('statementIds', () => {
    it('should validate with only statementIds', () => {
      const validData = {
        statementIds: [1, 2],
      };

      const result = GroupingDTOSchema.parse(validData);
      expect(result.statementIds).toEqual([1, 2]);
    });

    it('should throw error if statementIds is less than 2 items if not using virtual records', () => {
      const invalidData = {
        statementIds: [],
        useVirtualRecords: false,
      };

      expect(() => GroupingDTOSchema.parse(invalidData)).toThrow(
        'At least two statements are required'
      );
    });
  });

  describe('reportIds', () => {
    it('should validate with only reportIds', () => {
      const validData = {
        reportIds: [1, 2],
      };

      const result = GroupingDTOSchema.parse(validData);
      expect(result.reportIds).toEqual([1, 2]);
    });

    it('should throw error if reportIds is less than 2 items', () => {
      const invalidData = {
        reportIds: [],
      };

      expect(() => GroupingDTOSchema.parse(invalidData)).toThrow(
        'At least two policies are required'
      );
    });
  });

  it('should validate with all optional fields', () => {
    const validData = {
      isSync: true,
      policyDataPriorityField: 'test',
      taskId: '12345',
      worker: 'worker-1',
      excludeNonEditable: false,
      statementIds: [1, 2, 3],
      masterId: 123,
      reportIds: [4, 5, 6],
      isManual: true,
      actions: ['group'] as (typeof GroupingActions)[number][],
    };

    const result = GroupingDTOSchema.parse(validData);
    expect(result).toMatchObject(validData);
  });

  it('should accept null values for nullable fields', () => {
    const validData = {
      isSync: null,
      policyDataPriorityField: null,
      taskId: null,
      worker: null,
      excludeNonEditable: null,
      statementIds: null,
      masterId: null,
      reportIds: null,
      isManual: null,
    };

    const result = GroupingDTOSchema.parse(validData);
    expect(result.isSync).toBeNull();
    expect(result.policyDataPriorityField).toBeNull();
    expect(result.taskId).toBeNull();
    expect(result.worker).toBeNull();
    expect(result.excludeNonEditable).toBeNull();
    expect(result.statementIds).toBeNull();
    expect(result.masterId).toBeNull();
    expect(result.reportIds).toBeNull();
    expect(result.isManual).toBeNull();
  });

  it('should fail if actions contains invalid value', () => {
    const invalidData = {
      statementIds: [1, 2],
      actions: ['group', 'invalid'],
    };

    expect(() => GroupingDTOSchema.parse(invalidData)).toThrow(
      'Invalid enum value'
    );
  });
});

describe('UngroupingDTOSchema', () => {
  it('should validate when all required fields are provided', () => {
    const validData = {
      statementIds: [1],
    };

    const result = UngroupingDTOSchema.parse(validData);
    expect(result).toEqual(validData);
  });

  it('should validate with multiple statement IDs', () => {
    const validData = {
      statementIds: [1, 2, 3, 4, 5],
    };

    const result = UngroupingDTOSchema.parse(validData);
    expect(result).toEqual(validData);
  });

  it('should fail if statementIds is empty', () => {
    const invalidData = {
      statementIds: [],
    };

    expect(() => UngroupingDTOSchema.parse(invalidData)).toThrow(
      'At least one statement is required'
    );
  });

  it('should fail if statementIds is missing', () => {
    const invalidData = {};

    expect(() => UngroupingDTOSchema.parse(invalidData)).toThrow();
  });
});
