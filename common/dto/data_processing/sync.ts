import { z } from 'zod';
import { preprocessBoolean } from 'common/helpers/zod';

export type Entity =
  | 'agents'
  | 'agentHierarchy'
  | 'agentLevel'
  | 'carriersAndProducts'
  | 'policies'
  | 'policySplits'
  | 'compGrids'
  | 'compProfiles'
  | 'documents'
  | 'customers';

export type NowCertsPolicyStatus =
  | 'Active'
  | 'Cancelled'
  | 'Expired'
  | 'Fintary'
  | 'Non-Renewal'
  | 'Pending Cancel'
  | 'Renewed'
  | 'Renewing'
  | 'Replaced';

export const SyncParamsSchema = z
  .object({
    entities: z
      .array(
        z.enum(
          [
            'agents',
            'agentHierarchy',
            'agentLevel',
            'carriersAndProducts',
            'policies',
            'policySplits',
            'compGrids',
            'compProfiles',
            'documents',
            'customers',
          ],
          {
            invalid_type_error: 'Invalid entity',
          }
        )
      )
      .min(1, { message: 'At least one entity is required' }),
    sync: preprocessBoolean(z.boolean()).optional().nullable().default(false),
    isFullSync: preprocessBoolean(z.boolean())
      .optional()
      .nullable()
      .default(false),
    policyStates: z
      .array(
        z.enum([
          'Active',
          'Cancelled',
          'Expired',
          'Fintary',
          'Non-Renewal',
          'Pending Cancel',
          'Renewed',
          'Renewing',
          'Replaced',
        ])
      )
      .optional()
      .nullable(),
    startDate: z.coerce.date().nullable().optional(),
    endDate: z.coerce.date().nullable().optional(),
    worker: z.string().optional().nullable(),
    policyNumbers: z.array(z.string()).optional().nullable(),
  })
  .partial();

export type SyncParamsDTO = z.infer<typeof SyncParamsSchema>;
export type EntityType = z.infer<typeof SyncParamsSchema.shape.entities>;

export const QueryAccountEntitiesDTOSchema = z.object({
  accountId: z.string(),
});

export type QueryAccountEntitiesDTO = z.infer<
  typeof QueryAccountEntitiesDTOSchema
>;

export const SyncNowCertsPolicySchema = z.object({
  syncId: z.string().optional().nullable(),
  ids: z.array(z.coerce.number()).optional().nullable(),
});

export type SyncNowCertsPolicyDTO = z.infer<typeof SyncNowCertsPolicySchema>;

export const SyncBenefitPointStatementsDTOSchema = z.object({
  documentId: z.string(),
});

export type SyncBenefitPointStatementsDTO = Required<
  z.infer<typeof SyncBenefitPointStatementsDTOSchema>
>;

export const SyncOverrideFieldsDTOSchema = z.object({
  table: z.string(),
  id: z.coerce.number(),
  fields: z.array(z.string()),
});

export type SyncOverrideFieldsDTO = z.infer<typeof SyncOverrideFieldsDTOSchema>;

export const SyncBenefitPointMemberCountDTOSchema = z.object({
  documentId: z.string(),
});

export type SyncBenefitPointMemberCountDTO = z.infer<
  typeof SyncBenefitPointMemberCountDTOSchema
>;
