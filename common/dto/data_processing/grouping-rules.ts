import { z } from 'zod';
import { toArray } from 'common/helpers/toArray';
import { preprocessBoolean } from 'common/helpers/zod';

import { GroupingCalculationMethod } from './interfaces';

export const GroupingRuleSchema = z.object({
  name: z.string(),
  notes: z.string().optional().nullable(),
  entity: z.string(),
  filter: z
    .array(
      z.object({
        field: z.string(),
        op: z.string(),
        value: z.string().optional().nullable(),
        skipEmpty: preprocessBoolean(z.boolean()).nullable().optional(),
        usePolicyData: preprocessBoolean(z.boolean()).nullable().optional(),
        caseSensitive: preprocessBoolean(z.boolean()).nullable().optional(),
      })
    )
    .optional(),
  key_condition: z.array(
    z.object({
      field: z.string(),
      transformers: z.array(z.string()),
      params: z.record(z.any(), z.any()).optional(),
    })
  ),
  config: z
    .object({
      period_date_field: z.string().optional(),
      calculation_method: z.nativeEnum(GroupingCalculationMethod).optional(),
      advanced_rate_percent: z.number().min(0).max(100).optional(),
    })
    .nullable()
    .optional(),
});
export const UpdateGroupingRuleSchema = GroupingRuleSchema.extend({
  id: z.number(),
});

export type UpdateGroupingRule = z.infer<typeof UpdateGroupingRuleSchema>;

export type GroupingRule = z.infer<typeof GroupingRuleSchema>;

export const DeleteGroupingRuleSchema = z.object({
  ids: z.union([z.number(), z.array(z.number())]).transform(toArray),
});

export type DeleteGroupingRule = z.infer<typeof DeleteGroupingRuleSchema>;
