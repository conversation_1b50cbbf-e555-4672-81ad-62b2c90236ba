export enum GroupingCalculationMethod {
  SUM_BY_COMMISSION = 'sumByCommissionAmount',
  SPLIT_ON_PREMIUM = 'splitOnPremiumAmount',
  SPLIT_WITH_TOTAL_PREMIUM = 'splitWithTotalPremiumAmount',
  GROUP_BY_TOTAL_PREMIUM = 'groupByTotalPremium',
  GROUP_BY_PREMIUM = 'groupByPremiumAmount',
  SUM_BY_PREMIUM = 'sumByPremiumAmount',
  PREMIUM_FROM_POLICY = 'premiumFromPolicy',
  EMPTY_PREMIUM_AMOUNT = 'emptyPremiumAmount',
  BA_MOO_COMMISSION_CALCULATION = 'baMooCommissionCalculation',
}

export const GroupingCalculationMethodDescription = new Map<
  GroupingCalculationMethod,
  string
>([
  [GroupingCalculationMethod.SUM_BY_COMMISSION, 'Sum by commission amount'],
  [GroupingCalculationMethod.SPLIT_ON_PREMIUM, 'Split on premium amount'],
  [
    GroupingCalculationMethod.SPLIT_WITH_TOTAL_PREMIUM,
    'Split with total premium amount',
  ],
  [GroupingCalculationMethod.SUM_BY_PREMIUM, 'Multiple premium amounts'],
  [
    GroupingCalculationMethod.GROUP_BY_TOTAL_PREMIUM,
    'Combining group & split with total premium',
  ],
  [
    GroupingCalculationMethod.GROUP_BY_PREMIUM,
    'Combining group & split on premium',
  ],
  [GroupingCalculationMethod.PREMIUM_FROM_POLICY, 'Leverage policy premium'],
  [GroupingCalculationMethod.EMPTY_PREMIUM_AMOUNT, 'Empty premium amount'],
  [
    GroupingCalculationMethod.BA_MOO_COMMISSION_CALCULATION,
    'BA MOO commission calculation with bonus validation',
  ],
]);
