import { z } from 'zod';
import { AccessTypes } from 'common/globalTypes';

export const DataUpdateActionCreateSchema = z.object({
  data_entity: z.string(),
  data_update_actions: z.any(),
  data_update_actions_params: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  name: z.string(),
  access: z.nativeEnum(AccessTypes),
});

export const DataUpdateActionCreateArraySchema = z.array(
  DataUpdateActionCreateSchema
);

export const DataUpdateActionUpdateSchema = z.object({
  data_entity: z.string(),
  data_update_actions: z.any(),
  data_update_actions_params: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  name: z.string(),
  str_id: z.string(),
});

export const DataUpdateActionDeleteSchema = z.object({
  ids: z.array(z.number()),
});

export type DataUpdateActionCreateDTO = z.infer<
  typeof DataUpdateActionCreateSchema
>;
export type DataUpdateActionCreateArrayDTO = z.infer<
  typeof DataUpdateActionCreateArraySchema
>;
export type DataUpdateActionUpdateDTO = z.infer<
  typeof DataUpdateActionUpdateSchema
>;
export type DataUpdateActionDeleteDTO = z.infer<
  typeof DataUpdateActionDeleteSchema
>;
