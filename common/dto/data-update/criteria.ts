import { z } from 'zod';
import { AccessTypes } from 'common/globalTypes';

export const DataUpdateCriteriaCreateSchema = z.object({
  data_entity: z.string(),
  data_update_criteria: z.any().optional().nullable(),
  notes: z.string().optional().nullable(),
  name: z.string(),
  access: z.nativeEnum(AccessTypes).optional().nullable(),
  custom_data_update_criteria_mode: z.boolean().optional().nullable(),
  custom_data_update_criteria: z.string().optional().nullable(),
  custom_data_update_criteria_params: z.string().optional().nullable(),
});

export const DataUpdateCriteriaCreateArraySchema = z.array(
  DataUpdateCriteriaCreateSchema
);

export const DataUpdateCriteriaUpdateSchema = z.object({
  data_entity: z.string(),
  data_update_criteria: z.any().optional().nullable(),
  notes: z.string().optional().nullable(),
  name: z.string(),
  str_id: z.string(),
  custom_data_update_criteria_mode: z.boolean().optional().nullable(),
  custom_data_update_criteria: z.string().optional().nullable(),
  custom_data_update_criteria_params: z.string().optional().nullable(),
});

export const DataUpdateCriteriaDeleteSchema = z.object({
  ids: z.array(z.number()),
});

export type DataUpdateCriteriaCreateDTO = z.infer<
  typeof DataUpdateCriteriaCreateSchema
>;
export type DataUpdateCriteriaCreateArrayDTO = z.infer<
  typeof DataUpdateCriteriaCreateArraySchema
>;
export type DataUpdateCriteriaUpdateDTO = z.infer<
  typeof DataUpdateCriteriaUpdateSchema
>;
export type DataUpdateCriteriaDeleteDTO = z.infer<
  typeof DataUpdateCriteriaDeleteSchema
>;
