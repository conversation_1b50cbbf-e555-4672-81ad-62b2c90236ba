import { z } from 'zod';
import { AccessTypes } from 'common/globalTypes';

export const DataUpdateConfigCreateSchema = z.object({
  data_entity: z.string(),
  group: z.string(),
  data_update_criteria: z.any(),
  data_update_actions: z.any(),
  flag_config_mode: z.boolean().optional().nullable(),
  notes: z.string().optional().nullable(),
  name: z.string(),
  access: z.nativeEnum(AccessTypes),
});

export const DataUpdateConfigCreateArraySchema = z.array(
  DataUpdateConfigCreateSchema
);

export const DataUpdateConfigUpdateSchema = z.object({
  str_id: z.string(),
  group: z.string(),
  data_entity: z.string(),
  data_update_criteria: z.any(),
  data_update_actions: z.any(),
  flag_config_mode: z.boolean().optional().nullable(),
  notes: z.string().optional().nullable(),
  name: z.string(),
});

export const DataUpdateConfigDeleteSchema = z.object({
  ids: z.array(z.number()),
});

export type DataUpdateConfigCreateDTO = z.infer<
  typeof DataUpdateConfigCreateSchema
>;
export type DataUpdateConfigCreateArrayDTO = z.infer<
  typeof DataUpdateConfigCreateArraySchema
>;
export type DataUpdateConfigUpdateDTO = z.infer<
  typeof DataUpdateConfigUpdateSchema
>;
export type DataUpdateConfigDeleteDTO = z.infer<
  typeof DataUpdateConfigDeleteSchema
>;
