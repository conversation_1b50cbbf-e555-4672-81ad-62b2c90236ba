import { z } from 'zod';

export const DeleteAdminUsersDTOSchema = z
  .object({
    id: z
      .number()
      .or(z.string().transform((val) => parseInt(val, 10)))
      // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      .refine((val) => !isNaN(val) && Number.isInteger(val), {
        message: 'Invalid id',
      }),
  })
  .partial();

export type DeleteAdminUsersDTO = z.infer<typeof DeleteAdminUsersDTOSchema>;
