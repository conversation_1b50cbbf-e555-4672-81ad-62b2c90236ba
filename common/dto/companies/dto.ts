import { z } from 'zod';

export const DeleteCompaniesDTOSchema = z
  .object({
    ids: z
      .array(z.number())
      .default([])
      .refine((ids) => Array.isArray(ids) && ids.length > 0, {
        message: 'Missing ids',
      })
      .refine(
        (ids) =>
          // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          ids.every((id) => !isNaN(Number(id)) && Number.isInteger(Number(id))),
        {
          message: 'Invalid ids',
        }
      ),
  })
  .partial();

export const CompaniesDTOSchema = z
  .object({
    id: z.preprocess((val) => {
      if (typeof val === 'string') {
        const parsed = parseInt(val, 10);
        // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        return isNaN(parsed) ? val : parsed;
      }
      return val;
    }, z.number().int()),
    str_id: z.string().max(30).optional().nullable(),
    state: z.enum(['active', 'inactive', 'deleted']).default('active'),
    access: z.enum(['global', 'account']).default('account'),
    created_at: z.any().optional().nullable(),
    created_by: z.any().optional().nullable(),
    created_proxied_by: z.any().optional().nullable(),
    updated_at: z.any().optional().nullable(),
    updated_by: z.any().optional().nullable(),
    updated_proxied_by: z.any().optional().nullable(),
    companies_processors: z.any().optional().nullable(),

    address: z.string().optional().nullable(),
    alias_list: z.array(z.string()).default([]),
    company_id: z.string().max(30).optional().nullable(),
    company_name: z.string().max(100).optional().nullable(),
    canonical_id: z.preprocess((val) => {
      if (val === 'N/A') return null;
      return val;
    }, z.number().int().optional().nullable()),
    email: z.string().email().max(30).optional().nullable(),
    group_id: z.string().max(30).optional().nullable(),
    sync_id: z.string().max(30).optional().nullable(),
    log: z.any().optional().nullable(),
    notes: z.string().optional().nullable(),
    phone: z.string().max(15).optional().nullable(),
    type: z.any().optional().nullable(),
    website: z.string().url().optional().nullable(),
    profile_str_ids: z
      .union([z.array(z.string()), z.array(z.number())])
      .optional()
      .nullable(),
    config: z.any().optional().nullable(),
    sync_worker: z.string().optional().nullable(),
    processor_str_ids: z.array(z.string()).default([]),
    companies_document_profiles: z.any().optional().nullable(),
    child_companies: z
      .array(
        z.object({
          id: z.union([z.string(), z.number()]),
          company_name: z.string().optional(),
          str_id: z.string().optional(),
        })
      )
      .optional(),
  })
  .partial();

export type CompaniesDTO = z.infer<typeof CompaniesDTOSchema>;
export type DeleterCompaniesDTO = z.infer<typeof DeleteCompaniesDTOSchema>;

export const CompanyCreateDTOSchema = z.object({
  name: z.string().min(1, { message: 'Name cannot be empty' }),
  account_id: z.string().min(1),
  notes: z.string().optional().nullable(),
  custom_fields: z.record(z.any()).optional().nullable(),
});

export type CompanyCreateDTO = z.infer<typeof CompanyCreateDTOSchema>;

export const CompanyUpdateDTOSchema = CompanyCreateDTOSchema.extend({
  id: z.number(),
});

export type CompanyUpdateDTO = z.infer<typeof CompanyUpdateDTOSchema>;

export const CompanyDeleteDTOSchema = z.object({
  ids: z.array(z.number()),
  account_id: z.string(),
  uid: z.string(),
});

export type CompanyDeleteDTO = z.infer<typeof CompanyDeleteDTOSchema>;
