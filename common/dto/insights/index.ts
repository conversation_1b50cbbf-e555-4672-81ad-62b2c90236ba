import { z } from 'zod';

export const UpdateDashboardSchema = z.object({
  adminWidgetsSettings: z.array(z.number()),
  producerWidgetsSettings: z.array(z.number()),
  dataSpecialistWidgetsSettings: z.array(z.number()),
  dashboardName: z.string().nullable(),
  accessRoles: z.array(z.string()),
});

export const UpdateDashboardQuerySchema = z.object({
  params: z.tuple([
    z.literal('accountWidgetsSettings'),
    z.string().regex(/^\d+$/, 'invalid ID'),
  ]),
});

export type UpdateDashboard = z.infer<typeof UpdateDashboardSchema>;
export type UpdateDashboardQuery = z.infer<typeof UpdateDashboardQuerySchema>;
