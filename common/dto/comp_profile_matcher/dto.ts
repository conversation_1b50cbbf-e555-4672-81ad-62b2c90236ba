import { z } from 'zod';

export const CompProfileMatcherDTOSchema = z.object({
  compProfileMatchers: z
    .array(
      z.object({
        agentStrId: z.string().max(100),
        compProfileStrId: z.string().max(100),
      })
    )
    .min(1, { message: 'At least one comp profile matcher is required' }),
  commissionId: z.string().max(100),
});

export type CompProfileMatcherDTO = z.infer<typeof CompProfileMatcherDTOSchema>;
