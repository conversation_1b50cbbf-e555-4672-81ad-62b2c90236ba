import { z } from 'zod';
import { toArray } from 'common/helpers/toArray';

export const CompGridRateCreateDTOSchema = z.object({
  comp_grid_id: z.coerce.number().optional().nullable(),
  comp_grid_level_id: z.coerce.number().optional().nullable(),
  comp_grid_criterion_id: z.coerce.number().min(1),
  carrier_rate: z.coerce.number(),
  house_rate: z.coerce.number().optional().nullable(),
  rate: z.coerce.number().optional().nullable(),
});

export const CompGridRateCreateWithoutGridDTOSchema =
  CompGridRateCreateDTOSchema.omit({
    comp_grid_id: true,
  });

export const BulkCompGridRateCreateDTOSchema = z.object({
  data: CompGridRateCreateWithoutGridDTOSchema.array(),
});

export type BulkCompGridRateCreateDTO = z.infer<
  typeof BulkCompGridRateCreateDTOSchema
>;
export type CompGridRateCreateDTO = z.infer<typeof CompGridRateCreateDTOSchema>;

export const CompGridRateUpdateDTOSchema = CompGridRateCreateDTOSchema.extend({
  id: z.number(),
});

export type CompGridRateUpdateDTO = z.infer<typeof CompGridRateUpdateDTOSchema>;

export const CompGridDeleteDTOSchema = z.object({
  ids: z.union([z.number(), z.array(z.number())]).transform(toArray),
});

export type CompGridDeleteDTO = z.infer<typeof CompGridDeleteDTOSchema>;
