import { z } from 'zod';
// Use zod to create a schema
export const CreateAccountConfigSchema = z.object({
  value: z.any(),
  type: z.string(),
  notes: z.string().optional().nullable(),
  name: z.string().optional().nullable(),
});

export const UpdateAccountConfigSchema = z.object({
  id: z.number(),
  value: z.any(),
  notes: z.string().optional().nullable(),
  name: z.string().optional().nullable(),
});

export type CreateAccountConfig = z.infer<typeof CreateAccountConfigSchema>;
export type UpdateAccountConfig = z.infer<typeof UpdateAccountConfigSchema>;
