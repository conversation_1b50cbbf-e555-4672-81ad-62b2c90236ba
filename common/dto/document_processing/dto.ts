import { z } from 'zod';
import { preprocessBoolean } from 'common/helpers/zod';

export const DocumentProcessingDTOSchema = z.object({
  account_id: z.string(),
  uid: z.string(),
  document_id: z.number(),
  document_str_id: z.string(),
  file_path: z.string().nullable(),
  file_type: z.string().optional().nullable(),
  type: z.string().optional().nullable(),
  statement_amount: z.number().optional().nullable(),
  file_name: z.string().optional().nullable(),
  companies: z.any(),
  worker: z.string().optional().nullable(),
});

export type DocumentProcessingDTO = z.infer<typeof DocumentProcessingDTOSchema>;

export const DocumentProcessDTOSchema = z.object({
  document_id: z.string(),
  docType: z.string(),
  worker: z.string().optional().nullable(),
  override: preprocessBoolean(z.boolean()).optional().nullable(),
  account_id: z.string().optional().nullable(),
  uid: z.string().optional().nullable(),
});

export type DocumentProcessDTO = z.infer<typeof DocumentProcessDTOSchema>;

export const BatchDocumentProcessDTOSchema = z.object({
  document_ids: z.array(z.string()),
  docType: z.string(),
  worker: z.string().optional().nullable(),
  override: preprocessBoolean(z.boolean()).optional().nullable(),
  account_id: z.string().optional().nullable(),
  uid: z.string().optional().nullable(),
});

export type BatchDocumentProcessDTO = z.infer<
  typeof BatchDocumentProcessDTOSchema
>;
