import { z } from 'zod';
import { preprocessBoolean } from 'common/helpers/zod';

export const GetSavedReportsFileQuerySchema = z.object({
  page: z.coerce.number().optional().nullable(),
  limit: z.coerce.number().optional().nullable(),
  q: z.string().optional().nullable(),
  // @ts-expect-error
  is_dynamic_select: preprocessBoolean(z.boolean().optional().default(false)),
  file_type: z.string().optional().nullable(),
});

export type GetSavedReportsFileQuery = z.infer<
  typeof GetSavedReportsFileQuerySchema
>;

export const UploadedSavedReports = z.object({
  file_name: z.string(),
  file_type: z.string(),
  file_hash: z.string().optional().nullable(),
  file_content: z.string(), // Base64 encoded file content
});

export const SavedReportsFileUploadSchema = z.array(
  z.object({
    name: z.string().optional().nullable(),
    notes: z.string().optional().nullable(),
    access: z.enum(['user', 'global', 'user_list', 'account']),
    users_white_list: z.array(z.string()).optional().nullable(),
    uploaded_saved_reports: UploadedSavedReports,
  })
);

export const SavedReportsDelete = z.object({
  str_id: z.string(),
});

export type SavedReportsFileUpload = z.infer<
  typeof SavedReportsFileUploadSchema
>;

export type UploadSavedReports = z.infer<typeof UploadedSavedReports>;
