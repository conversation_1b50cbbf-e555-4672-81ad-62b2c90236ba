import { Expose } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class CreateCalculationMethodDTO {
  @Expose()
  @IsString()
  // @ts-expect-error
  method: string;

  @Expose()
  @IsString()
  // @ts-expect-error
  name: string;

  @Expose()
  @IsString()
  @IsOptional()
  formula?: string;
}

export class ListDTO {
  @Expose()
  @IsNumber()
  @IsOptional()
  page?: number;

  @Expose()
  @IsNumber()
  @IsOptional()
  limit?: number;

  @Expose()
  @IsOptional()
  @IsString()
  orderBy?: string;

  @Expose()
  @IsOptional()
  @IsString()
  sort?: string;
}
export class UpdateCalculationMethodDTO extends CreateCalculationMethodDTO {
  @Expose()
  // @ts-expect-error
  id: number;
}

export class DeleteDTO {
  @Expose()
  @IsNumber({}, { each: true })
  // @ts-expect-error
  ids: number[];
}
