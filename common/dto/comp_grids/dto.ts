import { z } from 'zod';
import { toArray } from 'common/helpers/toArray';

export const CompGridCreateDTOSchema = z.object({
  name: z.string().min(1, { message: 'Name cannot be empty' }),
  company_id: z.coerce.number(),
  rate_fields: z
    .union([
      z.array(z.string()),
      z.string().transform((val) => {
        try {
          return JSON.parse(val);
        } catch {
          return [];
        }
      }),
    ])
    .nullable()
    .optional(),
  notes: z.string().optional().nullable(),
});

export const BulkCompGridCreateDTOSchema = z.object({
  data: CompGridCreateDTOSchema.array(),
});

export type BulkCompGridCreateDTO = z.infer<typeof BulkCompGridCreateDTOSchema>;
export type CompGridCreateDTO = z.infer<typeof CompGridCreateDTOSchema>;

export const CompGridUpdateDTOSchema = CompGridCreateDTOSchema.extend({
  id: z.number(),
});

export type CompGridUpdateDTO = z.infer<typeof CompGridUpdateDTOSchema>;

export const CompGridDeleteDTOSchema = z.object({
  ids: z.union([z.number(), z.array(z.number())]).transform(toArray),
});

export type CompGridDeleteDTO = z.infer<typeof CompGridDeleteDTOSchema>;

export const GetCompGridOptionsSchema = z.object({
  company_str_ids: z
    .union([z.string(), z.array(z.string())])
    .transform(toArray)
    .optional(),
});

export type GetCompGridOptionsDto = z.infer<typeof GetCompGridOptionsSchema>;

export const GetCompGridViewerExportSchema = z.object({
  export_type: z.enum(['csv', 'pdf']),
  company_str_ids: z
    .union([z.string(), z.array(z.string())])
    .transform(toArray)
    .optional(),
  comp_grid_levels_str_ids: z
    .union([z.string(), z.array(z.string())])
    .transform(toArray)
    .refine((val) => !val || val.length <= 10, {
      message: 'You can select a maximum number of 10 comp grid levels.',
    })
    .optional(),
});

export type GetCompGridViewerExportDto = z.infer<
  typeof GetCompGridViewerExportSchema
>;
