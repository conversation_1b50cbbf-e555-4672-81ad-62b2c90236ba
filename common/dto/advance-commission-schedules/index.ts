import { z } from 'zod';

export const CreateAdvanceCommissionSchedulesDTOSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  advance_cap: z.number().nullable().optional(),
  company_ids: z.array(z.number()).min(1, 'Companies are required'),
  delay: z.number().nullable().optional(),
  notes: z.string().optional().nullable(),
  payment_date_basis: z.string().optional(),
  premium_amount_basis: z.string().optional(),
  product_type: z.string().nullable().optional(),
  schedule_type: z.string().optional(),
  schedules: z.any().nullable().optional(),
});

export type CreateAdvanceCommissionSchedulesDTO = z.infer<
  typeof CreateAdvanceCommissionSchedulesDTOSchema
> & { name: string };

export const UpdateAdvanceCommissionSchedulesDTOSchema =
  CreateAdvanceCommissionSchedulesDTOSchema.extend({
    id: z.number().min(1, 'ID is required'),
  });

export type UpdateAdvanceCommissionSchedulesDTO = z.infer<
  typeof UpdateAdvanceCommissionSchedulesDTOSchema
>;
export const DeleteAdvanceCommissionSchedulesDTOSchema = z.object({
  ids: z.array(z.number().min(1, 'ID is required')),
});

export type DeleteAdvanceCommissionSchedulesDTO = z.infer<
  typeof DeleteAdvanceCommissionSchedulesDTOSchema
>;
