import dayjs from 'dayjs';
import { z } from 'zod';

export const CarrierGridLevelSchema = z.object({
  agentId: z.string(),
  type: z.enum(['carrier', 'agency']),
  // Accept both number and string for level, and transform it to a string
  level: z.union([z.number(), z.string()]).transform((val) => String(val)),
  company: z.string().optional(),
  productType: z.string().optional(),
  startDate: z
    .string()
    .optional()
    .refine((val) => {
      return dayjs(val).isValid();
    }),

  endDate: z
    .string()
    .optional()
    .refine((val) => {
      return dayjs(val).isValid();
    }),
});

export const CarrierGridLevelArraySchema = z.array(CarrierGridLevelSchema);

export type CarrierGridLevelDTO = z.infer<typeof CarrierGridLevelSchema>;
