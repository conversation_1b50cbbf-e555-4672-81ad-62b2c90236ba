import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import currency from 'currency.js';
import { parseDate } from 'chrono-node';

dayjs.extend(utc);

const extractNumbers = (str: string): number[] => {
  const regex = /[-+]?\d*\.?\d+/g;
  const numbers = str.match(regex);
  return numbers ? numbers.map(Number) : [];
};

const normalizeDate = (date: string | number, isUtc = true): Date | string => {
  // @ts-expect-error
  if (!date) return null;

  const parsedDate = parseDate(date.toString());

  const result = isUtc
    ? new Date(dayjs.utc(parsedDate).startOf('date').valueOf())
    : new Date(dayjs(parsedDate).startOf('date').valueOf());

  return result;
};

const normalizeCurrency = (value: string | number): number | null => {
  if (!value) return null;

  try {
    return currency(value).value;
  } catch {
    return null;
  }
};

const normalizePercentage = (value: string | number): string | null => {
  if (!value) return null;

  const stringValue = value.toString();
  const numList = extractNumbers(stringValue);

  if (numList.length === 0) return '';

  const num = numList[0];

  return stringValue.includes('%') ? `${num}%` : `${num}%`;
};

const normalizeBoolean = (value: string | number): boolean => {
  if (!value) return false;

  const trueList = ['true', 'yes', '1'];
  return trueList.includes(value.toString().toLowerCase());
};

const normalizeInt = (value: string | number): number | null => {
  if (!value) return null;

  const parsed = parseInt(value.toString(), 10);
  // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  return isNaN(parsed) ? null : parsed;
};

export {
  normalizeDate,
  normalizeCurrency,
  normalizePercentage,
  normalizeBoolean,
  normalizeInt,
  extractNumbers,
};
