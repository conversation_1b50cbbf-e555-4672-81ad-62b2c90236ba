import { describe, it, expect } from 'vitest';

import isValidDateFormat from './isValidDateFormat';

describe('isValidDateFormat', () => {
  it('should return true for valid date strings', () => {
    expect(isValidDateFormat('12/31/2023')).toBe(true);
    expect(isValidDateFormat('2023-12-31')).toBe(true);
    expect(isValidDateFormat('12/31/23', false, true)).toBe(true);
    expect(isValidDateFormat('2023/12/31', true, false)).toBe(true);
  });

  it('should return false for invalid date strings', () => {
    expect(isValidDateFormat('12/31')).toBe(false);
    expect(isValidDateFormat('12/31/2023 jsdhs', false, false)).toBe(true);
    expect(isValidDateFormat('12/31/2023 jsdhs', true, false)).toBe(false);
  });
});
