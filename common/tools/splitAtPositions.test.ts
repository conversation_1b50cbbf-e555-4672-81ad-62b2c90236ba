import { describe, it, expect } from 'vitest';

import splitAtPositions from './splitAtPositions';

describe('splitAtPositions', () => {
  it('should split string at specified positions', () => {
    expect(splitAtPositions('Hello world how are you', ' ')).toEqual([
      'Hello',
      'world',
      'how',
      'are',
      'you',
    ]);
    expect(splitAtPositions('Hello world how are you', ' ', [1])).toEqual([
      'Hello',
      'world how are you',
    ]);
    expect(splitAtPositions('Hello world how are you', ' ', [-1])).toEqual([
      'Hello world how are',
      'you',
    ]);
    expect(splitAtPositions('Hello world how are you', ' ', [2, -2])).toEqual([
      'Hello world',
      'how',
      'are you',
    ]);
    expect(splitAtPositions('apple,orange,banana,grape', ',', [2])).toEqual([
      'apple,orange',
      'banana,grape',
    ]);
    expect(splitAtPositions('Hello world', '-')).toEqual(['Hello world']);
    expect(splitAtPositions('', ' ')).toEqual(['']);
    expect(splitAtPositions('a b c', ' ', [5])).toEqual(['a b c']);
    expect(splitAtPositions('a b c', ' ', [-5])).toEqual(['a b c']);
    expect(splitAtPositions('one::two::three', '::', [1])).toEqual([
      'one',
      'two::three',
    ]);
    expect(splitAtPositions('a b c d', ' ', [1, 10, -1])).toEqual([
      'a',
      'b c',
      'd',
    ]);
    expect(splitAtPositions('a b c', ' ', [0])).toEqual(['a b c']);
    expect(splitAtPositions('a b c', ' ', [])).toEqual(['a', 'b', 'c']);
  });
});
