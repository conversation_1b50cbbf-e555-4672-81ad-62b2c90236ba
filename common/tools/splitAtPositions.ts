const splitAtPositions = (
  str: string,
  separator: string,
  positions?: number[]
): string[] => {
  if (!positions || positions.length === 0) {
    return str.split(separator);
  }

  const indices: number[] = [];
  let idx = str.indexOf(separator);
  while (idx !== -1) {
    indices.push(idx);
    idx = str.indexOf(separator, idx + separator.length);
  }

  if (indices.length === 0) {
    return [str];
  }

  const targetIndices = positions
    .map((pos) => {
      if (pos > 0) {
        return indices[pos - 1] || -1;
      } else if (pos < 0) {
        return indices[indices.length + pos] || -1;
      }
      return -1;
    })
    .filter((idx) => idx !== -1)
    .sort((a, b) => a - b);

  if (targetIndices.length === 0) {
    return [str];
  }

  const result: string[] = [];
  let startIdx = 0;

  for (const idx of targetIndices) {
    result.push(str.substring(startIdx, idx));
    startIdx = idx + separator.length;
  }

  result.push(str.substring(startIdx));

  return result;
};

export const desc_splitAtPositions = {
  name: 'libs.tools.splitAtPositions(str, separator, positions)',
  description:
    'Splits a string at specified positions of a separator. Can use nth or nth-from-last separator.',
  arguments: {
    str: '[String] String to split',
    separator: '[String] Separator to use for splitting',
    positions:
      '[Array<number>] Optional array of separator positions, positive counts from start, negative from end',
  },
  returns: '[Array<string>] Array of split string parts',
  examples: `// Split at first space
splitAtPositions("Hello world how are you", " ", [1])
// → ["Hello", "world how are you"]

// Split at last space
splitAtPositions("Hello world how are you", " ", [-1])
// → ["Hello world how are", "you"]

// Split at second and second-to-last spaces
splitAtPositions("Hello world how are you", " ", [2, -2])
// → ["Hello world", "how", "are you"]

// Regular split (all separators)
splitAtPositions("Hello world how are you", " ")
// → ["Hello", "world", "how", "are", "you"]

// Split at second comma
splitAtPositions("apple,orange,banana,grape", ",", [2])
// → ["apple,orange", "banana,grape"]`,
};

export default splitAtPositions;
