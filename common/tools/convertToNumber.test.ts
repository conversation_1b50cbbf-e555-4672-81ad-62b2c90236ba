import { describe, it, expect } from 'vitest';

import convertToNumber from './convertToNumber';

describe('convertToNumber', () => {
  it('should return a number', () => {
    expect(convertToNumber('123')).toBe(123);
    expect(convertToNumber('123.45')).toBe(123.45);
    expect(convertToNumber('123,45.67')).toBe(12345.67);
    expect(convertToNumber('($123)')).toBe(-123);
    expect(convertToNumber('123-')).toBe(-123);
    expect(convertToNumber('$123)')).toBe(-123);
    expect(convertToNumber('($123')).toBe(-123);
    expect(convertToNumber('$.0')).toBe(0);
    expect(convertToNumber('$.', true)).toBe(0);
    expect(convertToNumber('$.')).toBe(NaN);
    expect(convertToNumber('')).toBe(0);
    expect(convertToNumber('test 123')).toBe(123);
  });
});
