// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const findAllDate = (json: any): string[] => {
  const datePattern = [
    /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{4}\b/g, // "Oct 2024", "January 2024"
    // eslint-disable-next-line no-useless-escape
    /\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b/g, // "1/1/2024", "1/1/24", "01/01/2024", "01-01-24", "01-01-2024"
    // eslint-disable-next-line no-useless-escape
    /\b\d{2,4}[-/]\d{1,2}[-/]\d{1,2}\b/g, // "2024/1/1", "24/1/1", "2024/01/01", "24-01-01", "2024-01-01"
    /\b\d{1,2}-[A-Z]{3}-\d{2,4}\b/g, // "11-NOV-24", "15-J<PERSON>-2024"
    /\b\d{1,2}\s(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*,?\s\d{4}\b/g, // "01 Jan, 2024", "01 January, 2024"
    /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{1,2},\s\d{4}\b/g, // "January 01, 2024"
    /\b\d{1,2}\s(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{4}\b/g, // "01 Jan 2024", "01 January 2024"
    /\b(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s\d{1,2}\s\d{4}\b/g, // "Jan 01 2024", "January 01 2024"
  ];

  const foundDates: string[] = [];

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  json.Lines?.forEach((lineItem: any) => {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    lineItem.LinesArray.forEach((line: any) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      datePattern.forEach((pattern) => {
        const matches = line.Line.matchAll(pattern);
        if (matches) {
          foundDates.push(
            ...Array.from<RegExpMatchArray>(matches).map((match) => match[0])
          );
        }
      });
    });
  });

  return foundDates;
};

export const desc_findAllDate = {
  name: 'libs.tools.findAllDate(json)',
  description:
    'Finds all date values in a JSON object that match common date formats in the text content.',
  arguments: {
    json: '[Object] JSON object containing Lines array with text content to search',
  },
  returns: '[Array<string>] Array of found date strings from the content',
  examples:
    'findAllDate({\n  Lines: [{\n    LinesArray: [{\n      Line: "Document from Oct 2024 and 01/02/2024"\n    }]\n  }]\n})\n// → ["Oct 2024", "01/02/2024"]\n\nfindAllDate({\n  Lines: [{\n    LinesArray: [{\n      Line: "Meeting on 15 Jan 2025 with John"\n    }]\n  }]\n})\n// → ["15 Jan 2025"]\n\nfindAllDate({\n  Lines: [{\n    LinesArray: [{\n      Line: "Due date: January 01 2025"\n    }]\n  }]\n})\n// → ["January 01 2025"]\n',
};

export default findAllDate;
