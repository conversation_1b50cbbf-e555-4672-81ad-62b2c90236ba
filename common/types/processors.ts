import { z } from 'zod';

export const ProcessorSchema = z.object({
  id: z.number().optional(),
  str_id: z.string().optional(),
  state: z.string().optional(),
  created_at: z.string().datetime().optional(),
  created_by: z.string().optional(),
  created_proxied_by: z.string().nullable().optional(),
  updated_at: z.string().datetime().optional(),
  updated_by: z.string().nullable().optional(),
  updated_proxied_by: z.string().nullable().optional(),
  owner: z.string().nullable().optional(),

  access: z.string().optional(),
  company_id: z.string().nullable().optional(),
  document_str_id: z.string().nullable().optional(),
  extractionsid: z.number().nullable().optional(),
  inner_name: z.string().nullable().optional(),
  method: z.string().nullable().optional(),
  name: z.string().optional(),
  notes: z.string().nullable().optional(),
  processor: z.string().optional(),
  processor_status: z.string().optional(),
  reviewed_at: z.string().datetime().nullable().optional(),
  reviewed_by: z.string().nullable().optional(),
  reviewer_id: z.number().nullable().optional(),
  reviewer_str_id: z.string().nullable().optional(),
  status: z.string().optional(),
  suggest_for: z.string().nullable().optional(),
  type: z.string().optional(),

  extractions: z.any().nullable().optional(),
  users_processors_created_byTousers: z
    .record(z.any(), z.any())
    .nullable()
    .optional(),
  users_processors_updated_byTousers: z
    .record(z.any(), z.any())
    .nullable()
    .optional(),
  users_processors_reviewed_byTousers: z
    .record(z.any(), z.any())
    .nullable()
    .optional(),

  companies_processors: z.array(z.any()).optional(),
  documents: z.number().optional(),
});

export type Processor = z.infer<typeof ProcessorSchema>;
