export type Company = {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  access: string;
  created_at: string;
  created_by: string;
  created_proxied_by: string;
  updated_at: string | null;
  updated_by: string | null;
  updated_proxied_by: string | null;
  address: string | null;
  alias_list: string[];
  company_id: number | null;
  company_name: string;
  canonical_id: number;
  email: string | null;
  group_id: string | null;
  sync_id: string;
  log: string | null;
  notes: string | null;
  phone: string | null;
  type: string[];
  website: string | null;
  profile_str_id: string | null;
  config: string | null;
  sync_worker: string;
  account_name?: string | null;
};

export type ApiGetResponose = { count: number; data: Company[] };
