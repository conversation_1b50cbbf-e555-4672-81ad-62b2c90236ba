// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export type ApiResponse<T = any> = {
  data: T;
  message?: string;
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export type ApiError<T = any> = {
  data: T;
  message?: string;
  code: string;
};

export enum DataStates {
  ACTIVE = 'active',
  ARCHIVED = 'archived',
  DELETED = 'deleted',
  DUPLICATE = 'duplicate',
  GROUPED = 'grouped',
  ALLOCATED = 'allocated',
}

export type StrIdToValueMap = Record<string, number>;

export type PolicyConfig = {
  advanced_commission_schedules?: boolean;
  allied_payment_rule: {
    mode: string;
    priority?: string[];
  };
  member_count?: {
    count: number;
    period_date: string;
  };
  overrideFields?: string[];
};
