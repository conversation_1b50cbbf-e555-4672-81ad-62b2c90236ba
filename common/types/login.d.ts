type LoginBody = {
  email: string;
  isImpUser?: boolean;
  sso?: boolean;
  role_name?: string;
};

type userAccount = {
  account_id: string;
  role_id: Roles;
  account: {
    str_id: string;
    name: string;
    mode: string;
    comp_grids_enabled: boolean;
    white_label_mode: boolean;
    accounting_transactions_enabled: boolean;
    logo_url: string;
    uid: string;
  };
};

type AuthResponse = {
  tos_accepted_at?: string | null;
  userOnboardingNeeded: boolean;
  userEmail: string;
  accountOnboardingNeeded: boolean;
  userOverallState: States;
  userAccounts: userAccount[];
  userLandingPage: string;
  fintaryAdmin?: {
    email: string;
    subType?: string;
    type?: string;
  };
};
