import BigNumber from 'bignumber.js';
import { UserStates } from 'common/constants/user-states.enum';
import currency from 'currency.js';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import prettyMilliseconds from 'pretty-ms';
import { isEmpty, mapValues } from 'lodash-es';

import { AccountIds, CALC_METHODS } from './constants';
import { formatSmallPercentageWithPrecision } from './formatters/currencyFormatter';
import { CompGridRateFields } from './globalTypes';
import { isNill } from './helpers';
import { toSentenceCase } from './helpers/string';
import { DataStates } from './types/common';
import { DATE_TIME_FORMATS } from './constants/date-time';

dayjs.extend(utc);
dayjs.extend(timezone);

interface CurrencyOptions {
  withoutSymbol?: boolean;
}

interface PercentageOptions {
  isPercentage?: boolean;
  addPrecisionForRateLowerThanTen?: boolean;
  withoutSymbol?: boolean;
}
export type Contact = {
  first_name: string;
  last_name: string;
  email?: string;
  str_id?: string;
  id?: number;
};

const isTransGlobalAccount = (accountID: string) =>
  accountID === AccountIds.TRANSGLOBAL;

// biome-ignore lint/complexity/noStaticOnlyClass: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
class Formatter {
  static calcMethod = (s: keyof typeof CALC_METHODS) => CALC_METHODS[s] ?? s;
  static asPercentage = (s) => {
    if (typeof s === 'number') {
      if (Number.isFinite(s)) {
        return `${(s * 100).toFixed(2)}%`;
      } else {
        return 'n/a';
      }
    }
    return s;
  };

  static number = (s) => {
    return s === undefined || s === '' ? '' : parseFloat(s).toLocaleString();
  };

  static currency = (s, options?: CurrencyOptions) => {
    if (s === undefined || s === '') return '';
    let s1 = s;
    if (typeof s1 === 'string') {
      s1 = s.trim();
    }
    if (typeof s1 === 'string') {
      s1 = s.replaceAll(',', '');
      s1 = s1.replaceAll('$', '');
    }
    if (typeof s1 === 'string' && s1.split('\n').length > 1) {
      [s1] = s.split('\n');
    }
    if (typeof s1 === 'string' && s1.split(' ').length > 1) {
      [s1] = s1.split(' ');
    }
    if (typeof s1 === 'string' && s1.endsWith('-')) {
      s1 = `-${s1.slice(0, -1)}`;
    }
    if (typeof s1 === 'string' && s1.startsWith('(') && s1.endsWith(')')) {
      s1 = `-${s1.slice(1, -1)}`;
    }
    s1 = parseFloat(s1);
    if (Number.isNaN(s1)) {
      s1 = '';
    } else {
      // Using currency for now, but should be replaced with BigNumber
      s1 = currency(s1, { symbol: options?.withoutSymbol ? '' : '$' }).format();

      // const bigNumber = new BigNumber(s1);
      // s1 = bigNumber.toFormat(2, BigNumber.ROUND_HALF_UP);

      // Example:
      // -0.375: Original value
      // -0.38: Rounded using BigNumber with ROUND_HALF_UP
      // -$0.37: Rounded and formatted using currency.js
    }
    return s1;
  };

  /**
   * Formats a given value as a percentage string with customizable precision.
   *
   * IMPORTANT: update the documentation for new changes at docs/formatter/README.md
   *
   * @param args.isPercentage - If true, treats `val` as already being a percentage (e.g., 15 for 15%). If false or omitted, treats `val` as a ratio (e.g., 0.15 for 15%).
   * @param args.addPrecisionForRateLowerThanTen - If true and the absolute percentage is less than 10, applies additional precision with max of 3 decimal places.
   * @param args.withoutSymbol - If true, returns the value without percentage symbol.
   *
   * @returns The formatted percentage string, or an empty string if the input is invalid or nil.
   */
  static percentage = (val: string | number, args?: PercentageOptions) => {
    if (isNill(val)) {
      return '';
    }

    if (typeof val === 'string') {
      val = parseFloat(val);
      // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (isNaN(val)) {
        return '';
      }
    }

    const percentageValue = args?.isPercentage
      ? new BigNumber(val)
      : new BigNumber(val).multipliedBy(100);

    let precision = 0;

    const absPercentage = percentageValue.abs();

    if (args?.addPrecisionForRateLowerThanTen && absPercentage.isLessThan(10)) {
      // For small rates (< 10%) and option enabled, use up to 3 decimal places
      precision = 3;
    } else if (absPercentage.isLessThan(1)) {
      // For very small rates (< 1%), use 2 decimal places
      precision = 2;
    } else if (!absPercentage.isInteger() && absPercentage.isLessThan(100)) {
      // For non-integer rates less than 100%, use 2 decimal places
      precision = 2;
    }

    // If value is exactly zero and no extra precision requested, just show 0
    const shouldShowRawZero =
      percentageValue.isZero() && !args?.addPrecisionForRateLowerThanTen;

    const formattedValue = shouldShowRawZero
      ? 0
      : formatSmallPercentageWithPrecision(percentageValue, { precision });

    return `${formattedValue}${args?.withoutSymbol ? '' : '%'}`;
  };

  static numberRange = (
    start: string | null | undefined,
    end: string | null | undefined,
    opts: { anyValue: string } = { anyValue: 'Any' }
  ) => {
    if (![null, undefined, ''].includes(start) && start === end) {
      return start;
    } else if (start && end) {
      return `${start} - ${end}`;
    } else if (start) {
      return `${start}+`;
    } else if (end) {
      return `< ${end}`;
    } else {
      return opts.anyValue;
    }
  };

  static duration = (
    val,
    opts: { truncate: string } = { truncate: undefined }
  ) => {
    let res = prettyMilliseconds(+val, { secondsDecimalDigits: 0 });
    if (opts?.truncate === 'seconds') {
      res = res.replace(/ \d+s/, '');
    }
    return res;
  };

  static dateRange = (
    start: Date | null | undefined,
    end: Date | null | undefined,
    useUTC = true
  ) => {
    const _dayjs = useUTC ? dayjs.utc : dayjs;
    const _startStr = _dayjs(start).format('MM/DD/YYYY');
    const _endStr = _dayjs(end).format('MM/DD/YYYY');
    const startStr = _startStr === 'Invalid Date' ? null : _startStr;
    const endStr = _endStr === 'Invalid Date' ? null : _endStr;
    if (!startStr && !endStr) {
      return 'Full date range';
    } else if (startStr && startStr === endStr) {
      return startStr;
    } else if (startStr && endStr) {
      return `${startStr}-${endStr}`;
    } else if (startStr) {
      return `${startStr} or later`;
    } else if (endStr) {
      return `${endStr} or earlier`;
    } else {
      return '';
    }
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  static compGridCriterion = (val: any) =>
    val
      ? `${val.comp_grid_product?.type} • ${
          val.comp_grid_product?.name
        } • Years (${Formatter.numberRange(
          val.policy_year_start,
          val.policy_year_end
        )}) • Ages (${Formatter.numberRange(
          val.issue_age_start,
          val.issue_age_end
        )})${val.compensation_type ? ` • ${val.compensation_type}` : ''}${
          val.transaction_type ? ` • ${val.transaction_type}` : ''
        }`
      : '';

  static date = (val: Date | string, useUTC = true, format = 'MM/DD/YYYY') => {
    const _dayjs = useUTC ? dayjs.utc : dayjs;
    return _dayjs(val).format(format);
  };

  static dateTime = (date) => new Date(date).toLocaleString();

  static contact = (
    contact: Contact | null | undefined,
    opts: {
      account_id?: string | null;
      incl_email?: boolean;
      last_first?: boolean;
    } = {
      incl_email: false,
      last_first: false,
    }
  ) => {
    const isTransGlobal = opts?.account_id === AccountIds.TRANSGLOBAL;
    const _lastFirst = (isTransGlobal || opts?.last_first) ?? false;

    if (!contact) {
      return '';
    }
    const arr = [];
    const nameArr = [];
    if (contact.first_name) nameArr.push(contact.first_name.trim());
    if (contact.last_name) nameArr.push(contact.last_name.trim());
    if (_lastFirst) {
      nameArr.reverse();
      arr.push(nameArr.join(', '));
    } else {
      arr.push(nameArr.join(' '));
    }
    if (opts?.incl_email && contact.email)
      arr.push(`(${contact.email.trim()})`);
    const str = arr.join(' ');
    return str;
  };

  static compGridRatesFields = (val: string) => {
    if (val === CompGridRateFields.CARRIER_RATE) {
      return 'Carrier Rate';
    } else if (val === CompGridRateFields.HOUSE_RATE) {
      return 'House Rate';
    } else if (val === CompGridRateFields.TOTAL_RATE) {
      return 'Total Rate';
    } else {
      return 'All';
    }
  };

  static agentReportLog = (args: {
    type: 'AGENT_COMPENSATION_LOG' | 'AGENT_COMMISSION_LOG';
    contentMapByIdOrStrId: {
      // Compensation log has id defied as key of the map
      // Comission log has strId defined as key of the map
      [IdOrStrId: string]: Record<string, unknown>[];
      // PS. We shouldn’t have these dynamic IDs, it should be one consistent, But it’s higher risk to change that at this point.
    };
    statementDataItem: {
      agentCommissionContacts: Record<string, string>;
      compCalcContactMapById: Record<string, { name: string; type: string }>;
    };
    timezone?: string;
  }) => {
    const { contentMapByIdOrStrId: val } = args;
    const { statementDataItem: item } = args;

    if (!val || typeof val !== 'object' || isEmpty(val)) return '';

    const logRows: string[] = [];

    const contacts =
      args.type === 'AGENT_COMMISSION_LOG'
        ? item.agentCommissionContacts
        : mapValues(item.compCalcContactMapById, (v) => v.name);

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.entries(val).forEach(([IdOrStrId, agentLogs]: [string, any[]]) => {
      if (IdOrStrId === 'total' || !Array.isArray(agentLogs)) {
        return;
      }

      const contactName = contacts[IdOrStrId];
      logRows.push(contactName || 'no contact name');

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      agentLogs.forEach((logs) => {
        const currentRow: string[] = [];
        const logFields = Formatter.getCommissionCalcLogFields({
          timezone: args.timezone,
        });

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        logFields.forEach((fieldConfig) => {
          if (fieldConfig.newLine && currentRow.length) {
            logRows.push(currentRow.join(', '));
            currentRow.length = 0;
          }

          const value = logs[fieldConfig.field];
          if (fieldConfig.skipEmpty && (value === null || value === undefined))
            return;

          if (fieldConfig.formatter) {
            currentRow.push(
              `${fieldConfig.label}: ${fieldConfig.formatter(value)}`
            );
          } else if (fieldConfig.render) {
            const comp = fieldConfig.render(logs);
            if (comp) {
              currentRow.push(
                `${fieldConfig.label}: ${fieldConfig.render(logs)}`
              );
            }
          } else {
            currentRow.push(`${fieldConfig.label}: ${value}`);
          }
        });

        if (currentRow.length) {
          logRows.push(currentRow.join(', '));
        }
      });
    });
    return logRows.join('\n');
  };

  static getCommissionCalcLogFields = ({
    statement_id,
    timezone,
  }: {
    statement_id?: string | null;
    timezone?: string;
  } = {}) => [
    { field: 'alerts', label: '⚠️', skipEmpty: true, newLine: true },
    {
      field: 'profile_name',
      label: 'Profile',
      skipEmpty: true,
      newLine: true,
    },
    {
      field: 'calcMethod',
      label: 'Method',
      formatter: Formatter.calcMethod,
      skipEmpty: true,
    },
    {
      field: 'commissionAmount',
      label: 'Amount',
      formatter: Formatter.currency,
      skipEmpty: true,
      newLine: true,
    },
    {
      field: 'payerCommissionRate',
      label: 'Payer commission rate',
      formatter: Formatter.percentage,
      skipEmpty: true,
    },
    {
      field: 'commissionRate',
      label: 'Effective rate',
      formatter: Formatter.percentage,
      skipEmpty: true,
    },
    {
      field: 'payoutRate',
      label: 'Payout rate',
      formatter: (val) => Formatter.percentage(val / 100),
      skipEmpty: true,
    },
    {
      field: 'calcBasis',
      label: 'Basis',
      formatter: Formatter.currency,
      collapsed: true,
      skipEmpty: true,
    },
    {
      field: 'criterionStr',
      label: 'Criteria',
      collapsed: true,
      newLine: true,
      skipEmpty: true,
    },
    {
      field: 'payerRates',
      label: 'Payer grid rates',
      collapsed: true,
      newLine: true,
      formatter: (val) => {
        return val.carrier_rate || val.house_rate || val.rate
          ? `Carrier: ${Formatter.percentage(
              val.carrier_rate / 100
            )} • House: ${Formatter.percentage(
              val.house_rate / 100
            )} • Total: ${Formatter.percentage(val.rate / 100)}`
          : null;
      },
      skipEmpty: true,
      nowrap: true,
    },
    {
      field: 'payeeRates',
      label: 'Payee grid rates',
      collapsed: true,
      newLine: true,
      formatter: (val) => {
        return val.carrier_rate || val.house_rate || val.rate
          ? `Carrier: ${Formatter.percentage(
              val.carrier_rate / 100
            )} • House: ${Formatter.percentage(
              val.house_rate / 100
            )} • Total: ${Formatter.percentage(val.rate / 100)}`
          : null;
      },
      skipEmpty: true,
      nowrap: true,
    },
    {
      field: 'agentSplit',
      label: 'Writing agent split',
      formatter: Formatter.percentage,
      skipEmpty: true,
      collapsed: true,
      newLine: true,
    },
    {
      field: 'splitPercentage',
      label: 'Commission split percentage',
      formatter: Formatter.percentage,
      skipEmpty: true,
      collapsed: true,
      newLine: true,
    },
    {
      field: 'hierarchySplit',
      label: 'Hierarchy split',
      formatter: Formatter.percentage,
      skipEmpty: true,
      collapsed: true,
    },
    {
      field: 'multiplier',
      label: 'Multiplier',
      formatter: Formatter.percentage,
      skipEmpty: true,
      collapsed: true,
      newLine: true,
    },
    {
      field: 'calcMethodMultiplier',
      label: 'Calc method multiplier',
      formatter: Formatter.percentage,
      skipEmpty: true,
      collapsed: true,
      newLine: true,
    },
    {
      field: 'agentUplines',
      label: 'Agent hierarchy',
      formatter: (val) =>
        Array.isArray(val) && val.length > 0 ? val.join(' / ') : 'n/a',
      skipEmpty: true,
      collapsed: true,
      newLine: true,
    },
    {
      field: 'product_type',
      label: 'Product type',
      collapsed: true,
      skipEmpty: true,
    },
    {
      field: 'referral_rates',
      label: 'Referral rates',
      collapsed: true,
      skipEmpty: true,
    },
    {
      field: 'formula',
      label: 'Formula',
      collapsed: true,
      render: (row) => {
        return row?.calResult?.formula;
      },
      newLine: true,
    },
    {
      field: 'formulaInterpreted',
      label: 'Formula interpreted',
      collapsed: true,
      render: (row) => {
        return row?.calResult?.formulaInterpreted;
      },
      newLine: true,
    },
    {
      field: 'notes',
      label: '📗',
      newLine: true,
      skipEmpty: true,
    },
    {
      field: 'commissionGroupUsed',
      label: 'Commission group used',
      newLine: true,
      skipEmpty: true,
      linkUrl: () =>
        statement_id
          ? `${window.location.origin}/commissions?id=${statement_id}&incl_linked=true`
          : null,
    },
    {
      field: 'scope',
      label: 'Scope',
      newLine: true,
      skipEmpty: true,
    },
    {
      field: 'policyCommissionsUsed',
      label: 'Policy commissions used',
      formatter: Formatter.currency,
      newLine: true,
      skipEmpty: true,
    },
    {
      field: 'calculatedAt',
      label: 'Calculated at',
      collapsed: true,
      newLine: true,
      skipEmpty: true,
      formatter: (val) =>
        Formatter.dateTimeWithTimezone(
          val,
          timezone,
          DATE_TIME_FORMATS.US_DATE_TIME_FORMAT_WITH_SECONDS
        ),
    },
  ];

  static userInviteStatus(val: UserStates): string {
    const statusMap: Record<UserStates, string> = {
      [UserStates.ACTIVE]: 'Active',
      [UserStates.DELETED]: 'Deleted',
      [UserStates.INACTIVE]: 'Inactive',
      [UserStates.INVITED]: 'Invited',
      [UserStates.PENDING]: 'Pending',
    };
    return statusMap[val] ?? val;
  }

  static label = (val: string) => {
    return toSentenceCase(val);
  };

  static displayName = (val: string | { name?: string; id?: string }) => {
    if (typeof val === 'string') {
      return val;
    }
    if (val && typeof val === 'object' && val !== null && 'name' in val) {
      return (val as { name: string }).name;
    }
    return String(val);
  };

  static dateTimeWithTimezone = (
    date?: Date | string,
    timezone?: string,
    format = 'MM/DD/YYYY HH:mm:ss'
  ) => {
    if (!dayjs(date).isValid()) return '';
    const dateToParse = typeof date === 'string' ? new Date(date) : date;
    let dateDayjs = dayjs(dateToParse);

    if (timezone) {
      dateDayjs = dateDayjs.tz(timezone);
    }
    return dateDayjs.format(format);
  };

  static policyNumber = (val: string, opts: { account_id?: string }) => {
    if (isTransGlobalAccount(opts?.account_id)) {
      return val.toUpperCase();
    }
    return val;
  };

  static percentageWithPrecision = (val: string | number) => {
    if (val === Number.POSITIVE_INFINITY || val === Number.NEGATIVE_INFINITY) {
      return '';
    }
    if (typeof val === 'string' || isNill(val)) {
      return val;
    }
    const percentageVal = val * 100;
    return Math.abs(percentageVal) < 100
      ? `${percentageVal.toPrecision(3)}%`
      : `${Math.round(percentageVal)}%`;
  };

  static isGrouped = (val: string | undefined | null) => {
    return val === DataStates.GROUPED ? 'Yes' : 'No';
  };
}

export default Formatter;
