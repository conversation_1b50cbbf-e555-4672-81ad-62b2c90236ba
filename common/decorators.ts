import {
  registerDecorator,
  type ValidationDecoratorOptions,
} from 'class-validator';

export const IsStringOrArray = (
  validationOptions?: ValidationDecoratorOptions['options']
) => {
  return (object: object, propertyName: string) => {
    registerDecorator({
      name: 'isStringOrArray',
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        validate: (value: any) => {
          if (typeof value === 'string') {
            return true;
          }

          if (Array.isArray(value)) {
            return value.every((item) => typeof item === 'string');
          }

          return false;
        },

        defaultMessage: () => 'Value must be a string or an array of strings',
      },
    });
  };
};
