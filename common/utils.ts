// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
// biome-ignore lint/complexity/noBannedTypes: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const arrayToMap = (arr: any[], key: string | Function) =>
  (arr ?? []).reduce((acc, e) => {
    acc[typeof key === 'string' ? e[key] : key(e)] = e;
    return acc;
  }, {});

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const enumToSelectOptions = (enumObj: any) =>
  Object.entries(enumObj).map(([k, v]) => ({
    id: k,
    label: v,
  }));

export const splitCustomerName = (name: string) => {
  if (!name) return { first_name: '', last_name: '' };

  if (name.includes(',')) {
    const index = name.indexOf(',');
    const first_name = name.slice(0, index);
    const last_name = name.slice(index + 1);
    return { first_name, last_name };
  }

  const [first_name, ...last_name] = name.split(/\s+/g);

  return {
    first_name,
    last_name: last_name.join(' '),
  };
};

export const mergeObjects = (
  obj1: object | Array<object>,
  obj2: object | Array<object>,
  asArray: boolean = false
) => {
  const isArray = Array.isArray(obj1);
  const validObjects = [obj1, obj2].filter(
    (obj) => obj && typeof obj === 'object'
  );

  if (asArray || isArray) {
    return isArray
      ? [...obj1, ...(Array.isArray(obj2) ? obj2 : [obj2].filter(Boolean))]
      : validObjects.length
        ? validObjects
        : null;
  }

  // biome-ignore lint/performance/noAccumulatingSpread: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  return validObjects.reduce((acc, obj) => ({ ...acc, ...obj }), {});
};

export const buildPrismaInclude = (
  paths: string[],
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  base: Record<string, any> = {}
) => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const includeObject: Record<string, any> = { ...base };
  const segmentsSet = new Set<string>();

  for (const path of paths) {
    const segments = path.split(',').filter(Boolean);

    for (const segment of segments) {
      segmentsSet.add(segment);
    }
  }

  for (const segment of segmentsSet) {
    const pathParts = segment.split('.').filter(Boolean);
    let current = includeObject;

    for (let i = 0; i < pathParts.length; i++) {
      const part = pathParts[i];

      if (i === pathParts.length - 1) {
        if (current.include) {
          current.include[part] = true;
        } else {
          current[part] = true;
        }
      } else {
        if (current.include) {
          current.include[part] = current.include[part] || { include: {} };
          current = current.include[part];
        } else {
          current[part] = current[part] || { include: {} };
          current = current[part];
        }
      }
    }
  }

  return includeObject;
};
