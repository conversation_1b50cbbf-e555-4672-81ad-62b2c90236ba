import { describe, it, expect } from 'vitest';

import { safeJsonParse } from './parse';

describe('safeJsonParse', () => {
  it('should parse a valid JSON string', () => {
    const jsonString = '{"key":"value"}';

    const result = safeJsonParse<{ key: string }>(jsonString);

    expect(result).toEqual({ key: 'value' });
  });

  it('should return default value if JSON string is invalid', () => {
    const invalidJsonString = '{"key":"value"';

    const defaultValue = { key: 'default' };

    const result = safeJsonParse<{ key: string }>(invalidJsonString, {
      defaultValue,
    });

    expect(result).toEqual(defaultValue);
  });

  it('should return null if JSON string is invalid and no default value is provided', () => {
    const invalidJsonString = '{"key":"value"';
    const result = safeJsonParse<{ key: string }>(invalidJsonString);
    expect(result).toBeNull();
  });

  it('should return default value if input is not a string', () => {
    const nonStringInput = 123 as unknown as string;

    const defaultValue = { key: 'default' };

    const result = safeJsonParse<{ key: string }>(nonStringInput, {
      defaultValue,
    });

    expect(result).toEqual(defaultValue);
  });

  it('should return null if input is not a string and no default value is provided', () => {
    const nonStringInput = 123 as unknown as string;

    const result = safeJsonParse<{ key: string }>(nonStringInput);

    expect(result).toBeNull();
  });

  it('should return null if input is an empty string and no default value is provided', () => {
    const emptyString = '';

    const result = safeJsonParse<{ key: string }>(emptyString);

    expect(result).toBeNull();
  });

  it('should return default value if input is an empty string', () => {
    const emptyString = '';

    const defaultValue = { key: 'default' };

    const result = safeJsonParse<{ key: string }>(emptyString, {
      defaultValue,
    });

    expect(result).toEqual(defaultValue);
  });
});
