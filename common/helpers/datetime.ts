import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);

export { dayjs };

export const isValidDateRange = (dateRange: {
  start?: dayjs.ConfigType;
  end?: dayjs.ConfigType;
}) => {
  const { start, end } = dateRange;

  if (start && !dayjs(start).isValid()) {
    return false;
  }

  if (end && !dayjs(end).isValid()) {
    return false;
  }

  if (start && end) {
    return dayjs(end).isSameOrAfter(dayjs(start));
  }

  return true;
};
