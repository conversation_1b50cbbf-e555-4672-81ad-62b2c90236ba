import type { DynamicSelectConfig } from 'common/field-config/shared/types/field';

export const getDynamicConfigWithData = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data: any,
  dynamicSelectsConfig?: DynamicSelectConfig[],
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  totals?: { [key: string]: any }
) => {
  if (data?.length && dynamicSelectsConfig) {
    const _dynamicSelectsConfig = [...dynamicSelectsConfig];

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    _dynamicSelectsConfig.forEach((config) => {
      const { collectDataFields } = config;

      const collectedValues = new Set<string | number>();

      // biome-ignore lint/suspicious/noForEach/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      data.forEach((item: Record<string, any>) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        collectDataFields.forEach((field) => {
          if (!item[field]) return;

          // If field value is complex, need extractor function
          if (config.dataExtractors?.[field]) {
            const values = config.dataExtractors[field](item, totals);
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            values.forEach((value) => {
              collectedValues.add(value);
            });
          }

          // Otherwise, handle simple field value - string, number, object, array
          if (Array.isArray(item[field])) {
            // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            item[field].forEach((value) => {
              collectedValues.add(value);
            });
          } else {
            switch (typeof item[field]) {
              case 'string':
              case 'number':
                collectedValues.add(item[field]);
                break;
              case 'object':
                // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                Object.keys(item[field]).forEach((key) => {
                  collectedValues.add(key);
                });
            }
          }
        });
      });

      // Remove empty values and trim strings
      const trimmedValues = [...collectedValues]
        .map((val) => {
          if (typeof val === 'string') {
            return val.trim();
          }
          return val;
        })
        .filter((val) => val !== '' && val !== null && val !== undefined);

      // Remove duplicates
      config.queryParamValue = [...new Set(trimmedValues)];
    });

    return _dynamicSelectsConfig;
  }

  return [];
};

export const convertDynamicSelectToMap = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  dynamicSelectsData: { [key: string]: any }[],
  dynamicSelectsConfig: DynamicSelectConfig[]
) => {
  return dynamicSelectsConfig.reduce((acc, curr) => {
    const item = dynamicSelectsData.find((item) =>
      Object.entries(item).some(([key]) => curr.table === key)
    );

    const dataArray = item?.[curr.table]?.data || item?.[curr.table];

    if (!dataArray?.length) return acc;

    const map = new Map<string | number, unknown>();
    // biome-ignore lint/complexity/noForEach/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataArray.forEach((val: Record<string, any>) => {
      if (Array.isArray(curr.mapKey)) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        curr.mapKey.forEach((key) => {
          map.set(val[key], val);
        });
      } else {
        map.set(val[curr.mapKey ?? curr.queryParamName], val);
      }
    });

    // @ts-expect-error
    acc[curr.table] = map;
    return acc;
  }, {});
};
