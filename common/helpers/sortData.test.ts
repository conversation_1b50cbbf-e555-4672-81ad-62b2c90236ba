import { SortOrder } from 'common/globalTypes';
import { describe, it, expect } from 'vitest';

import {
  sortAndPaginate,
  type SortOptions,
  type PaginationOptions,
  type SortAndPaginateResult,
} from './sortData';

describe('sortAndPaginate', () => {
  // Test data
  const testData = [
    {
      id: 3,
      name: '<PERSON>',
      age: 25,
      score: 85.5,
      active: true,
      createdAt: new Date('2023-01-15'),
    },
    {
      id: 1,
      name: '<PERSON>',
      age: 30,
      score: 92.0,
      active: false,
      createdAt: new Date('2023-01-10'),
    },
    {
      id: 2,
      name: '<PERSON>',
      age: 28,
      score: 78.5,
      active: true,
      createdAt: new Date('2023-01-20'),
    },
    {
      id: 4,
      name: '<PERSON>',
      age: 22,
      score: 95.0,
      active: true,
      createdAt: new Date('2023-01-05'),
    },
    {
      id: 5,
      name: '<PERSON>',
      age: 35,
      score: 88.0,
      active: false,
      createdAt: new Date('2023-01-25'),
    },
  ];

  const nestedData = [
    { id: 1, user: { name: '<PERSON>', profile: { age: 25 } } },
    { id: 2, user: { name: '<PERSON>', profile: { age: 30 } } },
    { id: 3, user: { name: '<PERSON>', profile: { age: 28 } } },
  ];

  const dataWithNulls = [
    { id: 1, name: 'Alice', value: 10 },
    { id: 2, name: null, value: 20 },
    { id: 3, name: 'Bob', value: null },
    { id: 4, name: 'Charlie', value: 15 },
  ];

  describe('Basic functionality', () => {
    it('should return original data when no options provided', () => {
      const result = sortAndPaginate(testData);

      expect(result.data).toEqual(testData);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(5);
    });

    it('should return original data when options are undefined', () => {
      const result = sortAndPaginate(testData, undefined, undefined);

      expect(result.data).toEqual(testData);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(5);
    });
  });

  describe('Sorting functionality', () => {
    describe('String sorting', () => {
      it('should sort strings in ascending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'name',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        expect(result.data.map((item) => item.name)).toEqual([
          'Alice',
          'Bob',
          'Charlie',
          'David',
          'Eve',
        ]);
      });

      it('should sort strings in descending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'name',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        expect(result.data.map((item) => item.name)).toEqual([
          'Eve',
          'David',
          'Charlie',
          'Bob',
          'Alice',
        ]);
      });
    });

    describe('Number sorting', () => {
      it('should sort numbers in ascending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'age',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        expect(result.data.map((item) => item.age)).toEqual([
          22, 25, 28, 30, 35,
        ]);
      });

      it('should sort numbers in descending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'age',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        expect(result.data.map((item) => item.age)).toEqual([
          35, 30, 28, 25, 22,
        ]);
      });

      it('should sort decimal numbers correctly', () => {
        const sortOptions: SortOptions = {
          orderBy: 'score',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        expect(result.data.map((item) => item.score)).toEqual([
          78.5, 85.5, 88.0, 92.0, 95.0,
        ]);
      });
    });

    describe('Date sorting', () => {
      it('should sort dates in ascending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'createdAt',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        expect(result.data.map((item) => item.createdAt.getTime())).toEqual([
          new Date('2023-01-05').getTime(),
          new Date('2023-01-10').getTime(),
          new Date('2023-01-15').getTime(),
          new Date('2023-01-20').getTime(),
          new Date('2023-01-25').getTime(),
        ]);
      });

      it('should sort dates in descending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'createdAt',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        expect(result.data.map((item) => item.createdAt.getTime())).toEqual([
          new Date('2023-01-25').getTime(),
          new Date('2023-01-20').getTime(),
          new Date('2023-01-15').getTime(),
          new Date('2023-01-10').getTime(),
          new Date('2023-01-05').getTime(),
        ]);
      });
    });

    describe('Boolean sorting', () => {
      it('should sort booleans in ascending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'active',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        // False values should come before true values in ascending order
        expect(result.data.map((item) => item.active)).toEqual([
          false,
          false,
          true,
          true,
          true,
        ]);
      });

      it('should sort booleans in descending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'active',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(testData, sortOptions);

        // True values should come before false values in descending order
        expect(result.data.map((item) => item.active)).toEqual([
          true,
          true,
          true,
          false,
          false,
        ]);
      });

      it('should sort boolean-only data correctly', () => {
        const booleanData = [
          { id: 1, flag: true },
          { id: 2, flag: false },
          { id: 3, flag: true },
          { id: 4, flag: false },
          { id: 5, flag: true },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(booleanData, sortOptions);

        expect(result.data.map((item) => item.flag)).toEqual([
          false,
          false,
          true,
          true,
          true,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([2, 4, 1, 3, 5]);
      });

      it('should sort boolean-only data in descending order', () => {
        const booleanData = [
          { id: 1, flag: true },
          { id: 2, flag: false },
          { id: 3, flag: true },
          { id: 4, flag: false },
          { id: 5, flag: true },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(booleanData, sortOptions);

        expect(result.data.map((item) => item.flag)).toEqual([
          true,
          true,
          true,
          false,
          false,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([1, 3, 5, 2, 4]);
      });

      it('should handle nested boolean properties', () => {
        const nestedBooleanData = [
          { id: 1, settings: { enabled: true, verified: false } },
          { id: 2, settings: { enabled: false, verified: true } },
          { id: 3, settings: { enabled: true, verified: true } },
          { id: 4, settings: { enabled: false, verified: false } },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'settings.enabled',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(nestedBooleanData, sortOptions);

        expect(result.data.map((item) => item.settings.enabled)).toEqual([
          false,
          false,
          true,
          true,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([2, 4, 1, 3]);
      });

      it('should handle deeply nested boolean properties', () => {
        const deepNestedData = [
          { id: 1, config: { user: { active: true, premium: false } } },
          { id: 2, config: { user: { active: false, premium: true } } },
          { id: 3, config: { user: { active: true, premium: true } } },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'config.user.active',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(deepNestedData, sortOptions);

        expect(result.data.map((item) => item.config.user.active)).toEqual([
          true,
          true,
          false,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([1, 3, 2]);
      });

      it('should handle boolean values with null/undefined in descending order', () => {
        const booleanWithNulls = [
          { id: 1, flag: true },
          { id: 2, flag: null },
          { id: 3, flag: false },
          { id: 4, flag: undefined },
          { id: 5, flag: true },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(booleanWithNulls, sortOptions);

        // Null/undefined should come last in descending order
        expect(result.data.map((item) => item.flag)).toEqual([
          true,
          true,
          false,
          null,
          undefined,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([1, 5, 3, 2, 4]);
      });

      it('should handle mixed boolean and other types gracefully', () => {
        const mixedData = [
          { id: 1, value: true },
          { id: 2, value: 'string' },
          { id: 3, value: false },
          { id: 4, value: 123 },
          { id: 5, value: true },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'value',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(mixedData, sortOptions);

        // Should not throw error and handle mixed types
        expect(result.data).toHaveLength(5);
        expect(result.totalCount).toBe(5);
      });

      it('should combine boolean sorting with pagination', () => {
        const booleanData = [
          { id: 1, flag: true },
          { id: 2, flag: false },
          { id: 3, flag: true },
          { id: 4, flag: false },
          { id: 5, flag: true },
          { id: 6, flag: false },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.ASC,
        };
        const paginationOptions: PaginationOptions = { page: 0, limit: 3 };
        const result = sortAndPaginate(
          booleanData,
          sortOptions,
          paginationOptions
        );

        // Should be sorted by boolean first, then paginated
        expect(result.data.map((item) => item.flag)).toEqual([
          false,
          false,
          false,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([2, 4, 6]);
        expect(result.totalCount).toBe(6);
        expect(result.page).toBe(0);
        expect(result.limit).toBe(3);
      });

      it('should combine boolean sorting with pagination to second page', () => {
        const booleanData = [
          { id: 1, flag: true },
          { id: 2, flag: false },
          { id: 3, flag: true },
          { id: 4, flag: false },
          { id: 5, flag: true },
          { id: 6, flag: false },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.DESC,
        };
        const paginationOptions: PaginationOptions = { page: 1, limit: 2 };
        const result = sortAndPaginate(
          booleanData,
          sortOptions,
          paginationOptions
        );

        // Should be sorted by boolean descending first, then paginated to second page
        expect(result.data.map((item) => item.flag)).toEqual([true, false]);
        expect(result.data.map((item) => item.id)).toEqual([5, 2]);
        expect(result.totalCount).toBe(6);
        expect(result.page).toBe(1);
        expect(result.limit).toBe(2);
      });

      it('should handle all false values', () => {
        const allFalseData = [
          { id: 1, flag: false },
          { id: 2, flag: false },
          { id: 3, flag: false },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(allFalseData, sortOptions);

        expect(result.data.map((item) => item.flag)).toEqual([
          false,
          false,
          false,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([1, 2, 3]);
      });

      it('should handle all true values', () => {
        const allTrueData = [
          { id: 1, flag: true },
          { id: 2, flag: true },
          { id: 3, flag: true },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(allTrueData, sortOptions);

        expect(result.data.map((item) => item.flag)).toEqual([
          true,
          true,
          true,
        ]);
        expect(result.data.map((item) => item.id)).toEqual([1, 2, 3]);
      });

      it('should handle single boolean value', () => {
        const singleBoolean = [{ id: 1, flag: true }];

        const sortOptions: SortOptions = {
          orderBy: 'flag',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(singleBoolean, sortOptions);

        expect(result.data.map((item) => item.flag)).toEqual([true]);
        expect(result.data.map((item) => item.id)).toEqual([1]);
      });
    });

    describe('Nested property sorting', () => {
      it('should sort by nested properties', () => {
        const sortOptions: SortOptions = {
          orderBy: 'user.name',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(nestedData, sortOptions);

        expect(result.data.map((item) => item.user.name)).toEqual([
          'Alice',
          'Bob',
          'Charlie',
        ]);
      });

      it('should sort by deeply nested properties', () => {
        const sortOptions: SortOptions = {
          orderBy: 'user.profile.age',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(nestedData, sortOptions);

        expect(result.data.map((item) => item.user.profile.age)).toEqual([
          30, 28, 25,
        ]);
      });
    });

    describe('Null and undefined handling', () => {
      it('should handle null values in ascending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'name',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(dataWithNulls, sortOptions);

        // Null values should be placed at the beginning in ascending order
        expect(result.data.map((item) => item.name)).toEqual([
          null,
          'Alice',
          'Bob',
          'Charlie',
        ]);
      });

      it('should handle null values in descending order', () => {
        const sortOptions: SortOptions = {
          orderBy: 'name',
          sort: SortOrder.DESC,
        };
        const result = sortAndPaginate(dataWithNulls, sortOptions);

        // Null values should be placed at the end in descending order
        expect(result.data.map((item) => item.name)).toEqual([
          'Charlie',
          'Bob',
          'Alice',
          null,
        ]);
      });

      it('should handle undefined values', () => {
        const dataWithUndefined = [
          { id: 1, name: 'Alice', value: 10 },
          { id: 2, name: undefined, value: 20 },
          { id: 3, name: 'Bob', value: 15 },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'name',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(dataWithUndefined, sortOptions);

        expect(result.data.map((item) => item.name)).toEqual([
          undefined,
          'Alice',
          'Bob',
        ]);
      });
    });

    describe('Object sorting', () => {
      it('should sort objects by JSON string representation', () => {
        const objectData = [
          { id: 1, config: { type: 'A', value: 10 } },
          { id: 2, config: { type: 'B', value: 5 } },
          { id: 3, config: { type: 'A', value: 5 } },
        ];

        const sortOptions: SortOptions = {
          orderBy: 'config',
          sort: SortOrder.ASC,
        };
        const result = sortAndPaginate(objectData, sortOptions);

        // Should be sorted by JSON string representation
        expect(result.data[0].config).toEqual({ type: 'A', value: 10 });
        expect(result.data[1].config).toEqual({ type: 'A', value: 5 });
        expect(result.data[2].config).toEqual({ type: 'B', value: 5 });
      });
    });
  });

  describe('Pagination functionality', () => {
    it('should return first page with correct limit', () => {
      const paginationOptions: PaginationOptions = { page: 0, limit: 2 };
      const result = sortAndPaginate(testData, undefined, paginationOptions);

      expect(result.data).toHaveLength(2);
      expect(result.data).toEqual([testData[0], testData[1]]);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(2);
    });

    it('should return second page with correct limit', () => {
      const paginationOptions: PaginationOptions = { page: 1, limit: 2 };
      const result = sortAndPaginate(testData, undefined, paginationOptions);

      expect(result.data).toHaveLength(2);
      expect(result.data).toEqual([testData[2], testData[3]]);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(2);
    });

    it('should return last page with remaining items', () => {
      const paginationOptions: PaginationOptions = { page: 2, limit: 2 };
      const result = sortAndPaginate(testData, undefined, paginationOptions);

      expect(result.data).toHaveLength(1);
      expect(result.data).toEqual([testData[4]]);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(2);
      expect(result.limit).toBe(2);
    });

    it('should return empty array for page beyond data length', () => {
      const paginationOptions: PaginationOptions = { page: 10, limit: 2 };
      const result = sortAndPaginate(testData, undefined, paginationOptions);

      expect(result.data).toHaveLength(0);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(10);
      expect(result.limit).toBe(2);
    });

    it('should handle limit larger than data length', () => {
      const paginationOptions: PaginationOptions = { page: 0, limit: 10 };
      const result = sortAndPaginate(testData, undefined, paginationOptions);

      expect(result.data).toHaveLength(5);
      expect(result.data).toEqual(testData);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(10);
    });
  });

  describe('Combined sorting and pagination', () => {
    it('should sort first then paginate', () => {
      const sortOptions: SortOptions = { orderBy: 'name', sort: SortOrder.ASC };
      const paginationOptions: PaginationOptions = { page: 0, limit: 2 };
      const result = sortAndPaginate(testData, sortOptions, paginationOptions);

      // Should be sorted by name first, then paginated
      expect(result.data.map((item) => item.name)).toEqual(['Alice', 'Bob']);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(2);
    });

    it('should sort and paginate to second page', () => {
      const sortOptions: SortOptions = { orderBy: 'age', sort: SortOrder.DESC };
      const paginationOptions: PaginationOptions = { page: 1, limit: 2 };
      const result = sortAndPaginate(testData, sortOptions, paginationOptions);

      // Should be sorted by age descending first, then paginated
      expect(result.data.map((item) => item.age)).toEqual([28, 25]);
      expect(result.totalCount).toBe(5);
      expect(result.page).toBe(1);
      expect(result.limit).toBe(2);
    });
  });

  describe('Edge cases', () => {
    it('should handle empty array', () => {
      const result = sortAndPaginate([]);

      expect(result.data).toEqual([]);
      expect(result.totalCount).toBe(0);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(0);
    });

    it('should handle empty array with pagination', () => {
      const paginationOptions: PaginationOptions = { page: 0, limit: 5 };
      const result = sortAndPaginate([], undefined, paginationOptions);

      expect(result.data).toEqual([]);
      expect(result.totalCount).toBe(0);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(5);
    });

    it('should handle single item array', () => {
      const singleItem = [{ id: 1, name: 'Test' }];
      const result = sortAndPaginate(singleItem);

      expect(result.data).toEqual(singleItem);
      expect(result.totalCount).toBe(1);
      expect(result.page).toBe(0);
      expect(result.limit).toBe(1);
    });

    it('should handle non-existent property path', () => {
      const sortOptions: SortOptions = {
        orderBy: 'nonExistent.property',
        sort: SortOrder.ASC,
      };
      const result = sortAndPaginate(testData, sortOptions);

      // Should return original order since all values are undefined
      expect(result.data).toEqual(testData);
      expect(result.totalCount).toBe(5);
    });

    it('should handle mixed data types gracefully', () => {
      const mixedData = [
        { id: 1, value: 'string' },
        { id: 2, value: 123 },
        { id: 3, value: new Date() },
        { id: 4, value: { nested: 'object' } },
      ];

      const sortOptions: SortOptions = {
        orderBy: 'value',
        sort: SortOrder.ASC,
      };
      const result = sortAndPaginate(mixedData, sortOptions);

      // Should not throw error and return some order
      expect(result.data).toHaveLength(4);
      expect(result.totalCount).toBe(4);
    });
  });

  describe('Type safety', () => {
    it('should maintain type safety with generic types', () => {
      interface TestType {
        id: number;
        name: string;
        value: number;
      }

      const typedData: TestType[] = [
        { id: 1, name: 'A', value: 10 },
        { id: 2, name: 'B', value: 20 },
      ];

      const result: SortAndPaginateResult<TestType> =
        sortAndPaginate(typedData);

      expect(result.data).toEqual(typedData);
      expect(result.data[0].id).toBe(1);
      expect(result.data[0].name).toBe('A');
      expect(result.data[0].value).toBe(10);
    });
  });
});
