import { describe, it, expect } from 'vitest';

import { isValidDateRange } from './datetime';

describe('isValidDateRange', () => {
  describe('valid date range', () => {
    it('returns true for valid start and end dates where end is after start', () => {
      expect(isValidDateRange({ start: '2023-01-01', end: '2023-01-02' })).toBe(
        true
      );
    });

    it('returns true for valid start and end dates where end is same as start', () => {
      expect(isValidDateRange({ start: '2023-01-01', end: '2023-01-01' })).toBe(
        true
      );
    });

    it('returns true if only start date is valid and end is undefined', () => {
      expect(isValidDateRange({ start: '2023-01-01', end: undefined })).toBe(
        true
      );
    });

    it('returns true if only end date is valid and start is undefined', () => {
      expect(isValidDateRange({ start: undefined, end: '2023-01-01' })).toBe(
        true
      );
    });

    it('returns true if both start and end are undefined', () => {
      expect(isValidDateRange({ start: undefined, end: undefined })).toBe(true);
    });
  });

  describe('invalid date range', () => {
    it('returns false if start date is after end date', () => {
      expect(isValidDateRange({ start: '2023-01-02', end: '2023-01-01' })).toBe(
        false
      );
    });

    it('returns false if start date is invalid', () => {
      expect(
        isValidDateRange({ start: 'invalid-date', end: '2023-01-01' })
      ).toBe(false);
    });

    it('returns false if end date is invalid', () => {
      expect(
        isValidDateRange({ start: '2023-01-01', end: 'invalid-date' })
      ).toBe(false);
    });

    it('returns false if start is undefined and end is invalid', () => {
      expect(isValidDateRange({ start: undefined, end: 'invalid-date' })).toBe(
        false
      );
    });

    it('returns false if end is undefined and start is invalid', () => {
      expect(isValidDateRange({ start: 'invalid-date', end: undefined })).toBe(
        false
      );
    });
  });
});
