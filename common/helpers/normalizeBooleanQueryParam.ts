import { isNill } from './isNill';

export const normalizeBooleanQueryParam = (
  value: string | null | undefined
): boolean => {
  if (value === 'true') return true;
  if (value === 'false') return false;
  // biome-ignore lint/complexity/noUselessTernary: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  return isNill(value) ? false : true;
};
