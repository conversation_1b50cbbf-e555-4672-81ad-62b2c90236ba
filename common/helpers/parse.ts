/**
 * Safely parses a JSON string into a TypeScript object of type `T`.
 * If the input is invalid or parsing fails, it returns a default value from args or null.
 *
 * @template T - The expected type of the parsed JSON object.
 * @param args.defaultValue - The value to return if parsing fails or the input is invalid.
 */
const safeJsonParse = <T>(
  string: string,
  args?: { defaultValue?: T }
): T | null => {
  const defaultValue = args?.defaultValue || null;

  try {
    if (!string || typeof string !== 'string') {
      return defaultValue;
    }

    return JSON.parse(string);
  } catch {
    return defaultValue;
  }
};

export { safeJsonParse };
