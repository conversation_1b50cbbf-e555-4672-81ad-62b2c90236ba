import { SortOrder } from 'common/globalTypes';
import get from 'lodash-es/get';

export interface SortOptions {
  orderBy: string;
  sort: SortOrder;
}

export interface PaginationOptions {
  page: number;
  limit: number;
}

export interface SortAndPaginateResult<T> {
  data: T[];
  totalCount: number;
  page: number;
  limit: number;
}

/**
 * @param data - Array of data to sort
 * @param sortOptions - Sorting options
 * @param paginationOptions - Pagination options
 * @returns SortAndPaginateResult with paginated data
 */
export const sortAndPaginate = <T>(
  data: T[],
  sortOptions?: SortOptions,
  paginationOptions?: PaginationOptions
): SortAndPaginateResult<T> => {
  let results = [...data];

  // Apply sorting if provided
  if (sortOptions) {
    results.sort((a, b) => {
      let aValue = get(a, sortOptions.orderBy, null);
      let bValue = get(b, sortOptions.orderBy, null);

      if (
        // String fields to number for sorting
        ['commissionable_premium_amount', 'premium_amount'].includes(
          sortOptions.orderBy
        )
      ) {
        // @ts-expect-error
        aValue = aValue ? Number(aValue) : null;
        // @ts-expect-error
        bValue = bValue ? Number(bValue) : null;
      }

      if (sortOptions.orderBy === 'amount_paid') {
        aValue = get(aValue, 'amount_paid.amount_paid', null);
        bValue = get(bValue, 'amount_paid.amount_paid', null);
      }

      if (aValue === null && bValue === null) return 0;
      if (aValue === null) return sortOptions.sort === SortOrder.ASC ? -1 : 1;
      if (bValue === null) return sortOptions.sort === SortOrder.ASC ? 1 : -1;

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOptions.sort === SortOrder.ASC
          ? // @ts-expect-error
            aValue.localeCompare(bValue)
          : // @ts-expect-error
            bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOptions.sort === SortOrder.ASC
          ? aValue - bValue
          : bValue - aValue;
      }

      // Handle Date objects
      if (aValue instanceof Date && bValue instanceof Date) {
        return sortOptions.sort === SortOrder.ASC
          ? aValue.getTime() - bValue.getTime()
          : bValue.getTime() - aValue.getTime();
      }

      if (typeof aValue === 'object' && typeof bValue === 'object') {
        return sortOptions.sort === SortOrder.ASC
          ? JSON.stringify(aValue).localeCompare(JSON.stringify(bValue))
          : JSON.stringify(bValue).localeCompare(JSON.stringify(aValue));
      }

      if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
        if (aValue === bValue) return 0;
        return sortOptions.sort === SortOrder.ASC
          ? aValue
            ? 1
            : -1
          : aValue
            ? -1
            : 1;
      }

      return 0;
    });
  }

  const totalCount = results.length;

  // Apply pagination if provided
  if (paginationOptions) {
    const { page, limit } = paginationOptions;
    const startIndex = page * limit;
    const endIndex = startIndex + limit;
    results = results.slice(startIndex, endIndex);
  }

  return {
    data: results,
    totalCount,
    page: paginationOptions?.page ?? 0,
    limit: paginationOptions?.limit ?? totalCount,
  };
};

export const DEFAULT_RECONCILIATION_SORT_OPTIONS: SortOptions = {
  orderBy: 'policy_id',
  sort: SortOrder.ASC,
};
