import { z, ZodError } from 'zod';

export const preprocessBoolean = (schema: z.ZodType<boolean>) =>
  z.preprocess((val) => {
    if (typeof val === 'string') {
      return val === 'true';
    }
    return Boolean(val);
  }, schema);

export type ZodIssue = {
  code: string;
  message: string;
  path?: string[];
  options?: string[];
  received?: string;
  unionErrors?: CustomZodError[];
};

export type CustomZodError = {
  issues: ZodIssue[];
  name: string;
};

export type ZodResponse = {
  message?: string;
  error?: CustomZodError;
};

export function isZodError(err: unknown): err is ZodError {
  return Boolean(
    err && (err instanceof ZodError || (err as ZodError).name === 'ZodError')
  );
}

export function parseZodError(errorResponse: ZodResponse): string {
  if (!errorResponse?.error?.issues?.length) {
    return errorResponse?.message || 'Invalid input';
  }

  const issues: ZodIssue[] = [];

  for (const issue of errorResponse.error.issues) {
    if (issue.unionErrors) {
      for (const unionErr of issue.unionErrors) {
        issues.push(...(unionErr.issues || []));
      }
    } else {
      issues.push(issue);
    }
  }

  const messages = issues.map((issue) => {
    const field = issue.path?.length ? issue.path.join('.') : undefined;

    if (issue.code === 'invalid_enum_value' && issue.options) {
      return field
        ? `Invalid value for "${field}". Allowed values: ${issue.options
            .map((o) => `"${o}"`)
            .join(', ')}.`
        : `Invalid value. Allowed values: ${issue.options
            .map((o) => `"${o}"`)
            .join(', ')}.`;
    }

    if (issue.message) {
      return field ? `${field}: ${issue.message}` : issue.message;
    }

    return 'Invalid input';
  });

  return [...new Set(messages)].join('\n');
}
