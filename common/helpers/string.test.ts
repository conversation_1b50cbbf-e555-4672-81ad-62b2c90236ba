import { describe, it, expect } from 'vitest';

import { toSentenceCase } from './string';

describe('toSentenceCase', () => {
  it('Given empty string, should return empty string', () => {
    const result = toSentenceCase('');
    expect(result).toBe('');
  });

  it('Given single lowercase word, should capitalize first letter', () => {
    const result = toSentenceCase('hello');
    expect(result).toBe('Hello');
  });

  it('Given single uppercase word, should convert to sentence case', () => {
    const result = toSentenceCase('HELLO');
    expect(result).toBe('Hello');
  });

  it('Given multiple lowercase words, should capitalize each word', () => {
    const result = toSentenceCase('hello world');
    expect(result).toBe('Hello World');
  });

  it('Given multiple uppercase words, should convert to sentence case', () => {
    const result = toSentenceCase('HELLO WORLD');
    expect(result).toBe('Hello World');
  });

  it('Given mixed case words, should convert to sentence case', () => {
    const result = toSentenceCase('heLLo WoRLd');
    expect(result).toBe('Hello World');
  });

  it('Given single character, should capitalize', () => {
    const result = toSentenceCase('a');
    expect(result).toBe('A');
  });

  it('Given multiple spaces between words, should preserve spacing', () => {
    const result = toSentenceCase('hello  world');
    expect(result).toBe('Hello  World');
  });

  it('Given words with numbers, should handle correctly', () => {
    const result = toSentenceCase('hello 123 world');
    expect(result).toBe('Hello 123 World');
  });

  it('Given words with special characters, should handle correctly', () => {
    const result = toSentenceCase('hello-world test_case');
    expect(result).toBe('Hello-world Test_case');
  });

  it('Given leading and trailing spaces, should preserve them', () => {
    const result = toSentenceCase(' hello world ');
    expect(result).toBe(' Hello World ');
  });
});
