import { defineConfig } from 'vitest/config';
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import path from 'path';

export default defineConfig({
  test: {
    env: {
      TZ: 'UTC',
    },
    include: [
      'dto/**/*.test.ts',
      'tools/**/*.test.ts',
      'validators/**/*.test.ts',
      'helpers/**/*.test.ts',
      'documents/**/*.test.ts',
      'customer/**/*.test.ts',
      'shared/**/*.test.ts',
      'field-config/**/*.test.ts',
      '*.test.ts',
    ],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, ''),
    },
  },
});
