export enum GROUP_BY_VALUES {
  MONTH = 'month',
  QUARTER = 'quarter',
  YEAR = 'year',
  COMPANY = 'company',
  UPLOAD_DATE = 'created_at',
  DEPOSIT_DATE = 'deposit_date',
  PROCESSING_DATE = 'processing_date',
  PAYMENT_DATE = 'payment_date',
}

export const GroupViewOptions = [
  {
    id: GROUP_BY_VALUES.MONTH,
    name: 'Month',
    subList: [
      { id: GROUP_BY_VALUES.UPLOAD_DATE, name: 'Upload date' },
      { id: GROUP_BY_VALUES.DEPOSIT_DATE, name: 'Deposit date' },
      { id: GROUP_BY_VALUES.PROCESSING_DATE, name: 'Processing date' },
      { id: GROUP_BY_VALUES.PAYMENT_DATE, name: 'Payment date' },
    ],
  },
  { id: GROUP_BY_VALUES.QUARTER, name: 'Quarter' },
  { id: GROUP_BY_VALUES.YEAR, name: 'Year' },
  { id: GROUP_BY_VALUES.COMPANY, name: 'Company' },
];

export const GROUP_BY_KEY = 'group_by';
