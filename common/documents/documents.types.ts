import type { ProcessMethod } from 'common/globalTypes';
import type { DocumentTypes } from 'common/constants/documents';

export type FilterOption = {
  id: string;
  name: string;
  type?: string;
  allCompanyIds?: string[];
  accountName?: string;
};

export type DocumentGroupItem = {
  date?: string;
  company_str_id?: string;
  company_name?: string;
  display_name?: string; // Only for client side to display and search
  file_count: number;
  total_statement_amount: number;
  total_bank_amount: number;
  total_commission_amount: number;
};

export type Document = {
  str_id: string;
  account_id?: string;
  uid?: string;
  type?: DocumentTypes;
  filename: string;
  file_size?: number;
  file_type?: string;
  created_at?: string;
  process_method?: ProcessMethod;
  statement_data?: {
    total_count?: number;
    total_commission?: number;
    groupedCountInfo?: Record<string, number>;
    groupedCommissionInfo?: Record<string, number>;
  };
  imports?: {
    summed_total_amount?: number;
  }[];
  override_filename?: string;
  status?: string;
  bank_total_amount?: number;
  statement_amount?: string;
};
