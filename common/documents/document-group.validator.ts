import dayjs from 'dayjs';
import z from 'zod';

import { account_id } from '../validators/account';
import { GROUP_BY_VALUES } from './documents.constants';

const start_date = z
  .string()
  .transform((v) => (dayjs(v).isValid() ? dayjs(v).toDate() : null))
  .optional();
const end_date = z
  .string()
  .transform((v) => (dayjs(v).isValid() ? dayjs(v).toDate() : null))
  .optional();

export const getDocumentGroupSchema = z.object({
  start_date,
  end_date,
  account_id,
  companies: z.array(z.string()).optional(),
  group_by: z.enum([
    GROUP_BY_VALUES.COMPANY,
    ...Object.values(GROUP_BY_VALUES),
  ]),
});

export type GetDocumentGroupDTO = z.infer<typeof getDocumentGroupSchema>;
