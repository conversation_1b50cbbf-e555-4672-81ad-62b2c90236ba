import * as Sentry from '@sentry/react';
import { type ReactNode, useContext, useEffect } from 'react';
import {
  createBrowserRouter,
  createRoutesFromElements,
  Navigate,
  Route,
  useRouteError,
} from 'react-router-dom';
import { QueryParamProvider } from 'use-query-params';
import { ReactRouter6Adapter } from 'use-query-params/adapters/react-router-6';
import { endpoint } from 'common/constants/table';

import TransactionDetailsView from '@/components/accounting/TransactionDetailsView';
import TransactionsView from '@/components/accounting/TransactionsView';
import AccountsView from '@/components/admin/AccountsView';
import AdminCompaniesView from '@/components/admin/AdminCompaniesView';
import AdminDocumentsView from '@/components/admin/AdminDocumentsView';
import AdminFieldsView from '@/components/admin/AdminFieldsView';
import AdminUsersView from '@/components/admin/AdminUsersView';
import AdminMetricsView from '@/components/admin/AdminMetricsView';
import CommissionCalculationView from '@/components/CommissionCalculation';
import CommissionsByAgent from '@/components/commissions/CommissionsByAgent';
import CommissionsDataView from '@/components/CommissionsDataView';
import CompaniesView from '@/components/companies/CompaniesView';
import CompanyProductOptionsView from '@/components/companies/CompanyProductOptionsView';
import CompanyProductsView from '@/components/companies/CompanyProductsView';
import ContactGroupsView from '@/components/contacts/ContactGroupsView';
import ContactsView from '@/components/contacts/ContactsView';
import DashboardView from '@/components/DashboardView';
import DocumentsView from '@/components/documents/DocumentsView';
import ExtractionsView from '@/components/documents/ExtractionsView';
import ImportsView from '@/components/documents/ImportsView';
import MappingsView from '@/components/documents/MappingsView';
import DownloadsPage from '@/components/DownloadsPage/DownloadsPage';
import EmptyState from '@/components/EmptyState';
import NotFoundPage from '@/components/NotFoundPage';
import ErrorImg from '@/illustrations/error.png';
import PolicyDataView from '@/components/PolicyDataView';
import ReconcilerView from '@/components/ReconcilerView';
import ReconcilersFlowsView from '@/components/reconciliation/ReconcilersFlowsView';
import ReconcilersView from '@/components/reconciliation/ReconcilersView';
import ReconciliationsHistoryView from '@/components/reconciliation/ReconciliationsHistoryView';
import ReconciliationsView from '@/components/ReconciliationsView';
import GroupingRulesViewContainer from '@/components/reconciliation/GroupingRulesView/GroupingRulesViewContainer';
import ReportsGroupView from '@/components/ReportsGroupView';
import ReportsGroupDetailsView from '@/components/ReportsGroupView/ReportsGroupDetailsView';
import ReportsView from '@/components/ReportsView';
import SnapshotReport from '@/components/ReportsView/SnapshotReport';
import CommissionSchedulesView from '@/components/schedules/CommissionSchedulesView';
import CompGridCriteriaView from '@/components/schedules/compGrids/CompGridCriteriaView';
import CompGridLevelsView from '@/components/schedules/compGrids/CompGridLevelsView';
import CompGridProductsView from '@/components/schedules/compGrids/CompGridProductsView';
import CompGridRatesView from '@/components/schedules/compGrids/CompGridRatesView';
import CompGridsView from '@/components/schedules/compGrids/CompGridsView';
import CompGridViewerView from '@/components/schedules/compGrids/CompGridViewerView';
import CompProfileSetsView from '@/components/schedules/CompProfileSets/CompProfileSetsView';
import CompProfilesView from '@/components/schedules/CompProfilesView';
import IncentiveTiersView from '@/components/schedules/IncentiveTiersView';
import SettingsView from '@/components/SettingsView/SettingsView';
import ToolsPage from '@/components/ToolsPage';
import Transactions from '@/components/Transactions';
import ViewsView from '@/components/ViewsView';
import CheckView from '@/components/ViewsView/CheckView';
import { LoadingContext } from '@/contexts/LoadingContext';
import { ReactComponent as ErrorIllustration } from '@/illustrations/error.svg';
import API from '@/services/API';
import { useRoleStore } from '@/store';
import Layout from '@/views/Layout';
import { Activities } from '@/components/admin/Activities';
import CustomInsights from '@/components/DashboardView/CustomInsightsView';
import ReleaseView from '@/components/Release';
import { ROUTES } from '@/constants/routes';
import { DocumentsGroupView } from '@/components/documents/groupview';
import { Customers } from '@/components/customers/customer-list';
import PreviewInsights from '@/components/DashboardView/PreviewInsightsView';
import AdminReports from '@/components/admin/AdminReports';
import CompReportsView from '@/components/CompReportsView';
import { SignInUp } from '@/components/SignInUp/SignInUp';
import type firebase from '@/firebase';
import CustomReportsView from '@/components/CustomReportsView';
import AdvanceCommissionSchedulePage from '@/components/schedules/AdvanceCommissionScheduleView';

const sentryCreateBrowserRouter =
  Sentry.wrapCreateBrowserRouter(createBrowserRouter);
let eventId: string | null = null;

const SentryRouteErrorFallback = () => {
  const routeError = useRouteError();

  useEffect(() => {
    if (!eventId) {
      eventId = Sentry.captureException(routeError);
    }

    Sentry.showReportDialog({
      eventId,
      title: 'Page Navigation Error',
      labelSubmit: 'Submit Report',
      onClose: () => {
        window.location.href = '/';
      },
    });
  }, [routeError]);

  return (
    <EmptyState image={<ErrorIllustration />} title="Something went wrong" />
  );
};

const RoleRequiredPage = (props: {
  children: React.ReactElement;
  pageKey: string;
}) => {
  const { children, pageKey } = props;
  const { userRole } = useRoleStore();
  const { setLoadingConfig } = useContext(LoadingContext);
  const { data, isLoading } = API.getBasicQuery(endpoint.viewsAndFields);
  useEffect(() => {
    setLoadingConfig({ loading: isLoading });
  }, [isLoading, setLoadingConfig]);
  if (isLoading) {
    return null;
  }

  const config = data?.data?.find(
    (r) => r.show_page && r.role === userRole && r.key === pageKey
  );

  if (!config) {
    return <Navigate to="/unauthorized" replace />;
  }
  return children;
};
const RouterComp = ({
  user,
  bar,
  defaultLandingPage,
}: {
  user: firebase.User | null;
  roles: string[];
  bar: ReactNode;
  defaultLandingPage: string;
}) => {
  const Router = sentryCreateBrowserRouter(
    createRoutesFromElements(
      <>
        <Route
          path="/sign-in"
          element={
            <QueryParamProvider
              adapter={ReactRouter6Adapter}
              options={{ removeDefaultsFromUrl: true }}
            >
              {user ? <Navigate to="/" replace /> : <SignInUp />}
            </QueryParamProvider>
          }
        />
        <Route
          path="/"
          element={
            user ? (
              <Layout Header={bar} user={user} />
            ) : (
              <Navigate to="/sign-in" replace />
            )
          }
          errorElement={<SentryRouteErrorFallback />}
        >
          <Route
            path="/accounting/transactions"
            element={<TransactionsView />}
          />
          <Route
            path="/accounting/transaction-details"
            element={<TransactionDetailsView />}
          />
          <Route path="/admin/activities" element={<Activities />} />
          <Route path="/admin/accounts" element={<AccountsView />} />
          <Route path="/admin/companies" element={<AdminCompaniesView />} />
          <Route
            path="/admin/documents/:tab"
            element={<AdminDocumentsView />}
          />
          <Route path="/admin/documents" element={<AdminDocumentsView />} />
          <Route
            path="/admin/calculations"
            element={<CommissionCalculationView />}
          />
          <Route path="/admin/metrics" element={<AdminMetricsView />} />
          <Route path="/admin/fields" element={<AdminFieldsView />} />
          <Route path="/admin/reports" element={<AdminReports />} />
          <Route path="/admin/tools/:tab" element={<ToolsPage />} />
          <Route path="/admin/tools" element={<ToolsPage />} />
          <Route path="/admin/users" element={<AdminUsersView />} />
          <Route path="/reconciler" element={<ReconcilerView />} />
          <Route path="/releases" element={<ReleaseView />} />
          <Route
            path="/insights"
            element={
              <RoleRequiredPage pageKey="insights">
                <DashboardView dashboardLabel={null} dashboardName={null} />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/insights/business-insights"
            element={
              <RoleRequiredPage pageKey="insights">
                <CustomInsights
                  label="Business Insights"
                  name="business-insights"
                />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/insights/policies-insights"
            element={
              <RoleRequiredPage pageKey="insights">
                <CustomInsights
                  label="Policies Insights"
                  name="policies-insights"
                />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/insights/agents-insights"
            element={
              <RoleRequiredPage pageKey="insights">
                <CustomInsights
                  label="Agents Insights"
                  name="agents-insights"
                />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/insights/agent-group-insights"
            element={
              <RoleRequiredPage pageKey="insights">
                <CustomInsights
                  label="Agent group Insights"
                  name="agent-group-insights"
                />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/insights/preview"
            element={
              <RoleRequiredPage pageKey="insights">
                <PreviewInsights />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/insights/:dashboardName"
            element={
              <RoleRequiredPage pageKey="insights">
                <CustomInsights label={null} name={null} />
              </RoleRequiredPage>
            }
          />
          <Route path="/transactions" element={<Transactions />} />
          <Route path="/contacts" element={<ContactsView />} />
          <Route
            path="/companies"
            element={
              <RoleRequiredPage pageKey="companies">
                <CompaniesView />
              </RoleRequiredPage>
            }
          />
          <Route path="/companies/products" element={<CompanyProductsView />} />
          <Route
            path="/companies/products/options"
            element={
              <RoleRequiredPage pageKey="options">
                <CompanyProductOptionsView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/commissions"
            element={
              <RoleRequiredPage pageKey="commissions">
                <CommissionsDataView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/reconciliation"
            element={
              <RoleRequiredPage pageKey="reconciliation">
                <ReconciliationsView />
              </RoleRequiredPage>
            }
          />
          {/* Deprecated */}
          <Route
            path="/commissions/schedules"
            element={<CommissionSchedulesView />}
          />
          <Route path="/commissions/agent" element={<CommissionsByAgent />} />
          {/* Deprecated */}
          <Route
            path="/commissions/agent-schedule-profiles"
            element={<CompProfilesView />}
          />
          <Route
            path="/agents/list"
            element={
              <RoleRequiredPage pageKey="agents">
                <ContactsView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/agents/groups"
            element={
              <RoleRequiredPage pageKey="agents_groups">
                <ContactGroupsView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/agents/production"
            element={
              <RoleRequiredPage pageKey="agents_production">
                <CommissionsByAgent />
              </RoleRequiredPage>
            }
          />
          <Route
            path={ROUTES.customers.url}
            element={
              <RoleRequiredPage pageKey={ROUTES.customers.pageKey}>
                <Customers />
              </RoleRequiredPage>
            }
          />
          <Route path="/mappings" element={<MappingsView />} />
          <Route path="/imports" element={<ImportsView />} />
          <Route path="/extractions" element={<ExtractionsView />} />
          <Route
            path="/policies"
            element={
              <RoleRequiredPage pageKey="policies">
                <PolicyDataView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/reconciliation/reconcilers"
            element={<ReconcilersView />}
          />
          <Route
            path="/reconciliation/flows"
            element={<ReconcilersFlowsView />}
          />
          <Route
            path="/reconciliation/history"
            element={<ReconciliationsHistoryView />}
          />
          <Route
            path="/reconciliation/grouping-rules"
            element={<GroupingRulesViewContainer />}
          />
          <Route
            path="/reports/summary/:id"
            element={<ReportsGroupDetailsView />}
          />
          <Route
            path="/reports/summary"
            element={
              <RoleRequiredPage pageKey="summaries">
                <ReportsGroupView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/reports"
            element={
              <RoleRequiredPage pageKey="reports">
                <ReportsView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/reports/custom-reports"
            element={
              <RoleRequiredPage pageKey="custom_reports">
                <CustomReportsView />
              </RoleRequiredPage>
            }
          />
          {/* Deprecated */}
          <Route path="/reports/:id" element={<SnapshotReport />} />
          <Route path="/comp-reports/:str_id" element={<CompReportsView />} />
          <Route
            path="/schedules/comp-profiles"
            element={<CompProfilesView />}
          />
          <Route
            path="/schedules/comp-profile-sets"
            element={<CompProfileSetsView />}
          />
          <Route
            path="/schedules/carriers"
            element={
              <RoleRequiredPage pageKey="carriers_schedules">
                <CommissionSchedulesView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/schedules/comp-grids"
            element={
              <RoleRequiredPage pageKey="comp_grids_schedules">
                <CompGridsView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/schedules/comp-grids/criteria"
            element={<CompGridCriteriaView />}
          />
          <Route
            path="/schedules/comp-grids/levels"
            element={<CompGridLevelsView />}
          />
          <Route
            path="/schedules/comp-grids/products"
            element={<CompGridProductsView />}
          />
          <Route
            path="/schedules/comp-grids/rates"
            element={<CompGridRatesView />}
          />
          <Route
            path="/schedules/comp-grids/viewer"
            element={
              <RoleRequiredPage pageKey="comp_grids_schedules">
                <CompGridViewerView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/schedules/incentive-tiers"
            element={
              <RoleRequiredPage pageKey="incentives_schedules">
                <IncentiveTiersView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/schedules/advance"
            element={<AdvanceCommissionSchedulePage />}
          />
          <Route
            path="/views"
            element={
              <RoleRequiredPage pageKey="views">
                <ViewsView />
              </RoleRequiredPage>
            }
          />
          <Route path="/views/:id" element={<CheckView />} />
          <Route
            path="/documents"
            element={
              <RoleRequiredPage pageKey="documents">
                <DocumentsView />
              </RoleRequiredPage>
            }
          />
          <Route
            path="/documents/groups"
            element={
              <RoleRequiredPage pageKey="documents">
                <DocumentsGroupView />
              </RoleRequiredPage>
            }
          />
          <Route path="/settings/:tab" element={<SettingsView />} />
          <Route path="/settings" element={<SettingsView />} />
          <Route
            path="/settings/data-update-tools/:tab"
            element={<SettingsView />}
          />
          <Route path="/downloads" element={<DownloadsPage />} />
          <Route
            path="/unauthorized"
            element={
              <EmptyState
                title={'Unauthorized'}
                // biome-ignore lint/a11y/useAltText: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                image={<img src={ErrorImg} width="100%" />}
                description={'You do not have permission to access this page.'}
                size="large"
              />
            }
          />
          <Route path="/" element={<Navigate to={`${defaultLandingPage}`} />} />
          <Route element={<NotFoundPage />} />
        </Route>
      </>
    ),
    {
      basename: process.env.REACT_APP_BASENAME || '/',
    }
  );

  return Router;
};

export default RouterComp;
