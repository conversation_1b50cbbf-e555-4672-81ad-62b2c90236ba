import type { SxProps } from '@mui/material';
import type { UserStates } from 'common/constants/user-states.enum';

declare global {
  const __BUILD_TIME__: string;
  const __APP_ENV__: 'development' | 'production' | 'test';
}

export enum Roles {
  ACCOUNT_ADMIN = 1,
  PRODUCER = 2,
  FINTARY_ADMIN = 3,
  ACCOUNT_ADMIN_RO = 4,
  PRODUCER_RO = 5,
  FINTARY_ADMIN_RO = 6,
  DATA_SPECIALIST = 7,
  DATA_SPECIALIST_RO = 8,
}

export interface ErrorResponse {
  error: string;
}

export interface UserRolesAccounts {
  account_id: string;
  role_id: Roles;
}

export interface UserCheck {
  userOverallState: UserStates;
  userEmail?: string;
  userOnboardingNeeded: boolean;
  accountOnboardingNeeded: boolean;
  userAccounts: UserRolesAccounts[];
}

export interface AnyObj {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  [key: string]: any;
}

export interface Field {
  id?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  value?: any;
  defaultTableHidden?: boolean;
  label: string;
  description?: string;
  table?: string;
  type?: FieldTypes;
  multiple?: boolean;
  options?: string[] | object[];
  matches?: string[];
  enabled?: boolean;
  readOnly?: boolean;
  global?: boolean;
  required?: boolean;
  copyable?: boolean;
  reconciler?: boolean;
  subPolicyDataIfEmpty?: boolean;
  queryParamValue?: string;
  queryParamName?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  normalizer?: (value: any) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  formatter?: (value: any, collectionVals?: any[] | any) => any;
  dynamicFormatter?: (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    value: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    collectionVals?: any[] | any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    row?: any
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  validator?: (value: any) => boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  tableFormatter?: (value: any, row?: any) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  optionFormatter?: (value: any) => any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  optionValuer?: (value: any) => any;
  queryFields?: string[];
  render?: (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    value: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    row?: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    setter?: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dynamicSelect?: any, // Deprecated
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dynamicSelectData?: any
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ) => any;
  fieldMatcherType?: string;
  bulkEdit?: boolean;
  headerSx?: SxProps;
  sx?: SxProps;
  width?: number;
  sticky?: 'left' | 'right';
  getWidth?: (data: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    estimatedWidth: any;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    allRows?: any;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dynamicSelectData: any;
  }) => number;
  style?: SxProps;
}

export interface DateRange {
  id?: number;
  start_date: Date | null;
  end_date: Date | null;
  type: DateRangesTypes;
  notes?: string | null;
  name?: string | null;
}

export enum savedReportsGroupsTemplates {
  COMMISSION_PAYOUT = 'commission_payout',
}

export enum FieldTypes {
  BOOLEAN = 'boolean',
  CODE = 'code',
  CODEMIRROR = 'codemirror',
  CURRENCY = 'currency',
  CUSTOM = 'custom',
  DATE = 'date',
  DATE_RANGE = 'date_range',
  DIVIDER = 'divider',
  DYNAMIC_SELECT = 'dynamic-select',
  FIELD_MATCHER = 'field-matcher',
  HEADING = 'heading',
  INTEGER = 'integer',
  PERCENTAGE = 'percentage',
  RATE_SCHEDULE = 'rate-schedule-annual',
  SCHEDULE = 'schedule',
  SELECT = 'select',
  DRAGGABLE_SELECT = 'draggable-select',
  SUB_HEADING = 'sub-heading',
  TEXT = 'text',
  STRING_ARRAY = 'string-array',
  // The table skips the formatter function if a field does not exist in the row data.
  // But if we have a custom field and want to call the formatter function
  // use this type to achieve that.
  TABLE_CELL_CUSTOM = 'table-cell-custom',
  MULTI_SELECT = 'multiSelect',
}

export enum DataTypes {
  BOOLEAN = 'boolean',
  TEXT = 'text',
  NUMBER = 'number',
  CURRENCY = 'currency',
  INTEGER = 'integer',
  PERCENTAGE = 'percentage',
  DATE = 'date',
  JSON = 'json',
  DECIMAL = 'decimal',
  ARRAY = 'array',
}

export enum GlobalStateCodes {
  FE_INCOMPATIBLE = 'FEIncompatible',
  FE_OUT_OF_DATE = 'FEOutOfDate',
}

export enum ReferralTypes {
  AGENT = 'agent',
  REGION = 'region',
  RECRUITING = 'recruiting',
  OTHER = 'other',
}

export enum DateRangesTypes {
  COMP_GRID_CRITERION = 'comp_grid_criterion',
  COMP_GRID_RATES = 'comp_grid_rates',
  ANY = 'any',
}

export enum DocumentPreviewKeys {
  PREVIEW = 'preview',
  OVERRIDE = 'override',
  ORIGINAL = 'original',
}

export enum AccountAccessLevels {
  GLOBAL = 'global',
  ACCOUNT = 'account',
}

export enum NotesEntityTypes {
  ACCOUNT = 'account',
  PROCESSOR = 'processor',
}

export enum DocumentProcessActionTypes {
  ADD_MAPPING = 'Add mapping',
  EDIT_MAPPING = 'Edit mapping',
  SELECT_MAPPING = 'Select mapping',
  ADD_DATA = 'Add data',
  EDIT_DATA = 'Edit data',
  DELETE_DATA = 'Delete data',
  SELECT_PROCESSOR = 'Select processor',
  FIX_EXTRACTION = 'Fix extraction',
  SELECT_SPREADSHEET = 'Select spreadsheet',
  SELECT_METHOD = 'Select method',
  SELECT_GEMINI_PROMPT = 'Select Gemini prompt',
  FILTER_DATA = 'Filter data',
  SELECT_COMPANY = 'Select company',
}

export type DocumentProcessTypes =
  | 'Add mapping'
  | 'Edit mapping'
  | 'Select mapping'
  | 'Add data'
  | 'Edit data'
  | 'Delete data'
  | 'Select processor'
  | 'Fix extraction'
  | 'Select spreadsheet'
  | 'Select method'
  | 'Select company'
  | 'Filter data'
  | 'Select Gemini prompt';

export type AccountSettings = {
  pages_settings: {
    policies?: PageSettings;
    commissions?: PageSettings;
    agents_groups?: PageSettings;
  };
  agent_settings: {
    policies?: Record<string, unknown>;
    commissions?: Record<string, unknown>;
    agents_groups?: Record<string, unknown>;
  };
  companies_view: null | unknown;
  default_page: string;
  insights_widgets: null | unknown;
  dashboardNames: Array<{
    name: null | string;
    label: null | string;
  }>;
};

type PageSettings = {
  fields: string[];
  read_only: boolean;
  show_page: boolean;
  menu_label: string;
  page_label: string;
  page_options: unknown[];
  default_filters: unknown[];
  custom_fields_id: string;
  outstandingMobileFields: unknown[];
};
