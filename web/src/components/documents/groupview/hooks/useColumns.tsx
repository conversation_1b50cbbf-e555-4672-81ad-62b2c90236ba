import { Business, CalendarMonth, LaunchOutlined } from '@mui/icons-material';
import {
  Box,
  Chip,
  colors,
  IconButton,
  Link,
  Tooltip,
  Typography,
} from '@mui/material';
import { GROUP_BY_VALUES } from 'common/documents/documents.constants';
import type { DocumentGroupItem } from 'common/documents/documents.types';
import type { MRT_ColumnDef } from 'material-react-table';
import { useMemo } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { formatCurrency } from 'common/helpers/formatCurrency';
import { DATE_FILTERS } from 'common/constants';

import { ROUTES } from '../../constants';
import { getStartAndEndDates } from '../utils';
import { useFilterParams } from './useFilterParams';

const getDateParamName = (groupBy: GROUP_BY_VALUES) => {
  switch (groupBy) {
    case GROUP_BY_VALUES.DEPOSIT_DATE:
      return {
        start: DATE_FILTERS.DEPOSIT_DATE_START,
        end: DATE_FILTERS.DEPOSIT_DATE_END,
      };
    case GROUP_BY_VALUES.PROCESSING_DATE:
      return {
        start: DATE_FILTERS.PROCESSING_DATE_START,
        end: DATE_FILTERS.PROCESSING_DATE_END,
      };
    case GROUP_BY_VALUES.UPLOAD_DATE:
      return {
        start: DATE_FILTERS.UPLOAD_DATE_START,
        end: DATE_FILTERS.UPLOAD_DATE_END,
      };
    case GROUP_BY_VALUES.PAYMENT_DATE:
      return {
        start: DATE_FILTERS.PAYMENT_DATE_START,
        end: DATE_FILTERS.PAYMENT_DATE_END,
      };
    default:
      return { start: DATE_FILTERS.START_DATE, end: DATE_FILTERS.END_DATE };
  }
};

const createUrlParams = ({
  groupBy,
  row,
  start_date,
  end_date,
  companyIds,
}: {
  groupBy: GROUP_BY_VALUES;
  row: DocumentGroupItem;
  start_date?: string;
  end_date?: string;
  companyIds?: string[];
}) => {
  const urlParams = new URLSearchParams({});

  if (groupBy === GROUP_BY_VALUES.COMPANY) {
    if (start_date) {
      urlParams.append(DATE_FILTERS.UPLOAD_DATE_START, start_date);
    }
    if (end_date) {
      urlParams.append(DATE_FILTERS.UPLOAD_DATE_END, end_date);
    }
    urlParams.append('companies', row.company_str_id || '');
  } else {
    // Group by month
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    companyIds?.forEach((company) => {
      urlParams.append('companies', company);
    });

    if (row.date) {
      const { startDate, endDate } = getStartAndEndDates(
        row.date as string,
        groupBy
      );
      const { start, end } = getDateParamName(groupBy);
      urlParams.append(start, startDate);
      urlParams.append(end, endDate);
    }
  }

  return urlParams;
};

export const useColumns = ({
  groupBy,
  totalData,
}: {
  groupBy: GROUP_BY_VALUES;
  totalData: Pick<
    DocumentGroupItem,
    | 'file_count'
    | 'total_commission_amount'
    | 'total_bank_amount'
    | 'total_statement_amount'
  >;
}) => {
  const { companies, start_date, end_date } = useFilterParams();

  const dateColumn = useMemo<MRT_ColumnDef<DocumentGroupItem>>(() => {
    return {
      accessorKey: 'display_name',
      header: 'Date',
      Footer: () => <Typography>Total</Typography>,
      Cell: ({ renderedCellValue, row }) => {
        if (!row.original.date) {
          return 'Empty date';
        }
        if (row.original.date === 'total') {
          return 'Total';
        }

        const { startDate, endDate } = getStartAndEndDates(
          row.original.date as string,
          groupBy
        );

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              cursor: 'pointer',
              ':hover': {
                '& .action': {
                  visibility: 'visible',
                },
              },
            }}
          >
            {renderedCellValue}
            <Box
              className="action"
              sx={{
                display: 'flex',
                alignItems: 'center',
                '& a': {
                  cursor: 'pointer',
                  color: colors.grey[800],
                  display: 'inline-flex',
                },
              }}
            >
              <Tooltip title="View companies in this period">
                <RouterLink
                  to={{
                    pathname: ROUTES.documentsGroupView,
                    search: new URLSearchParams({
                      start_date: startDate,
                      end_date: endDate,
                      group_by: GROUP_BY_VALUES.COMPANY,
                    }).toString(),
                  }}
                >
                  <Business sx={{ ml: 1, fontSize: 18 }} />
                </RouterLink>
              </Tooltip>
            </Box>
          </Box>
        );
      },
    };
  }, [groupBy]);

  const companyColumn = useMemo<MRT_ColumnDef<DocumentGroupItem>>(() => {
    return {
      accessorKey: 'company_name',
      header: 'Company',
      Footer: () => <Typography>Total</Typography>,
      Cell: ({ renderedCellValue, row }) => {
        if (row.original.company_str_id === 'total') {
          return 'Total';
        }

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              ':hover': {
                '& .action': {
                  visibility: 'visible',
                },
              },
            }}
          >
            <Chip
              label={renderedCellValue as string}
              clickable
              component="a"
              href={`/companies?id=${row.original.company_str_id}`}
              target="_blank"
            />
            <Box
              className="action"
              sx={{
                display: 'flex',
                alignItems: 'center',
                '& a': {
                  cursor: 'pointer',
                  color: colors.grey[700],
                  display: 'inline-flex',
                },
              }}
            >
              <Tooltip title="View group by month">
                <RouterLink
                  to={{
                    pathname: ROUTES.documentsGroupView,
                    search: new URLSearchParams({
                      companies: row.original.company_str_id || '',
                    }).toString(),
                  }}
                >
                  <CalendarMonth sx={{ ml: 1, fontSize: 18 }} />
                </RouterLink>
              </Tooltip>
            </Box>
          </Box>
        );
      },
    };
  }, []);

  const columns = useMemo<MRT_ColumnDef<DocumentGroupItem>[]>(() => {
    return [
      groupBy === GROUP_BY_VALUES.COMPANY ? companyColumn : dateColumn,
      {
        accessorKey: 'file_count',
        header: 'Documents',
        size: 150,
        Footer() {
          return (
            <Typography sx={{ textAlign: 'right' }}>
              {totalData.file_count}
            </Typography>
          );
        },
        muiTableHeadCellProps: {
          sx: {
            '&>.Mui-TableHeadCell-Content': {
              justifyContent: 'flex-end',
            },
          },
        },
        muiTableBodyCellProps: {
          align: 'right',
        },
        Cell: ({ renderedCellValue, row }) => {
          const urlParams = createUrlParams({
            groupBy,
            row: row.original,
            start_date,
            end_date,
            companyIds: companies,
          });

          const link = `${ROUTES.documents}?${urlParams.toString()}`;

          return (
            <Box>
              {renderedCellValue}
              <IconButton
                component={Link}
                sx={{
                  ml: 1,
                  opacity: 0.5,
                  '&:hover': { opacity: 1 },
                  color: '#2196f3',
                }}
                href={link}
                target="_blank"
              >
                <LaunchOutlined />
              </IconButton>
            </Box>
          );
        },
      },
      {
        accessorKey: 'total_commission_amount',
        header: 'Commission amount',
        size: 150,
        Footer() {
          return (
            <Typography sx={{ textAlign: 'right' }}>
              {formatCurrency(totalData.total_commission_amount)}
            </Typography>
          );
        },
        muiTableHeadCellProps: {
          sx: {
            '&>.Mui-TableHeadCell-Content': {
              justifyContent: 'flex-end',
            },
          },
        },
        muiTableBodyCellProps: {
          align: 'right',
        },
        Cell: ({ renderedCellValue }) => {
          if (typeof renderedCellValue === 'number') {
            return formatCurrency(renderedCellValue as number);
          }
          return renderedCellValue;
        },
      },
      {
        accessorKey: 'total_statement_amount',
        header: 'Statement amount',
        size: 150,
        Footer() {
          return (
            <Typography sx={{ textAlign: 'right' }}>
              {formatCurrency(totalData.total_statement_amount)}
            </Typography>
          );
        },
        muiTableHeadCellProps: {
          sx: {
            '&>.Mui-TableHeadCell-Content': {
              justifyContent: 'flex-end',
            },
          },
        },
        muiTableBodyCellProps: {
          align: 'right',
        },
        Cell: ({ renderedCellValue }) =>
          formatCurrency((renderedCellValue || 0) as number),
      },
      {
        accessorKey: 'total_bank_amount',
        header: 'Bank amount',
        size: 150,
        Footer() {
          return (
            <Typography sx={{ textAlign: 'right' }}>
              {formatCurrency(totalData.total_bank_amount)}
            </Typography>
          );
        },
        muiTableHeadCellProps: {
          sx: {
            '&>.Mui-TableHeadCell-Content': {
              justifyContent: 'flex-end',
            },
          },
        },
        muiTableBodyCellProps: {
          align: 'right',
        },
        Cell: ({ renderedCellValue }) =>
          formatCurrency((renderedCellValue || 0) as number),
      },
    ];
  }, [
    companies,
    companyColumn,
    dateColumn,
    end_date,
    groupBy,
    start_date,
    totalData,
  ]);

  return { columns };
};
