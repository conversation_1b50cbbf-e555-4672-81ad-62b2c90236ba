import { DocumentTypeLabels } from 'common/constants/documents';
import type { FilterOption } from 'common/documents/documents.types';
import {
  DocumentStatuses,
  DocumentStatusesLabels,
  AccessTypes,
  DocumentFilters,
} from 'common/globalTypes';
import { Box } from '@mui/material';

import { useCompanies } from '@/api/companies';
import { SyncStatusList } from '@/components/admin/AdminDocumentsView/DocumentsView/DocumentsView';
import { useUserInfo } from '@/hooks/useUserInfo';
import API from '@/services/API';

interface Company {
  str_id: string;
  id: number;
  company_name: string;
  access: string;
  canonical_id?: number;
  account_name?: string | null;
}

interface CompanyGroup {
  global: Company | null;
  accounts: Company[];
}

export const useFilters = ({ isAdmin }: { isAdmin?: boolean } = {}) => {
  const { data: { data: companies } = {} } = useCompanies({ isAdmin });
  const { data: { fintaryAdmin } = {} } = useUserInfo();
  const isFintaryAdmin = !!fintaryAdmin;

  const { data: documentsData } = API.getBasicQuery('documents');
  const filterCounts = documentsData?.filterCounts || {};

  const statusList = isFintaryAdmin
    ? Object.values(DocumentStatuses)
    : Object.values(DocumentStatuses).filter(
        (item) =>
          item !== DocumentStatuses.PENDING_REVIEW &&
          item !== DocumentStatuses.PENDING_UPLOAD
      );

  const processedCompanies = (() => {
    if (!isAdmin) {
      return (
        companies?.reduce((unique: FilterOption[], company) => {
          if (!unique.find((item) => item.name === company.company_name)) {
            unique.push({
              id: company.str_id,
              name: company.company_name,
            });
          }
          return unique;
        }, []) || []
      );
    }

    const globalCompanies =
      companies?.filter((c) => c.access === AccessTypes.GLOBAL) || [];
    const accountCompanies =
      companies?.filter((c) => c.access === AccessTypes.ACCOUNT) || [];

    const companyGroups: Record<string, CompanyGroup> = {};

    for (const company of globalCompanies) {
      const name = company.company_name;
      if (!companyGroups[name]) {
        companyGroups[name] = { global: null, accounts: [] };
      }
      companyGroups[name].global = company;
    }

    for (const company of accountCompanies) {
      if (company.canonical_id) {
        const globalCompany = globalCompanies.find(
          (g) => g.id === company.canonical_id
        );
        if (globalCompany) {
          const name = globalCompany.company_name;
          if (companyGroups[name]) {
            companyGroups[name].accounts.push(company);
          }
        }
      }
    }

    const processedGlobalCompanies = Object.entries(companyGroups)
      .filter(([_, group]) => group.global)
      .map(([_, group]) => {
        return {
          id: group.global?.str_id,
          name: group.global?.company_name,
          type: 'Global',
          allCompanyIds: [
            group.global?.str_id,
            ...group.accounts.map((acc) => acc.str_id),
          ],
        };
      });

    const processedAccountCompanies =
      accountCompanies
        ?.filter((c) => !c.canonical_id)
        ?.reduce((unique: FilterOption[], company) => {
          if (!unique.find((item) => item.name === company.company_name)) {
            unique.push({
              id: company.str_id,
              name: company.company_name,
              type: company.account_name || 'Account',
              allCompanyIds: [company.str_id],
            });
          }
          return unique;
        }, []) || [];

    return [
      ...processedGlobalCompanies,
      ...processedAccountCompanies,
    ] as FilterOption[];
  })();

  const showFilter =
    filterCounts.missing_companies > 0 ||
    filterCounts.missing_types > 0 ||
    filterCounts.unverified > 0 ||
    filterCounts.missing_statement_amount > 0;

  const filtersData: {
    type: FilterOption[];
    companies: FilterOption[];
    sync_status: FilterOption[];
    status: FilterOption[];
    filter: FilterOption[];
  } = {
    type: Object.entries(DocumentTypeLabels).map(([id, name]) => ({
      id,
      name,
    })),
    companies: processedCompanies,
    sync_status: SyncStatusList.map((syncStatus) => ({
      id: syncStatus.id,
      name: syncStatus.label,
    })),
    status: statusList.map((status) => ({
      id: status,
      name: DocumentStatusesLabels[status],
    })),

    filter:
      showFilter && !isAdmin
        ? [
            ...(filterCounts.missing_companies > 0
              ? [
                  {
                    id: DocumentFilters.MISSING_COMPANIES,
                    name: `Missing companies (${filterCounts.missing_companies})`,
                  },
                ]
              : []),
            ...(filterCounts.missing_types > 0
              ? [
                  {
                    id: DocumentFilters.MISSING_TYPES,
                    name: `Missing types (${filterCounts.missing_types})`,
                  },
                ]
              : []),
            ...(filterCounts.missing_statement_amount > 0
              ? [
                  {
                    id: DocumentFilters.MISSING_STATEMENT_AMOUNT,
                    name: `Missing statement amount (${filterCounts.missing_statement_amount})`,
                  },
                ]
              : []),
            ...(filterCounts.unverified > 0
              ? [
                  {
                    id: DocumentFilters.UNVERIFIED_DOCUMENTS,
                    name: `Unverified documents (${filterCounts.unverified})`,
                  },
                ]
              : []),
          ]
        : [],
  };

  const filters = {
    type: {
      label: 'Type',
    },
    companies: {
      label: 'Companies',
      listContainerSx: {
        minWidth: 750,
        p: 1,
      },
      tokenizeSearch: false,
      renderLabel: ({ key }) => {
        const company = filtersData.companies.find((c) => c.id === key);
        if (!company) return null;

        if (!isAdmin) {
          return company.name;
        }

        return (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              width: '100%',
              gap: 1,
              pr: 0.5,
            }}
          >
            <Box sx={{ flex: 1, fontWeight: 500, color: '#333' }}>
              {company.name}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {company.type === 'Account' && company.accountName && (
                <Box
                  sx={{
                    color: '#888',
                    fontSize: '0.7rem',
                    backgroundColor: '#f9f9f9',
                    px: 1,
                    py: 0.25,
                    borderRadius: 1,
                    whiteSpace: 'nowrap',
                  }}
                >
                  {company.accountName}
                </Box>
              )}
              <Box
                sx={{
                  color: '#666',
                  fontSize: '0.75rem',
                  backgroundColor:
                    company.type === 'Global' ? '#e3f2fd' : '#f5f5f5',
                  px: 1.5,
                  py: 0.25,
                  borderRadius: 1,
                  whiteSpace: 'nowrap',
                  ml: 1,
                  minWidth: '60px',
                  textAlign: 'center',
                }}
              >
                {company.type}
              </Box>
            </Box>
          </Box>
        );
      },
    },
    sync_status: {
      label: 'Sync status',
    },
    status: {
      label: 'Status',
    },
    filter: {
      label: 'Filters',
    },
  };
  return {
    filters: filters,
    filtersData: filtersData,
  };
};
