import {
  But<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>nackbar,
  Alert,
  CircularProgress,
  IconButton,
} from '@mui/material';
import { useEffect, useState, useContext } from 'react';
import {
  CloudUpload,
  Download,
  Delete,
  DeleteOutline,
} from '@mui/icons-material';
import { Navigate } from 'react-router-dom';
import { saveAs } from 'file-saver';
import CommonFormatter from 'common/Formatter';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Box,
  Toolbar,
} from '@mui/material';
import { removeLeadingTrailingChar } from 'common/helpers';

import API from '@/services/API';
import type { CustomReport } from './types';
import UploadReportModal from './components/UploadReportModal';
import { LoadingContext } from '@/contexts/LoadingContext';
import { BasicDialog } from '@/common';

const PAGE_SIZE = 10;

const CustomReportsView = () => {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(PAGE_SIZE);
  const [reports, setReports] = useState<CustomReport[]>([]);
  const [total, setTotal] = useState(0);
  const [refreshKey] = useState(0);
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error',
  });
  const [downloadingId, setDownloadingId] = useState('');
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [reportToDelete, setReportToDelete] = useState<CustomReport | null>(
    null
  );
  const { setLoadingConfig } = useContext(LoadingContext);

  // Build query string
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const buildQueryString = (params: Record<string, any>) =>
    Object.entries(params)
      .filter(([, value]) => value !== undefined && value !== null)
      .map(
        ([key, value]) =>
          `${encodeURIComponent(key)}=${encodeURIComponent(value as string)}`
      )
      .join('&');

  const params = {
    page: page,
    limit: rowsPerPage,
    refreshKey,
  };
  const queryString = buildQueryString(params);
  const {
    data: reportsData,
    isLoading,
    refetch,
    isRefetching,
  } = API.getBasicQuery('saved_reports/file', queryString);

  useEffect(() => {
    if (reportsData) {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const items = (reportsData.data || []).map((item: any) => ({
        id: item.id,
        str_id: item.str_id,
        name: item.name,
        description: item.notes,
        report_type: item.uploaded_saved_reports?.file_type,
        created_at: item.created_at,
        updated_at: item.uploaded_saved_reports?.updated_at,
        isOwner: item.access === 'owner',
        file_name: item.uploaded_saved_reports?.file_name,
        file_link: item.uploaded_saved_reports?.file_path,
      }));
      setReports(items);
      setTotal(reportsData.count || 0);
    }
    if (isLoading || isRefetching) {
      setLoadingConfig({
        loading: true,
        message: 'Loading custom reports...',
      });
    } else {
      setLoadingConfig({
        loading: false,
      });
    }
  }, [reportsData, isLoading, isRefetching, setLoadingConfig]);

  // Check if the view should be shown based on account settings
  const { data: accountSettings, isFetched: isFetchedAccountSettings } =
    API.getBasicQuery(`accounts/settings`);

  const viewSettings = accountSettings?.pages_settings?.custom_reports;

  if (isFetchedAccountSettings && viewSettings?.show_page === false) {
    return <Navigate to="/settings" />;
  }

  // Mutation for deleting reports
  const deleter = API.getMutation('saved_reports/delete', 'DELETE');

  // Handle opening the upload modal
  const handleOpenUploadModal = () => {
    setIsUploadModalOpen(true);
  };

  // Handle closing the upload modal
  const handleCloseUploadModal = () => {
    setIsUploadModalOpen(false);
  };

  // Add a refetch function
  const refetchReports = () => {
    console.log('Refetching reports...');
    refetch();
  };

  // Handle delete report
  const handleDeleteReport = (report: CustomReport) => {
    setReportToDelete(report);
    setShowDeleteConfirm(true);
  };

  // Handle confirm delete
  const handleConfirmDelete = async () => {
    if (!reportToDelete) return;

    try {
      await deleter.mutateAsync({ str_id: reportToDelete.str_id });
      setSnackbar({
        open: true,
        message: `Report "${reportToDelete.name}" deleted successfully`,
        severity: 'success',
      });
      refetchReports();
    } catch (error) {
      console.error('Delete failed:', error);
      setSnackbar({
        open: true,
        message: 'Failed to delete report',
        severity: 'error',
      });
    } finally {
      setShowDeleteConfirm(false);
      setReportToDelete(null);
    }
  };

  // Table columns
  const columns = [
    { id: 'name', label: 'Name' },
    { id: 'description', label: 'Description' },
    { id: 'report_type', label: 'Report type' },
    { id: 'created_at', label: 'Created at' },
    { id: 'updated_at', label: 'Updated at' },
    { id: 'actions', label: 'Actions' },
  ];

  // Use custom label if provided in settings
  if (viewSettings?.page_label) {
    columns[0].label = viewSettings?.page_label;
  }

  const downloadfilePoster = API.getMutation('saved_reports/download', 'POST', {
    rawData: true,
  });

  return (
    <Box sx={{ width: '100%', padding: 2, overflow: 'scroll' }}>
      {/* Action Bar */}
      <Toolbar sx={{ justifyContent: 'space-between', mb: 2 }}>
        <Typography variant="h6">
          {viewSettings?.page_label || 'Custom reports'}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<CloudUpload />}
          onClick={handleOpenUploadModal}
        >
          Upload
        </Button>
      </Toolbar>

      {/* Table */}
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              {columns.map((col) => (
                <TableCell key={col.id}>{col.label}</TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {reports.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length} align="center">
                  No reports found.
                </TableCell>
              </TableRow>
            ) : (
              reports.map((row) => (
                <TableRow key={row.id}>
                  <TableCell>
                    {downloadingId === row.id.toString() ? (
                      <CircularProgress size={24} />
                    ) : (
                      <Button
                        onClick={async () => {
                          setDownloadingId(row.id.toString());
                          try {
                            const response =
                              await downloadfilePoster.mutateAsync({
                                str_id: row.str_id,
                              });
                            const filename = removeLeadingTrailingChar(
                              response.headers['content-disposition']?.split(
                                '='
                              )[1] || 'filename',
                              '"'
                            );
                            const decodeFilename = decodeURIComponent(filename);
                            const blob = response.data;
                            saveAs(blob, decodeFilename);
                            setSnackbar({
                              open: true,
                              message: `Downloaded ${decodeFilename}`,
                              severity: 'success',
                            });
                          } catch (err) {
                            setSnackbar({
                              open: true,
                              message: 'Download failed',
                              severity: 'error',
                            });

                            console.error('Download failed', err);
                          } finally {
                            setDownloadingId('');
                          }
                        }}
                        endIcon={<Download />}
                        disabled={!!downloadingId}
                      >
                        {row.name}
                      </Button>
                    )}
                  </TableCell>
                  <TableCell>{row.description}</TableCell>
                  <TableCell>{row.report_type}</TableCell>
                  <TableCell>
                    {CommonFormatter.dateTime(row.created_at)}
                  </TableCell>
                  <TableCell>
                    {CommonFormatter.dateTime(row.updated_at)}
                  </TableCell>
                  <TableCell>
                    <IconButton
                      onClick={() => handleDeleteReport(row)}
                      title="Delete report"
                      size="small"
                    >
                      <DeleteOutline />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
      <TablePagination
        component="div"
        count={total}
        page={page}
        onPageChange={(_, newPage) => setPage(newPage)}
        rowsPerPage={rowsPerPage}
        onRowsPerPageChange={(e) => {
          setRowsPerPage(parseInt(e.target.value, 10));
          setPage(0);
        }}
        rowsPerPageOptions={[5, 10, 25, 50]}
      />

      {/* Upload Modal Component */}
      <UploadReportModal
        open={isUploadModalOpen}
        handleClose={handleCloseUploadModal}
        onUploadSuccess={refetchReports}
      />

      {/* Delete Confirmation Dialog */}
      <BasicDialog
        open={showDeleteConfirm}
        title="Delete report"
        positiveLabel="Delete"
        onClose={async (confirmed) => {
          if (confirmed) {
            await handleConfirmDelete();
          } else {
            setShowDeleteConfirm(false);
            setReportToDelete(null);
          }
        }}
        bodyComponent={
          <Alert severity="warning">
            Are you sure you want to delete "{reportToDelete?.name}"?
            <br />
            This action cannot be undone.
          </Alert>
        }
      />

      <Snackbar
        open={snackbar.open}
        autoHideDuration={4000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CustomReportsView;
