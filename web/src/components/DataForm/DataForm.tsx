// @ts-nocheck
import { javascript } from '@codemirror/lang-javascript';
import {
  Alert,
  Box,
  Checkbox,
  Chip,
  Divider,
  FormControl,
  FormControlLabel,
  InputLabel,
  MenuItem,
  Select,
  Tooltip,
  Typography,
} from '@mui/material';
import CodeMirror from '@uiw/react-codemirror';
import { SystemRoles } from 'common/globalTypes';
import dayjs from 'dayjs';
import { isEqual } from 'lodash-es';
import {
  memo,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';
import { useBeforeUnload } from 'react-use';

import { BasicDialog } from '@/common';
import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import DataFormActions from '@/components/DataForm/DataFormActions';
import DebugInfo from '@/components/DataForm/DebugInfo';
import DynamicSelect from '@/components/DataForm/DynamicSelect';
import BasicDatePicker from '@/components/molecules/BasicDatePicker';
import { useInitDynamicSelects } from '@/components/molecules/EnhancedTable/NewTable';
import FieldMatcher from '@/components/molecules/FieldMatcher';
import RateScheduleAnnual from '@/components/molecules/RateScheduleAnnual';
import { UIStateContext } from '@/contexts/UIStateProvider';
import useBeforeUnloadPage from '@/contexts/useBeforeunloadPage';
import { useUserInfo } from '@/hooks/useUserInfo';
import API from '@/services/API';
import { hasAccess } from '@/services/helpers';
import { useRoleStore } from '@/store';
import { FieldTypes } from '@/types';
import DraggableSelect from './DraggableSelect';
import FieldComponent from './FieldComponent';
import FieldRow from './FieldRow';
import NullCheckbox from './NullCheckbox';

const defaultCode = `
const handler = ({record, BigNumber}) => {
    // Your code here
    return true;
}

return handler;
`;

const /** @type {Record<String, any>} */ dataDescInitialValues = {
    onDeleteText: undefined,
    disableDelete: undefined,
  };

/**
 * @typedef {Object} DataFormProps
 * @property {any} [dataDesc]
 * @property {Array<any>} fields
 * @property {Object} newData
 * @property {Function} setNewData
 * @property {Object} oldData
 * @property {any} [validateData]
 * @property {Function} onCancel
 * @property {Function} onSave
 * @property {Function} onDelete
 * @property {boolean} [formModeOnly]
 * @property {boolean} [embed]
 * @property {boolean} [readOnly]
 * @property {Array<any>} [extraActions]
 * @property {Object} [currentData]
 * @property {Array<any>} [dynamicSelectsConfig]
 */
const DataForm = ({
  dataDesc = dataDescInitialValues,
  fields,
  newData,
  setNewData,
  oldData,
  validateData = (_any) => true,
  onCancel,
  onSave,
  onDelete,
  formModeOnly,
  embed = false,
  readOnly = false,
  extraActions = [],
  currentData = {},
  dynamicSelectsConfig = [],
}) => {
  const [formState, setFormState] = useState({});
  const [dynamicSelects, setDynamicSelects] = useState({});
  const [showDelConfirm, setShowDelConfirm] = useState(false);

  const { data: { fintaryAdmin } = {} } = useUserInfo();

  const { dynamicSelectsData } = useInitDynamicSelects({
    data: [oldData],
    dynamicSelectsConfig,
  });

  const navigate = useNavigate();
  const [dirty, setDirty] = useState(false);
  const { userRole } = useRoleStore();

  const dirtyFn = useCallback(() => {
    const isChange = !isEqual(newData, oldData);
    setDirty(isChange);
    return isChange;
  }, [newData, oldData]);

  useBeforeUnload(dirtyFn, 'You have unsaved changes, are you sure?');
  useBeforeUnloadPage(dirty, 'You have unsaved changes, are you sure?');

  const {
    role: [role],
  } = useContext(UIStateContext);

  const dynamicSelectsPoster = API.getMutation('dynamic_selects', 'POST', {
    gcTime: 1,
  });

  const formattedData = newData;

  const fieldIds = fields.flat().map((field) => field.id);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const dynamicSelectVals = useMemo(
    () =>
      fields
        .flat()
        .filter((field) => field.type === 'dynamic-select' || field.table)
        .map((field) => ({
          table: field.table,
          queryParamValue: fieldIds.includes(field.queryParamValue)
            ? formattedData[field.queryParamValue]
            : field.queryParamValue,
          queryParamName: field.queryParamName,
        })),
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  May/2024
     * MISSED REFs: 'fieldIds' and 'fields'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [formattedData, JSON.stringify(fieldIds)]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (dynamicSelectVals.length > 0) getDynamicSelects(dynamicSelectVals);
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  May/2024
     * MISSED REFs: 'dynamicSelectVals' and 'getDynamicSelects'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [JSON.stringify(dynamicSelectVals)]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    fields.flat().forEach((field) => {
      if (
        field.type === FieldTypes.DYNAMIC_SELECT &&
        Array.isArray(formattedData[field.id]) &&
        formattedData[field.id]?.[0]?.id &&
        field.id !== 'parent_relationships'
      ) {
        setNewData({
          ...formattedData,
          [field.id]: formattedData[field.id].map((e) => e.id),
        });
      }
    });
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Feb/2024
     * MISSED REFs: 'fields', 'formattedData', and 'setNewData'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dynamicSelects]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    return () => {
      dynamicSelectsPoster.abort();
    };
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Feb/2024
     * MISSED REFs: 'dynamicSelectsPoster'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getDynamicSelects = async (dynamicSelectVals) => {
    const data = await dynamicSelectsPoster
      .mutateAsync(dynamicSelectVals)
      // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      .catch((_err) => {});

    if (Array.isArray(data) && data.length > 0) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      data.forEach((field) => {
        setDynamicSelects((prev) => ({
          ...prev,
          ...field,
        }));
      });
    }
  };

  const getFieldElement = useCallback(
    (field) => {
      if (field.type === FieldTypes.DIVIDER) {
        return field.access === SystemRoles.ADMIN ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'row',
              alignItems: 'center',
              mb: 1,
            }}
            key={field.id}
          >
            🔒
            <Box sx={{ width: '100%', ml: 1 }}>
              <Divider />
            </Box>
          </Box>
        ) : (
          <Divider key={field.id} sx={{ mt: 1, mb: 1 }} />
        );
      }
      if (field.type === FieldTypes.HEADING) {
        return (
          <Box key={field.id ?? field.label} sx={{ mb: 0.5 }}>
            <Typography variant="h6" sx={{ fontWeight: 400, fontSize: 18 }}>
              {field.label}
            </Typography>
          </Box>
        );
      }
      if (field.type === FieldTypes.SUB_HEADING) {
        return (
          <Box key={field.id ?? field.label} sx={{ mb: 1 }}>
            <Typography
              variant="h6"
              sx={{ fontWeight: 400, fontSize: 14, whiteSpace: 'nowrap' }}
            >
              {field.label}
            </Typography>
          </Box>
        );
      }
      if (field.type === FieldTypes.BOOLEAN) {
        const titleTooltip =
          typeof field.tip === 'function'
            ? field.tip?.(formattedData?.[field.id] ?? false, formattedData)
            : field.tip;
        return (
          <FieldRow key={field.id}>
            <Box sx={{ mb: 0, ml: 1 }}>
              <Tooltip title={titleTooltip} placement="top-start">
                <FormControlLabel
                  control={
                    <Checkbox
                      disabled={
                        readOnly ||
                        (typeof field.readOnly === 'function'
                          ? field.readOnly(newData)
                          : field.readOnly) ||
                        formattedData?.[`${field.id}-null`]
                      }
                      checked={formattedData?.[field.id] ?? false}
                      onChange={() =>
                        setNewData({
                          ...formattedData,
                          [field.id]: !formattedData?.[field.id],
                        })
                      }
                    />
                  }
                  label={field.label}
                />
              </Tooltip>
            </Box>
            {field.enableNullCheckbox && (
              <NullCheckbox
                readOnly={readOnly}
                field={field}
                formattedData={formattedData}
                setNewData={setNewData}
                isBulkUpdate={field.isBulkUpdate ?? false}
              />
            )}
          </FieldRow>
        );
      }
      if (field.type === FieldTypes.DRAGGABLE_SELECT) {
        return (
          <FieldRow key={field.id}>
            <DraggableSelect data={field} field={newData} setter={setNewData} />
          </FieldRow>
        );
      }
      if (field.type === FieldTypes.SELECT) {
        return (
          <FieldRow key={field.id}>
            <Tooltip title={field.tip} placement="right">
              <FormControl
                key={field.id}
                sx={{ width: '100%' }}
                required={field.required}
                disabled={
                  readOnly ||
                  (typeof field.readOnly === 'function'
                    ? field.readOnly(newData)
                    : field.readOnly)
                }
              >
                <InputLabel id={`${field.id}-label`}>{field.label}</InputLabel>
                <Select
                  labelId={`${field.id}-label`}
                  id={field.id}
                  open={formState?.[field.id] || false}
                  onClose={() =>
                    setFormState({ ...formState, [field.id]: false })
                  }
                  endAdornment={
                    typeof field.endAdornment === 'function'
                      ? field.endAdornment(formattedData, field, setNewData)
                      : field.endAdornment
                  }
                  multiple={field.multiple ?? false}
                  onOpen={() =>
                    setFormState({ ...formState, [field.id]: true })
                  }
                  value={
                    Array.isArray(formattedData?.[field.id])
                      ? formattedData?.[field.id]
                      : field.multiple
                        ? []
                        : (formattedData?.[field.id] ?? field.default ?? '')
                  }
                  label={field.label}
                  onChange={(e) => {
                    setNewData({
                      ...formattedData,
                      [field.id]: e.target.value,
                    });
                  }}
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {field.multiple
                        ? selected?.map((value) => (
                            <Chip
                              key={
                                typeof field.optionValuer === 'function'
                                  ? field.optionValuer(value)
                                  : value
                              }
                              label={
                                typeof field.optionFormatter === 'function'
                                  ? field.optionFormatter(value)
                                  : value
                              }
                              clickable={typeof field.linker === 'function'}
                              component={
                                typeof field.linker === 'function' ? 'a' : 'div'
                              }
                              href={
                                typeof field.linker === 'function'
                                  ? field.linker(value)
                                  : undefined
                              }
                            />
                          ))
                        : (field.options.find(
                            (option) => option.id === selected
                          )?.label ?? selected)}
                    </Box>
                  )}
                >
                  {field.options
                    ?.sort((a, b) =>
                      field.sort === false ? undefined : a < b ? -1 : 1
                    )
                    ?.filter((option) => option !== null)
                    ?.map((option) =>
                      typeof option === 'object' ? (
                        <MenuItem value={option.id} key={option.id}>
                          {option.label === '' ? (
                            <span>&nbsp;</span>
                          ) : (
                            option.label
                          )}
                        </MenuItem>
                      ) : (
                        <MenuItem value={option} key={option}>
                          {option === '' ? <span>&nbsp;</span> : option}
                        </MenuItem>
                      )
                    )}
                </Select>
              </FormControl>
            </Tooltip>
            {field.enableNullCheckbox && (
              <NullCheckbox
                readOnly={readOnly}
                field={field}
                formattedData={formattedData}
                setNewData={setNewData}
                isBulkUpdate={field.isBulkUpdate ?? false}
              />
            )}
          </FieldRow>
        );
      }
      if (field.type === FieldTypes.DYNAMIC_SELECT) {
        if (
          field.id === 'contacts_agent_commission_schedule_profiles' ||
          field.id === 'contacts_agent_commission_schedule_profiles_sets' ||
          field.id === 'contacts_agent_commission_profiles_names' ||
          field.id === 'parent_relationships'
        )
          return field.render(
            field,
            newData,
            setNewData,
            dynamicSelects[field.table]?.data ?? dynamicSelects[field.table]
          );
        return (
          <FieldRow key={field.id}>
            {/* @ts-ignore */}
            <Tooltip title={field.tip} placement="right">
              <DynamicSelect
                field={field}
                readOnly={readOnly}
                formattedData={formattedData}
                setNewData={setNewData}
                formState={formState}
                endAdornment={field.endAdornment}
                setFormState={setFormState}
                dynamicSelects={dynamicSelects}
                disabled={formattedData?.[`${field.id}-null`]}
                fullWidth={true}
              />
              {field.enableNullCheckbox && (
                <NullCheckbox
                  readOnly={readOnly}
                  field={field}
                  formattedData={formattedData}
                  setNewData={setNewData}
                  isBulkUpdate={field.isBulkUpdate ?? false}
                />
              )}
            </Tooltip>
          </FieldRow>
        );
      }
      if (field.type === FieldTypes.DATE_RANGE) {
        return (
          <FieldRow key={`${field.startDateId}-${field.endDateId}`}>
            <BasicDateRangePicker
              range={{
                startDate: formattedData?.[field.startDateId]
                  ? dayjs.utc(new Date(formattedData?.[field.startDateId]))
                  : null,
                endDate: formattedData?.[field.endDateId]
                  ? dayjs.utc(new Date(formattedData?.[field.endDateId]))
                  : null,
                startDateLabel: field.startDateLabel || 'Start Date',
                endDateLabel: field.endDateLabel || 'End Date',
                disableStartDatePicker:
                  readOnly ||
                  (typeof field.readOnly === 'function'
                    ? field.readOnly(newData)
                    : field.readOnly) ||
                  formattedData?.[`${field.startDateId}-null`],
                disableEndDatePicker:
                  readOnly ||
                  (typeof field.readOnly === 'function'
                    ? field.readOnly(newData)
                    : field.readOnly) ||
                  formattedData?.[`${field.endDateId}-null`],
                endAdornmentStartDate:
                  typeof field.endAdornment === 'function'
                    ? field?.endAdornment(formattedData, field, setNewData)
                    : field.endAdornment,
                endAdornmentEndDate:
                  typeof field.endAdornment === 'function'
                    ? field?.endAdornment(formattedData, field, setNewData)
                    : field.endAdornment,
              }}
              onChange={({ startDate, endDate }) => {
                setNewData({
                  ...formattedData,
                  [field.startDateId]: startDate
                    ? dayjs.isDayjs(startDate)
                      ? startDate.toDate()
                      : new Date(startDate)
                    : null,
                  [field.endDateId]: endDate
                    ? dayjs.isDayjs(endDate)
                      ? endDate.toDate()
                      : new Date(endDate)
                    : null,
                });
              }}
              width={200}
              mt={1}
              my={1}
            />
            {field.enableNullCheckbox && (
              <NullCheckbox
                readOnly={readOnly}
                field={field}
                formattedData={formattedData}
                setNewData={setNewData}
                isBulkUpdate={field.isBulkUpdate ?? false}
              />
            )}
          </FieldRow>
        );
      }
      if (field.type === FieldTypes.DATE) {
        return (
          <FieldRow key={field.id}>
            <BasicDatePicker
              label={field.label}
              value={
                formattedData?.[field.id]
                  ? new Date(formattedData?.[field.id])
                  : null
              }
              setValue={(e) => {
                setNewData({
                  ...formattedData,
                  [field.id]: e,
                });
              }}
              disabled={
                readOnly ||
                (typeof field.readOnly === 'function'
                  ? field.readOnly(newData)
                  : field.readOnly) ||
                formattedData?.[`${field.id}-null`]
              }
              endAdornment={
                typeof field.endAdornment === 'function'
                  ? field?.endAdornment(formattedData, field, setNewData)
                  : field.endAdornment
              }
            />
            {field.enableNullCheckbox && (
              <NullCheckbox
                readOnly={readOnly}
                field={field}
                formattedData={formattedData}
                setNewData={setNewData}
                isBulkUpdate={field.isBulkUpdate ?? false}
              />
            )}
          </FieldRow>
        );
      }
      if (field.type === FieldTypes.FIELD_MATCHER) {
        return (
          <FieldMatcher
            key={field.id}
            value={newData?.[field.id] ?? []}
            setValue={(e) => {
              setNewData({
                ...newData,
                [field.id]: e,
              });
            }}
            hideUsePolicyData={
              typeof field.usePolicyData === 'function'
                ? // biome-ignore lint/correctness/useHookAtTopLevel: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  !field.usePolicyData(newData)
                : false
            }
            fields={
              typeof field.fieldGetter === 'function'
                ? field.fieldGetter(newData)
                : field.fields
            }
          />
        );
      }
      if (field.type === FieldTypes.RATE_SCHEDULE) {
        return (
          <RateScheduleAnnual
            key={field.id}
            value={newData?.[field.id] ?? []}
            setValue={(e) => {
              setNewData({
                ...newData,
                [field.id]: e,
              });
            }}
          />
        );
      }
      if (field.type === FieldTypes.SCHEDULE) {
        return (
          <RateScheduleAnnual
            key={field.id}
            value={newData?.[field.id] ?? []}
            scheduleOnly={true}
            setValue={(e) => {
              setNewData({
                ...newData,
                [field.id]: e,
              });
            }}
          />
        );
      }
      if (field.type === FieldTypes.CODE) {
        return (
          <Box sx={{ mb: 1 }} key={field.id}>
            <Typography
              variant="inherit"
              sx={{
                mb: 1,
              }}
            >
              {field.label}
            </Typography>
            <CodeMirror
              height="350px"
              value={newData?.[field.id] || defaultCode}
              width="100%"
              extensions={[javascript({ jsx: true })]}
              onChange={(e) => {
                setNewData({
                  ...newData,
                  [field.id]: e,
                });
              }}
            />
          </Box>
        );
      }
      if (field.type === FieldTypes.CUSTOM) {
        return (
          <FieldRow key={field.id}>
            {typeof field.render === 'function' ? (
              field.render(
                field,
                newData,
                setNewData,
                dynamicSelects[field.table]?.data ??
                  dynamicSelects[field.table],
                dynamicSelectsData
              )
            ) : (
              <>
                {/* Display a user-friendly error message */}
                <Typography color="error">
                  An error occurred while rendering the {field.id} field. Please
                  contact support.
                </Typography>
              </>
            )}
            {field.enableNullCheckbox && (
              <NullCheckbox
                readOnly={readOnly}
                field={field}
                formattedData={formattedData}
                setNewData={setNewData}
                isBulkUpdate={field.isBulkUpdate ?? false}
              />
            )}
          </FieldRow>
        );
      }
      if (field.type === FieldTypes.CODEMIRROR) {
        return (
          <FieldRow key={field.id}>
            <CodeMirror
              height="350px"
              value={newData?.[field.id]}
              width="100%"
              maxWidth="100%"
              extensions={[javascript({ jsx: true })]}
              onChange={(e) => {
                setNewData({
                  ...newData,
                  [field.id]: e,
                });
              }}
            />
          </FieldRow>
        );
      }
      return (
        <FieldComponent
          field={field}
          newData={newData}
          formattedData={formattedData}
          setNewData={setNewData}
          readOnly={
            readOnly ||
            (typeof field.readOnly === 'function'
              ? field.readOnly(newData)
              : field.readOnly)
          }
          navigate={navigate}
          oldData={oldData}
          key={field.id}
        />
      );
    },
    [
      newData,
      formattedData,
      setNewData,
      readOnly,
      navigate,
      oldData,
      formState,
      dynamicSelects,
      dynamicSelectsData,
    ]
  );

  if (!newData) return null;

  const fieldsWithValidationIssues = fields.filter(
    (field) =>
      (field.validator instanceof Function &&
        !field.validator(newData?.[field.id] || '')) ||
      (field.required && !newData?.[field.id])
  );

  const /** @type any */ onDeleteText = dataDesc?.onDeleteText;

  return (
    <Box sx={{ mt: 0.75, maxWidth: 1200, flexGrow: 1 }}>
      <BasicDialog
        title="Delete record?"
        positiveLabel="Delete"
        open={showDelConfirm}
        onClose={async (val) => {
          if (val) await onDelete();
          setShowDelConfirm(false);
        }}
        bodyComponent={
          <Alert severity="warning">
            Are you sure you want to delete this record?
            <br />
            {onDeleteText ? onDeleteText?.toString() : ''}
          </Alert>
        }
      />
      {/* =============== Main form ===============*/}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          pb: 3,
        }}
      >
        {!!fields.length &&
          fields.some((field) => field.enableNullCheckbox) && (
            <FieldRow>
              <Typography variant="caption">New value</Typography>
              <Typography variant="caption" sx={{ textAlign: 'right' }}>
                Clear data
              </Typography>
            </FieldRow>
          )}
        {fields.map((field) => {
          if (Array.isArray(field)) {
            return (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'row',
                }}
                key={field[0].id}
              >
                {field.map((subField, i) => {
                  if (
                    !hasAccess(
                      subField.access,
                      userRole,
                      role === SystemRoles.ADMIN
                    )
                  ) {
                    return null;
                  }
                  return (
                    <Box
                      sx={
                        i === field.length - 1
                          ? { width: '100%' }
                          : { width: '100%', mr: 1 }
                      }
                      key={subField.id}
                    >
                      {getFieldElement(subField)}
                    </Box>
                  );
                })}
              </Box>
            );
          } else {
            if (
              !hasAccess(field.access, userRole, role === SystemRoles.ADMIN)
            ) {
              return null;
            }
            return getFieldElement(field);
          }
        })}
      </Box>
      <DataFormActions
        currentData={currentData}
        dataDesc={dataDesc}
        extraActions={extraActions}
        fieldsWithValidationIssues={fieldsWithValidationIssues}
        formModeOnly={formModeOnly}
        newData={newData}
        onCancel={onCancel}
        onSave={onSave}
        readOnly={readOnly}
        setNewData={setNewData}
        setShowDelConfirm={setShowDelConfirm}
        validateData={validateData}
      />
      {!!fintaryAdmin && !embed && <DebugInfo oldData={oldData} />}
    </Box>
  );
};

export default memo(DataForm);
