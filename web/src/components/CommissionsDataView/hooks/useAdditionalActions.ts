import { useCallback } from 'react';
import { AgentCommissionsStatuses } from 'common/globalTypes';

import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';
import { useCreatePolicy } from './useCreatePolicy';
import { useAdvanceSchedule } from '@/hooks/useAdvanceSchedule';

export const useAdditionalActions = () => {
  const { showSnackbar } = useSnackbar();
  const policyRatesPatcher = API.getMutation(
    'report_data/update-payout-rates',
    'PATCH'
  );
  const { handleCreatePolicy } = useCreatePolicy();
  const { handleAdvanceSchedule, handleStopAdvanceSchedule } =
    useAdvanceSchedule();

  const getAdditionalActions = useCallback(
    ({
      setSelectedStatment,
      setShowReconcile,
    }: {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      setSelectedStatment: (row: any) => void;
      setShowReconcile: (show: boolean) => void;
    }) => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const actions: any[] = [
        {
          id: 'reconcile',
          label: 'Manual reconcile',
          enabled: (row) =>
            ![
              AgentCommissionsStatuses.PAID,
              AgentCommissionsStatuses.APPROVED,
              AgentCommissionsStatuses.MANUAL,
            ].includes(row.agent_commissions_status),
          onClick: (row) => {
            setSelectedStatment(row);
            setShowReconcile(true);
          },
        },
        {
          id: 'advance-schedule',
          label: 'Advance schedule',
          enabled: (row) =>
            row?.report?.config?.advanced_commission_schedules !== false,
          onClick: (row) => handleAdvanceSchedule(row.report_data_id),
        },
        {
          id: 'stop advance-schedule',
          label: 'Stop advance schedule',
          enabled: (row) =>
            row?.report?.config?.advanced_commission_schedules !== false,
          onClick: (row) => handleStopAdvanceSchedule(row.report_data_id),
        },
        {
          id: 'update_policy_payout_rates',
          label: 'Update policy with payout rates',
          onClick: async (row) => {
            try {
              const response = await policyRatesPatcher.mutateAsync({
                statement_id: row.id,
              });
              if (response) {
                showSnackbar(
                  'Policy payout rates updated successfully',
                  'success'
                );
              }
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : JSON.stringify(error);
              showSnackbar(errorMessage, 'error');
            }
          },
        },
        {
          id: 'create_policy',
          label: 'Create policy',
          onClick: (row) => {
            handleCreatePolicy(row);
          },
        },
      ];

      return actions;
    },
    [
      handleCreatePolicy,
      policyRatesPatcher,
      showSnackbar,
      handleAdvanceSchedule,
      handleStopAdvanceSchedule,
    ]
  );

  return {
    getAdditionalActions,
  };
};
