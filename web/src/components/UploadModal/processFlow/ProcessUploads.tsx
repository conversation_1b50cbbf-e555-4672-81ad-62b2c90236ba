import { Box } from '@mui/material';
import * as Sentry from '@sentry/react';
import { Allotment } from 'allotment';
import 'allotment/dist/style.css';
import { ImportMethod, ProcessMethod } from 'common/globalTypes';
import { tool } from 'common/tools';
import { nanoid } from 'nanoid';
import type React from 'react';
import {
  forwardRef,
  useCallback,
  useContext,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useBeforeUnload } from 'react-use';

import ProcessPreview from '@/components/UploadModal/ProcessPreview';
import CommissionMapper from '@/components/UploadModal/processFlow/CommissionMapper';
import useCommonData from '@/components/UploadModal/processFlow/hoc/useCommonData';
import useFileToData from '@/components/UploadModal/processFlow/hoc/useFileToData';
import {
  DocumentTypeE,
  type ErrorMsg,
  ProcessMethodE,
  type ProcessorFormatModel,
  type SpreadSheetProps,
  type ProcessFormModel,
} from '@/components/UploadModal/processFlow/process';
import { LoadingContext } from '@/contexts/LoadingContext';
import useAutoPolling from '@/contexts/useAutoPolling';
import useBeforeUnloadPage from '@/contexts/useBeforeunloadPage';
import useExtraction from '@/contexts/useExtraction';
import useWindowSize from '@/contexts/useWindowSize';
import API from '@/services/API';
import DataTransformation from '@/services/DataTransformation';
import type { DocumentProcessTypes } from '@/types';
import StickyTips from './StickyTips';
import { ProcessSuccessStatus } from './config';
import type { IDocumentModel, IMappingModel, IProcessorModel } from './model';
import useCommissionStore from './stores/useCommissionStore';

const Normalizer = DataTransformation;

interface ProcessUploadsProps {
  rowData: Partial<IDocumentModel>;
  setRowData: React.Dispatch<React.SetStateAction<Partial<IDocumentModel>>>;
  mpData: {
    mappings: IMappingModel[];
    processors: IProcessorModel[];
  };
  actionCount: number;
  setActionCount: React.Dispatch<React.SetStateAction<number>>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  timerCountRef: any;
  feedback: string;
}

const ProcessUploads = (
  {
    rowData,
    mpData,
    setRowData,
    actionCount,
    setActionCount,
    timerCountRef,
    feedback,
  }: ProcessUploadsProps,
  ref
) => {
  const setRowMapping = useCommissionStore((s) => s.setRowMapping);
  const [processForm, setProcessForm] = useState<ProcessFormModel>({
    mapping: 0,
    newMappingCarrier: null,
    newMappingName: '',
    createMapping: false,
    processor: '',
    prompt: '',
    promptText: '',
    method: '',
    fileName: '',
    fileType: DocumentTypeE.Statement,
    selectedSheet: '',
  });

  const tipRef = useRef<{ ignoreErrors: boolean }>(null);

  const [fileType, setFileType] = useState<DocumentTypeE>(
    DocumentTypeE.Statement
  );

  const [spreadsheet, setSpreadsheet] = useState<SpreadSheetProps | null>();
  const [selectedSheet, setSelectedSheet] = useState('');
  const [startPolling, setStartPolling] = useState(false);
  const [pollingParams, setPollingParams] = useState({
    jobId: '',
    uploadId: rowData.id,
  });

  const [errors, setErrors] = useState<ErrorMsg>({});
  const [processActionMap, setProcessActionMap] = useState({});

  const [processFormatData, setProcessFormatData] =
    useState<ProcessorFormatModel>({
      mappingOptions: {},
      cmsTotal: '',
      data: [],
      rowData: [],
    });
  const { loadingConfig, setLoadingConfig } = useContext(LoadingContext);
  const { data: prompts } = API.getBasicQuery('prompts');
  const { data: agents = [] } = API.getBasicQuery('contacts');
  const validAgentIds: Set<string> = new Set(
    Array.isArray(agents.data) ? agents.data.map((agent) => agent.str_id) : []
  );

  useBeforeUnload(
    loadingConfig.loading,
    'You have unsaved changes, are you sure you want to leave?'
  );
  useBeforeUnloadPage(
    loadingConfig.loading,
    'You have unsaved changes, are you sure you want to leave?'
  );

  const addActionCount = useCallback(
    (type?: DocumentProcessTypes) => {
      setActionCount(actionCount + 1);
      if (!type) return;

      setProcessActionMap((prev) => {
        return {
          ...prev,
          [type]: (prev[type] ?? 0) + 1,
        };
      });
    },
    [actionCount, setActionCount]
  );

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    setLoadingConfig({
      loading: startPolling,
      message: 'Processing document...',
      allowClose: true,
    });
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Jun/2024
     * MISSED REFs: 'setLoadingConfig'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [startPolling]);

  const pollingFetchExtractTable = async () => {
    const extractionOption = rowData.method?.split('::') || [];
    const extractionId =
      extractionOption.length > 1 ? extractionOption.pop() : '';
    const extractionStrId = `jobId=${pollingParams.jobId}&uploadId=${pollingParams.uploadId}&extractionId=${extractionId}`;
    try {
      const url = `${process.env.REACT_APP_API}/api/documents/extractData?${extractionStrId}`;
      const _res = await fetch(url, {
        method: 'GET',
        headers: await API.getHeaders(),
      });
      const res = await _res.json();
      if (res.error || res.type === 'expired') {
        setErrors({
          ...errors,
          pollingRow: res.message || res.error,
        });
        setStartPolling(false);
        Sentry.captureException(res.error || res.message);
        return null;
      }
      return res;
    } catch (e) {
      setErrors({
        ...errors,
        pollingRow: `Error uploading file (${
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (e as unknown as any).message
        })`,
      });
      setStartPolling(false);
      Sentry.captureException(e);
      return null;
    }
  };

  const checkDataCondition = (data) => {
    const successList = ProcessSuccessStatus;
    if (successList.includes(data?.JobStatus)) {
      setErrors({
        ...errors,
        pollingRow: '',
      });
      return true;
    }
    return false;
  };

  const { data: extractTableData } = useAutoPolling(
    pollingFetchExtractTable,
    checkDataCondition,
    8000,
    startPolling
  );
  const { width: winWidth } = useWindowSize();
  const [previewWidth, setPreviewWidth] = useState(winWidth * 0.25);

  const { fileData, file, setFile } = useFileToData(rowData, processForm);
  const { fields } = useCommonData(fileType, file);
  // Extract table hook
  const {
    setExtraction,
    setExtractionMethod,
    extractTableJson,
    extractTableRaw,
  } = useExtraction();

  const mappingsPoster = API.getMutation('mappings', 'POST');
  const documentsPatcher = API.getMutation('documents', 'PATCH');
  const statementDataPoster = API.getMutation('statement_data', 'POST');
  const reportDataPoster = API.getMutation('report_data', 'POST');

  const { data: extractionData = [], isLoading: extractionLoading } =
    API.getBasicQuery('extractions', `document_id=${rowData.id}`, !!rowData.id);

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const setDataToSpreetSheet = (data: any[][][]) => {
    if (data && data.length > 0) {
      const newSpreadsheet = {
        getSheets: () => data.map((_e, i) => `${i}`),
        getJson: (i) => data?.[i] ?? [],
      };
      setSpreadsheet(newSpreadsheet);
    }
  };

  /** Polling result */
  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    const doAction = async () => {
      if (extractTableData && !extractionLoading) {
        setStartPolling(false);
        setExtractionMethod('extractTable');
        setExtraction(JSON.stringify(extractTableData));
        setRowData(() => {
          const method = `extractTable::${extractionData[0]?.str_id ?? ''}::${
            extractionData[0]?.id ?? ''
          }`;
          return {
            ...rowData,
            extractions: extractionData,
            method,
          };
        });
        setErrors({
          ...errors,
          dataRows: '',
          expiredRow: '',
          pollingRow: '',
        });
      }
    };
    doAction();
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  July/2024
     * MISSED REFs:  'errors', 'rowData', 'setExtraction', 'setExtractionMethod'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [extractTableData, extractionData, extractionLoading]);

  useEffect(() => {
    if (processForm.prompt && prompts) {
      const target = prompts.find((item) => item.str_id === processForm.prompt);
      if (target?.prompt) {
        setProcessForm((prev) => {
          return {
            ...prev,
            promptText: target.prompt,
          };
        });
      }
    }
  }, [processForm.prompt, prompts]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (!fileData) {
      return;
    }
    if (fileData.error) {
      setErrors({
        ...errors,
        dataRows: fileData.error,
      });
    } else {
      const { data, type } = fileData;
      if (type === 'spreadsheet') {
        setSpreadsheet(data as unknown as SpreadSheetProps);
      } else if (type === 'documentAI') {
        setDataToSpreetSheet(data.table);
        setExtractionMethod('documentAI');
        setExtraction(data);
      } else if (type === 'extractTable') {
        // Check the document is processing or not
        const parseData = JSON.parse(data);
        if (parseData?.JobStatus === 'Processing') {
          setErrors({
            ...errors,
            pollingRow:
              'This document is taking a while to process. You can continue to wait or leave this page and check back later. Document extractions usually finish within a couple of minutes.',
          });

          setPollingParams({
            jobId: parseData.JobId,
            uploadId: rowData.id,
          });
          setStartPolling(true);
          return;
        }

        if (parseData?.ProTip || parseData?.message) {
          setErrors({
            ...errors,
            expiredRow: `${parseData.ProTip} (${parseData.message})`,
          });
          return;
        }

        setExtractionMethod('extractTable');
        setExtraction(data);
        const getExtraction = async () => {
          setRowData(() => {
            const method = `extractTable::${extractionData[0]?.str_id ?? ''}::${
              extractionData[0]?.id ?? ''
            }`;
            return {
              ...rowData,
              extractions: extractionData,
              method,
            };
          });
        };
        if (
          rowData.method &&
          !rowData.method.includes('::') &&
          !extractionLoading
        ) {
          getExtraction();
        }
        setErrors({
          ...errors,
          dataRows: '',
          expiredRow: '',
        });
      } else if (type === 'adobeExtract') {
        setDataToSpreetSheet(data);
        setExtractionMethod('adobeExtract');
        setExtraction(data);
      } else if (type === 'nanonets') {
        setExtractionMethod('nanonets');
        setExtraction(data);
      } else if (type === 'htmlExtract') {
        setExtractionMethod('htmlExtract');
        setExtraction(data);
      } else if (type === 'gemini') {
        setProcessForm((pre) => {
          return {
            ...pre,
            method: type,
          };
        });
      }
    }
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  July/2024
     * MISSED REFs: 'errors', 'extractionData', 'rowData', 'setExtraction', 'setExtractionMethod', and 'setRowData'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileData, extractionLoading]);

  /**
   * Set extract table json to spreadsheet
   */

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    if (extractTableJson) {
      const formatData = [JSON.parse(extractTableJson)];
      setDataToSpreetSheet(formatData);
    }
  }, [extractTableJson]);

  useEffect(() => {
    if (rowData.type) {
      setFileType(rowData.type as DocumentTypeE);
    }
  }, [rowData]);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEffect(() => {
    // Filter the mapping value which it's name includes company name from the rowData
    const targetMapping = mpData.mappings?.find((mapping) => {
      return mapping.carrier?.str_id === rowData.company_str_id;
    });
    let _method = rowData.method as string;
    if (_method) {
      const isGemini = _method?.includes('gemini');
      const isExtractTable = _method?.includes('extractTable');
      const isMapping = _method?.includes('mapping');
      const isAdobePDFExtract = _method?.includes('adobeExtract');
      const isNanonets = _method?.includes('nanonets');
      const isHtmlExtract = _method?.includes('htmlExtract');

      if (isMapping) _method = ProcessMethodE.Mapping;
      if (isExtractTable || isAdobePDFExtract || isNanonets || isHtmlExtract)
        _method = ProcessMethodE.Processor;
      if (isGemini) _method = ProcessMethodE.Gemini;
    }

    setProcessForm((prev) => ({
      ...prev,
      fileName: file?.name || '',
      selectedSheet,
      mapping: targetMapping ? targetMapping.str_id : 0,
      newMappingName:
        prev.newMappingName ||
        (rowData.companies ? `${rowData.companies.company_name} mapping` : ''),
      method: _method,
      prompt: rowData.prompt,
      newMappingCarrier: rowData.companies ? rowData.companies : 0,

      fileType:
        fileType === DocumentTypeE.Statement
          ? 'Commission Statement'
          : 'Policy Report',
    }));
    /*
     * Lint disabled due to uncertain impact on adding missed references.
     *
     * WARN SINCE:  Jun/2024
     * MISSED REFs: 'rowData.companies', 'rowData.company_str_id'
     */
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [file, fileType, selectedSheet, mpData.mappings]);

  //* ****************************Upload mapping start************************************/
  const uploadMapping = async () => {
    try {
      // Upload
      if (file) {
        // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        let mapping;
        if (processForm.mapping === 0 && processForm.createMapping) {
          const newMapping = {
            name: processForm.newMappingName,
            type: fileType,
            carrier_id:
              processForm.newMappingCarrier &&
              typeof processForm.newMappingCarrier !== 'number'
                ? processForm.newMappingCarrier?.str_id
                : processForm.newMappingCarrier,
            mapping: processFormatData.mappingOptions,
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          } as any;
          const res = await mappingsPoster.mutateAsync(newMapping);
          if (res.error) {
            setErrors({
              ...errors,
              upload: `Error uploading mapping (${res.error})`,
            });
            Sentry.captureException(res.error);
            return null;
          }
          mapping = res.str_id;
        } else if (processForm.mapping === 0 && !processForm.createMapping) {
          mapping = null;
        } else {
          mapping = processForm.mapping;
        }
        return mapping;
      }

      return null;
    } catch (e) {
      setErrors({
        ...errors,
        upload: `Error uploading mapping (${
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (e as unknown as any).message
        })`,
      });
      Sentry.captureException(e);
      return null;
    }
  };

  const bulkUpload = async ({ mapping }) => {
    try {
      const importId = nanoid();
      const mappingList = Object.keys(processFormatData.mappingOptions);
      const formatFields: string[] = [];
      const updatedValidations: Record<string, boolean> = {
        ...(rowData.validations || {}),
      };
      if (updatedValidations) {
        for (const [key, value] of Object.entries(updatedValidations)) {
          if (value === true) {
            updatedValidations[key] = false;
          }
        }
      }
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      mappingList.forEach((k) => {
        const targetField = fields[k];
        if (
          targetField?.normalizer &&
          typeof targetField.normalizer === 'function'
        ) {
          formatFields.push(k);
        }
      });

      const copyData = JSON.parse(JSON.stringify(processFormatData.rowData));
      const normalizedData = copyData.map((datum) => {
        const processedDatum = { ...datum };

        // Format value
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.keys(processedDatum).forEach((k) => {
          if (formatFields?.includes(k)) {
            processedDatum[k] = fields[k].normalizer?.(processedDatum[k]);
          }
        });

        if (
          processedDatum.contacts &&
          typeof processedDatum.contacts === 'string'
        ) {
          processedDatum.contacts = processedDatum.contacts.split(',');
        }

        if (processedDatum.commission_rate) {
          processedDatum.new_commission_rate =
            tool.convertToNumber(processedDatum.commission_rate) / 100;
        }

        if (processedDatum.carrier_rate) {
          processedDatum.new_carrier_rate =
            tool.convertToNumber(processedDatum.carrier_rate) / 100;
        }

        if (
          processedDatum.tags != null &&
          !Array.isArray(processedDatum.tags)
        ) {
          processedDatum.tags = [processedDatum.tags];
        }

        const jsonFields = {
          agent_commissions: Normalizer.normalizeCurrency,
          agent_payout_rate: Normalizer.normalizePercentage,
          agent_commission_payout_rate: Normalizer.normalizePercentage,
        };

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.entries(jsonFields).forEach(([field, normalizer]) => {
          if (processedDatum[field]) {
            processedDatum[field] = Normalizer.normalizeJsonValues(
              processedDatum[field],
              normalizer,
              validAgentIds
            );

            if (field === 'agent_commissions') {
              const jsonObj = JSON.parse(processedDatum[field]);

              let total = 0;
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              Object.entries(jsonObj).forEach(([key, val]) => {
                if (key !== 'total' && typeof val === 'number') {
                  total += val;
                }
              });

              jsonObj.total = total;
              processedDatum[field] = JSON.stringify(jsonObj);
            }
          }
        });

        return {
          ...processedDatum,
          type: fileType,
          document_id: rowData.str_id,
          import_id: importId,
        };
      });

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const resList: any[] = [];
      if (fileType === 'statement') {
        for (let i = 0; i < normalizedData.length; i += 1000) {
          const chunk = normalizedData.slice(i, i + 1000);

          const res = await statementDataPoster.mutateAsync(chunk);
          resList.push(res);
          if (res.error) {
            setErrors({
              ...errors,
              upload: `Error uploading data (${res.error})`,
            });
            Sentry.captureException(res.error);
            return null;
          }
        }
        // Await statementDataPoster.mutateAsync(dataArray);
      } else if (fileType === 'report') {
        for (let i = 0; i < normalizedData.length; i += 1000) {
          const chunk = normalizedData.slice(i, i + 1000);

          const res = await reportDataPoster.mutateAsync(chunk);
          resList.push(res);
          if (res.error) {
            setErrors({
              ...errors,
              upload: `Error uploading data (${res.error})`,
            });
            Sentry.captureException(res.error);
            return null;
          }
        }
      } else {
        throw new Error(`Unsupported file type: ${fileType}`);
      }
      const filename = rowData.override_filename || rowData.filename;
      const notes =
        fileType === 'statement' ? 'New statement data' : 'New report data';
      const allSuccess = resList.every((res) => res.status === 'OK');

      const processDuration = (timerCountRef.current?.totalSeconds || 0) * 1000;

      const processingLogParams = {
        type: 'document_processing',
        duration: processDuration,
        status: allSuccess ? 'completed' : 'failed',
        params: filename,
        notes,
        stats: {
          count: normalizedData.length,
        },
      };

      const metadata = {
        status: allSuccess ? 'Success' : 'Failed',
        count: normalizedData.length,
      };
      const importsData = {
        process_duration: processDuration,
        summed_total_amount: processFormatData.cmsTotal,
        count: normalizedData.length,
        type: fileType,
        document_str_id: rowData.str_id,
        company_str_id: rowData?.companies?.str_id,
        str_id: importId,
        metadata,
        status: allSuccess ? 'Success' : 'Failed',
        feedback: feedback,
        process_count: actionCount,
        process_action_records: Object.keys(processActionMap).length
          ? processActionMap
          : null,
        processor_str_id: processForm.processor || null,
        mapping_str_id: mapping || null,
        profile_str_id: rowData.profile_str_id || null,
        import_method: ImportMethod.MANUAL,
      };

      const params = {
        id: rowData.id,
        company_str_id: rowData?.companies?.str_id,
        mapping,
        processor: processForm.processor,
        prompt: processForm.prompt,
        type: fileType,
        method: processForm.method,
        state: 'active',

        status: 'processed',
        process_method: ProcessMethod.MANUAL,
        import_id: importId,
        imported_at: new Date().toISOString(),

        processing_log: processingLogParams,
        imports_log: importsData,
        validations: updatedValidations,
      };
      const resp = await documentsPatcher.mutateAsync(params);
      if (resp.error) {
        setErrors({
          ...errors,
          upload: `Error uploading data (${resp.error})`,
        });
        Sentry.captureException(resp.error);
        return null;
      }
      return normalizedData;
    } catch (e) {
      setErrors({
        ...errors,
        upload: `Error uploading data (${
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (e as unknown as any).message
        })`,
      });
      Sentry.captureException(e);
    }
  };

  const submit = async () => {
    if (errors.missingField && !tipRef.current?.ignoreErrors) {
      return {
        error: errors.missingField,
      };
    }
    const mappingStrId = await uploadMapping();
    const res = await bulkUpload({ mapping: mappingStrId });

    if (!res) {
      return;
    }
    setExtraction('');
    setFile(null);
    return !!res;
  };
  //* ****************************Upload mapping end************************************/

  const onDragFinished = (size) => {
    setPreviewWidth(size[0]);
  };

  useImperativeHandle(ref, () => ({
    submit,
  }));

  // Reset row mapping on loading new filed
  useEffect(() => {
    setRowMapping({}, true);
  }, [setRowMapping]);
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
        position: 'relative',
        height: '100%',
      }}
    >
      <Box sx={{ flex: 1 }}>
        <Allotment
          onDragEnd={onDragFinished}
          className="flex flex-1 h-full"
          defaultSizes={[25, 75]}
        >
          <Allotment.Pane>
            {previewWidth ? (
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  height: '100%',
                }}
              >
                <Box sx={{ flex: 1, overflow: 'auto' }}>
                  <ProcessPreview
                    previewWidth={previewWidth}
                    rowData={rowData}
                    processForm={processForm}
                  />
                </Box>
              </Box>
            ) : null}
          </Allotment.Pane>
          <Allotment.Pane className="h-full flex flex-1">
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                overflow: 'auto',
                height: '100%',
                ml: 2,
              }}
            >
              <Box
                sx={{
                  flex: 1,
                  overflow: 'auto',
                }}
              >
                <CommissionMapper
                  errors={errors}
                  setErrors={setErrors}
                  file={file}
                  fileType={fileType}
                  mpData={mpData}
                  rowData={rowData}
                  processForm={processForm}
                  setProcessForm={setProcessForm}
                  extraction={extractTableRaw}
                  selectedSheet={selectedSheet}
                  setSelectedSheet={setSelectedSheet}
                  spreadsheet={spreadsheet}
                  setProcessFormatData={setProcessFormatData}
                  processFormatData={processFormatData}
                  fileData={fileData}
                  addActionCount={addActionCount}
                />
              </Box>
              <Box>
                <StickyTips
                  tipsMap={errors}
                  data={processFormatData}
                  ref={tipRef}
                />
              </Box>
            </Box>
          </Allotment.Pane>
        </Allotment>
      </Box>
    </Box>
  );
};

export default forwardRef(ProcessUploads);
