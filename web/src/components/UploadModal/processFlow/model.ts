interface IDocumentCompanies {
  id: number;
  str_id: string;
  company_name: string;
  account_id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  alias_list: any[];
}

interface IDocumentExtractions {
  id: number;
  str_id: string;
  created_at: string;
  method: string;
  status: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  output_format: any;
}

interface IDocumentUserModel {
  first_name: string;
  last_name: string;
  id: number;
  str_id: string;
}

interface IDocumentValidations {
  type?: boolean;
  company_str_id?: boolean;
  statement_amount?: boolean;
}

export interface IDocumentModel {
  id: number;
  str_id: string;
  created_at: string;
  created_by: string;
  filename: string;
  file_path: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  mapping: any;
  method: string;
  override_file_path: string;
  override_filename: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  override_mapping: any;
  processor: string;
  prompt: string;
  status: string;
  tag: string;
  type: string;
  file_type: string;
  company_str_id: string | null;
  profile_str_id: string;
  companies: IDocumentCompanies | null;
  extractions: IDocumentExtractions[];
  created_by_user: IDocumentUserModel;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_by_user: any;
  company_id: number;
  bank_total_amount: string;
  statement_amount: string;
  validations?: IDocumentValidations;
}

// ProcessFormModel

export interface ProcessFormModel {
  mapping: string | number | null;
  newMappingCarrier: IDocumentCompanies | number | null;
  newMappingName: string;
  processor: string;
  method: string;
  fileName: string;
  fileType: string;
  selectedSheet?: string;
  prompt?: string;
  promptText?: string;
  createMapping?: boolean;
}

// ICompanyModel
export interface ICompanyModel {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  access: string;
  created_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_proxied_by: any;
  updated_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_proxied_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  address: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  alias_list: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  company_id: any;
  company_name: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  email: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  group_id: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  sync_id: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  log: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  notes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  phone: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  type: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  website: any;
}

export interface IMappingModel {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  state: string;
  access: string;
  created_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  created_proxied_by: any;
  updated_at: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updated_proxied_by: any;
  carrier: ICompanyModel | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  carrier_id: any;
  mapping: {
    [key: string]: number;
  };
  name: string;
  type: string;
  modify_status: string;
}

interface IUsersProcessorsUserModel {
  first_name: string;
  last_name: string;
  id: number;
  str_id: string;
}

interface IProcessCompaniesModel {
  id: number;
  str_id: string;
  company_name: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  alias_list: any[];
}

export interface IProcessorModel {
  access: string;
  company_id: string | null;
  created_at: string;
  created_by: string;
  document_str_id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extractionsid: any;
  file_type: string;
  id: number;
  str_id: string;
  inner_name: string;
  method: string;
  name: string;
  notes: string;
  processor_status: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  reviewed_at: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  reviewed_by: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  reviewer_id: any;
  reviewer_str_id: string;
  state: string;
  status: string;
  owner: string;
  type: string;
  updated_at: string;
  updated_by: string;
  created_proxied_by: string;
  suggest_for: string;
  profile_str_id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  extractions: any;
  users_processors_created_byTousers: Partial<IUsersProcessorsUserModel> | null;
  users_processors_updated_byTousers: Partial<IUsersProcessorsUserModel> | null;
  users_processors_reviewed_byTousers: Partial<IUsersProcessorsUserModel> | null;
  companies: IProcessCompaniesModel;
}

export interface ProcessorSelectItem {
  value: string;
  label: string;
  company_name?: string;
  updated_at?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  [key: string]: any;
}

export interface DocumentProfileModel {
  id: number;
  str_id: string;
  uid: string;
  account_id: string;
  state: string;
  created_at: string;
  created_by: string;
  created_proxied_by: string;
  updated_at: string;
  updated_by: string;
  updated_proxied_by: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  description: any;
  carrier_name: string;
  paying_entity: string;
  file_link: string[];
  field_mapping: string;
  owner: string;
  status: string;
  priority: number;
  notes: string;
  processor_str_ids: string[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  mappings_str_ids: any[];
  document_str_ids: string[];
  create_type: string;
}

export interface LoadingConfig {
  loading: boolean;
  message?: string;
  allowClose?: boolean;
}

export interface UseExtractionHandlersProps {
  rowData: IDocumentModel;
  processForm?: import('./process').ProcessFormModel;
  setFileData: (data: import('./process').FileDataModel) => void;
  setExtraction: (data: string) => void;
  setExtractionMethod: (method: string) => void;
  setLoadingConfig: (config: LoadingConfig) => void;
  loadingConfig: LoadingConfig;
}

export interface UseFileToDataReturn {
  setFileData: (data: import('./process').FileDataModel) => void;
  fileData?: import('./process').FileDataModel;
  file: File | null;
  setFile: (file: File | null) => void;
}
