import * as math from 'mathjs';

// biome-ignore lint/suspicious/noShadowRestrictedNames: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import DataView from '@/components//DataView';
import API from '@/services/API';
import {
  CommissionScheduleType,
  PaymentDateBasis,
  PremiumAmountBasis,
} from 'common/constants/commission-schedule';

const dataDesc = {
  label: 'Advance commission receivable schedules',
  table: 'schedules/advance',
  editable: true,
  copyable: true,
  fields: [
    { id: 'name', label: 'Name' },
    {
      id: 'filters',
      type: 'heading',
      label: 'Filters',
    },
    [
      {
        id: 'company_ids',
        label: 'Carriers',
        type: 'dynamic-select',
        table: 'companies',
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        formatter: (val, collectionVals: any = []) =>
          (Array.isArray(collectionVals) &&
            collectionVals?.find((company) => company.id === val)
              ?.company_name) ||
          '',
        optionFormatter: (option) => option.company_name,
        optionValuer: (option) => option.id,
        required: true,
        multiple: true,
      },
      {
        id: 'product_type',
        label: 'Product type',
        type: 'select',
        options: [
          'Life',
          'IUL',
          'UL',
          'Term',
          'Annuity',
          'Indexed Annuity',
          'Fixed Annuity',
          'Health',
          'Dental',
          'Vision',
          'Disability',
          'Long Term Care',
          'Other',
        ],
      },
    ],
    [
      {
        id: 'payment_date_basis',
        label: 'Payment date basis',
        type: 'select',
        options: [
          // { id: PaymentDateBasisEnums.EFFECTIVE_DATE, label: 'Effective Date' },
          {
            id: PaymentDateBasis.FIRST_PAYMENT_DATE,
            label: 'First Payment Date',
          },
        ],
        optionFormatter: (option) => option.label,
        optionValuer: (option) => option.id,
      },
      {
        id: 'premium_amount_basis',
        label: 'Premium amount basis',
        type: 'select',
        options: [
          {
            id: PremiumAmountBasis.POLICY_TARGET_PREMIUM,
            label: 'Policy target premium',
          },
          {
            id: PremiumAmountBasis.POLICY_ANNUALIZED_REVENUE,
            label: 'Policy annualized revenue',
          },
          {
            id: PremiumAmountBasis.COMMISSION_PREMIUM_AMOUNT_12,
            label: 'Commission premium amount / 12',
          },
        ],
        optionFormatter: (option) => option.label,
        optionValuer: (option) => option.id,
      },
    ],

    {
      id: 'commission_schedules',
      type: 'heading',
      label: 'Commission schedules',
    },
    {
      id: 'schedule_type',
      label: 'Commission schedule type',
      type: 'select',
      options: [
        {
          id: CommissionScheduleType.NINE_MONTH,
          label: '75% Advance for 9 month',
        },
        {
          id: CommissionScheduleType.SIX_MONTH,
          label: '50% Advance for 6 month',
        },
        {
          id: CommissionScheduleType.CUSTOM,
          label: 'Custom',
        },
      ],
      optionFormatter: (option) => option.label,
      optionValuer: (option) => option.id,
    },
    {
      id: 'schedules',
      label: 'Commission rate schedule',
      type: 'schedule',
      enabled: (row) => row.schedule_type === 'custom',
      formatter: (val) =>
        `Year ${val.year}: ${val.rate}% (${val?.breakdown?.join(', ') ?? '-'})${
          val?.max_commission ? `, Max: ${val?.max_commission}` : ''
        }`,
      validator: (val) => {
        return val.schedule_type !== 'custom'
          ? true
          : Array.isArray(val) &&
              val.every(
                (item) =>
                  Array.isArray(item.breakdown) &&
                  item.breakdown.length > 0 &&
                  item.breakdown?.every((breakdownItem) => {
                    try {
                      math.fraction(breakdownItem);
                      return true;
                    } catch {
                      return false;
                    }
                  })
              );
      },
      normalizer: (val) => {
        return val.map((year) => ({
          ...year,
          breakdown: year.breakdown.map((item) => item.toString()),
        }));
      },
    },
    [
      {
        id: 'delay',
        label: 'Delay',
        tip: 'Number of months between policy effective date and first expected commission payment',
        type: 'number',
        validator: (val) =>
          val === null ||
          val === '' ||
          (Number.isInteger(Number.parseInt(val, 10)) &&
            +val >= 0 &&
            +val <= 12),
        normalizer: (val) => (val === '' ? null : +val),
      },
      {
        id: 'advance_cap',
        label: 'Advance Cap',
        tip: 'Maximum advanced commission the agency can receive',
        validator: (val) =>
          val === null ||
          val === '' ||
          (Number.isInteger(Number.parseInt(val, 10)) &&
            +val >= 0 &&
            +val <= 12),
        normalizer: (val) => (val === '' ? null : +val),
      },
    ],
    { id: 'notes', label: 'Notes' },
    { id: 'divider', type: 'divider' },
    {
      id: 'created_at',
      label: 'Created',
      formatter: (val) => (val ? new Date(val).toLocaleString() : null),
      readOnly: true,
    },
  ],
};

const AdvanceCommissionSchedulePage = () => {
  const { data: accountSettings } = API.getBasicQuery(`accounts/settings`);

  const viewSettings = accountSettings?.pages_settings?.carriers_schedules;
  const viewOnly = viewSettings?.read_only ?? false;

  if (viewSettings?.page_label) {
    dataDesc.label = viewSettings?.page_label;
  }

  return (
    <DataView
      dataDesc={dataDesc}
      hideExport
      viewOnly={viewOnly}
      refresh={undefined}
      extraActions={undefined}
      dataCallback={undefined}
      handleCustomExportOptions={undefined}
    />
  );
};

export default AdvanceCommissionSchedulePage;
