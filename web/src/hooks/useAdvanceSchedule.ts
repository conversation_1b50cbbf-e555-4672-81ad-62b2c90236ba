import useSnackbar from '@/contexts/useSnackbar';
import API from '@/services/API';

export const useAdvanceSchedule = () => {
  const { showSnackbar } = useSnackbar();
  const scheduleService = API.getMutation('statement_data/schedule', 'POST');
  const stopScheduleService = API.getMutation(
    'statement_data/schedule/stop',
    'POST'
  );

  const handleAdvanceSchedule = async (reportId: number) => {
    try {
      const response = await scheduleService.mutateAsync({
        report_id: reportId,
      });
      if (response) {
        showSnackbar('Advance schedules generate successfully', 'success');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
    }
  };

  const handleStopAdvanceSchedule = async (reportId: number) => {
    try {
      const response = await stopScheduleService.mutateAsync({
        report_id: reportId,
      });
      if (response) {
        showSnackbar('Stop advance schedules generate successfully', 'success');
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : JSON.stringify(error);
      showSnackbar(errorMessage, 'error');
    }
  };

  return {
    handleAdvanceSchedule,
    handleStopAdvanceSchedule,
  };
};
