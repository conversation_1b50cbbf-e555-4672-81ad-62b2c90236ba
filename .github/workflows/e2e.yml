name: E2E Tests
on:
  pull_request:
    branches: [main, preview, prod]

permissions:
  actions: read
  contents: read
  pull-requests: write

jobs:
  test:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    
    outputs:
      download-report-url: ${{ steps.artifact-upload.outputs.artifact-url }}
      environment: ${{ steps.set-env.outputs.environment }}
      
    steps:
      - uses: actions/checkout@v4
        
      - uses: actions/setup-node@v4
        with:
          node-version: lts/*

      - name: Set environment based on branch
        id: set-env
        run: |
          case "${{ github.base_ref }}" in
            main)
              echo "env_file=.env.dev" >> $GITHUB_OUTPUT
              echo "command=npx playwright test --grep '@Basic|@Commission'" >> $GITHUB_OUTPUT
              echo "environment=development" >> $GITHUB_OUTPUT
              ;;
            preview)
              echo "env_file=.env.preview" >> $GITHUB_OUTPUT
              echo "command=npx playwright test --grep '@Basic|@Commission'" >> $GITHUB_OUTPUT
              echo "environment=preview" >> $GITHUB_OUTPUT
              ;;
            prod)
              echo "env_file=.env.prod" >> $GITHUB_OUTPUT
              echo "command=npx playwright test --grep '@Basic|@Commission'" >> $GITHUB_OUTPUT
              echo "environment=production" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "Unknown branch: ${{ github.base_ref }}"
              exit 1
              ;;
          esac

      - name: Install dependencies
        run: npm ci
        working-directory: misc/e2e-tests

      - name: Install Playwright Browsers
        run: npx playwright install --with-deps
        working-directory: misc/e2e-tests

      - name: Setup environment
        run: |
          cp ${{ steps.set-env.outputs.env_file }} .env
          echo "Running tests for ${{ steps.set-env.outputs.environment }} environment"
        working-directory: misc/e2e-tests

      - name: Run Playwright tests
        run: ${{ steps.set-env.outputs.command }}
        working-directory: misc/e2e-tests

      - uses: actions/upload-artifact@v4
        id: artifact-upload
        if: always()
        with:
          name: playwright-report-${{ steps.set-env.outputs.environment }}
          path: misc/e2e-tests/playwright-report/
          retention-days: 3

  send-report:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    needs: test
    if: always()
    steps:
      - name: Send report
        run: |
          STATUS="${{ needs.test.result }}"
          ENV="${{ needs.test.outputs.environment }}"
          URL="${{ needs.test.outputs.download-report-url }}"
          
          if [ "$STATUS" == "success" ]; then
            MESSAGE="✅ E2E tests passed for *$ENV* env"
          else
            MESSAGE="❌ E2E tests failed for *$ENV* env"
          fi
          
          if [ -n "$URL" ]; then
            MESSAGE="$MESSAGE: $URL"
          fi
          
          curl -X POST "https://chat.googleapis.com/v1/spaces/AAAAauX49ao/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=q0dEcShHg0N3zJ0bmEcvqP2zcxM4sQoPFHqHajxnqG4" \
          -H "Content-Type: application/json" \
          -d "{\"text\": \"$MESSAGE\"}"