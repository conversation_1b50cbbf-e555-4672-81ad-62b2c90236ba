import { getOrderBy } from 'common/helpers';

import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import { calculateSkipAndTake } from '@/prisma';

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getData(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

export default withAuth(handler, { adminOnly: true });

const TABLE = 'data_processing';

export const getData = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const where = {
    state: 'active',
    master_str_id: null,
    str_id: undefined,
    companies: undefined,
    type: undefined,
  };

  const {
    page,
    limit,
    qc: processingType,
    orderBy = 'created_at',
    sort = 'desc',
    id,
    incl_tasks,
  } = req.query;

  const { take, skip } = calculateSkipAndTake({ page, limit });

  if (processingType) {
    // @ts-expect-error
    where.type = processingType;
  }

  if (id) {
    // @ts-expect-error
    where.str_id = id;
  }
  if (incl_tasks?.toLowerCase() === 'true') {
    // @ts-expect-error
    where.master_str_id = undefined;
  }

  const orderByData = getOrderBy(orderBy, sort);

  const [data, count] = await Promise.all([
    prisma[TABLE].findMany({
      where,
      take,
      skip,
      orderBy: {
        ...orderByData,
      },
      include: {
        account: true,
      },
    }),
    prisma[TABLE].count({
      where,
    }),
  ]);

  res.json({
    data,
    count,
  });
};
