import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Post, Req, Res } from 'next-api-decorators';

import { <PERSON>Hand<PERSON> } from '@/lib/baseHandler';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { withAuth } from '@/lib/middlewares';
import { container } from '@/ioc';
import {
  DataUpdateService,
  type IDataUpdateService,
} from '@/services/data-update';
import { DataUpdateValidator } from '@/pages/api/admin/data-update/validator';

class Handler extends BaseHandler {
  private dataUpdateService: IDataUpdateService;
  private validator: DataUpdateValidator;

  constructor() {
    super();
    this.dataUpdateService =
      container.get<IDataUpdateService>(DataUpdateService);
    this.validator = container.get(DataUpdateValidator);
  }

  @Post()
  async postDataUpdate(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const body = this.validator.validateDataUpdatePreviewParams(req.body);

    const {
      data_update_group,
      global,
      processing_date_start,
      processing_date_end,
      document_str_ids,
      only_reconcilied_data,
      select_grouped_records,
      fieldsToPreview,
    } = body;

    const isGlobal = typeof global === 'string' ? global === 'true' : global;

    const response = await this.dataUpdateService.previewData(
      // @ts-expect-error
      req.account_id,
      data_update_group,
      isGlobal,
      processing_date_start,
      processing_date_end,
      document_str_ids,
      only_reconcilied_data,
      select_grouped_records,
      fieldsToPreview
    );
    if (!Number.isNaN(response)) {
      res.json({ data: response });
    } else {
      res.status(500).json({ error: 'An error occurred' });
    }
  }
}

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '25mb',
    },
  },
};

export default withAuth(createHandler(Handler), {
  adminOnly: true,
  allowNoAccount: true,
});
