import * as dto from 'common/dto/companies/dto';
import {
  AccessTypes,
  ImportStatuses,
  ProcessorStatuses,
  SortOrder,
} from 'common/globalTypes';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next/types';
import { isNill } from 'common/helpers';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { Pagination, ZodBody } from '@/lib/decorators';
import { limitConcurrency } from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import { CompaniesService } from '@/services/companies';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';
import {
  DataStates,
  type ExtAccountInfo,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';

const FIELDS_TO_SEARCH = ['company_name', 'str_id', 'account_id'];

class AdminCompaniesHandler extends BaseHandler {
  @Get()
  @Pagination()
  async getAccount(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const orderBy = req.query?.orderBy;
    const sort = req.query?.sort as SortOrder;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let where: any = {
      state: DataStates.ACTIVE,
      access: undefined,
      str_id: undefined,
    };
    if (req.query?.canonical_id) {
      if (isNill(req.query.canonical_id)) {
        where.canonical_id = null;
      } else {
        const canonicalId = parseInt(req.query.canonical_id, 10);
        // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (!isNaN(canonicalId)) {
          where.OR = [{ id: canonicalId }, { canonical_id: canonicalId }];
        }
      }
    }

    if (req.query.access) {
      where.access = req.query.access;
    }
    if (req.query?.id) {
      where.str_id = req.query.id;
    }
    if (req.query?.account_id) {
      const accountIds = Array.isArray(req.query.account_id)
        ? req.query.account_id
        : [req.query.account_id];
      where.account_id = { in: accountIds };
    }

    const q = req.query?.q;
    if (q) {
      where = this.whereClauseBuilder(FIELDS_TO_SEARCH, q, where);
    }

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let orderByClause: any = { company_name: SortOrder.ASC };
    if (orderBy === 'processor_str_ids') {
      orderByClause = {
        companies_processors: {
          _count: sort === SortOrder.DESC ? SortOrder.DESC : SortOrder.ASC,
        },
      };
    } else if (orderBy === 'profile_str_ids') {
      orderByClause = {
        companies_document_profiles: {
          _count: sort === SortOrder.DESC ? SortOrder.DESC : SortOrder.ASC,
        },
      };
    } else if (orderBy) {
      orderByClause = {
        [orderBy]: sort === SortOrder.DESC ? SortOrder.DESC : SortOrder.ASC,
      };
    }

    const [_data, meta] = await prisma.companies
      .paginate({
        where: where,
        select: {
          access: true,
          account_id: true,
          alias_list: true,
          canonical_id: true,
          company_name: true,
          company_products: true,
          created_at: true,
          documents: true,
          id: true,
          str_id: true,
          type: true,
          updated_at: true,
          canonical_company: {
            select: {
              id: true,
              company_name: true,
              str_id: true,
            },
          },
          companies_document_profiles: {
            where: {
              state: DataStates.ACTIVE,
            },
            select: {
              document_profile_str_id: true,
              auto_mapping_id: true,
              document_profile: {
                select: {
                  str_id: true,
                  name: true,
                },
              },
            },
          },
          companies_processors: {
            where: {
              state: DataStates.ACTIVE,
            },
            include: {
              processor: {
                select: {
                  id: true,
                  str_id: true,
                  name: true,
                },
              },
            },
          },
          child_companies: {
            where: {
              state: DataStates.ACTIVE,
            },
            select: {
              id: true,
              str_id: true,
              company_name: true,
              access: true,
              account_id: true,
              created_at: true,
            },
          },
          _count: {
            select: {
              child_companies: { where: { state: DataStates.ACTIVE } },
              comp_grids: { where: { state: DataStates.ACTIVE } },
              company_products: { where: { state: DataStates.ACTIVE } },
              documents: { where: { state: DataStates.ACTIVE } },
              companies_processors: {
                where: {
                  state: DataStates.ACTIVE,
                  processor: {
                    state: DataStates.ACTIVE,
                    processor_status: {
                      in: [ProcessorStatuses.NEW, ProcessorStatuses.PROCESSED],
                    },
                  },
                },
              },
              prompts: { where: { state: DataStates.ACTIVE } },
            },
          },
        },
        orderBy: orderByClause,
        accountInject: false,
      })
      .withPages({
        limit: req.limit,
        // @ts-expect-error
        page: req.page + 1,
        includePageCount: true,
      });

    const accounts = await prisma.accounts.findMany({
      select: { str_id: true, name: true },
      orderBy: { name: 'asc' },
      accountInject: false,
    });
    // @ts-expect-error
    const accountMap = new Map(accounts.map((acc) => [acc.str_id, acc.name]));

    const globalCompanies = await prisma.companies.findMany({
      where: {
        access: AccessTypes.GLOBAL,
        state: DataStates.ACTIVE,
      },
      select: {
        id: true,
        company_name: true,
        str_id: true,
      },
      orderBy: {
        company_name: 'asc',
      },
      accountInject: false,
    });

    // @ts-expect-error
    const dataFlatCounts = _data.map((d) => {
      const processor_str_ids = d.companies_processors.map(
        // @ts-expect-error
        (cp) => cp.processor_str_id
      );

      // @ts-expect-error
      const childCompaniesWithAccountNames = d.child_companies.map((child) => ({
        ...child,
        account_name: accountMap.get(child.account_id) || 'N/A',
      }));

      const companiesDocumentProfiles = d.companies_document_profiles.map(
        // @ts-expect-error
        (cdp) => ({
          document_profile_str_id: cdp.document_profile_str_id,
          auto_mapping_id: cdp.auto_mapping_id,
          document_profile: cdp.document_profile,
        })
      );

      const document_profiles = companiesDocumentProfiles.map(
        // @ts-expect-error
        (cdp) => cdp.document_profile
      );

      return {
        ...d,
        canonical_id: d.canonical_id,
        canonical_company_name: d.canonical_company?.company_name || null,
        account_name: accountMap.get(d.account_id) || 'N/A',
        child_companies_count: d._count.child_companies,
        comp_grids_count: d._count.comp_grids,
        company_products_count: d._count.company_products,
        documents_count: d._count.documents,
        processors_count: d._count.companies_processors,
        prompts_count: d._count.prompts,
        // @ts-expect-error
        processors: d.companies_processors.map((cp) => cp.processor),
        processor_str_ids: processor_str_ids,
        companies_document_profiles: companiesDocumentProfiles,
        document_profiles: document_profiles,
        // @ts-expect-error
        profile_str_ids: document_profiles.map((dp) => dp.str_id),
        child_companies: childCompaniesWithAccountNames,
      };
    });

    const companiesService = container.get<CompaniesService>(CompaniesService);
    const dataWithPotentialMatches =
      await companiesService.addPotentialMatches(dataFlatCounts);

    res.json({
      data: dataWithPotentialMatches,
      count: meta.totalCount,
      fieldOptions: {
        // @ts-expect-error

        account_id: accounts.map((account) => ({
          id: account.str_id,
          name: account.name,
        })),
        canonical_id: [
          // @ts-expect-error
          ...globalCompanies.map((company) => ({
            id: company.id,
            name: company.company_name,
            str_id: company.str_id,
          })),
        ],
      },
    });
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.COMPANIES)
  async createCompany(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompaniesDTOSchema)()) validBody: dto.CompaniesDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const {
      companies_processors,
      profile_str_ids,
      companies_document_profiles,
      ...companyData
    } = validBody;

    // @ts-expect-error
    const data = await prisma.$transaction(async (tx) => {
      const company = await tx.companies.create({
        data: {
          ...companyData,
          uid: null,
          account_id: req.account_id,
          created_by: req.uid,
          created_proxied_by: req.ouid,
        },
      });

      if (
        companies_document_profiles &&
        companies_document_profiles.length > 0
      ) {
        await limitConcurrency(
          async (profileRelation) => {
            await tx.companies_document_profiles.create({
              data: {
                company_str_id: company.str_id,
                document_profile_str_id:
                  profileRelation.document_profile_str_id,
                auto_mapping_id: profileRelation.auto_mapping_id || null,
                account_id: req.account_id,
                created_by: req.uid,
                created_proxied_by: req.ouid,
              },
            });
          },
          companies_document_profiles,
          15
        );
      } else if (
        profile_str_ids &&
        Array.isArray(profile_str_ids) &&
        profile_str_ids.length > 0
      ) {
        await limitConcurrency(
          async (profileStrId) => {
            await tx.companies_document_profiles.create({
              data: {
                company_str_id: company.str_id,
                document_profile_str_id: profileStrId,
                account_id: req.account_id,
                created_by: req.uid,
                created_proxied_by: req.ouid,
              },
            });
          },
          profile_str_ids,
          15
        );
      }

      if (
        companies_processors &&
        Array.isArray(companies_processors) &&
        companies_processors.length > 0
      ) {
        await limitConcurrency(
          async (processorItem) => {
            await tx.companies_processors.create({
              data: {
                company_str_id: company.str_id,
                processor_str_id: processorItem.processor_str_id,
                import_status:
                  processorItem.import_status || ImportStatuses.NONE,
                account_id: req.account_id,
                created_at: new Date(),
                created_by: req.uid,
                created_proxied_by: req.ouid,
                updated_at: new Date(),
                updated_by: req.uid,
                updated_proxied_by: req.ouid,
              },
            });
          },
          companies_processors,
          15
        );
      }

      return company;
    });

    res.status(201).json(data);
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.COMPANIES)
  async patchCompany(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompaniesDTOSchema)()) validBody: dto.CompaniesDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    if (validBody.alias_list && typeof validBody.alias_list === 'string') {
      validBody.alias_list = JSON.parse(validBody.alias_list);
    }

    const {
      companies_processors,
      companies_document_profiles,
      processor_str_ids: _processor_str_ids,
      profile_str_ids: _profile_str_ids,
      child_companies,
      ...companyData
    } = validBody;

    await container.get<SyncFieldService>(SyncFieldService).canUpdateIfChanged({
      newData: companyData,
      tableName: 'companies',
      id: Number(companyData.id),
      config: companyData.config,
      account: { account_id: req.account_id } as ExtAccountInfo,
      accountInject: false,
    });

    const data = await prisma.$transaction(
      // @ts-expect-error
      async (tx) => {
        const company = await tx.companies.update({
          where: {
            id: Number(companyData.id),
          },
          data: {
            ...companyData,
            updated_at: new Date(),
            updated_by: req.uid,
            updated_proxied_by: req.ouid,
          },
        });

        await tx.companies_processors.deleteMany({
          where: { company_str_id: company.str_id },
        });

        await tx.companies_document_profiles.deleteMany({
          where: { company_str_id: company.str_id },
        });

        if (companies_processors?.length > 0) {
          await tx.companies_processors.createMany({
            // @ts-expect-error
            data: companies_processors.map((processorItem) => ({
              company_str_id: company.str_id,
              processor_str_id: processorItem.processor_str_id,
              import_status: processorItem.import_status || ImportStatuses.NONE,
              account_id: req.account_id,
              created_at: new Date(),
              created_by: req.uid,
              created_proxied_by: req.ouid,
              updated_at: new Date(),
              updated_by: req.uid,
              updated_proxied_by: req.ouid,
            })),
          });
        }

        if (companies_document_profiles?.length > 0) {
          await tx.companies_document_profiles.createMany({
            // @ts-expect-error
            data: companies_document_profiles.map((profileRelation) => ({
              company_str_id: company.str_id,
              document_profile_str_id: profileRelation.document_profile_str_id,
              auto_mapping_id: profileRelation.auto_mapping_id || null,
              account_id: req.account_id,
              created_by: req.uid,
              created_proxied_by: req.ouid,
            })),
          });
        }
        if (
          child_companies &&
          Array.isArray(child_companies) &&
          child_companies.length > 0
        ) {
          const childCompanyIds = child_companies
            .map((cc) => cc.id)
            .filter((id) => id && id !== company.id);

          if (childCompanyIds.length > 0) {
            const canonicalId = company.id;

            await tx.companies.updateMany({
              where: {
                id: { in: childCompanyIds },
                state: DataStates.ACTIVE,
              },
              data: {
                canonical_id: canonicalId,
                updated_by: req.uid,
                updated_proxied_by: req.ouid,
                updated_at: new Date(),
              },
            });
          }
        }

        return company;
      },
      {
        timeout: 15000,
      }
    );
    res.status(200).json(data);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.COMPANIES)
  async deleteData(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DeleteCompaniesDTOSchema)())
    validBody: dto.DeleterCompaniesDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const companiesToDelete = await prisma.companies.findMany({
      where: {
        id: {
          // @ts-expect-error
          in: validBody.ids.map((id) => Number(id)),
        },
      },
      select: { str_id: true },
    });

    // @ts-expect-error
    const companyStrIds = companiesToDelete.map((c) => c.str_id);

    // @ts-expect-error
    await prisma.$transaction(async (tx) => {
      if (companyStrIds.length > 0) {
        await tx.companies_processors.deleteMany({
          where: {
            company_str_id: { in: companyStrIds },
          },
        });

        await tx.companies_document_profiles.deleteMany({
          where: {
            company_str_id: { in: companyStrIds },
          },
        });
      }

      // @ts-expect-error
      for (const id of validBody.ids) {
        await tx.companies.update({
          where: { id: Number(id) },
          data: {
            state: DataStates.DELETED,
            updated_at: new Date(),
            updated_by: req.uid,
            updated_proxied_by: req.ouid,
          },
        });
      }
    });
    res.status(200).json({ status: 'OK' });
  }
}

export default withAuth(createHandler(AdminCompaniesHandler), {
  adminOnly: true,
  allowNoAccount: true,
});
