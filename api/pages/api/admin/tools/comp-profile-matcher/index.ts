import type { NextRequest, NextResponse } from 'next/server';
import { create<PERSON><PERSON><PERSON>, <PERSON>, Req, Res } from 'next-api-decorators';
import * as dto from 'common/dto/comp_profile_matcher/dto';

import { ZodBody } from '@/lib/decorators';
import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { CompProfileMatchingService } from '@/pages/api/admin/tools/comp-profile-matcher/CompProfileMatchingService';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

class CompProfileMatchHandler extends BaseHandler {
  private readonly compProfileMatchingService: CompProfileMatchingService;

  constructor() {
    super();
    this.compProfileMatchingService = container.get<CompProfileMatchingService>(
      CompProfileMatchingService
    );
  }

  @Post()
  async matchCompProfile(
    @Req() req: ExtNextApiRequest & NextRequest,
    @Res() res: ExtNextApiResponse & NextResponse,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompProfileMatcherDTOSchema)())
    compProfileMatcherBody: dto.CompProfileMatcherDTO
  ) {
    const { commissionId, compProfileMatchers } = compProfileMatcherBody;

    const matchResult = await this.compProfileMatchingService.match(
      // @ts-expect-error
      req?.account_id,
      commissionId,
      compProfileMatchers
    );

    return res.status(200).json(matchResult);
  }
}

export default withAuth(createHandler(CompProfileMatchHandler));
