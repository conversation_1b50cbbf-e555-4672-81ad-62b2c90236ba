import type { NextApiRequest, NextApiResponse } from 'next';
import { Get, Post, Put, Req, Re<PERSON>, createHand<PERSON> } from 'next-api-decorators';
import { OAuth2Client } from 'google-auth-library';

import { BaseHandler } from '@/lib/baseHandler';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { withLogger } from '@/lib/middlewares';
import {
  GOOGLE_AUTH_CLIENT_ID,
  GOOGLE_AUTH_CLIENT_SECRET,
  GOOGLE_AUTH_REDIRECT_URI,
  GOOGLE_AUTH_SCOPES,
  UPLOADS_EMAIL,
} from '@/services/auto-upload-attachments/gmail/gmail.constant';
import { AppLoggerService } from '@/services/logger/appLogger';

class Handler extends BaseHandler {
  private oauth2Client: OAuth2Client;
  private logger: AppLoggerService = new AppLoggerService();

  constructor() {
    super();
    this.logger.info('Initializing OAuth2Client');
    this.oauth2Client = new OAuth2Client(
      GOOGLE_AUTH_CLIENT_ID,
      GOOGLE_AUTH_CLIENT_SECRET,
      GOOGLE_AUTH_REDIRECT_URI
    );
  }

  @Post('/auth-url')
  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async getLoginUrl(@Req() req: NextApiRequest, @Res() res: NextApiResponse) {
    this.logger.info('Generating authentication URL');
    const url = this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: GOOGLE_AUTH_SCOPES.split(','),
      prompt: 'consent',
    });

    this.logger.info('Authentication URL generated successfully');
    return res.json({ loginUrl: url });
  }

  @Get('/callback')
  async handleCallback(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: NextApiResponse
  ) {
    this.logger.info('Handling OAuth callback');
    const { code } = req.query;

    if (!code) {
      this.logger.warn('Authorization code is missing in callback');
      return res.status(400).json({ error: 'Authorization code is missing' });
    }

    this.logger.info('Exchanging authorization code for tokens');
    const { tokens } = await this.oauth2Client.getToken(code as string);
    this.oauth2Client.setCredentials(tokens);
    this.logger.info('Successfully obtained and set tokens');

    this.logger.info('Fetching token info');
    const userInfo = await this.oauth2Client.getTokenInfo(
      // @ts-expect-error
      tokens.access_token
    );
    this.logger.info('Token info retrieved', { email: userInfo.email });

    const isLocalEnvironment =
      // @ts-expect-error
      process.env.AUTH_DOMAIN.startsWith('localhost');
    const protocol = isLocalEnvironment ? 'http' : 'https';
    const baseUrl = `${protocol}://${process.env.AUTH_DOMAIN}`;
    this.logger.info('Determined base URL for redirect', { baseUrl });

    if (userInfo.email !== UPLOADS_EMAIL) {
      this.logger.warn('Unauthorized email attempted access', {
        email: userInfo.email,
      });
      res.writeHead(302, {
        Location: `${baseUrl}/admin/documents/gmail-sync`,
      });
      return;
    }

    this.logger.info('Updating user refresh token in database', {
      email: userInfo.email,
    });
    await prisma.users.update({
      where: { email: userInfo.email },
      data: {
        refresh_token: tokens.refresh_token,
      },
    });
    this.logger.info('Successfully updated user refresh token');

    res.writeHead(302, {
      Location: `${baseUrl}/admin/documents/gmail-sync`,
    });
    res.end();
    this.logger.info('Redirecting to settings page');
  }

  @Post('/verify-user')
  async verifyUser(
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    this.logger.info('Verifying user');
    const user = await prisma.users.findFirst({
      where: { email: UPLOADS_EMAIL },
      include: {
        account_user_roles: true,
      },
    });

    if (!user) {
      this.logger.info(`Account not found for email: ${UPLOADS_EMAIL}`);
      throw new Error('Account not found');
    }

    this.logger.info('User found', { userId: user.id });
    const account = await prisma.accounts.findFirst({
      where: { str_id: String(user.account_user_roles[0].account_id) },
    });

    if (!account) {
      this.logger.error('Account not found for user', { userId: user.id });
      throw new Error('Account not found');
    }

    if (!account.short_name) {
      this.logger.error('Account short name is missing', {
        accountId: account.id,
      });
      throw new Error('Account short name is missing');
    }

    if (user?.refresh_token) {
      this.logger.info('User verification successful', {
        email: UPLOADS_EMAIL,
      });
      return res.json({
        valid: true,
        user: {
          email: UPLOADS_EMAIL,
          googleDriveFolder: user.drive_folder_name,
        },
      });
    }

    this.logger.info('User verification failed - no refresh token', {
      email: UPLOADS_EMAIL,
    });
    return res.json({ valid: false });
  }

  @Put('/configs')
  async syncProfile(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    this.logger.info('Updating user configs');
    const { email, googleDriveFolder } = req.body;

    if (!email) {
      this.logger.error('Email is required for config update');
      throw new Error('Email is required');
    }

    this.logger.info('Updating user drive folder', {
      email,
      googleDriveFolder,
    });
    await prisma.users.update({
      where: { email },
      data: {
        drive_folder_name: googleDriveFolder,
      },
    });
    this.logger.info('Successfully updated user configs');

    return res.json({ success: true });
  }

  @Post('/logout')
  async logout(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    this.logger.info('Processing logout request');
    const { email } = req.body;

    if (!email) {
      this.logger.error('Email is required for logout');
      throw new Error('Email is required');
    }

    this.logger.log('Logging out user:', email);
    await prisma.users.update({
      where: { email },
      data: {
        refresh_token: null,
      },
    });
    this.logger.info('Successfully logged out user', { email });

    return res.json({ success: true });
  }
}

export default withLogger(createHandler(Handler));
