import type { NextApiRequest, NextApiResponse } from 'next';
import * as Sentry from '@sentry/nextjs';
import { createH<PERSON>ler, Post, Req, Res } from 'next-api-decorators';
import { injectable } from 'inversify';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { container } from '@/ioc';
import { ProcessorsService } from '@/services/processors/service';

@injectable()
class Handler extends BaseHandler {
  private service: ProcessorsService;

  constructor() {
    super();
    this.service = container.get(ProcessorsService);
  }

  @Post()
  async getProcessors(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { rowData } = req.body;
    const { str_id: document_id, companies, method = '' } = rowData;

    if (!document_id) {
      return res.status(400).json({ error: 'Missing document id' });
    }

    try {
      const processors = await this.service.getProcessorsByDocument({
        document_id: document_id as string,
        // @ts-expect-error
        account_id: req.account_id,
        companies,
        method,
      });

      if (!processors || !processors?.length) {
        return res.status(200).json([]);
      }

      return res.status(200).json(processors);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error fetching processors:', error);
      Sentry.captureException(error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }
}

export default withAuth(createHandler(Handler));
