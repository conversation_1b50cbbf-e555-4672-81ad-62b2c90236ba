import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { isNill } from '@/lib/helpers';

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await getOne(req, res);
      break;
    case 'PATCH':
      await updateOne(req, res);
      break;
    case 'DELETE':
      await deleteByIds(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

// GET /processors/:id
const getOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const {
    // Account_id,
    query: { id },
  } = req;
  const existProcessor = await prisma.processors.findUniqueOrThrow({
    where: { id: +id },
    include: {
      companies_processors: {
        include: {
          company: true,
        },
      },
    },
  });
  // if (existProcessor.account_id !== account_id) {
  //   res.status(403).end('Forbidden');
  // }
  res.status(200).json(existProcessor);
};

// PATCH /processors/:id
const updateOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const {
    account_id,
    body,
    query: { id },
  } = req;
  const existProcessor = await prisma.processors.findUniqueOrThrow({
    where: { id: +id },
  });
  if (existProcessor.account_id !== account_id) {
    res.status(403).end('Forbidden');
  }

  if (!isNill(body.company_id)) {
    const companyId = body.company_id;
    delete body.company_id;

    if (companyId) {
      await prisma.companies_processors.deleteMany({
        where: { processor_str_id: existProcessor.str_id },
      });

      await prisma.companies_processors.create({
        data: {
          account_id: account_id,
          company_str_id: companyId,
          processor_str_id: existProcessor.str_id,
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      });
    } else {
      await prisma.companies_processors.deleteMany({
        where: { processor_str_id: existProcessor.str_id },
      });
    }
  }

  await prisma.processors.update({
    where: { id: +id },
    data: body,
  });
  res.status(200).end('OK');
};

// DELETE /processors/:id
const deleteByIds = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const {
    account_id,
    query: { id },
  } = req;
  const ids = id.split(',').map((id) => +id);
  const existProcessors = await prisma.processors.findMany({
    where: { id: { in: ids } },
  });
  if (existProcessors.length !== ids.length) {
    throw new Error('Not all processors exists');
  }
  // @ts-expect-error
  if (existProcessors.find((r) => r.account_id !== account_id)) {
    res.status(403).end('Forbidden');
  }

  const processorStrIds = existProcessors
    // @ts-expect-error
    .map((p) => p.str_id)
    .filter(Boolean);

  if (processorStrIds.length > 0) {
    await prisma.companies_processors.deleteMany({
      where: { processor_str_id: { in: processorStrIds } },
    });
  }

  await prisma.processors.deleteMany({
    where: { id: { in: ids } },
  });
  res.status(200).end('OK');
};

export default withAuth(handler);
