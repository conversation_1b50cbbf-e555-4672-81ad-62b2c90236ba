import { UserStates } from 'common/constants/user-states.enum';
import { getBasicRelevancySort, getOrderBy } from 'common/helpers';
import type { NextApiRequest, NextApiResponse } from 'next';
import { Get, Req, Res, createHandler } from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { Pagination } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';

const TABLE = 'accounts';
const FIELDS_TO_SEARCH = ['name', 'str_id', 'account_user_roles.user.email'];

class Handler extends BaseHandler {
  @Get()
  @Pagination()
  async getAccount(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { type, status } = req.query;

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let where: any = {
      state: DataStates.ACTIVE,
      status: undefined,
      str_id: undefined,
      type: undefined,
    };
    if (req.query?.id) {
      where.str_id = req.query.id;
    }
    const q = req.query?.q?.trim();
    if (q) {
      where = this.whereClauseBuilder(FIELDS_TO_SEARCH, q, where);
    }
    if (type) {
      where.type = { in: Array.isArray(type) ? type : [type] };
    }
    if (status) {
      where.status = { in: Array.isArray(status) ? status : [status] };
    }

    const orderBy = req.query?.orderBy;
    const sort = req.query?.sort;

    // TODO: Use a better way to determine if sort is applied or not
    const isDefaultSort = orderBy === 'created_at' && sort === 'desc';

    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let orderByData;
    if (orderBy && sort) {
      orderByData = getOrderBy(orderBy, sort, [
        {
          query_field: 'account_user_roles',
          db_field: 'role_id',
        },
      ]);
    }

    const [_data, meta] = await prisma[TABLE]
      .paginate({
        where: where,
        accountInject: false,
        orderBy: orderByData,
        include: {
          account_user_roles: {
            where: { state: DataStates.ACTIVE },
            include: { user: true, role: true },
          },
        },
      })
      .withPages({
        limit: req.limit,
        // @ts-expect-error
        page: req.page + 1,
        includePageCount: true,
      });

    // Sort account_user_roles by user state
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    _data.forEach(
      (datum: { account_user_roles: { user: { state: UserStates } }[] }) => {
        datum.account_user_roles.sort((a, b) => {
          const stateOrder = {
            [UserStates.ACTIVE]: 1,
            [UserStates.PENDING]: 2,
            [UserStates.INVITED]: 3,
            [UserStates.DELETED]: 4,
          };
          return (
            // @ts-expect-error
            (stateOrder[a.user.state as UserStates] || 5) -
            // @ts-expect-error
            (stateOrder[b.user.state as UserStates] || 5)
          );
        });
      }
    );

    const accountIds = _data.map((datum: { str_id: string }) => datum.str_id);

    const reconciliations = await prisma.reconciliations.findMany({
      where: {
        state: DataStates.ACTIVE,
        account_id: { in: accountIds },
      },
      select: {
        account_id: true,
        result: true,
      },
    });

    const reconciliationMap = new Map();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    reconciliations.forEach(
      (reconciliation: { account_id: string; result: string }) => {
        reconciliationMap.set(reconciliation.account_id, reconciliation.result);
      }
    );

    const data = _data.map(
      (datum: { str_id: string; reconciliation_result: string }) => {
        const reconciliationResult = reconciliationMap.get(datum.str_id);
        if (reconciliationResult !== undefined) {
          datum.reconciliation_result = reconciliationResult;
        }
        return datum;
      }
    );

    if (isDefaultSort && q) {
      data.sort(getBasicRelevancySort(FIELDS_TO_SEARCH, q));
    }

    const filterOptions = {
      status: [
        { label: 'Active', id: 'active' },
        { label: 'Onboarding', id: 'onboarding' },
        { label: 'Demo', id: 'demo' },
        { label: 'Closed', id: 'closed' },
      ],
      type: [
        { label: 'Test', id: 'fintary' },
        { label: 'Life', id: 'life' },
        { label: 'Health', id: 'health' },
        { label: 'P&C', id: 'p&c' },
      ],
    };

    res.json({
      data: data,
      count: meta.totalCount,
      fieldOptions: filterOptions,
    });
  }
}

export default withAuth(createHandler(Handler), {
  adminOnly: true,
  allowNoAccount: true,
});
