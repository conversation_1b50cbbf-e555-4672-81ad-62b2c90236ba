import { nanoid } from 'nanoid';

import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  AccountAccessLevels,
} from '@/types';

enum CommentEntityType {
  account = 'account',
  processor = 'processor',
}

const handler = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  switch (req.method) {
    case 'GET':
      await queryMany(req, res);
      break;
    case 'POST':
      await createOne(req, res);
      break;
    case 'PATCH':
      await patchOne(req, res);
      break;
    case 'DELETE':
      await deleteOnCondition(req, res);
      break;
    default:
      res.status(405).end(`Method ${req.method} Not Allowed`);
  }
};

// @ts-expect-error
export const getComments = async (req) => {
  const where = {
    processor_str_id: undefined,
    account_str_id: undefined,
    state: 'active',
    OR: undefined,
  };

  if (!req.isFintaryAdmin) {
    // @ts-expect-error
    where.OR = [
      {
        account_id: req.account_id,
      },
      {
        access: AccountAccessLevels.GLOBAL,
      },
    ];
  }

  const { entity, id } = req.query;
  if (entity === CommentEntityType.processor) {
    where.processor_str_id = id;
  } else if (entity === CommentEntityType.account) {
    where.account_str_id = id;
  }

  const data = await prisma.comments.findMany({
    where,
    orderBy: {
      created_at: 'asc',
    },
  });

  return data;
};

const queryMany = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const data = await getComments(req);
  res.json(data);
};

const createOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { uid: _uid, ...post } = req.body;
  const data = await prisma.comments.create({
    data: {
      ...post,
      user_str_id: req.ouid || req.uid,
      created_proxied_by: req.ouid,
      str_id: nanoid(),
      created_at: new Date(),
    },
  });
  res.status(201).json(data);
};

// PATCH /comments
const patchOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body;
  const { id } = body;
  if (!id) throw new Error('Missing id');
  const data = await prisma.comments.update({
    where: { id },
    data: {
      ...body,
      updated_proxied_by: req.ouid,
    },
  });
  res.status(200).json(data);
};

// DELETE /comments
const deleteOnCondition = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse
) => {
  const { ids } = req.body || {};
  const existMappings = await prisma.comments.findMany({
    where: { id: { in: ids } },
  });
  const existIds = existMappings.map((r: { id: number }) => r.id);
  const resp = await prisma.comments.updateMany({
    where: { id: { in: existIds } },
    data: {
      state: 'deleted',
    },
  });
  res.status(200).json(resp);
};

export default withAuth(handler);
