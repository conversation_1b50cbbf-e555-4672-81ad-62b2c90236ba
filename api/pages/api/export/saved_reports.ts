import { startCase } from 'lodash-es';
import { CompReportViewTypes } from 'common/globalTypes';
import { createHandler, Get, Req, Res } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';

import { withAuth } from '@/lib/middlewares';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  Roles,
} from '@/types';
import { getExportData } from '@/pages/api/export/base';
import { exportTotalRows } from './exportFields';
import { formatPdfCurrency } from '@/services/export-report/format-currency';
import { exportCsvResponse } from './base/export-csv-response';
import { BaseHandler } from '@/lib/baseHandler';
import { processContacts } from '@/lib/helpers/processContacts';
import { processReportDataToFormattedData } from './report_data';
import { Timezone } from '@/lib/decorators';
import { getData } from '../saved_reports/reports';
import { SettingsService } from '@/services/settings';
import { container } from '@/ioc';

class ExportSavedReportHandler extends BaseHandler {
  private settingsService: SettingsService;
  constructor() {
    super();
    this.settingsService = container.get(SettingsService);
  }

  @Get()
  async exportHandler(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @Timezone() timezone: string
  ) {
    const { q, isExportRelationship } = req.query;
    const query = JSON.parse(req.query?.exportOptions);
    const viewType = query?.view;

    const reportData = await getData(req, this.settingsService, {
      ...query,
      report_id: q,
    });

    let roleToUse = Roles.PRODUCER;
    if (viewType === CompReportViewTypes.ADMIN_VIEW)
      roleToUse = Roles.ACCOUNT_ADMIN;

    let table: 'report_data' | 'statement_data' | 'reconciliation_data';
    if (reportData.page === 'policies') {
      table = 'report_data';
    } else if (reportData.page === 'commissions') {
      table = 'statement_data';
      await processContacts(reportData.snapshot_data.data.data);
    } else if (reportData.page === 'reconciliation') {
      table = 'reconciliation_data';
    }

    const templateType = reportData.saved_report_group?.template;
    let formatData = [];

    // @ts-expect-error
    if (table === 'report_data') {
      formatData = await processReportDataToFormattedData({
        data: reportData.snapshot_data.data.data,
        req,
        res,
        timezone,
        producerView: viewType === CompReportViewTypes.PRODUCER_VIEW,
      });
    } else {
      formatData = await getExportData({
        data: reportData.snapshot_data.data.data,
        // @ts-expect-error
        table,
        roleId: roleToUse.toString(),
        // @ts-expect-error
        accountId: req.account_id,
        isExportRelationship: isExportRelationship === 'true',
        templateType,
        // @ts-expect-error
        uid: req.uid,
        contactStrId:
          viewType === CompReportViewTypes.PRODUCER_VIEW
            ? reportData.snapshot_data.data.contactStrId
            : undefined,
      });
    }

    formatData.push(await exportTotalRows(formatData));

    formatData = formatPdfCurrency(formatData);

    // Set the response headers to trigger the download
    const subFilename = startCase(
      (templateType ?? reportData.page).replace(/_/g, ' ')
    ).replace(/ /g, '-');
    const filename = `Fintary-${subFilename}-Report-Export.csv`;
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    // Converting the data to CSV
    exportCsvResponse(formatData, res);
  }
}

export default withAuth(createHandler(ExportSavedReportHandler));
