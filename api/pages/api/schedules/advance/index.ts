import * as dto from 'common/dto/advance-commission-schedules';
import type { NextApiRequest } from 'next';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
} from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { AdvancedCommissionService } from '@/services/advance-commission-schedule';
import type { ExtAccountInfo, ExtNextApiRequest } from '@/types';
import { AccountInfo, ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  service: AdvancedCommissionService;

  constructor() {
    super();
    this.service = container.get<AdvancedCommissionService>(
      AdvancedCommissionService
    );
  }

  @Get()
  async list(
    @Req() _req: NextApiRequest & ExtNextApiRequest,
    @AccountInfo() account: ExtAccountInfo
  ) {
    return await this.service.list(account.account_id);
  }

  @Post()
  async create(
    @Req() _req: NextApiRequest & ExtNextApiRequest,
    @AccountInfo() account: ExtAccountInfo,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CreateAdvanceCommissionSchedulesDTOSchema)()) body: dto.CreateAdvanceCommissionSchedulesDTO
  ) {
    return await this.service.create({
      ...body,
      account_id: account.account_id,
    });
  }

  @Patch()
  async update(
    @Req() _req: NextApiRequest & ExtNextApiRequest,
    @AccountInfo() account: ExtAccountInfo,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.UpdateAdvanceCommissionSchedulesDTOSchema)()) body: dto.UpdateAdvanceCommissionSchedulesDTO
  ) {
    return await this.service.update({
      ...body,
      account_id: account.account_id,
    });
  }
  @Delete()
  async delete(
    @Req() _req: NextApiRequest & ExtNextApiRequest,
    @AccountInfo() account: ExtAccountInfo,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.DeleteAdvanceCommissionSchedulesDTOSchema)()) body: dto.DeleteAdvanceCommissionSchedulesDTO
  ) {
    return await this.service.delete({
      ...body,
      account_id: account.account_id,
    });
  }
}

export default withAuth(createHandler(Handler));
