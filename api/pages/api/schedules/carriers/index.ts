// import * as Sentry from '@sentry/nextjs';

import { nanoid } from 'nanoid';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';

import dayjs from '@/lib/dayjs';
import prisma from '@/lib/prisma';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { BaseHandler } from '@/lib/baseHandler';
import { Guard } from '@/services/permission/decorator';
import { CrudAction, EntityType } from '@/services/permission/interface';

class Handler extends BaseHandler {
  @Get()
  @Guard(CrudAction.READ, EntityType.CARRIERS_SCHEDULES)
  async getData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await getData(req, res);
  }

  @Post()
  @Guard(CrudAction.CREATE, EntityType.CARRIERS_SCHEDULES)
  async createData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await postData(req, res);
  }

  @Patch()
  @Guard(CrudAction.UPDATE, EntityType.CARRIERS_SCHEDULES)
  async updateData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await patchData(req, res);
  }

  @Delete()
  @Guard(CrudAction.DELETE, EntityType.CARRIERS_SCHEDULES)
  async deleteData(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteData(req, res);
  }
}

export default withAuth(createHandler(Handler));

const TABLE = 'commission_schedules';

interface CommissionSchedulePayload {
  id: number;
  str_id: string;
  uid: string;
  state: string;
  created_at: string;
  name: string;
  carrier_company_id: number;
  paying_entity_company_id: number;
  product_type: string;
  product_name: string;
  start_date: string;
  end_date: string;
  commission_schedule: string;
  delay: number;
  notes: string;
  issue_age_end: number;
  issue_age_start: number;
  premium_min: number;
  premium_max: number;
}

const getData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const where = {
    account_id: req.account_id,
    state: 'active',
    str_id: undefined,
  };
  if (req.query?.id) {
    where.str_id = req.query.id;
  }
  const data = await prisma[TABLE].findMany({
    where,
  });
  res.json(data);
};

const postData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as CommissionSchedulePayload;
  try {
    const _data = await prisma[TABLE].create({
      data: {
        ...body,
        account_id: req.account_id,
        str_id: nanoid(),
        uid: req.uid,
        created_by: req.uid,
        created_proxied_by: req.ouid,
      },
    });
    res.status(201).json({});
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error creating commission schedule: ${error}`);
    res.status(500).json({ error: error.message });
  }
};

const patchData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body as CommissionSchedulePayload;
  try {
    if (!body.id) throw new Error('Missing id');

    if (body.start_date) {
      body.start_date = dayjs(body.start_date).toISOString();
    }
    if (body.end_date) {
      body.end_date = dayjs(body.end_date).toISOString();
    }

    const data = await prisma[TABLE].update({
      where: { id: Number(body.id), account_id: String(req.account_id) },
      data: {
        ...body,
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
    res.status(200).json(data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

const deleteData = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids } = req.body;
  try {
    if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
      throw new Error('Invalid id(s)');
    const promises = ids.map((_id) => {
      return prisma[TABLE].update({
        where: { id: Number(_id), account_id: String(req.account_id) },
        data: {
          state: 'deleted',
          updated_at: new Date(),
          updated_by: req.uid,
          updated_proxied_by: req.ouid,
        },
      });
    });
    await prisma.$transaction(promises);
    res.status(200).json({ status: 'OK' });
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error deleting data: ${error}`);
    res.status(500).json({ error: error.message });
  }
};
