import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { AccountInfo, ZodBody } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import { AdvancedCommissionService } from '@/services/advance-commission-schedule';
import type { ExtAccountInfo, ExtNextApiRequest } from '@/types';
import * as statement_data from 'common/dto/statement_data';
import type { NextApiRequest } from 'next';
import { createHandler, Post, Req } from 'next-api-decorators';

class Handler extends BaseHandler {
  service: AdvancedCommissionService;

  constructor() {
    super();
    this.service = container.get<AdvancedCommissionService>(
      AdvancedCommissionService
    );
  }
  @Post()
  async schedule(
    @Req() _req: NextApiRequest & ExtNextApiRequest,
    // biome-ignore format: compound decorator
    @(ZodBody(statement_data.GenerateScheduleSchema)()) body: statement_data.GenerateScheduleDTO,
    @AccountInfo() account: ExtAccountInfo
  ) {
    await this.service.processStatement(body.report_id, account.account_id);
    return { success: true };
  }

  @Post('/stop')
  async stop(
    @Req() _req: NextApiRequest & ExtNextApiRequest,
    // biome-ignore format: compound decorator
    @(ZodBody(statement_data.StopScheduleSchema)()) body: statement_data.StopScheduleDTO
  ) {
    await this.service.stop(body.report_id);
    return { success: true };
  }
}

export default withAuth(createHandler(Handler));
