import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';
import * as dto from 'common/dto/comp_grid_levels/dto';
import { convertDateFields } from 'common/helpers';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import {
  DataStates,
  type ExtNextApiRequest,
  type ExtNextApiResponse,
} from '@/types';
import { ZodBody } from '@/lib/decorators';
import type { Prisma } from '@prisma/client';

class Handler extends BaseHandler {
  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const data = await _getData(req);
    res.json(data);
  }

  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridLevelCreateDTOSchema)())
    body: dto.CompGridLevelCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await createOne(req, res, body);
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridLevelUpdateDTOSchema)())
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    body: dto.CompGridLevelUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res);
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridLevelDeleteDTOSchema)())
    body: dto.CompGridLevelDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteMany(req, res, body);
  }
}

export default withAuth(createHandler(Handler));

export const _getData = async (req: ExtNextApiRequest & NextApiRequest) => {
  const where: Prisma.accounting_transaction_detailsWhereInput = {
    account_id: req.account_id,
    state: { not: 'deleted' },
    str_id: req.query?.id,
    contact_id: undefined,
    transaction_id: undefined,
  };
  if (req.query.contact_id) where.contact_id = +req.query.contact_id;
  if (req.query.transaction_id)
    where.transaction_id = +req.query.transaction_id;
  const data = await prisma.accounting_transaction_details.findMany({
    where,
  });
  return data;
};

const createOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridLevelCreateDTO
) => {
  body = convertDateFields(body);
  let accountTransaction = null;
  if (body.transaction_id) {
    accountTransaction = await prisma.accounting_transactions.findFirst({
      where: {
        id: body.transaction_id,
        account_id: req.account_id,
        state: DataStates.ACTIVE,
      },
      select: {
        report_id: true,
      },
    });
  }
  const data = await prisma.accounting_transaction_details.create({
    data: {
      ...body,
      account_id: req.account_id,
      report_id: accountTransaction?.report_id,
      created_by: req.uid,
      created_proxied_by: req.ouid,
    },
  });
  res.status(201).json(data);
};

const updateOne = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const body = req.body;
  const { id, ..._body } = body;
  if (!id) throw new Error('Missing id');
  let data = {};
  data = await prisma.accounting_transaction_details.update({
    where: { id, account_id: String(req.account_id) },
    data: {
      ..._body,
      updated_at: new Date(),
      updated_by: req.uid,
      updated_proxied_by: req.ouid,
      date: _body.date ? new Date(_body.date) : undefined,
    },
  });
  res.status(200).json(data);
};

const deleteMany = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridLevelDeleteDTO
) => {
  const { ids } = body;
  if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
  // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
    throw new Error('Invalid ids');
  const promises = ids.map((_id) => {
    return prisma.accounting_transaction_details.update({
      where: { id: Number(_id), account_id: String(req.account_id) },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    });
  });
  await prisma.$transaction(promises);
  res.status(200).json({ status: 'OK' });
};
