import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import {
  AccountingService,
  type IAccountingService,
} from '@/services/accounting';
import { ContactService, type IContactService } from '@/services/contact';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { createHandler, Get, Req, Res } from 'next-api-decorators';

class CompReportsTransactionHandler extends BaseHandler {
  private accountingService: IAccountingService;
  private contactService: IContactService;

  constructor() {
    super();
    this.accountingService = container.get(AccountingService);
    this.contactService = container.get(ContactService) as IContactService;
  }

  // TODO: Add zod schema validation and integration tests for this endpoint.
  @Get()
  async get(@Req() req: ExtNextApiRequest, @Res() res: ExtNextApiResponse) {
    const { agent_str_id } = req.query;

    const contactId =
      await this.contactService.getContactIdsByStrIdList(agent_str_id);

    if (contactId.length === 0) {
      res.json([]);
      return;
    }

    const response =
      await this.accountingService.getApprovedTransactionsAndReportsByContact(
        contactId[0]
      );

    res.json(
      response.map((transaction) => ({
        report_name: transaction.saved_report?.name || '',
        transaction_amount: transaction.amount,
        report_str_id: transaction.saved_report?.str_id || '',
        report_group_str_id:
          transaction.saved_report?.saved_report_group?.str_id || '',
      }))
    );
  }
}

export default withAuth(createHandler(CompReportsTransactionHandler));
