import type { NextApiRequest, NextApiResponse } from 'next';
import { Body, Post, Req, Res, createHandler } from 'next-api-decorators';
import type { CompGridLevelCreateDTO } from 'common/dto/comp_grid_levels/dto';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

class Handler extends BaseHandler {
  @Post()
  async post(
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Body() body: CompGridLevelCreateDTO[],
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const data = await createMany(req);
    res
      .status(201)
      .json({ stats: { current_length: data.count }, statusText: 'ok' });
  }
}

export default withAuth(createHandler(Handler));

const createMany = async (req: ExtNextApiRequest & NextApiRequest) => {
  const { data } = req.body;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const newData = data.map((datum: any) => ({
    ...datum,
    comp_grid_id: +datum.comp_grid_id,
    account_id: req.account_id,
    created_by: req.uid,
    created_proxied_by: req.ouid,
  }));
  const results = await prisma.accounting_transactions.createMany({
    data: newData,
  });

  return results;
};
