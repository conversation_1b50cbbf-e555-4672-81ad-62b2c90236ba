import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON><PERSON>, Get, Post, Req, Res } from 'next-api-decorators';
import {
  PerAgentGetTransactionsSchema,
  PerAgentPostTransactionSchema,
} from 'common/dto/accounting_transactions';
import type {
  PerAgentGetTransactionsDto,
  PerAgentPostTransactionDto,
} from 'common/dto/accounting_transactions';

import { container } from '@/ioc';
import {
  AccountingService,
  type IAccountingService,
} from '@/services/accounting';
import { ZodBody, ZodQuery } from '@/lib/decorators';
import { ContactService, type IContactService } from '@/services/contact';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';

class Handler extends BaseHandler {
  private accountingService: IAccountingService;
  private contactService: IContactService;

  constructor() {
    super();
    this.accountingService = container.get(AccountingService);
    this.contactService = container.get(ContactService) as IContactService;
  }

  @Get()
  async get(
    @Req() _req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    // biome-ignore format: compound decorator
    @(ZodQuery(PerAgentGetTransactionsSchema)())
    query: PerAgentGetTransactionsDto
  ) {
    const { agent_str_id, start_date, end_date, transaction_str_id, search } =
      query;

    const queryPagination =
      query.page && query.limit
        ? { page: Number(query.page), limit: Number(query.limit) }
        : undefined;

    const response =
      await this.accountingService.getAccountingTransactionsByContact({
        agent_str_id,
        // @ts-expect-error
        start_date,
        // @ts-expect-error
        end_date,
        // @ts-expect-error
        transaction_str_id,
        pagination: queryPagination,
        query_search: search,
      });

    res.json({
      transactions: response.transactions,
      total_transactions: response.total_transactions,
      pagination: response.pagination,
    });
  }

  @Post()
  async create(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse,
    // biome-ignore format: compound decorator
    @(ZodBody(PerAgentPostTransactionSchema)())
    body: PerAgentPostTransactionDto
  ) {
    const { agent_str_id, transactions } = body;

    const agentId =
      await this.contactService.getContactIdsByStrIdList(agent_str_id);

    const result = await this.accountingService.manageAccountingDataByContact({
      contactId: agentId[0],
      // @ts-expect-error
      accountId: req.account_id,
      // @ts-expect-error
      uid: req.uid,
      // @ts-expect-error
      ouid: req.ouid,
      updatedAccountingData: transactions.updates,
      deletedAccountingData: transactions.deletes,
    });

    res.json({ success: true, data: result });
  }
}

export default withAuth(createHandler(Handler));
