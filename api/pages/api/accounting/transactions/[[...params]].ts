import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';
import * as dto from 'common/dto/comp_grid_levels/dto';
import { convertDateFields } from 'common/helpers';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { ZodBody } from '@/lib/decorators';
import type { Prisma } from '@prisma/client';

class Handler extends BaseHandler {
  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const data = await _getData(req);
    res.json(data);
  }

  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridLevelCreateDTOSchema)())
    body: dto.CompGridLevelCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await createOne(req, res, body);
  }

  @Patch()
  async patch(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridLevelUpdateDTOSchema)())
    body: dto.CompGridLevelUpdateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await updateOne(req, res, body);
  }

  @Delete()
  async delete(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CompGridLevelDeleteDTOSchema)())
    _body: dto.CompGridLevelDeleteDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    return await deleteMany(req, res);
  }
}

export default withAuth(createHandler(Handler));

export const _getData = async (req: ExtNextApiRequest & NextApiRequest) => {
  const where: Prisma.accounting_transactionsWhereInput = {
    account_id: req.account_id,
    state: { not: 'deleted' },
    str_id: req.query?.id,
    contact_id: undefined,
  };
  if (req.query.contact_id) where.contact_id = +req.query.contact_id;
  const data = await prisma.accounting_transactions.findMany({
    where,
    include: {
      contact: {
        select: {
          id: true,
          email: true,
          first_name: true,
          last_name: true,
        },
      },
      report_data: {
        select: {
          id: true,
          str_id: true,
          policy_id: true,
          product_name: true,
          effective_date: true,
          policy_status: true,
        },
      },
      accounting_transaction_details: {
        select: {
          id: true,
          str_id: true,
          amount: true,
          type: true,
          status: true,
        },
      },
    },
  });
  return data;
};

const createOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridLevelCreateDTO
) => {
  body = convertDateFields(body);
  const { contact_id, report_id, ..._body } = body;

  const data = await prisma.accounting_transactions.create({
    data: {
      ..._body,
      contact_id: contact_id ? contact_id : null,
      report_id: report_id ? report_id : null,
      account_id: req.account_id,
      created_by: req.uid,
      created_proxied_by: req.ouid,
    },
  });
  res.status(201).json(data);
};

const updateOne = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  body: dto.CompGridLevelUpdateDTO
) => {
  const { id, contact_id, report_id, ..._body } = body;
  if (!id) throw new Error('Missing id');
  let data = {};
  data = await prisma.accounting_transactions.update({
    where: { id, account_id: String(req.account_id) },
    data: {
      ..._body,
      contact: contact_id ? { connect: { id: contact_id } } : undefined,
      report_data: report_id ? { connect: { id: report_id } } : undefined,
      updated_at: new Date(),
      updated_by: req.uid,
      updated_proxied_by: req.ouid,
      accounting_transaction_details: undefined,
    },
  });
  res.status(200).json(data);
};

const deleteMany = async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
  const { ids } = req.body;
  if (!Array.isArray(ids) || ids.length === 0) throw new Error('Missing ids');
  // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
    throw new Error('Invalid ids');
  const promises = ids.flatMap((_id) => [
    prisma.accounting_transactions.update({
      where: { id: Number(_id), account_id: String(req.account_id) },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    }),
    prisma.accounting_transaction_details.updateMany({
      where: {
        transaction_id: Number(_id),
        account_id: String(req.account_id),
      },
      data: {
        state: 'deleted',
        updated_at: new Date(),
        updated_by: req.uid,
        updated_proxied_by: req.ouid,
      },
    }),
  ]);
  await prisma.$transaction(promises);
  res.status(200).json({ status: 'OK' });
};
