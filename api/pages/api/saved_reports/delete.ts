import { captureException } from '@sentry/nextjs';
import * as dto from 'common/dto/saved_reports/dto';
import type { NextApiRequest } from 'next';
import { createHandler, Delete, Req } from 'next-api-decorators';
import { BaseHandler } from '@/lib/baseHandler';
import { ZodBody } from '@/lib/decorators';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest } from '@/types';
import { prismaClient } from '@/lib/prisma';

class <PERSON><PERSON> extends BaseHandler {
  @Delete()
  async delete(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.SavedReportsDelete)())
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    body: any
  ) {
    const { str_id } = body;

    try {
      // @ts-expect-error
      await prismaClient.saved_reports.delete({
        where: { str_id },
      });
      return { success: true };
    } catch (error) {
      captureException(error);
      throw new Error('Failed to delete saved report');
    }
  }
}

export default withAuth(createHandler(Handler));
