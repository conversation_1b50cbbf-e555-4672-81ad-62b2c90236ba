import type { NextApiRequest, NextApiResponse } from 'next';
import { Post, Req, Res, createHandler } from 'next-api-decorators';
import * as dto from 'common/dto/comp_grid_criteria/dto';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.BulkCompGridCriteriaCreateDTOSchema)())
    body: dto.BulkCompGridCriteriaCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const data = await createMany(req, body);
    res
      .status(201)
      .json({ stats: { current_length: data.count }, statusText: 'ok' });
  }
}

export default withAuth(createHandler(Handler));

const idify = (s: string) => {
  const trimmed = typeof s === 'string' ? s.trim() : s;
  if (trimmed) return +trimmed;
  else return null;
};

const createMany = async (
  req: ExtNextApiRequest & NextApiRequest,
  body: dto.BulkCompGridCriteriaCreateDTO
) => {
  const { data } = body;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const newData = data.map((datum: any) => ({
    ...datum,
    comp_grid_id: idify(datum.comp_grid_id),
    company_id: idify(datum.company_id),
    grid_product_id: idify(datum.grid_product_id),
    issue_age_end: idify(datum.issue_age_end),
    issue_age_start: idify(datum.issue_age_start),
    premium_min: idify(datum.premium_min),
    premium_max: idify(datum.premium_max),
    policy_year_end: idify(datum.policy_year_end),
    policy_year_start: idify(datum.policy_year_start),
    account_id: req.account_id,
    created_by: req.uid,
    created_proxied_by: req.ouid,
  }));
  const results = await prisma.comp_grid_criteria.createMany({
    data: newData,
  });

  return results;
};
