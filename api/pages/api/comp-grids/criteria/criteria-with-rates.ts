import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON>ler, Post, Req, Res } from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import {
  CompGridCriteriaService,
  type ICompGridCriteriaService,
} from '@/services/comp-grids/criteria';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { withAuth } from '@/lib/middlewares';

class Handler extends BaseHandler {
  private compGridCriteriaService: CompGridCriteriaService;

  constructor() {
    super();
    this.compGridCriteriaService = container.get<ICompGridCriteriaService>(
      CompGridCriteriaService
    );
  }

  @Post()
  async criteriaWithRates(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const criteria = await this.compGridCriteriaService.addCriteriaWithRates(
      req.body,
      // @ts-expect-error
      req.account_id,
      req.uid,
      req.ouid
    );

    if (!criteria) {
      res.status(500).json({ error: 'Error creating criteria' });
      return;
    }
    res.status(200).json({ status: 'OK', data: criteria });
  }
}

export default withAuth(createHandler(Handler));
