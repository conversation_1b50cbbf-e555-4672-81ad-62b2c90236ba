import type { NextApiRequest, NextApiResponse } from 'next';
import {
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
  createHandler,
} from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import {
  CompGridLevelsService,
  type ICompGridLevelsService,
} from '@/services/comp-grids/levels';
import { container } from '@/ioc';
import { CompGridValidator } from '@/pages/api/comp-grids/validator';

class Handler extends BaseHandler {
  private compGridLevelsService: CompGridLevelsService;
  private compGridValidator: CompGridValidator;

  constructor() {
    super();
    // @ts-expect-error
    this.compGridLevelsService = container.get<ICompGridLevelsService>(
      CompGridLevelsService
    );
    this.compGridValidator =
      container.get<CompGridValidator>(CompGridValidator);
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { page, comp_grid_id, limit } =
      this.compGridValidator.validateCompGridLevelsGetParams(req.query);

    const data = await this.compGridLevelsService.getCompGridLevels(
      page,
      limit,
      // @ts-expect-error
      comp_grid_id
    );

    res.json(data);
  }

  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const params = this.compGridValidator.validateCompGridLevelsCreateParams(
      req.body
    );
    const { uid, ouid, account_id } = req;
    const data = await this.compGridLevelsService.createCompGridLevel(
      params,
      // @ts-expect-error
      account_id,
      uid,
      ouid
    );
    res.status(201).json(data);
  }

  @Patch()
  async patch(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const params = this.compGridValidator.validateCompGridLevelsUpdateParams(
      req.body
    );
    const { uid, ouid } = req;
    const data = await this.compGridLevelsService.updateCompGridLevel(
      params,
      // @ts-expect-error
      uid,
      ouid
    );

    res.status(200).json(data);
  }

  @Delete()
  async delete(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const { ids } = this.compGridValidator.validateCompGridLevelsDeleteParams(
      req.body
    );
    const { uid, ouid } = req;

    const data = await this.compGridLevelsService.deleteCompGridLevels(
      ids,
      // @ts-expect-error
      uid,
      ouid
    );

    res.status(200).json(data);
  }
}

export const getCompGridLevelsDynamicSelects = async (
  req: ExtNextApiRequest & NextApiRequest
) => {
  const compGridLevelsService = container.get<CompGridLevelsService>(
    CompGridLevelsService
  );
  const compGridValidator = container.get<CompGridValidator>(CompGridValidator);

  const { comp_grid_id, is_dynamic_select } =
    compGridValidator.validateCompGridLevelsGetParams(req.query);

  return await compGridLevelsService.getCompGridLevels(
    1,
    undefined,
    // @ts-expect-error
    comp_grid_id,
    is_dynamic_select
  );
};

export default withAuth(createHandler(Handler));
