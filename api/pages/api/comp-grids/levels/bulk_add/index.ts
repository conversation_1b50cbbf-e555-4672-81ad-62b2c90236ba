import type { NextApiRequest, NextApiResponse } from 'next';
import { Post, Req, Res, createHandler } from 'next-api-decorators';
import * as dto from 'common/dto/comp_grid_levels/dto';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { ZodBody } from '@/lib/decorators';

class <PERSON><PERSON> extends BaseHandler {
  @Post()
  async post(
    // biome-ignore format: compound decorator
    @(ZodBody(dto.BulkCompGridLevelCreateDTOSchema)())
    body: dto.BulkCompGridLevelCreateDTO,
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const data = await createMany(req, body);
    res
      .status(201)
      .json({ stats: { current_length: data.count }, statusText: 'ok' });
  }
}

export default withAuth(createHandler(Handler));

const createMany = async (
  req: ExtNextApiRequest & NextApiRequest,
  body: dto.BulkCompGridLevelCreateDTO
) => {
  const { data } = body;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const newData = data.map((datum: any) => ({
    ...datum,
    comp_grid_id: +datum.comp_grid_id,
    account_id: req.account_id,
    created_by: req.uid,
    created_proxied_by: req.ouid,
  }));

  const results = await prisma.comp_grid_levels.createMany({
    data: newData,
  });

  return results;
};
