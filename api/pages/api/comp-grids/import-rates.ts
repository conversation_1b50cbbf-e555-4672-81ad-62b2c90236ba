import { arrayT<PERSON><PERSON><PERSON>, is<PERSON><PERSON> } from 'common/helpers';
import { cloneDeep } from 'lodash-es';
import type { NextApiRequest, NextApiResponse } from 'next';
import { Post, Req, Res, createHandler } from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import dayjs from '@/lib/dayjs';
import { limitConcurrency } from '@/lib/helpers';
import { withAuth } from '@/lib/middlewares';
import prisma from '@/lib/prisma';
import type {
  ExtAccountInfo,
  ExtNextApiRequest,
  ExtNextApiResponse,
} from '@/types';

class Handler extends BaseHandler {
  @Post()
  async post(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const data = await importRates(req);
    res.status(201).json({
      data,
      // Stats: { added: data.added, updated: data.updated },
      statusText: 'ok',
    });
  }
}

export default withAuth(createHandler(Handler));

// @ts-expect-error
const getGridStructures = (grids) => {
  const gridStructures = {};
  for (const [gridName, gridLevels] of Object.entries(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    grids as Record<string, any>
  )) {
    for (const [gridLevel, gridData] of Object.entries(
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      gridLevels as Record<string, any[]>
    )) {
      const gridHeader = gridData[0];
      const gridrates = gridData.slice(1);
      const gridJson = arrayToJson(gridrates, gridHeader);
      for (const gridRow of gridJson as {
        product_name: string;
        product_type: string;
        compensation_type: string;
        issue_age_start: number;
        issue_age_end: number;
        policy_year_start: number;
        policy_year_end: number;
        premium_min: number;
        premium_max: number;
        effective_date?: string;
        carrier_rate?: number;
        house_rate?: number;
        total_rate?: number;
      }[]) {
        // @ts-expect-error
        if (!gridStructures[gridName]) {
          // @ts-expect-error
          gridStructures[gridName] = {
            products: new Set(),
            levels: new Set(),
            criteria: new Set(),
          };
        }
        // @ts-expect-error
        gridStructures[gridName].products.add(
          JSON.stringify({
            product_type: gridRow.product_type,
            product_name: gridRow.product_name,
          })
        );
        // @ts-expect-error
        gridStructures[gridName].levels.add(gridLevel);
        // @ts-expect-error
        gridStructures[gridName].criteria.add(
          JSON.stringify({
            product_type: gridRow.product_type,
            product_name: gridRow.product_name,
            compensation_type: gridRow.compensation_type,
            issue_age_start: gridRow.issue_age_start,
            issue_age_end: gridRow.issue_age_end,
            policy_year_start: gridRow.policy_year_start,
            policy_year_end: gridRow.policy_year_end,
            premium_min: gridRow.premium_min,
            premium_max: gridRow.premium_max,
          })
        );
      }
    }
  }
  return gridStructures;
};

const ratesFormatter = (rates: {
  carrier_rate: number;
  house_rate: number;
  total_rate?: number;
}) =>
  `Carrier: ${rates.carrier_rate}, House: ${rates.house_rate}, Total: ${rates.total_rate ?? rates.total_rate}`;

const createGridStructures = async (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  gridStructures: Record<string, any>,
  accountInfo: ExtAccountInfo,
  createGridStructures = true
) => {
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.log(
    `${createGridStructures ? 'Creating' : 'Checking'} grid structures`
  );
  const defaultGridStats: {
    [key: string]: { existing: string[]; missing: string[] };
  } = {
    grid: { existing: [], missing: [] },
    levels: { existing: [], missing: [] },
    products: { existing: [], missing: [] },
    criteria: { existing: [], missing: [] },
  };
  const errors = [];
  const stats: { [key: string]: typeof defaultGridStats } = {};
  for await (const [gridName, gridStructure] of Object.entries(
    gridStructures
  )) {
    stats[gridName] = cloneDeep(defaultGridStats);
    let compGrid = await prisma.comp_grids.findMany({
      where: {
        account_id: accountInfo.account_id,
        state: 'active',
        name: gridName,
      },
      select: {
        id: true,
        name: true,
        company_id: true,
        comp_grid_levels: { where: { state: 'active' } },
        comp_grid_products: { where: { state: 'active' } },
        comp_grid_criteria: { where: { state: 'active' } },
      },
    });
    if (compGrid.length !== 1) {
      stats[gridName].grid.missing.push(gridName);
      errors.push(
        `Unable to find unique comp grid: ${gridName} (${compGrid.length})`
      );
      continue;
    } else {
      stats[gridName].grid.existing.push(gridName);
    }

    for await (const level of gridStructure.levels) {
      // @ts-expect-error
      if (compGrid[0].comp_grid_levels.some((l) => l.name === level)) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(`Level "${level}" for grid ${gridName} - EXISTS`);
        stats[gridName].levels.existing.push(level);
        continue;
      } else {
        stats[gridName].levels.missing.push(level);
      }
      if (createGridStructures) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(
          `Create new comp grid level: "${level}" for grid ${gridName}`
        );
        await prisma.comp_grid_levels.create({
          data: {
            account_id: accountInfo.account_id,
            created_by: accountInfo.uid,
            created_proxied_by: accountInfo.ouid,
            name: level,
            state: 'active',
            comp_grid: { connect: { id: compGrid[0].id } },
          },
        });
      } else {
        errors.push(`Level "${level}" does not exist for grid ${gridName}`);
      }
    }

    for await (const product of gridStructure.products) {
      const { product_name, product_type } = JSON.parse(product);
      if (
        compGrid[0].comp_grid_products.some(
          (p: { name: string; type: string }) =>
            p.name === product_name && p.type === product_type
        )
      ) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(
          `Product "${product_name} (${product_type})" for grid ${gridName} - EXISTS`
        );
        stats[gridName].products.existing.push(product);
        continue;
      } else {
        stats[gridName].products.missing.push(product);
      }
      if (createGridStructures) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(
          `Create new comp grid product: "${product_name} (${product_type})" for grid ${gridName}`
        );
        const company_product = await prisma.company_products.findMany({
          where: {
            account_id: accountInfo.account_id,
            state: 'active',
            product_name: product_name,
            product_type: product_type,
          },
          select: { id: true },
        });
        await prisma.comp_grid_products.create({
          data: {
            account_id: accountInfo.account_id,
            created_by: accountInfo.uid,
            created_proxied_by: accountInfo.ouid,
            name: product_name,
            type: product_type,
            state: 'active',
            comp_grid: { connect: { id: compGrid[0].id } },
            company_products: company_product?.[0]?.id
              ? { connect: { id: company_product[0].id } }
              : undefined,
          },
        });
      } else {
        errors.push(
          `Product "${product_name} (${product_type})" does not exist`
        );
      }
    }

    compGrid = await prisma.comp_grids.findMany({
      where: {
        account_id: accountInfo.account_id,
        state: 'active',
        name: gridName,
      },
      select: {
        id: true,
        name: true,
        company_id: true,
        comp_grid_levels: { where: { state: 'active' } },
        comp_grid_products: { where: { state: 'active' } },
        comp_grid_criteria: {
          where: { state: 'active' },
          select: {
            issue_age_start: true,
            issue_age_end: true,
            policy_year_start: true,
            policy_year_end: true,
            premium_min: true,
            premium_max: true,
            compensation_type: true,
            comp_grid_product: {
              where: { state: 'active' },
              select: { name: true, type: true },
            },
          },
        },
      },
    });

    for await (const criterion of gridStructure.criteria) {
      const {
        product_name,
        product_type,
        issue_age_start,
        issue_age_end,
        policy_year_start,
        policy_year_end,
        premium_min,
        premium_max,
        compensation_type,
      } = JSON.parse(criterion);
      if (
        compGrid[0].comp_grid_criteria.some(
          (c: {
            comp_grid_product?: { name: string; type: string };
            issue_age_start: number | null;
            issue_age_end: number | null;
            policy_year_start: number | null;
            policy_year_end: number | null;
            premium_min: number | null;
            premium_max: number | null;
            compensation_type: string | null;
          }) =>
            (c.comp_grid_product?.name === product_name ||
              (!c.comp_grid_product?.name && !product_name)) &&
            (c.comp_grid_product?.type === product_type ||
              (!c.comp_grid_product?.type && !product_type)) &&
            (c.issue_age_start === issue_age_start ||
              (c.issue_age_start === null && isNill(issue_age_start))) &&
            (c.issue_age_end === issue_age_end ||
              (c.issue_age_end === null && isNill(issue_age_end))) &&
            (c.policy_year_start === policy_year_start ||
              (c.policy_year_start === null && isNill(policy_year_start))) &&
            (c.policy_year_end === policy_year_end ||
              (c.policy_year_end === null && isNill(policy_year_end))) &&
            (c.premium_min === premium_min ||
              (c.premium_min === null && isNill(premium_min))) &&
            (c.premium_max === premium_max ||
              (c.premium_max === null && isNill(premium_max))) &&
            (c.compensation_type === compensation_type ||
              (!c.compensation_type && isNill(compensation_type)))
        )
      ) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(
          `Criteria "${product_name} (${product_type})/${issue_age_start}-${issue_age_end}/${compensation_type}" for grid ${gridName} - EXISTS`
        );
        stats[gridName].criteria.existing.push(criterion);
        continue;
      } else {
        stats[gridName].criteria.missing.push(criterion);
      }
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.log(
        `Create new comp grid criteria: "${product_name} (${product_type})/${issue_age_start}-${issue_age_end}/${compensation_type}" for grid ${gridName}`
      );
      if (createGridStructures) {
        await prisma.comp_grid_criteria.create({
          data: {
            account_id: accountInfo.account_id,
            created_by: accountInfo.uid,
            created_proxied_by: accountInfo.ouid,
            issue_age_start,
            issue_age_end,
            policy_year_start,
            policy_year_end,
            premium_min,
            premium_max,
            compensation_type: compensation_type,
            state: 'active',
            comp_grid: { connect: { id: compGrid[0].id } },
            comp_grid_product: {
              connect: {
                id: compGrid[0].comp_grid_products.find(
                  (p: { name: string; type: string; state: string }) =>
                    p.name === product_name &&
                    p.type === product_type &&
                    p.state === 'active'
                ).id,
              },
            },
            company: {
              connect: { id: compGrid[0].company_id },
            },
          },
        });
      } else {
        errors.push(
          `Criteria "${product_name} (${product_type})/${issue_age_start}-${issue_age_end}/${compensation_type}" does not exist`
        );
      }
    }
  }
  return { stats, errors };
};

const importRates = async (req: ExtNextApiRequest & NextApiRequest) => {
  const { uid: _uid, account_id: _account_id, opts, data: grids } = req.body;
  const stats = { rates: { added: 0, updated: 0 } };

  // Check all comp grid products exist, create if necessary
  const gridStructures = getGridStructures(grids);
  const { errors: structureErrors, stats: structureStats } =
    await createGridStructures(
      gridStructures,
      {
        // @ts-expect-error
        account_id: req.account_id,
        // @ts-expect-error
        uid: req.uid,
        // @ts-expect-error
        ouid: req.ouid,
      },
      false
    );
  if (opts?.validateOnly) {
    return structureStats;
  }
  if (structureErrors.length > 0 && !opts?.createGridStructure) {
    throw new Error(structureErrors.join('. '));
  }
  if (
    !Object.values(Object.values(structureStats)).every((grid) =>
      Object.values(grid).every((entity) => entity.missing.length === 0)
    )
  ) {
    await createGridStructures(
      gridStructures,
      {
        // @ts-expect-error
        account_id: req.account_id,
        // @ts-expect-error
        uid: req.uid,
        // @ts-expect-error
        ouid: req.ouid,
      },
      !!opts?.createGridStructure
    );
  }

  const compGrids = await prisma.comp_grids.findMany({
    where: {
      account_id: req.account_id,
      state: 'active',
    },
    select: {
      id: true,
      name: true,
      company_id: true,
    },
  });
  const compGridMap = {};
  for await (const grid of compGrids) {
    // @ts-expect-error
    if (compGridMap[grid.name]) {
      throw new Error(
        // @ts-expect-error
        `Duplicate comp grid name found: ${grid.name} (${compGridMap[grid.name].id}, ${grid.id})`
      );
    }
    // @ts-expect-error
    compGridMap[grid.name] = grid;
  }

  const compGridProducts = await prisma.comp_grid_products.findMany({
    where: {
      account_id: req.account_id,
      state: 'active',
    },
    select: {
      id: true,
      name: true,
      type: true,
      comp_grid_id: true,
    },
  });
  // @ts-expect-error
  const getProductKey = (product) =>
    `${product.comp_grid_id}::${product.type ?? ''}::${product.name ?? ''}`;
  const compGridProductMap = {};
  for (const product of compGridProducts) {
    const key = getProductKey(product);
    // @ts-expect-error
    if (compGridProductMap[key]) {
      throw new Error(
        `Duplicate comp grid product type / name found: ${product.name} (${product.type}) for grid (${product.comp_grid_id})`
      );
    }
    // @ts-expect-error
    compGridProductMap[key] = product;
  }

  for await (const [gridName, gridLevels] of Object.entries(grids)) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log(`Processing grid: ${gridName}`);
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.group();
    for await (const [levelName, gridLevel] of Object.entries(
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      gridLevels as Record<string, any[]>
    )) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.log(`Processing level: ${levelName}`);
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.group();
      const gridHeader = gridLevel[0];
      const gridData = gridLevel.slice(1);
      const gridJson = arrayToJson(gridData, gridHeader);

      const compGridCriteriaMap = {};

      const criteriaResults = await limitConcurrency(
        async (gridRow) => {
          // @ts-expect-error
          const compGrid = compGridMap[gridName];
          if (!compGrid)
            throw new Error(`Unable to find comp grid: ${gridName}`);
          const compGridCriteriaKey = `${compGrid.id}::${gridRow.product_type}::${gridRow.product_name}::${gridRow.compensation_type}::${gridRow.issue_age_start}::${gridRow.issue_age_end}::${gridRow.policy_year_start}::${gridRow.policy_year_end}::${gridRow.premium_min}::${gridRow.premium_max}`;
          const criteria = await prisma.comp_grid_criteria.findMany({
            where: {
              account_id: req.account_id,
              state: 'active',
              compensation_type: gridRow.compensation_type,
              issue_age_start: gridRow.issue_age_start,
              issue_age_end: gridRow.issue_age_end,
              policy_year_start: gridRow.policy_year_start,
              policy_year_end: gridRow.policy_year_end,
              premium_min: gridRow.premium_min,
              premium_max: gridRow.premium_max,
              comp_grid: {
                state: 'active',
                name: gridName,
              },
              comp_grid_product: {
                state: 'active',
                name: gridRow.product_name,
                type: gridRow.product_type,
              },
              company_id: compGrid.company_id,
            },
            select: {
              id: true,
              comp_grid_rates: {
                where: { state: 'active' },
                select: {
                  id: true,
                  date_ranges: {
                    where: { state: 'active' },
                    select: {
                      id: true,
                      name: true,
                      start_date: true,
                      end_date: true,
                    },
                  },
                  comp_grid_level: true,
                  carrier_rate: true,
                  house_rate: true,
                  rate: true,
                },
              },
              comp_grid: {
                where: { state: 'active' },
                select: {
                  id: true,
                  name: true,
                  comp_grid_levels: {
                    where: {
                      state: 'active',
                      name: levelName,
                    },
                  },
                },
              },
              date_ranges: {
                where: { state: 'active' },
                select: {
                  id: true,
                  name: true,
                  start_date: true,
                  end_date: true,
                },
              },
            },
          });
          if (criteria.length !== 1) {
            throw new Error(
              `Unable to find unique criteria: ${Object.values(gridRow).join(',')}`
            );
          }
          return [compGridCriteriaKey, criteria[0]];
        },
        gridJson as {
          grid_name: string;
          product_name: string;
          product_type: string;
          compensation_type: string;
          issue_age_start: number;
          issue_age_end: number;
          policy_year_start: number;
          policy_year_end: number;
          premium_min: number;
          premium_max: number;
          effective_date?: string;
          carrier_rate?: number;
          house_rate?: number;
          total_rate?: number;
        }[],
        20,
        {
          onFail: (error) => {
            // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            console.error('Failed to get criteria:', error);
            throw error;
          },
        }
      );
      for (const [key, value] of criteriaResults) {
        // @ts-expect-error
        compGridCriteriaMap[key] = value;
      }

      for await (const gridRow of gridJson as {
        grid_name: string;
        product_name: string;
        product_type: string;
        compensation_type: string;
        issue_age_start: number;
        issue_age_end: number;
        policy_year_start: number;
        policy_year_end: number;
        premium_min: number;
        premium_max: number;
        effective_date?: string;
        carrier_rate?: number;
        house_rate?: number;
        total_rate?: number;
      }[]) {
        // @ts-expect-error
        const compGrid = compGridMap[gridName];
        if (!compGrid) {
          throw new Error(`Unable to find comp grid: ${gridName}`);
        }
        const compGridProduct =
          // @ts-expect-error
          compGridProductMap[
            `${compGrid.id}::${gridRow.product_type}::${gridRow.product_name}`
          ];
        if (!compGridProduct) {
          throw new Error(
            `Unable to find comp grid product: ${gridRow.product_name} (${gridRow.product_type})`
          );
        }
        const compGridCriteriaKey = `${compGrid.id}::${gridRow.product_type}::${gridRow.product_name}::${gridRow.compensation_type}::${gridRow.issue_age_start}::${gridRow.issue_age_end}::${gridRow.policy_year_start}::${gridRow.policy_year_end}::${gridRow.premium_min}::${gridRow.premium_max}`;

        // @ts-expect-error
        const criteria = compGridCriteriaMap[compGridCriteriaKey];
        if (!criteria) {
          throw new Error(
            `Unable to find criteria: ${Object.values(gridRow).join(',')}`
          );
        }

        if (
          criteria.comp_grid.comp_grid_levels.filter(
            (l: { name: string }) => l.name === levelName
          ).length !== 1
        ) {
          // Need to create new level, allow in bulk import?
          throw new Error(`Unique level not found: ${levelName}`);
        }

        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(
          `Update rates for criteria: (${Object.values(gridRow).join(',')}) and level ${levelName}`
        );
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.group();
        const existingRate =
          criteria.comp_grid_rates.filter(
            (rate: { comp_grid_level: { name: string } }) =>
              rate.comp_grid_level.name === levelName
          ).length > 0;
        if (existingRate && !gridRow.effective_date) {
          throw new Error(
            `Existing rates, but no new date specified: ${Object.values(gridRow).join(',')}`
          );
        }

        const newStartDate = gridRow.effective_date
          ? dayjs.utc(gridRow.effective_date)
          : null;
        // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        let endDate;

        if (existingRate) {
          const oldEndDate = dayjs
            .utc(gridRow.effective_date)
            .subtract(1, 'day');

          // Does not count open ended date ranges as conflict, since those will be closed below
          const ratesWithDateRangeConflict = criteria.comp_grid_rates.filter(
            // @ts-expect-error
            (rate) => {
              if (rate.comp_grid_level.name !== levelName) return false;
              if (rate.date_ranges.length === 0) return false;
              // @ts-expect-error
              return rate.date_ranges.some((range) => {
                if (!range.start_date)
                  return dayjs.utc(range.end_date).isSameOrAfter(newStartDate);
                if (!range.end_date)
                  return dayjs.utc(range.start_date).isSame(newStartDate);
                return (
                  dayjs.utc(range.start_date).isSameOrBefore(newStartDate) &&
                  dayjs.utc(range.end_date).isSameOrAfter(newStartDate)
                );
              });
            }
          );
          if (ratesWithDateRangeConflict.length > 0) {
            // Existing data for criteria, level, date range
            const existingRateWithSameDate = ratesWithDateRangeConflict.filter(
              // @ts-expect-error
              (rate) =>
                rate.date_ranges.length === 1 &&
                dayjs.utc(rate.date_ranges[0].start_date).isSame(newStartDate)
            );
            // We have some duplicates with the same ranges - if so, consider the same
            const allSameStartDate =
              existingRateWithSameDate.length > 0 &&
              existingRateWithSameDate.every(
                // @ts-expect-error
                (rate) =>
                  rate.date_ranges.length === 1 &&
                  dayjs.utc(rate.date_ranges[0].start_date).isSame(newStartDate)
              );
            if (
              // RatesWithDateRangeConflict.length === 1 &&
              // RatesWithDateRangeConflict[0].date_ranges.length === 1 &&
              existingRateWithSameDate.length === 1 ||
              allSameStartDate
            ) {
              for await (const existingRate of existingRateWithSameDate) {
                if (
                  // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  existingRate.carrier_rate ==
                    (gridRow.carrier_rate ?? gridRow.total_rate) &&
                  // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  existingRate.house_rate == gridRow.house_rate &&
                  // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  existingRate.rate == gridRow.total_rate
                ) {
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  console.log(
                    `Skipping - no change for rate id: ${existingRate.id}, rates: ${ratesFormatter(
                      existingRate
                    )}`
                  );
                } else {
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  console.log(
                    // @ts-expect-error
                    `Updating existing rate with same start date, rate id: ${existingRate.id}. Old: [${ratesFormatter(existingRate)}], New: [${ratesFormatter(gridRow)}]`
                  );
                  await prisma.comp_grid_rates.update({
                    where: {
                      account_id: req.account_id,
                      id: existingRate.id,
                    },
                    data: {
                      updated_at: new Date(),
                      updated_by: req.uid,
                      updated_proxied_by: req.ouid,
                      carrier_rate: gridRow.carrier_rate ?? gridRow.total_rate,
                      house_rate: gridRow.house_rate,
                      rate: gridRow.total_rate,
                    },
                  });
                  stats.rates.updated++;
                }
              }
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              console.groupEnd();
              continue;
            } else {
              throw new Error(
                `Existing rates with date range conflict: ${Object.values(gridRow).join(',')} for level "${levelName}"`
              );
            }
          }
          const ratesWithLaterStartDates = criteria.comp_grid_rates.filter(
            // @ts-expect-error
            (rate) => {
              if (rate.comp_grid_level.name !== levelName) return false;
              // @ts-expect-error
              return rate.date_ranges.some((range) => {
                return dayjs.utc(range.start_date).isAfter(newStartDate);
              });
            }
          );
          if (ratesWithLaterStartDates.length > 0) {
            endDate = dayjs
              .utc(
                ratesWithLaterStartDates.sort(
                  // @ts-expect-error
                  (a, b) =>
                    dayjs.utc(a.date_ranges[0].start_date).unix() -
                    dayjs.utc(b.date_ranges[0].start_date).unix()
                )[0].date_ranges[0].start_date
              )
              .subtract(1, 'day');
          }
          const openEndedRatesToEnd = criteria.comp_grid_rates.filter(
            // @ts-expect-error
            (rate) =>
              rate.comp_grid_level.name === levelName &&
              (rate.date_ranges.length === 0 ||
                (rate.date_ranges[0].end_date === null &&
                  dayjs
                    .utc(rate.date_ranges[0].start_date)
                    .isBefore(newStartDate)))
          );
          if (openEndedRatesToEnd.length > 0) {
            if (openEndedRatesToEnd.length > 1) {
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              console.warn(
                `Multiple open ended rates found: ${Object.values(gridRow).join(',')}. This is an invalid state.`
              );
              // Throw new Error(
              //   `Multiple open ended rates found: ${Object.values(gridRow).join(',')}. This is an invalid state.`
              // );
            }
            // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            console.log(
              `End existing open ended rates for: ${Object.values(gridRow).join(',')}`
            );

            for await (const openEndedRateToEnd of openEndedRatesToEnd) {
              // If no date range, create one
              if (openEndedRateToEnd.date_ranges.length === 0) {
                // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                console.log(
                  `No date range exists, adding date range for comp_grid_rate_id: ${openEndedRateToEnd.id} end date to ${oldEndDate.toISOString()}`
                );
                await prisma.comp_grid_rates.update({
                  where: { id: openEndedRateToEnd.id },
                  data: {
                    date_ranges: {
                      create: {
                        account_id: req.account_id,
                        created_by: req.uid,
                        created_proxied_by: req.ouid,
                        start_date: null,
                        end_date: oldEndDate.toISOString(),
                        type: 'any',
                      },
                    },
                  },
                });
              }
              // Date range exists, set end date
              else {
                // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                console.log(
                  `Date range exists for comp_grid_rate_id: ${openEndedRateToEnd.id}, setting end date to ${oldEndDate.toISOString()}`
                );
                // If the date range is shared with other rates, we don't
                // know whether those other rates are going to be updated or
                // not, so we'll detatch and use a new date_range
                await prisma.comp_grid_rates.update({
                  where: { id: openEndedRateToEnd.id },
                  data: {
                    date_ranges: {
                      set: [],
                      create: {
                        account_id: req.account_id,
                        created_by: req.uid,
                        created_proxied_by: req.ouid,
                        start_date:
                          openEndedRateToEnd.date_ranges[0].start_date,
                        end_date: oldEndDate.toISOString(),
                        type: 'any',
                      },
                    },
                  },
                });
              }
            }
          }
        }

        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(
          `Create new rates for criteria (${Object.values(gridRow).join(',')}), level: ${levelName}, date_range: ${newStartDate ? newStartDate.toISOString() : 'null'}`
        );
        await prisma.comp_grid_rates.create({
          data: {
            account_id: req.account_id,
            created_by: req.uid,
            created_proxied_by: req.ouid,
            date_ranges: newStartDate
              ? {
                  create: {
                    account_id: req.account_id,
                    created_by: req.uid,
                    created_proxied_by: req.ouid,
                    start_date: newStartDate.toISOString(),
                    end_date: endDate,
                    type: 'any',
                  },
                }
              : undefined,
            // TODO: What logic should be used for carrier_rate / total_rate when one or the other is missing?
            carrier_rate: gridRow.carrier_rate ?? gridRow.total_rate,
            house_rate: gridRow.house_rate,
            rate: gridRow.total_rate ?? gridRow.carrier_rate,
            comp_grid_criterion: {
              connect: {
                id: criteria.id,
              },
            },
            comp_grid_level: {
              connect: {
                id: criteria.comp_grid.comp_grid_levels.filter(
                  (l: { name: string }) => l.name === levelName
                )[0].id,
              },
            },
          },
        });
        stats.rates.added++;
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.groupEnd();
      }
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.groupEnd();
    }
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.groupEnd();
  }

  return stats;
};
