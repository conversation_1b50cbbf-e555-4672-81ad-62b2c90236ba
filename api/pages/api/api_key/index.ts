import type { NextApiRequest, NextApiResponse } from 'next';
import {
  createHandler,
  Delete,
  Get,
  Patch,
  Post,
  Req,
  Res,
} from 'next-api-decorators';

import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { container } from '@/ioc';
import { APIKeyService } from '@/services/api_key/service';

class Handler extends BaseHandler {
  private service: APIKeyService;
  constructor() {
    super();
    this.service = container.get(APIKeyService);
  }

  @Get()
  async get(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const params = {
      account_id: req.account_id,
      id: req.query?.id,
    };
    // @ts-expect-error
    const result = await this.service.getAPIKeys(params);
    res.json(result);
  }

  @Post()
  async create(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const result = await this.service.createAPIKey({
      ...req.body,
      uid: req.uid,
      account_id: req.account_id,
      created_by: req.uid,
      created_proxied_by: req.ouid,
    });
    res.status(201).json({ result });
  }

  @Patch()
  async update(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const result = await this.service.updateAPIKey({
      ...req.body,
      account_id: req.account_id,
      updated_by: req.uid,
      updated_proxied_by: req.ouid,
    });
    res.status(200).json(result);
  }

  @Delete()
  async delete(
    @Req() req: ExtNextApiRequest & NextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    await this.service.deleteAPIKey({
      id: req.body.ids[0],
      // @ts-expect-error
      account_id: req.account_id,
      // @ts-expect-error
      updated_by: req.uid,
      // @ts-expect-error
      updated_proxied_by: req.ouid,
    });
    res.status(200).json({ status: 'OK' });
  }
}

export default withAuth(createHandler(Handler));
