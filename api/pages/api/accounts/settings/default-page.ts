import type { NextApiRequest, NextApiResponse } from 'next';
import { create<PERSON><PERSON>ler, Get, Post, Req, Res } from 'next-api-decorators';

import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { withAuth } from '@/lib/middlewares';
import { SettingsService } from '@/services/settings';
import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { AccountSettingsValidator } from '@/pages/api/accounts/settings/validator';

class Handler extends BaseHandler {
  private settingsService: SettingsService;
  private validator: AccountSettingsValidator;

  constructor() {
    super();
    this.settingsService = container.get<SettingsService>(SettingsService);
    this.validator = container.get(AccountSettingsValidator);
  }

  @Get()
  async getDefaultLanding(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    // @ts-expect-error
    req.query.account_id = req.account_id;
    // @ts-expect-error
    const params = this.validator.validateDefaultPageGetParams(req.query);
    const { role_id, account_id } = params;

    if (!account_id) {
      throw new Error('Account id is required');
    }

    const response = await this.settingsService.getDefaultLandingPages(
      account_id,
      role_id
    );

    res.json(response);
  }

  @Post()
  async postDefaultLanding(
    @Req() req: NextApiRequest & ExtNextApiRequest,
    @Res() res: ExtNextApiResponse & NextApiResponse
  ) {
    const body = this.validator.validateDefaultPagePostParams({
      ...req.body,
      account_id: req.account_id,
      role_id: req.role_id,
    });
    const { default_page } = body;
    const { account_id, role_id } = req;
    if (!account_id) {
      throw new Error('Account id is required');
    }

    if (!default_page) {
      throw new Error('Default page is required');
    }

    if (!role_id) {
      throw new Error('Role is required');
    }

    const response = await this.settingsService.updateDefaultLandingPage(
      account_id,
      +role_id,
      default_page,
      // @ts-expect-error
      req.uid,
      req.ouid
    );

    res.json({ data: response });
  }
}

export default withAuth(createHandler(Handler));
