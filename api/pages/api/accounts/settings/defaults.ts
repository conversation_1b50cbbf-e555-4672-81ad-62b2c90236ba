import { customViewDefault } from 'common/constants/account_role_settings';

import {
  type ContactSettings,
  Roles,
  type ViewsAndFieldsPageSettings,
} from '@/types';

export enum DirectDownlineDataAccessCompGridRatesOptions {
  NO = 'No',
  YES = 'Yes',
}

export enum ExtendedDownlineDataAccessCompGridRatesOptions {
  NO = 'No',
  EXTENDED_DOWNLINES = 'extended_downlines',
  LEVELS_LOWER = 'levels_lower', // Hardcoded setting only for Transglobal account
  GRID_LEVELS_LOWER = 'grid_levels_lower',
}

export const defaultLandingPage = {
  id: '/reconciliation',
};

export const defaultRoleList = [
  {
    id: Roles.ACCOUNT_ADMIN,
  },
  {
    id: Roles.PRODUCER,
  },
  {
    id: Roles.DATA_SPECIALIST,
  },
];

// TODO: We need to use enums and boolean values instead of strings and add a custom component in the FE so we can use labels there.
export const defaultContactSettingsObject: ContactSettings = {
  uplineHierarchyAccessLevel: 'null',
  downlineHierarchyAccessLevel: 'null',
  directUplineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
  },
  extendedUplineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
  },
  directDownlineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
    policiesConfig: 'No',
    compGridRates: 'No',
  },
  extendedDownlineDataAccess: {
    nameConfig: 'Full name',
    emailConfig: 'Yes',
    commissionsConfig: 'No',
    policiesConfig: 'No',
    payoutLevels: [],
    compGridRates: 'No',
  },
};

export const commonDefaultSettings: Partial<ViewsAndFieldsPageSettings> = {
  show_page: true,
  fields: [],
  default_filters: [],
  read_only: false,
  page_options: [],
  custom_view_name: customViewDefault,
  editable: [],
};

export const defaultSettingsObject: Record<string, ViewsAndFieldsPageSettings> =
  {
    customers: {
      // @ts-expect-error
      custom_fields_id: 'customers',
      key: 'customers',
      // @ts-expect-error
      page_label: 'Customers',
      // @ts-expect-error
      menu_label: 'Customers',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    reconciliation: {
      // @ts-expect-error
      custom_fields_id: 'reconciliations',
      key: 'reconciliation',
      // @ts-expect-error
      page_label: 'Reconciliation',
      // @ts-expect-error
      menu_label: 'Reconciliation',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    commissions: {
      // @ts-expect-error
      custom_fields_id: 'statements',
      key: 'commissions',
      // @ts-expect-error
      page_label: 'Commissions',
      // @ts-expect-error
      menu_label: 'Commissions',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    policies: {
      // @ts-expect-error
      custom_fields_id: 'reports',
      key: 'policies',
      // @ts-expect-error
      page_label: 'Policies',
      // @ts-expect-error
      menu_label: 'Policies',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    insights: {
      // @ts-expect-error
      custom_fields_id: 'insights',
      key: 'insights',
      // @ts-expect-error
      page_label: 'Insights',
      // @ts-expect-error
      menu_label: 'Insights',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    add_documents: {
      // @ts-expect-error
      custom_fields_id: 'addDocuments',
      key: 'add_documents',
      // @ts-expect-error
      page_label: 'Upload data',
      // @ts-expect-error
      menu_label: 'Upload data',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    agents: {
      // @ts-expect-error
      custom_fields_id: 'agents',
      key: 'agents',
      // @ts-expect-error
      page_label: 'Agents',
      // @ts-expect-error
      menu_label: 'Agents',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    agents_groups: {
      // @ts-expect-error
      custom_fields_id: 'agentsGroups',
      key: 'agents_groups',
      // @ts-expect-error
      page_label: 'Agent groups',
      // @ts-expect-error
      menu_label: 'Groups',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    agents_production: {
      // @ts-expect-error
      custom_fields_id: 'agentsProduction',
      key: 'agents_production',
      // @ts-expect-error
      page_label: 'Agent production and commissions',
      // @ts-expect-error
      menu_label: 'Production',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    settings_data_processing: {
      custom_fields_id: 'settingsDataProcessing',
      key: 'settings_data_processing',
      page_label: 'Data processing',
      menu_label: 'Data processing',
      show_page: true,
      fields: [],
      default_filters: [],
      read_only: false,
      page_options: `[
      {
        enable_run_reconciliation_button: true,
      }
    ]`,
      editable: [],
    },
    carriers_schedules: {
      // @ts-expect-error
      custom_fields_id: 'carriersSchedules',
      key: 'carriers_schedules',
      // @ts-expect-error
      page_label: 'Commission receivable schedules',
      // @ts-expect-error
      menu_label: 'Carriers',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    compensation_profiles: {
      // @ts-expect-error
      custom_fields_id: 'compensationProfiles',
      key: 'compensation_profiles',
      // @ts-expect-error
      page_label: 'Compensation profiles',
      // @ts-expect-error
      menu_label: 'Comp profiles',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    compensation_profile_sets: {
      // @ts-expect-error
      custom_fields_id: 'compensationProfileSets',
      key: 'compensation_profile_sets',
      // @ts-expect-error
      page_label: 'Compensation profile sets',
      // @ts-expect-error
      menu_label: 'Comp profile sets',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    // Deprecated
    // agent_schedules: {
    //   custom_fields_id: 'agentSchedules',
    //   key: 'agent_schedules',
    //   page_label: 'Agent commission schedule profiles',
    //   menu_label: 'Agents',
    //   ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    // },
    incentives_schedules: {
      // @ts-expect-error
      custom_fields_id: 'incentivesSchedules',
      key: 'incentives_schedules',
      // @ts-expect-error
      page_label: 'Agent incentive tiers',
      // @ts-expect-error
      menu_label: 'Incentives',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    comp_grids_schedules: {
      // @ts-expect-error
      custom_fields_id: 'compGridsSchedules',
      key: 'comp_grids_schedules',
      // @ts-expect-error
      page_label: 'Comp grids',
      // @ts-expect-error
      menu_label: 'Comp grids',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    views: {
      // @ts-expect-error
      custom_fields_id: 'views',
      key: 'views',
      // @ts-expect-error
      page_label: 'Saved views',
      // @ts-expect-error
      menu_label: 'Views',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    reports: {
      // @ts-expect-error
      custom_fields_id: 'reports',
      key: 'reports',
      // @ts-expect-error
      page_label: 'Saved reports',
      // @ts-expect-error
      menu_label: 'Reports',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    summaries: {
      // @ts-expect-error
      custom_fields_id: 'summaries',
      key: 'summaries',
      // @ts-expect-error
      page_label: 'Saved report groups',
      // @ts-expect-error
      menu_label: 'Summaries',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
      editable: [Roles.ACCOUNT_ADMIN, Roles.DATA_SPECIALIST],
    },
    custom_reports: {
      // @ts-expect-error
      custom_fields_id: 'customReports',
      key: 'custom_reports',
      // @ts-expect-error
      page_label: 'Custom reports',
      // @ts-expect-error
      menu_label: 'Custom reports',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    companies: {
      // @ts-expect-error
      custom_fields_id: 'companies',
      key: 'companies',
      // @ts-expect-error
      page_label: 'Companies',
      // @ts-expect-error
      menu_label: 'Companies',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    products: {
      // @ts-expect-error
      custom_fields_id: 'products',
      key: 'products',
      // @ts-expect-error
      page_label: 'Products',
      // @ts-expect-error
      menu_label: 'Products',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    options: {
      // @ts-expect-error
      custom_fields_id: 'options',
      key: 'options',
      // @ts-expect-error
      page_label: 'Product options',
      // @ts-expect-error
      menu_label: 'Options',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
    documents: {
      // @ts-expect-error
      custom_fields_id: 'documents',
      key: 'documents',
      // @ts-expect-error
      page_label: 'Documents',
      // @ts-expect-error
      menu_label: 'Documents',
      ...(commonDefaultSettings as ViewsAndFieldsPageSettings),
    },
  };
