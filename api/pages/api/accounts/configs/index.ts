import { create<PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, Req, Res } from 'next-api-decorators';
import type { NextApiRequest, NextApiResponse } from 'next';
import * as dto from 'common/dto/account_configs';

import { withAuth } from '@/lib/middlewares';
import type {
  ExtAccountInfo,
  ExtNextApiRequest,
  ExtNextApiResponse,
} from '@/types';
import { AccountConfigService } from '@/services/account/config';
import { container } from '@/ioc';
import { BaseHandler } from '@/lib/baseHandler';
import { AccountInfo, ZodBody } from '@/lib/decorators';

class Handler extends BaseHandler {
  accountConfigService: AccountConfigService;

  constructor() {
    super();
    this.accountConfigService =
      container.get<AccountConfigService>(AccountConfigService);
  }
  @Get()
  async list(
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Req() req: ExtNextApiRequest & NextApiRequest,
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @AccountInfo() accountInfo: ExtAccountInfo
  ) {
    return await this.accountConfigService.getAccountConfig(
      accountInfo.account_id
    );
  }

  @Post()
  async create(
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Req() req: ExtNextApiRequest & NextApiRequest,
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Res() res: ExtNextApiResponse & NextApiResponse,
    @AccountInfo() accountInfo: ExtAccountInfo,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.CreateAccountConfigSchema)())
    body: dto.CreateAccountConfig
  ) {
    if (body.type === 'dataSync') {
      return await this.accountConfigService.create({
        ...body,
        account_id: accountInfo.account_id,
        uid: accountInfo.uid,
      });
    }
    return await this.accountConfigService.setAccountConfig(
      accountInfo.account_id,
      accountInfo.uid,
      body.value,
      body.type,
      // @ts-expect-error
      body.notes
    );
  }

  @Patch()
  async update(
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Req() req: ExtNextApiRequest & NextApiRequest,
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    @Res() res: ExtNextApiResponse & NextApiResponse,
    // biome-ignore format: compound decorator
    @(ZodBody(dto.UpdateAccountConfigSchema)())
    body: dto.UpdateAccountConfig
  ) {
    return await this.accountConfigService.updateConfigById(body);
  }
}

export default withAuth(createHandler(Handler));
