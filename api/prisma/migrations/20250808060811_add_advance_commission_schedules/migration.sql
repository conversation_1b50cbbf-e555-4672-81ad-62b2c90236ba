-- CreateTable
CREATE TABLE "advance_commission_schedules_companies" (
    "id" SERIAL NOT NULL,
    "advance_commission_schedule_id" INTEGER NOT NULL,
    "company_id" INTEGER NOT NULL,

    CONSTRAINT "advance_commission_schedules_companies_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "advance_commission_schedules" (
    "id" SERIAL NOT NULL,
    "str_id" VARCHAR(36) NOT NULL DEFAULT "substring"(md5((random())::text), 0, 21),
    "account_id" VARCHAR(36),
    "state" TEXT NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" VARCHAR(36),
    "created_proxied_by" VARCHAR(36),
    "updated_at" TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_by" VARCHAR(36),
    "updated_proxied_by" VARCHAR(36),
    "advance_cap" INTEGER,
    "delay" INTEGER,
    "name" TEXT NOT NULL,
    "notes" TEXT,
    "schedule_type" TEXT,
    "schedules" JSONB NOT NULL DEFAULT '[]',
    "payment_date_basis" TEXT,
    "premium_amount_basis" TEXT,
    "product_type" TEXT,

    CONSTRAINT "advance_commission_schedules_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "advance_commission_schedules_companies_advance_commission_s_key" ON "advance_commission_schedules_companies"("advance_commission_schedule_id", "company_id");

-- CreateIndex
CREATE UNIQUE INDEX "advance_commission_schedules_str_id_key" ON "advance_commission_schedules"("str_id");

-- AddForeignKey
ALTER TABLE "advance_commission_schedules_companies" ADD CONSTRAINT "advance_commission_schedules_companies_advance_commission__fkey" FOREIGN KEY ("advance_commission_schedule_id") REFERENCES "advance_commission_schedules"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "advance_commission_schedules_companies" ADD CONSTRAINT "advance_commission_schedules_companies_company_id_fkey" FOREIGN KEY ("company_id") REFERENCES "companies"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
