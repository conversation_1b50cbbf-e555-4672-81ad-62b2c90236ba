import 'reflect-metadata';
// @ts-expect-error
import * as levenshtein from 'damerau-levenshtein';
import dayjs from 'dayjs';
import { distance } from 'fastest-levenshtein';
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { createHash } from 'crypto';
import { isNill } from 'common/helpers';
import type {
  reconcilers,
  reconciliation_data,
  report_data,
  statement_data,
} from '@prisma/client';

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
const genKeyFn = (config: Record<string, any>) => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  return (data: Record<string, any>) => {
    let key = '';
    key = config
      // @ts-expect-error
      .map((fieldConfig) => {
        const curKey = applyTransformers(
          data[fieldConfig.field],
          fieldConfig.transformers,
          fieldConfig.params
        );
        return curKey;
      })
      .join('::');
    return key;
  };
};

export const getKeyMethods = (reconciler: reconcilers) => {
  let getStatementKey = (statement: statement_data) => statement.policy_id;
  if (reconciler.method_type === 'key-config') {
    const statementConfig =
      typeof reconciler.key_config_statement === 'string'
        ? JSON.parse(reconciler.key_config_statement)
        : reconciler.key_config_statement;
    getStatementKey = genKeyFn(statementConfig);
  } else if (reconciler.method_type === 'key-custom') {
    // biome-ignore lint/security/noGlobalEval: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    getStatementKey = eval(String(reconciler.key_config_statement));
  }
  let getPolicyKey = (policy: report_data) => policy.policy_id;
  if (reconciler.method_type === 'key-config') {
    const reportConfig =
      typeof reconciler.key_config_report === 'string'
        ? JSON.parse(reconciler.key_config_report)
        : reconciler.key_config_report;
    getPolicyKey = genKeyFn(reportConfig);
  } else if (reconciler.method_type === 'key-custom') {
    // biome-ignore lint/security/noGlobalEval: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    getPolicyKey = eval(String(reconciler.key_config_report));
  }
  return { getStatementKey, getPolicyKey };
};

export const applyTransformers = (
  // @ts-expect-error
  input,
  transformers: string | string[],
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  params: Record<string, any> = {}
) => {
  let result = input;
  const _transformers = Array.isArray(transformers)
    ? transformers
    : [transformers];
  const standardizeTransformers = _transformers.filter((t) =>
    t.startsWith('standardize')
  );
  const otherTransformers = _transformers.filter(
    (t) => !t.startsWith('standardize')
  );
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  standardizeTransformers.forEach((transformer) => {
    if (isNill(result)) return result;
    switch (transformer) {
      case 'standardizeProduct':
        try {
          const mappings: Record<string, string> = JSON.parse(
            // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            params['standardizeProduct'].mapping
          );
          for (const [key, value] of Object.entries(mappings)) {
            if (value.includes(result)) {
              result = key;
              break;
            }
          }
        } catch (e) {
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.error('Error parsing standardizeProduct mapping', e);
        }
        break;
      case 'standardizeProductType':
        try {
          const mappings: Record<string, string> = JSON.parse(
            // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            params['standardizeProductType'].mapping
          );
          for (const [key, value] of Object.entries(mappings)) {
            if (value.includes(result)) {
              result = key;
              break;
            }
          }
        } catch (e) {
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.error('Error parsing standardizeProduct mapping', e);
        }
        break;
    }
  });
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  otherTransformers.forEach((transformer) => {
    if (isNill(result)) return result;
    switch (transformer) {
      case 'toLowerCase':
        result = result.toLowerCase();
        break;
      case 'toUpperCase':
        result = result.toUpperCase();
        break;
      case 'removeDashes':
        result = result.replace(/-/g, '');
        break;
      case 'removeSpaces':
        result = result.replace(/\s/g, '');
        break;
      case 'trimZeros':
        result = result.replace(/^0+/g, '');
        result = result.replace(/0+$/g, '');
        break;
      case 'removePeriods':
        result = result.replace(/\./g, '');
        break;
      case 'removeCommas':
        result = result.replace(/,/g, '');
        break;
      case 'removeApostrophes':
        result = result.replace(/'/g, '');
        break;
      case 'normalizeSpaces':
        result = result.replace(/\s+/g, ' ');
        break;
      case 'normalizeCompany':
        result = normalizeCompany(result);
        break;
      case 'normalizeSymbols':
        result = normalizeSymbols(result);
        break;
      case 'removeLetters':
        result =
          result.replace(/[a-zA-Z]/g, '').length >= 6
            ? result.replace(/[a-zA-Z]/g, '')
            : result;
        break;
      case 'removedashnn':
        result = result.replace(/-\S\S?$/g, '');
        result = result.replace(/_\S\S?$/g, '');
        break;
      case 'removespacenn':
        result = result.replace(/ \d\d?$/g, '');
        break;
      case 'removeFromEnd':
        result = result.substring(
          0,
          // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          result.length - (params ? params['removeFromEnd']?.n || 1 : 1)
        );
        break;
      case 'slice':
        result = result.slice(
          params?.slice?.indexStart || 0,
          params?.slice?.indexEnd || undefined
        );
        break;
    }
  });
  return result;
};

const distanceLevenshtein = (s1: string, s2: string) => {
  const _s1 = s1 ?? '';
  const _s2 = s2 ?? '';
  const maxLength = Math.max(_s1.length, _s2.length);
  const dist = distance(_s1, _s2);
  const score = (maxLength - dist) / maxLength;
  return score;
};
const distanceDamerauLevenshtein = (s1: string, s2: string) => {
  const res = levenshtein(s1, s2);
  return res.similarity;
};

const normalizeCompany = (s: string) => {
  return s
    ?.replace(
      /(llc|inc|inc\.|co|corp|ltd|limited|insurance|agency|co, llc)$/i,
      ''
    )
    ?.replace(/(,\s*$)/, '')
    ?.trim();
};

const normalizeSymbols = (s: string) => {
  return s?.replace(/ & /, ' and ');
};

export const funcLib = {
  distanceLevenshtein,
  distanceDamerauLevenshtein,
  normalizeCompany,
  normalizeSymbols,
};

// @ts-expect-error
export const getScoreMethod = (reconciler) => {
  // @ts-expect-error
  let scoreMethod = (report, statement, _libs) =>
    distanceLevenshtein(report.policy_id, statement.policy_id);
  if (reconciler.method_type === 'similarity-config') {
    scoreMethod = (report, statement, _libs) => {
      // @ts-expect-error
      const results = [];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      reconciler.similarity_config.forEach(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (fieldConfig: Record<string, any>) => {
          let reportFieldVal = applyTransformers(
            report[fieldConfig.reportField],
            fieldConfig.reportTransformers
          );
          let statementFieldVal = applyTransformers(
            statement[fieldConfig.statementField],
            fieldConfig.statementTransformers
          );
          if (
            fieldConfig?.additionalSettings?.includes(
              'simiarlityOnShorterLength'
            )
          ) {
            const shorterLength = Math.min(
              reportFieldVal?.length ?? 0,
              statementFieldVal?.length ?? 0
            );
            if (shorterLength >= 16) {
              reportFieldVal = reportFieldVal?.substring(0, shorterLength);
              statementFieldVal = statementFieldVal?.substring(
                0,
                shorterLength
              );
            }
          }

          let score = distanceLevenshtein(reportFieldVal, statementFieldVal);
          if (
            fieldConfig?.additionalSettings?.includes('startsWithMatch') &&
            reportFieldVal?.length > 2 &&
            statementFieldVal?.length > 2 &&
            (reportFieldVal.startsWith(statementFieldVal) ||
              statementFieldVal.startsWith(reportFieldVal))
          ) {
            score = 1;
          }
          const result = score >= (fieldConfig.threshold ?? 0) ? score : 0;
          if (
            !fieldConfig?.additionalSettings?.includes('ignoreEmpty') ||
            (reportFieldVal && statementFieldVal)
          ) {
            results.push(result);
          }
        }
      );
      // @ts-expect-error
      const avg = results.reduce((acc, cur) => cur + acc, 0) / results.length;
      return avg;
    };
  } else if (reconciler.method_type === 'similarity-custom') {
    // biome-ignore lint/security/noGlobalEval: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    scoreMethod = eval(reconciler.similarity_config);
  }
  return scoreMethod;
};

export const getFieldConfigs = (mode = 'insurance') =>
  [
    {
      type: 'field',
      visible: false,
      fieldId: 'id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      visible: false,
      fieldId: 'override',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'policy_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'writing_carrier_name',
      source: 'statements',
      enabled: mode === 'insurance',
    },
    {
      type: 'field',
      fieldId: 'carrier_name',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'customer_name',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'product_type',
      source: 'reports',
      enabled: mode === 'insurance',
    },
    {
      type: 'field',
      fieldId: 'product_sub_type',
      source: 'reports',
      enabled: mode === 'insurance',
    },
    { type: 'field', fieldId: 'agent_name', source: 'statements' },
    {
      type: 'field',
      fieldId: 'effective_date',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'premium_amount',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'premium_amount',
      source: 'reports',
      label: 'Report Premium',
      infoIcon: true,
    },
    {
      type: 'field',
      fieldId: 'commissions_expected',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'notes',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'commissionable_premium_amount',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'product_name',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'issue_age',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'group_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'internal_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'transaction_type',
      source: 'statements',
      enabled: true,
    },
    {
      type: 'aggregate',
      getKey: () => 'amount_paid',
      fieldId: 'commission_amount',
      keyAs: 'amount_paid',
      id2: 'amount_paid',
      enabled: true,
      getter: (v: { amount_paid?: { amount_paid: number } }) =>
        v?.amount_paid?.amount_paid,
      source: 'statements',
    },
    // {
    //   type: 'aggregate',
    //   getKey: () => 'aggregate_premiums',
    //   fieldId: 'premium_amount',
    //   keyAs: 'aggregate_premiums',
    //   id2: 'aggregate_premiums',
    //   enabled: mode === 'insurance',
    // },
    {
      type: 'computed',
      fieldId: 'balance',
      compute: (v: {
        commissions_expected?: number;
        amount_paid?: { amount_paid?: { amount_paid: number } };
      }) => {
        return (
          (v.commissions_expected ?? 0) -
          (v?.amount_paid?.amount_paid?.amount_paid ?? 0)
        );
      },
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'policy_status',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'cancellation_date',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'reinstatement_date',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'agent_name',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'field',
      fieldId: 'agent_id',
      source: 'reports',
      enabled: true,
    },
    {
      type: 'computed',
      fieldId: 'reconciled',

      // @ts-expect-error
      compute: (v: reconciliation_data, settings) => {
        const usingCommissionSchedule =
          v.commission_balance_monthly &&
          Object.keys(v.commission_balance_monthly).length > 0;

        if (v.override) return '✅ Reconciled (Manual)';

        if (
          !v.amount_paid ||
          typeof v.amount_paid !== 'object' ||
          Array.isArray(v.amount_paid)
        ) {
          if (v.report_id && v.commissions_expected === undefined) {
            return '⚠️ Commissions received, but amount_paid is missing or invalid';
          }
          return '⚠️ Missing or invalid amount_paid data';
        }

        const deeplyPaid =
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (v.amount_paid as any)?.amount_paid?.amount_paid ??
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (v.amount_paid as any)?.amount_paid ??
          undefined;

        if (
          v.report_id &&
          v.commissions_expected === undefined &&
          deeplyPaid !== undefined &&
          deeplyPaid !== null
        ) {
          return '✅ Commission received';
        }

        if (v?.statement_ids && !v?.report_id) return '⚠️ Missing policy data';
        if (!v?.statement_ids && v?.report_id)
          return '⚠️ Missing commissions data';

        if (usingCommissionSchedule) {
          const [_latestMonth, latestBal] = Object.entries(
            // @ts-expect-error
            v.commission_balance_monthly
          ).sort(
            ([ka], [kb]) => new Date(kb).getTime() - new Date(ka).getTime()
          )[0];

          if (latestBal === 0) return '✅ Reconciled';
          // @ts-expect-error
          if (Math.abs(latestBal) < settings.reconciliationThreshold)
            return '✅ Reconciled (negligible balance)';
          // @ts-expect-error
          if (latestBal >= settings.reconciliationThreshold)
            return '⛔️ Commissions shortfall';
          // @ts-expect-error
          if (latestBal <= settings.reconciliationThreshold)
            return '✅ Excess commissions';
        } else {
          const expected = Number(v.commissions_expected) ?? 0;
          const paid = Number(deeplyPaid) ?? 0;
          const diff = expected - paid;

          if (diff === 0) return '✅ Reconciled';
          if (
            diff <= settings.reconciliationThreshold &&
            diff >= -settings.reconciliationThreshold
          )
            return '✅ Reconciled (negligible balance)';
          if (diff > settings.reconciliationThreshold)
            return '⛔️ Commissions shortfall';
          if (diff < settings.reconciliationThreshold)
            return '✅ Excess commissions';
        }
        return '';
      },
      enabled: true,
    },
    { type: 'field', visible: false, label: 'Notes', fieldId: 'notes' },
    {
      type: 'aggregate',
      visible: false,
      // @ts-expect-error
      getKey: (s) =>
        dayjs(s.payment_date).startOf('month').format('MM/DD/YYYY'),
      fieldId: 'commission_amount',
      keyAs: 'commission_amount_monthly',
      id2: 'amount_paid_monthly',
      enabled: true,
      source: 'statements',
    },
    {
      type: 'aggregate',
      visible: false,
      fieldId: 'payment_date',
      keyAs: 'payment_date_first',
      getKey: () => 'payment_date_first',
      enabled: true,
      method: (acc: Date, cur: Date) => {
        const safeAcc = acc ?? new Date('1900-01-01');
        const safeCur = cur ?? new Date('1900-01-01');
        return safeCur > safeAcc ? safeAcc : safeCur;
      },
      // Add support for collapsing this to first level, doing it manually below for now
      source: 'statements',
    },
    {
      type: 'aggregate',
      visible: false,
      fieldId: 'payment_date',
      keyAs: 'payment_date_last',
      getKey: () => 'payment_date_last',
      enabled: true,
      method: (acc: Date, cur: Date) => {
        const safeAcc = acc ?? new Date('2100-01-01');
        const safeCur = cur ?? new Date('2100-01-01');
        return safeCur < safeAcc ? safeCur : safeAcc;
      },
      // Add support for collapsing this to first level, doing it manually below for now
      source: 'statements',
    },
  ].filter((e) => e.enabled);

export const digestReconciler = (data = {}) => {
  const fields = [
    'config',
    'key_config_report',
    'key_config_statement',
    'key_condition',
    'method_type',
    'method_threshold_match',
    'method_threshold_maybe',
    'similarity_config',
  ].sort();
  // @ts-expect-error
  const content = fields.map((key) => JSON.stringify(data[key])).join('');
  return createHash('md5').update(content).digest('hex');
};
