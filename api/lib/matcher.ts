import BigNumber from 'bignumber.js';
import {
  type DataUpdateFilter,
  FieldMatchertDateOperatorOptions,
  FieldMatchertUnitOptions,
  FiltersOperators,
} from 'common/globalTypes';
import { isNill } from 'common/helpers';

import dayjs from '@/lib/dayjs';
import { get } from '@/lib/helpers/get';
import {
  containsOperator,
  notContainsOperator,
  isNotEmptyOperator,
} from '@/lib/matcher.helpers';

export type FilterableValue = string | number | boolean | null | undefined;
export type RecordValue = FilterableValue | FilterableValue[];
export type FilterValue = FilterableValue | FilterableValue[];

export const filterMatcher =
  (
    filter: DataUpdateFilter
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): ((row: any, intputFilterData?: any) => boolean) =>
  (row, intputFilterData = {}) => {
    const {
      op,
      field,
      value,
      caseSensitive,
      skipEmpty,
      usePolicyData,
      number,
      from,
      unit,
    } = filter;

    // @ts-expect-error
    let effectiveRecordValue = get(row, field);

    if (usePolicyData && isNill(effectiveRecordValue)) {
      // @ts-expect-error
      effectiveRecordValue = row.report?.[field];
    }

    if (skipEmpty && isNill(effectiveRecordValue)) return true;

    const recordValue: RecordValue =
      Array.isArray(effectiveRecordValue) &&
      effectiveRecordValue.every(
        (el) => typeof el === 'string' && !caseSensitive
      )
        ? effectiveRecordValue.map((el) => el.toLowerCase())
        : typeof effectiveRecordValue === 'string' && !caseSensitive
          ? effectiveRecordValue.toLowerCase()
          : (effectiveRecordValue as RecordValue);

    const filterValue: FilterValue = Array.isArray(value)
      ? value.map((v) =>
          typeof v === 'string' && !caseSensitive ? v.toLowerCase() : v
        )
      : typeof value === 'string' && !caseSensitive
        ? value?.toLowerCase()
        : value;

    // TODO: support full list of ops
    // { id: 'in', label: 'In' },
    // { id: 'nin', label: 'Not in' },
    // { id: 'regex', label: 'Regex' },
    // { id: 'nregex', label: 'Not regex' },
    // { id: 'string-distance', label: 'Similarity score' },
    // { id: 'custom', label: 'Custom' },

    // TODO: Change the way we handle custom operators to use a map object instead of a switch statement
    // and move each operator into functions in matcher.helpers.ts
    switch (op) {
      case FiltersOperators.IS_WRITING_AGENT:
        return (
          intputFilterData?.statementContactStrId ===
          intputFilterData?.agentStrId
        );
      case FiltersOperators.NOT_WRITING_AGENT:
        return (
          intputFilterData?.statementContactStrId !==
          intputFilterData?.agentStrId
        );
      case FiltersOperators.GT:
        return new BigNumber(recordValue as string).isGreaterThan(
          new BigNumber(filterValue as string)
        );
      case FiltersOperators.LT:
        return new BigNumber(recordValue as string).isLessThan(
          new BigNumber(filterValue as string)
        );
      case FiltersOperators.GTE:
        return new BigNumber(recordValue as string).isGreaterThanOrEqualTo(
          new BigNumber(filterValue as string)
        );
      case FiltersOperators.LTE:
        return new BigNumber(recordValue as string).isLessThanOrEqualTo(
          new BigNumber(filterValue as string)
        );
      case FiltersOperators.EQNUM:
        return (
          new BigNumber(recordValue as string).toFixed(2) ===
          new BigNumber(filterValue as string).toFixed(2)
        );
      case FiltersOperators.NEQNUM:
        return (
          new BigNumber(recordValue as string).toFixed(2) !==
          new BigNumber(filterValue as string).toFixed(2)
        );
      case FiltersOperators.EQ:
        if (Array.isArray(recordValue)) {
          const normalizedFilterValue = normalizeFilterValue(filterValue);
          return (
            recordValue.length === normalizedFilterValue.length &&
            recordValue.every((val) => normalizedFilterValue.includes(val))
          );
        }
        return String(recordValue) === filterValue;
      case FiltersOperators.NEQ:
        if (Array.isArray(recordValue)) {
          const normalizedFilterValue = normalizeFilterValue(filterValue);
          return (
            recordValue.length !== normalizedFilterValue.length ||
            !recordValue.every((val) => normalizedFilterValue.includes(val))
          );
        }
        if (isNill(filterValue)) {
          return !isNill(recordValue);
        } else {
          return String(recordValue) !== filterValue;
        }
      case FiltersOperators.STARTSWITH:
        return (recordValue as string)?.startsWith(filterValue as string);
      case FiltersOperators.ENDSWITH:
        return (recordValue as string)?.endsWith(filterValue as string);
      case FiltersOperators.CONTAINS:
        return containsOperator(recordValue, filterValue);
      case FiltersOperators.NCONTAINS:
        return notContainsOperator(recordValue, filterValue);
      case FiltersOperators.CONTAINEDIN:
        try {
          const parsedFilterValue = Array.isArray(filterValue)
            ? filterValue
            : JSON.parse(filterValue as string);
          return parsedFilterValue?.includes(recordValue);
        } catch (error) {
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.error(`Error parsing filter value: ${error}`);
          return false;
        }
      case FiltersOperators.NCONTAINEDIN:
        try {
          const parsedFilterValue = Array.isArray(filterValue)
            ? filterValue
            : JSON.parse(filterValue as string);
          return !parsedFilterValue?.includes(recordValue);
        } catch (error) {
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.error(`Error parsing filter value: ${error}`);
          return false;
        }
      case FiltersOperators.BEFORE:
        if (!recordValue) return false;

        if (from === FieldMatchertDateOperatorOptions.FromDateField) {
          const baseDate = dayjs.utc(row[value as string]);
          const adjustedDate = getAdjustedDate(baseDate, number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() < adjustedDate.toDate()
          );
        }

        if (number && unit) {
          const baseDate = dayjs.utc(value as string);
          const adjustedDate = adjustDate(baseDate, +number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() < adjustedDate.toDate()
          );
        }

        return (
          dayjs.utc(recordValue as string).toDate() <
          dayjs.utc(filterValue as string).toDate()
        );
      case FiltersOperators.BEFORE_EQUALS:
        if (!recordValue) return false;

        if (from === FieldMatchertDateOperatorOptions.FromDateField) {
          const baseDate = dayjs.utc(row[value as string]);
          const adjustedDate = getAdjustedDate(baseDate, number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() <= adjustedDate.toDate()
          );
        }

        if (number && unit) {
          const baseDate = dayjs.utc(value as string);
          const adjustedDate = adjustDate(baseDate, +number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() <= adjustedDate.toDate()
          );
        }

        return (
          dayjs.utc(recordValue as string).toDate() <=
          dayjs.utc(filterValue as string).toDate()
        );

      case FiltersOperators.AFTER:
        if (!recordValue) return false;

        if (from === FieldMatchertDateOperatorOptions.FromDateField) {
          const baseDate = dayjs.utc(row[value as string]);
          const adjustedDate = getAdjustedDate(baseDate, number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() > adjustedDate.toDate()
          );
        }

        if (number && unit) {
          const baseDate = dayjs.utc(value as string);
          const adjustedDate = adjustDate(baseDate, +number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() > adjustedDate.toDate()
          );
        }

        return (
          dayjs.utc(recordValue as string).toDate() >
          dayjs.utc(filterValue as string).toDate()
        );

      case FiltersOperators.AFTER_EQUALS:
        if (!recordValue) return false;

        if (from === FieldMatchertDateOperatorOptions.FromDateField) {
          const baseDate = dayjs.utc(row[value as string]);
          const adjustedDate = getAdjustedDate(baseDate, number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() >= adjustedDate.toDate()
          );
        }

        if (number && unit) {
          const baseDate = dayjs.utc(value as string);
          const adjustedDate = adjustDate(baseDate, +number, unit);
          return (
            dayjs.utc(recordValue as string).toDate() >= adjustedDate.toDate()
          );
        }

        return (
          dayjs.utc(recordValue as string).toDate() >=
          dayjs.utc(filterValue as string).toDate()
        );

      case FiltersOperators.WITHIN: {
        if (!recordValue) return false;

        const recordDate = dayjs.utc(recordValue as string).toDate();

        if (from === FieldMatchertDateOperatorOptions.FromDateField) {
          const baseDate = dayjs.utc(row[value as string]);
          const { startDate, endDate } = calculateDateRange(
            baseDate,
            // @ts-expect-error
            +number,
            // @ts-expect-error
            unit
          );
          return (
            recordDate >= startDate.toDate() && recordDate <= endDate.toDate()
          );
        }

        if (number && unit) {
          const baseDate = dayjs.utc(value as string);
          const { startDate, endDate } = calculateDateRange(
            baseDate,
            +number,
            unit
          );
          return (
            recordDate >= startDate.toDate() && recordDate <= endDate.toDate()
          );
        }

        return false;
      }

      case FiltersOperators.IS_EMPTY:
        if (Array.isArray(recordValue)) {
          return recordValue.length === 0;
        }
        return isNill(recordValue);
      // TODO: Deprecate this operator
      case FiltersOperators.WITHIN_ONE_YEAR:
        return !effectiveRecordValue || !row[value as string]
          ? false
          : isWithinOneYear(effectiveRecordValue as Date, row[value as string]);
      // TODO: Deprecate this operator
      case FiltersOperators.AT_LEAST_ONE_YEAR:
        return !effectiveRecordValue || !row[value as string]
          ? false
          : !isWithinOneYear(
              effectiveRecordValue as Date,
              row[value as string]
            );
      case FiltersOperators.IS_NOT_EMPTY:
        return isNotEmptyOperator(recordValue);
      // Custom operator return the condition set in the value
      case FiltersOperators.CUSTOM:
        return value;
      default:
        return false;
    }
  };

// @ts-expect-error
export const matchesFilter = (row, filters) => {
  if (!filters || filters.length === 0) return true;
  // @ts-expect-error
  return filters.every((filter) => filterMatcher(filter)(row));
};

export const isWithinOneYear = (startDate: Date, endDate: Date): boolean => {
  const oneYearFromStartDate = new Date(startDate);
  oneYearFromStartDate.setFullYear(startDate.getFullYear() + 1);
  return endDate <= oneYearFromStartDate;
};

const normalizeFilterValue = (filterValue: FilterValue): FilterableValue[] => {
  if (Array.isArray(filterValue)) {
    return filterValue.map((v) => (typeof v === 'string' ? v.trim() : v));
  }
  if (typeof filterValue === 'string') {
    return filterValue.split(',').map((v) => v.trim());
  }
  return [filterValue];
};

const adjustDate = (
  baseDate: dayjs.Dayjs,
  number: number,
  unit: FieldMatchertUnitOptions
): dayjs.Dayjs => {
  switch (unit) {
    case FieldMatchertUnitOptions.Days:
      return baseDate.add(number, 'day');
    case FieldMatchertUnitOptions.Months:
      return baseDate.add(number, 'month');
    case FieldMatchertUnitOptions.Years:
      return baseDate.add(number, 'year');
    default:
      throw new Error(`Unsupported unit: ${unit}`);
  }
};

const getAdjustedDate = (
  baseDate: dayjs.Dayjs,
  number: string | undefined,
  unit: FieldMatchertUnitOptions | undefined
): dayjs.Dayjs => {
  return number && unit ? adjustDate(baseDate, +number, unit) : baseDate;
};

const calculateDateRange = (
  baseDate: dayjs.Dayjs,
  number: number,
  unit: FieldMatchertUnitOptions
): { startDate: dayjs.Dayjs; endDate: dayjs.Dayjs } => {
  const adjustedDate = adjustDate(baseDate, number, unit);
  return {
    startDate: number < 0 ? adjustedDate : baseDate,
    endDate: number < 0 ? baseDate : adjustedDate,
  };
};
