import { type ClassConstructor, plainToInstance } from 'class-transformer';
import {
  Body as OriginBody,
  Query as OriginQuery,
  ValidationPipe,
  createParamDecorator,
  createMiddlewareDecorator,
  type ValidationPipeOptions,
} from 'next-api-decorators';
import type { ParameterPipe } from 'next-api-decorators/dist/pipes/ParameterPipe';
import 'reflect-metadata';
import {
  type ValidationDecoratorOptions,
  registerDecorator,
} from 'class-validator';
import pRetry from 'p-retry';
import type { z, ZodType } from 'zod';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import get from 'lodash/get';

import type {
  ExtNextApiRequest,
  ExtNextApiResponse,
  ExtAccountInfo,
} from '@/types';
import type { ConfigService, Environment } from '@/services/config';
import type { AppLoggerService } from '@/services/logger/appLogger';
import { ConfigNotFoundException } from '@/lib/exceptionHandler';
import type { NextApiRequest } from 'next';

// Only defined with exposed fields will be exposed
export const convertModelToVO = <T, V>(
  cls: ClassConstructor<T>,
  plain: V
): T => {
  return plainToInstance(cls, plain, { strategy: 'excludeAll' });
};

// Only defined with exposed fields will be exposed， support array data
export const convertModelToVOList = <T, V>(
  cls: ClassConstructor<T>,
  plain: V[]
): T[] => {
  return plainToInstance(cls, plain, { strategy: 'excludeAll' });
};

// Enhanced  body decorator that will remove all the extra fields
export const EnhancedBody = (
  opts: {
    transformOptions?: ValidationPipeOptions['transformOptions'];
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    pipes?: ParameterPipe<any>[];
  } = {
    transformOptions: { excludeExtraneousValues: true },
    pipes: [],
  }
) => {
  return OriginBody(
    ValidationPipe({
      transformOptions: {
        excludeExtraneousValues: true,
        ...opts.transformOptions,
      },
    }),
    ...(opts.pipes || [])
  );
};

// Enhanced  query decorator that will remove all the extra fields
// @ts-expect-error
export const EnhancedQuery = (...args) => {
  return OriginQuery(
    ...args,
    ValidationPipe({
      transformOptions: {
        excludeExtraneousValues: true,
      },
    })
  );
};

// Get account info from request
export const AccountInfo = createParamDecorator(
  // @ts-expect-error
  (req: ExtNextApiRequest & NextApiRequest): ExtAccountInfo => {
    return {
      // @ts-expect-error
      uid: req.uid,
      // @ts-expect-error
      ouid: req.ouid,
      // @ts-expect-error
      account_id: req.account_id,
      // @ts-expect-error
      role_id: req.role_id,
    };
  }
);

// Get Logger from request
export const Logger = createParamDecorator(
  // @ts-expect-error
  (req: ExtNextApiRequest): AppLoggerService => {
    return req.logger;
  }
);

// Get timezone from request headers
export const Timezone = createParamDecorator(
  // @ts-expect-error
  (req: ExtNextApiRequest): string => {
    return req.timezone || 'UTC';
  }
);

export const Pagination = createMiddlewareDecorator(
  // @ts-expect-error
  (req: ExtNextApiRequest, _res: ExtNextApiResponse, next) => {
    const {
      query: { page = '0', limit = '50' },
    } = req;

    let pageNum = 0;
    let limitNum: number;
    if (Number.isInteger(Number.parseInt(page, 10))) {
      pageNum = Number.parseInt(page, 10);
    }
    if (Number.isInteger(Number.parseInt(limit, 10))) {
      limitNum = Number.parseInt(limit, 10);
    }

    // Modify request parameters as needed
    req.page = pageNum;
    // @ts-expect-error
    req.limit = limitNum;

    next();
  }
);

export const IsNotEmptyStringOrArray = (
  validationOptions?: ValidationDecoratorOptions['options']
) => {
  return (target: unknown, propertyName: string) => {
    registerDecorator({
      name: 'IsNotEmptyStringOrArray',
      // @ts-expect-error
      target: target.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        validate(value: any) {
          if (typeof value === 'string' && value.trim() !== '') {
            return true;
          }
          if (Array.isArray(value) && value.length > 0) {
            return true;
          }
          return false;
        },
      },
    });
  };
};

export const Config = <K extends keyof Environment>(
  key: K,
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  transformFn?: (val: any) => unknown
) => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  return (target: any, propertyKey: string) => {
    Object.defineProperty(target, propertyKey, {
      get: function () {
        const configService: ConfigService = this.configService;
        if (configService === undefined) {
          throw new Error('ConfigService is not injected');
        }
        const config = configService.get(key);
        if (config === undefined) {
          configService.logger.error(`Config ${key} is not found`);
          throw new ConfigNotFoundException(`Config ${key} is not found`);
        }
        return transformFn ? transformFn(config) : config;
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      set: function (value: any) {
        const configService: ConfigService = this.configService;
        if (configService === undefined) {
          throw new Error('ConfigService is not injected');
        }
        configService.set(key, value);
      },
      enumerable: true,
      configurable: true,
    });
  };
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const Cache = (key: string | ((...args: any[]) => string)) => {
  return function cacheTemporaryResult(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    target: any,
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    descriptor.value = async function (...args: any[]) {
      // @ts-expect-error
      if (!this._cache) {
        // @ts-expect-error
        this._cache = new Map();
      }
      // @ts-expect-error
      if (!this._promiseCache) {
        // @ts-expect-error
        this._promiseCache = new Map();
      }
      // @ts-expect-error
      const cache = this._cache;
      // @ts-expect-error
      const promiseCache = this._promiseCache;

      // Generate cache key based on the key parameter
      const cacheKey = typeof key === 'function' ? key(...args) : key;

      // Check regular cache first
      if (cache.has(cacheKey)) {
        return cache.get(cacheKey);
      }

      // Check if there's an ongoing request
      if (promiseCache.has(cacheKey)) {
        return promiseCache.get(cacheKey);
      }

      // Create new promise for this request
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const promise = originalMethod.apply(this, args).then((result: any) => {
        if (result) {
          cache.set(cacheKey, result);
        }
        promiseCache.delete(cacheKey);
        return result;
      });

      // Store promise in promise cache
      promiseCache.set(cacheKey, promise);
      return promise;
    };

    return descriptor;
  };
};

// A retry decorator using p-retry, apply retry logic to class methods
export const Retry = (options: Parameters<typeof pRetry>[1]) => {
  return function retryDecorator(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    target: any,
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    propertyKey: string,
    descriptor: PropertyDescriptor
  ) {
    const originalMethod = descriptor.value;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    descriptor.value = async function (...args: any[]) {
      return pRetry(() => originalMethod.apply(this, args), options);
    };
    return descriptor;
  };
};

// Create a new decorator for Zod validation
export const ZodBody = <T extends ZodType>(schema: T, pathKey = 'body') => {
  // @ts-expect-error
  return createParamDecorator((req: ExtNextApiRequest): z.infer<T> => {
    // Parse and validate the data
    return schema.parse(get(req, pathKey));
  });
};

export const ZodQuery = <T extends ZodType>(schema: T) => {
  // @ts-expect-error
  return createParamDecorator((req: ExtNextApiRequest): z.infer<T> => {
    // Parse and validate the data
    return schema.parse(req.query);
  });
};
