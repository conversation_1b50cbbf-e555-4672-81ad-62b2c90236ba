import { LRUCacheService } from '@/services/cache/lru';
import * as Sentry from '@sentry/nextjs';
import { HTTP_HEADERS, ResponseAction } from 'common/constants';
import { UserStates } from 'common/constants/user-states.enum';
import type { JwtPayload } from 'jsonwebtoken';
import { nanoid } from 'nanoid';
// biome-ignore lint/correctness/noUndeclaredDependencies: This is fine, it's not that kind of dependency.
import packageJson from 'package.json';

import { container } from '@/ioc';
import { SessionExpiredException } from '@/lib/exceptionHandler';
import {
  AppLoggerService,
  asyncLocalStorage,
} from '@/services/logger/appLogger';
import { TokenService } from '@/services/token';
import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  GlobalStateCodes,
} from '@/types';
import { auth } from './firebase-admin';
import prisma, { prismaClient } from './prisma';

const CACHE_EXPIRY_MS = 60 * 1000; // 1 minute
const lastVisitedCache = new LRUCacheService<number>({
  ttl: CACHE_EXPIRY_MS,
  ttlAutopurge: true,
});

const tokenService = container.get<TokenService>(TokenService);

export const getFintaryAdmin = async (uid: string) => {
  const admin = await prismaClient.admin.findUnique({
    where: {
      uid,
    },
  });
  if (admin) {
    // @ts-ignore
    const user = await prismaClient.users.findUnique({
      where: { uid },
      select: { email: true, sub_type: true, type: true },
    });
    if (!user) return null;
    const { sub_type, ...rest } = user;
    return {
      ...rest,
      subType: sub_type,
    };
  }

  return null;
};

// @ts-expect-error
export const withAuthWithNoLogger = (
  handler,
  options: {
    adminOnly?: boolean;
    allowNoAccount?: boolean;
    allowAnonymous?: boolean;
  } = null
) => {
  return async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
    if (req.method !== 'OPTIONS' && options?.allowAnonymous) {
      return handler(req, res);
    }
    const filterResult = await requestFilter(req, res, options);
    if (!filterResult) {
      const store = asyncLocalStorage.getStore();
      asyncLocalStorage.enterWith({
        ...store,
        account: {
          // @ts-expect-error
          account_id: req.account_id,
          // @ts-expect-error
          uid: req.uid,
          // @ts-expect-error
          ouid: req.ouid,
          // @ts-expect-error
          role_id: req.role_id,
        },
      });
      return handler(req, res);
    }
    return;
  };
};

// @ts-expect-error
export const withAuth = (
  handler,
  options: {
    adminOnly?: boolean;
    allowNoAccount?: boolean;
    allowAnonymous?: boolean;
  } = null
) => {
  return withLogger(withAuthWithNoLogger(handler, options));
};

const parseIdToken = async (logger: AppLoggerService, idToken: string) => {
  try {
    return await auth.verifyIdToken(idToken);
  } catch (e: unknown) {
    logger.warn(`Couldn't decipher IdToken ${idToken} for some reason.`, {
      error: e,
    });
    return null;
  }
};

const checkSession = async ({
  uid,
  ouid,
  accountId,
  url,
  user,
  decodedToken,
}: {
  uid: string;
  ouid?: string;
  url: string;
  accountId: string;
  user: Awaited<
    ReturnType<
      typeof prismaClient.users.findUniqueOrThrow<{
        where: { uid: string };
        select: {
          uid: true;
          email: true;
          sub_type: true;
          type: true;
          last_visited_at: true;
          account_user_roles: {
            where: { account_id: string };
            select: { role_id: true; state: true };
          };
        };
      }>
    >
  >;
  decodedToken: JwtPayload;
}) => {
  // Check if the session is expired, only if the user is logged in and has a session config
  if (accountId && url !== '/api/session/check') {
    const authConfig = await prismaClient.account_configs.findFirst({
      where: {
        account_id: accountId,
        state: 'active',
        type: 'session',
      },
    });
    // The session is in minutes, so we need to convert it to milliseconds
    const config = authConfig?.value as { session?: number };

    if (config?.session == null) {
      return;
    }
    const isNotAdmin = !!uid && !ouid;
    const isSessionExpired =
      Math.max(
        decodedToken.auth_time * 1000,
        user.last_visited_at?.getTime() || Date.now()
      ) <
      Date.now() - config.session * 60 * 1000;
    if (isNotAdmin && isSessionExpired) {
      throw new SessionExpiredException();
    }
    // Update user last activity time, if the user is not impersonating
    if (isNotAdmin) {
      const now = Date.now();
      const lastUpdated = lastVisitedCache.get(uid);

      if (!lastUpdated) {
        await prismaClient.users.updateMany({
          where: {
            uid,
          },
          data: {
            last_visited_at: new Date(),
          },
        });
        lastVisitedCache.set(uid, now);
      }
    }
  }
};

export const requestFilter = async (
  req: ExtNextApiRequest,
  res: ExtNextApiResponse,
  // @ts-expect-error
  options
) => {
  // FE version check
  res.setHeader('Access-Control-Expose-Headers', 'out_of_date');
  res.setHeader('out_of_date', '');

  // Remove uid from body if it exists, never trust data from the client
  if (req.body) {
    delete req.body.uid;
    delete req.body.account_id;
    delete req.body.ouid;
  }

  if (req.headers.feversion) {
    const feVersionDate = new Date(req.headers.feversion as string);
    const requiredVersionDate = new Date(packageJson.required_fe_version);
    const preferredVersionDate = new Date(packageJson.preferred_fe_version);

    if (feVersionDate < requiredVersionDate) {
      if (feVersionDate >= preferredVersionDate) {
        res.setHeader('out_of_date', GlobalStateCodes.FE_OUT_OF_DATE);
      } else {
        res.setHeader('out_of_date', GlobalStateCodes.FE_INCOMPATIBLE);
        res.status(400).json({
          error: GlobalStateCodes.FE_INCOMPATIBLE,
          message: GlobalStateCodes.FE_INCOMPATIBLE,
        });
        return res;
      }
    }
  }

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return res;
  }
  const authHeader = req.headers.authentication as string;
  const impuid = req.headers.impuid as string;
  const timezone = req.headers[HTTP_HEADERS.TIMEZONE] as string;

  if (!authHeader) {
    res.status(401).end('Not authenticated');
    return res;
  }

  // Extract and set timezone if provided
  if (timezone) {
    req.timezone = timezone;
  }

  const token = authHeader.split(' ')[1];
  let decodedToken: JwtPayload;

  try {
    if (req.headers.accountid) {
      req.account_id = req.headers.accountid as string;
    } else if (!req.headers.accountid && !options?.allowNoAccount) {
      req.logger.error('No account specified');
      res.status(401).end('No account specified');
      return res;
    }
    if (token.startsWith('sso:')) {
      const result = await tokenService.decodeAndVerifyJWT(
        token.replace('sso:', '')
      );
      decodedToken = result?.decodedToken as JwtPayload;
      if (result?.contact_id) {
        res.setHeader('x-contact-id', result.contact_id);
      }
      if (result?.uid) {
        decodedToken.uid = result.uid;
      }
    } else {
      // @ts-expect-error
      decodedToken = await parseIdToken(req.logger, token);
    }

    if (!decodedToken || !decodedToken.uid) {
      res.status(401).end('Not authenticated');
      return res;
    }

    const [user, fintaryAdmin] = await Promise.all([
      prismaClient.users.findUnique({
        where: { uid: decodedToken.uid },
        select: {
          uid: true,
          email: true,
          sub_type: true,
          type: true,
          last_visited_at: true,
          account_user_roles: {
            // @ts-expect-error
            where: { account_id: req.account_id },
            select: { role_id: true, state: true },
          },
        },
      }),
      getFintaryAdmin(decodedToken.uid),
    ]);

    // @ts-expect-error
    req.fintaryAdmin = fintaryAdmin;

    if (options?.adminOnly && !fintaryAdmin) {
      res.status(401).end('Not authorized');
      return res;
    }

    req.uid = decodedToken.uid;
    Sentry.setUser({ id: decodedToken.uid, username: decodedToken.uid });

    // Substitute target user's uid in req.uid if Fintary admin is impersonating a user
    if (impuid) {
      if (fintaryAdmin) {
        req.ouid = decodedToken.uid;
        req.uid = impuid;
        Sentry.setUser({
          id: impuid,
          username: `${impuid} (via ${decodedToken.uid})`,
        });
      } else {
        throw new Error('Not authorized (impuid)');
      }
    }
    // On login req.account_id is undefined
    if (req.account_id !== 'undefined' && !options?.doNotCheckRole) {
      // TODO: Update check above to check that req.account_id is truthy (and !== 'undefined'?) instead of !== 'undefined'
      if (!req.account_id) {
        Sentry.captureMessage('Account should be required');
        req.logger?.error('Account should be required');
      }
      // Check in database for user role and add it to the request
      // TODO: Remove this query and use user retrieved above
      // But first, need to understand implications of different state filters used here
      const userInfo = await prisma.users.findUnique({
        where: {
          uid: req.uid,
          OR: [{ state: 'active' }, { state: 'pending' }],
        },
        include: {
          account_user_roles: {
            where: {
              account_id: req.account_id,
              OR: [
                { state: UserStates.ACTIVE },
                { state: UserStates.DELETED },
                { state: UserStates.INVITED },
              ],
            },
            select: { role_id: true, state: true },
          },
        },
      });
      if (!userInfo) throw new Error(`No user found.`);
      if (
        !userInfo.account_user_roles ||
        userInfo.account_user_roles.length === 0
      ) {
        // TODO: For debugging purposes, remove latter
        throw new Error(`Could not find role for user.`);
      }

      if (userInfo.account_user_roles.length > 1) {
        // TODO: For debugging purposes, remove latter
        req.logger.debug(userInfo);
        throw new Error(`Could not find role for user.`);
      }
      const userRoleState = userInfo.account_user_roles[0].state;
      if (
        userRoleState === UserStates.ACTIVE ||
        userRoleState === UserStates.INVITED
      ) {
        const roleId = userInfo.account_user_roles[0].role_id;
        if (roleId) {
          req.role_id = roleId;
        } else {
          // TODO: For debugging purposes, remove latter
          req.logger.debug(userInfo);
          throw new Error(`Could not find role for user.`);
        }
      } else {
        res.status(403).json({ error: 'No active user' });
        return res;
      }
    }

    await checkSession({
      // @ts-expect-error
      uid: req.uid,
      // @ts-expect-error
      ouid: req.ouid,
      // @ts-expect-error
      accountId: req.account_id,
      // @ts-expect-error
      url: req.url,
      // @ts-expect-error
      user,
      decodedToken,
    });

    const contextStore = asyncLocalStorage.getStore();
    // Inject account info into asyncLocalStorage
    asyncLocalStorage.enterWith({
      ...contextStore,
      account: {
        // @ts-expect-error
        uid: req.uid,
        // @ts-expect-error
        ouid: req.ouid,
        // @ts-expect-error
        account_id: req.account_id,
        // @ts-expect-error
        role_id: req.role_id,
      },
    });
  } catch (error) {
    // @ts-expect-error
    req.logger.error(`error: ${error}`, error);
    // @ts-expect-error
    const errorCode = error.errorInfo?.code || 'Error';
    // @ts-expect-error
    error.status = 401;
    if (errorCode === 'auth/internal-error') {
      // @ts-expect-error
      error.status = 500;
    }
    if (error instanceof SessionExpiredException) {
      return res.status(401).json({
        error: errorCode,
        statusText: 'error',
        message: error.message || 'error',
        success: false,
        action: ResponseAction.LOG_OUT,
      });
    }
    // TODO: Handle Firebase admin errors in more detail
    Sentry.captureException(error);
    // @ts-expect-error
    res.status(error.status).json({
      error: errorCode,
      statusText: 'error',
      // @ts-expect-error
      message: error.message || 'error',
      success: false,
    });
    return res;
  }
};

// @ts-expect-error
export const withLogger = (handler) => {
  return async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
    if (req.method === 'OPTIONS') {
      res.status(200).end();
      return res;
    }

    const traceId =
      (req.headers['trace-id'] as string) ||
      (req.headers['x-cloud-trace-context'] as string) ||
      nanoid();

    const store = asyncLocalStorage.getStore();

    return await asyncLocalStorage.run({ ...store, traceId }, async () => {
      const requestLogger = new AppLoggerService();
      req.logger = requestLogger;
      requestLogger.debug(`${req.method} ${req.url}`, {
        method: req.method,
        url: req.url,
        headers: req.headers,
        body: req.body,
        query: req.query,
      });
      return handler(req, res);
    });
  };
};

// @ts-expect-error
export const withOpenApi = (handler) => {
  return withLogger(async (req: ExtNextApiRequest, res: ExtNextApiResponse) => {
    try {
      const result = await handler(req, res);

      // If response has already been sent, return it as-is
      if (res.writableEnded) {
        return result;
      }

      // Return standardized success response
      return res.status(200).json({
        success: true,
        data: result,
        message: 'Success',
        statusCode: 200,
      });
    } catch (error) {
      // @ts-expect-error
      req.logger.error(`Error in request: ${error.message}`, error);
      Sentry.captureException(error);

      // Return standardized error response
      // @ts-expect-error
      return res.status(error.status || 500).json({
        success: false,
        // @ts-expect-error
        error: error.message || 'Internal Server Error',
        // @ts-expect-error
        statusCode: error.status || 500,
        // @ts-expect-error
        message: error.message || 'An unexpected error occurred',
      });
    }
  });
};
