/** @jsxRuntime classic */
/** @jsx React.createElement */
// biome-ignore lint/style/useImportType: This file will fail if only imported as type for some reason
import React from 'react';
import ReactPDF, {
  Page,
  Text,
  View,
  Document,
  StyleSheet,
  Image,
  Font,
} from '@react-pdf/renderer';

import type { CompGridsViewerExportResponse } from '@/services/comp-grids/viewer/export/types';

interface CompGridViewerTemplateProps {
  data: CompGridsViewerExportResponse;
  logoImg?: string;
  accountName?: string;
  createdAt?: string;
}

Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf',
      fontWeight: 'normal',
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf',
      fontWeight: 'bold',
    },
  ],
});

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Roboto',
    backgroundColor: '#FFFFFF',
    padding: 20,
  },
  table: {
    flexDirection: 'column',
    marginBottom: 5,
    borderWidth: 1,
    borderColor: '#bfbfbf',
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#bfbfbf',
    alignItems: 'stretch',
  },
  cell: {
    flex: 1,
    fontSize: 7,
    padding: 2,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#bfbfbf',
  },
  header: {
    backgroundColor: '#bfbfbf',
  },
  headerCell: {
    fontSize: 6,
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    margin: 3,
  },
  headerSubTitle: {
    fontSize: 7,
    fontWeight: 'bold',
    margin: 3,
  },
  dataBox: {
    marginBottom: 5,
    flexDirection: 'row',
  },
  image: {
    width: 50,
    height: 50,
    marginRight: 10,
    borderRadius: 25,
  },
  textContainer: {
    flex: 1,
  },
  // Column width specifications for comp grid viewer
  productTypeCell: {
    flex: 2,
  },
  productNameCell: {
    flex: 3,
  },
  compensationTypeCell: {
    flex: 2,
  },
  issueAgeCell: {
    flex: 1,
  },
  policyYearCell: {
    flex: 1,
  },
  levelCell: {
    flex: 2,
  },
  rateCell: {
    flex: 1,
  },
});

const CompGridViewerTemplate: React.FC<CompGridViewerTemplateProps> = ({
  data,
  logoImg,
  accountName,
  createdAt,
}) => {
  if (!data?.data || data.data.length === 0) {
    return (
      <Document>
        <Page size="LETTER" style={styles.page} orientation="landscape">
          <Text style={styles.headerTitle}>No Data Available</Text>
          <Text>No compensation grid data found for export.</Text>
        </Page>
      </Document>
    );
  }

  // Organize data by carrier
  const carrierData = data.data.reduce(
    (acc, item) => {
      const carrierKey = item.carrier.str_id || item.carrier.name || 'Unknown';
      if (!acc[carrierKey]) {
        acc[carrierKey] = {
          carrier: item.carrier,
          grids: [],
        };
      }
      acc[carrierKey].grids.push(item);
      return acc;
    },
    {} as Record<
      string,
      {
        carrier: { str_id?: string; name?: string };
        grids: typeof data.data;
      }
    >
  );

  const renderHeaderInfo = () => (
    <View style={styles.dataBox}>
      {logoImg ? (
        <View
          style={{
            alignItems: 'center',
            justifyContent: 'center',
          }}
        >
          <Image src={logoImg} style={styles.image} />
          <Text style={styles.headerTitle}>{accountName}</Text>
        </View>
      ) : (
        <View>
          <Text style={styles.headerTitle}>{accountName}</Text>
        </View>
      )}
      <View style={styles.textContainer}>
        <Text style={styles.headerTitle}>Comp Grid Export</Text>
        {createdAt && (
          <Text style={styles.headerSubTitle}>Created at: {createdAt}</Text>
        )}
      </View>
    </View>
  );

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const getVisibleRateFields = (rateFields: any) => {
    const defaultRateFields = ['carrier_rate', 'house_rate', 'rate'];
    if (!rateFields || !Array.isArray(rateFields) || rateFields.length === 0) {
      return defaultRateFields;
    }
    return rateFields.filter((field) => defaultRateFields.includes(field));
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const formatRate = (rate: any): string => {
    if (rate === null || rate === undefined) return '';
    const numericRate =
      typeof rate === 'object' && rate.toNumber
        ? rate.toNumber()
        : Number(rate);
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    return isNaN(numericRate) ? '' : `${numericRate}%`;
  };

  const renderCarrierPages = () => {
    return Object.entries(carrierData).flatMap(([carrierKey, carrierInfo]) => {
      const pages: React.ReactElement[] = [];

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      carrierInfo.grids.forEach((gridItem) => {
        const visibleRateFields = getVisibleRateFields(
          gridItem.comp_grid.rate_fields
        );

        const baseHeaders = [
          'Product Type',
          'Product Name',
          'Compensation Type',
          'Issue Age',
          'Policy Year',
        ];

        const allLevels = new Set<string>();
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        gridItem.criteria.forEach((criterion) => {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          criterion.levels.forEach((level) => {
            if (level.name) {
              allLevels.add(level.name);
            }
          });
        });

        const levelHeaders = Array.from(allLevels).sort();

        const rateHeaders: string[] = [];
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        levelHeaders.forEach((levelName) => {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          visibleRateFields.forEach((rateField) => {
            const displayName =
              rateField === 'carrier_rate'
                ? 'Carrier'
                : rateField === 'house_rate'
                  ? 'House'
                  : 'Total';
            rateHeaders.push(`${levelName} ${displayName}`);
          });
        });

        const allHeaders = [...baseHeaders, ...rateHeaders];

        const tableData: Record<string, string>[] = [];
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        gridItem.criteria.forEach((criterion) => {
          const rowData: Record<string, string> = {};

          rowData['Product Type'] = criterion.product_type || '';
          rowData['Product Name'] = criterion.product_name || '';
          rowData['Compensation Type'] = criterion.compensation_type || '';

          const issueAgeStart = criterion.issue_age_start;
          const issueAgeEnd = criterion.issue_age_end;
          let issueAgeDisplay = '';
          if (issueAgeStart !== null && issueAgeEnd !== null) {
            issueAgeDisplay = `${issueAgeStart}-${issueAgeEnd}`;
          } else if (issueAgeStart !== null) {
            issueAgeDisplay = `${issueAgeStart}+`;
          } else if (issueAgeEnd !== null) {
            issueAgeDisplay = `0-${issueAgeEnd}`;
          }
          rowData['Issue Age'] = issueAgeDisplay;

          const policyYearStart = criterion.policy_year_start;
          const policyYearEnd = criterion.policy_year_end;
          let policyYearDisplay = '';
          if (policyYearStart !== null && policyYearEnd !== null) {
            policyYearDisplay = `${policyYearStart}-${policyYearEnd}`;
          } else if (policyYearStart !== null) {
            policyYearDisplay = `${policyYearStart}+`;
          } else if (policyYearEnd !== null) {
            policyYearDisplay = `1-${policyYearEnd}`;
          }
          rowData['Policy Year'] = policyYearDisplay;

          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          levelHeaders.forEach((levelName) => {
            const level = criterion.levels.find((l) => l.name === levelName);
            if (level && level.rates.length > 0) {
              const rate = level.rates[0];
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              visibleRateFields.forEach((rateField) => {
                const displayName =
                  rateField === 'carrier_rate'
                    ? 'Carrier'
                    : rateField === 'house_rate'
                      ? 'House'
                      : 'Total';
                const headerKey = `${levelName} ${displayName}`;
                // @ts-expect-error
                rowData[headerKey] = formatRate(rate[rateField]);
              });
            } else {
              // Fill empty rate cells
              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              visibleRateFields.forEach((rateField) => {
                const displayName =
                  rateField === 'carrier_rate'
                    ? 'Carrier'
                    : rateField === 'house_rate'
                      ? 'House'
                      : 'Total';
                const headerKey = `${levelName} ${displayName}`;
                rowData[headerKey] = '';
              });
            }
          });

          tableData.push(rowData);
        });

        pages.push(
          <Page
            key={`${carrierKey}-${gridItem.comp_grid.str_id}`}
            size="LETTER"
            style={styles.page}
            orientation="landscape"
          >
            {renderHeaderInfo()}

            <Text style={styles.headerTitle}>
              {carrierInfo.carrier.name} - {gridItem.comp_grid.name}
            </Text>

            <View style={styles.table}>
              {/* Header row */}
              <View style={[styles.row, styles.header]}>
                {allHeaders.map((header, index) => (
                  <Text
                    key={`header-${
                      // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      index
                    }`}
                    style={[
                      styles.cell,
                      styles.headerCell,
                      header === 'Product Type'
                        ? styles.productTypeCell
                        : header === 'Product Name'
                          ? styles.productNameCell
                          : header === 'Compensation Type'
                            ? styles.compensationTypeCell
                            : header === 'Issue Age'
                              ? styles.issueAgeCell
                              : header === 'Policy Year'
                                ? styles.policyYearCell
                                : header.includes(' ')
                                  ? styles.rateCell
                                  : styles.levelCell,
                    ]}
                  >
                    {header}
                  </Text>
                ))}
              </View>

              {/* Data rows */}
              {tableData.map((row, rowIndex) => (
                <View
                  key={`row-${
                    // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    rowIndex
                  }`}
                  style={styles.row}
                >
                  {allHeaders.map((header, cellIndex) => (
                    <Text
                      key={`cell-${rowIndex}-${
                        // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        cellIndex
                      }`}
                      style={[
                        styles.cell,
                        header === 'Product Type'
                          ? styles.productTypeCell
                          : header === 'Product Name'
                            ? styles.productNameCell
                            : header === 'Compensation Type'
                              ? styles.compensationTypeCell
                              : header === 'Issue Age'
                                ? styles.issueAgeCell
                                : header === 'Policy Year'
                                  ? styles.policyYearCell
                                  : header.includes(' ')
                                    ? styles.rateCell
                                    : styles.levelCell,
                      ]}
                    >
                      {row[header] || ''}
                    </Text>
                  ))}
                </View>
              ))}
            </View>
          </Page>
        );
      });

      return pages;
    });
  };

  return <Document>{renderCarrierPages()}</Document>;
};

export default async (data: CompGridViewerTemplateProps) => {
  return await ReactPDF.renderToStream(<CompGridViewerTemplate {...data} />);
};
