import React from 'react';
import ReactPDF, {
  Page,
  Text,
  View,
  Document,
  StyleSheet,
  Image,
  Font,
} from '@react-pdf/renderer';

// TODO: Define proper types for data, totals and summary objects
interface PdfData {
  reportData: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data: any[];
    totals: unknown[];
    reportInfo: {
      name: string;
      commissionAmount: string;
      createdAt: string;
      logoImg: string;
      accountName: string;
      customTermsEnabled?: boolean;
      customTermsText?: string;
    }[];
  };
  summary?: unknown[];
}

interface CommissionPayoutTemplateProps {
  data: PdfData;
}

Font.register({
  family: 'Roboto',
  fonts: [
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-regular-webfont.ttf',
      fontWeight: 'normal',
    },
    {
      src: 'https://cdnjs.cloudflare.com/ajax/libs/ink/3.1.10/fonts/Roboto/roboto-bold-webfont.ttf',
      fontWeight: 'bold',
    },
  ],
});

const styles = StyleSheet.create({
  page: {
    fontFamily: 'Roboto',
    backgroundColor: '#FFFFFF',
    padding: 40,
  },
  table: {
    flexDirection: 'column',
    marginBottom: 5,
    borderWidth: 1,
    borderColor: '#bfbfbf',
  },
  row: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#bfbfbf',
    alignItems: 'stretch',
  },
  cell: {
    flex: 2,
    fontSize: 8,
    padding: 3,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#bfbfbf',
  },
  header: {
    backgroundColor: '#bfbfbf',
  },
  headerCell: {
    fontSize: 7,
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 10,
    fontWeight: 'bold',
    margin: 3,
  },
  headerSubTitle: {
    fontSize: 7,
    fontWeight: 'bold',
    margin: 3,
  },
  reportHeaderCell: {
    fontSize: 7,
    fontWeight: 'bold',
  },
  reportCell: {
    flex: 2,
    fontSize: 7,
    textAlign: 'center',
    borderRightWidth: 1,
    borderRightColor: '#bfbfbf',
  },
  termsBox: {
    flexDirection: 'row',
    borderTopWidth: 1,
    borderTopColor: '#bfbfbf',
    alignItems: 'flex-end',
    justifyContent: 'flex-end',
  },
  termsText: {
    textAlign: 'left',
    fontSize: 7,
    fontWeight: 'bold',
  },
  dataBox: {
    marginBottom: 5,
    flexDirection: 'row',
  },
  image: {
    width: 50,
    height: 50,
    marginRight: 10,
    borderRadius: 25,
  },
  textContainer: {
    flex: 1,
  },
  // Custom cell width for specific field columns
  'cell-Commissionable premium amount': {
    flex: 3,
  },
  'cell-Agent commission': {
    flex: 3,
  },
  'cell-Compensation type': {
    flex: 3,
    maxWidth: 65,
  },
  'cell-Payment date': {
    flex: 2,
  },
  'cell-Effective date': {
    flex: 2,
  },
  'cell-Period date': {
    flex: 2,
  },
  'cell-Policy number': {
    flex: 2,
  },
  'cell-Issue age': {
    flex: 1,
  },
  'cell-Product name': {
    flex: 3,
  },
});

const CommissionPayoutTemplate: React.FC<CommissionPayoutTemplateProps> = ({
  data,
}) => {
  if (!data) {
    return null;
  }

  let summaryKeys: string[] = [];
  if (data.summary) {
    summaryKeys = Object.keys(data.summary[0] || {});
  }

  let fieldKeys: string[] = [];
  // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  if (data.reportData && data.reportData.data) {
    fieldKeys = Object.keys((data.reportData.data[0] || [])[0] || {});
  }

  const columnsPerPage = 14;

  return (
    <Document>
      {data.summary && data.summary.length > 0 && (
        <Page size="LETTER" style={styles.page} orientation="landscape">
          <Text style={styles.headerTitle}>Summary</Text>
          <View style={styles.table}>
            <View style={[styles.row, styles.header]}>
              {summaryKeys.map((key, j) => (
                <Text
                  key={`summaryHeaderKey-${
                    // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    j
                  }`}
                  style={[styles.cell, styles.headerCell]}
                >
                  {key}
                </Text>
              ))}
            </View>
            {data.summary.map((summaryRow, i) => (
              <View
                key={`summaryRow-${
                  // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  i
                }`}
                style={styles.row}
                wrap={false}
              >
                {/* @ts-ignore */}
                {summaryRow &&
                  summaryKeys.map((key, j) => (
                    <Text
                      key={`summaryContent-${i}-${
                        // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                        j
                      }`}
                      style={styles.cell}
                    >
                      {/* @ts-expect-error */}
                      {summaryRow[key]}
                    </Text>
                  ))}
              </View>
            ))}
          </View>
        </Page>
      )}
      {/** biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
      {data.reportData &&
        data.reportData.data &&
        data.reportData.data.length > 0 &&
        data.reportData.data.map((reportDataTable, i) => {
          const pages = Math.ceil(fieldKeys.length / columnsPerPage);
          return Array.from({ length: pages }).map((_, pageIndex) => {
            const start = pageIndex * columnsPerPage;
            const end = start + columnsPerPage;
            const pageKeys = fieldKeys.slice(start, end);
            return (
              <Page
                key={`page-${i}-${
                  // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  pageIndex
                }`}
                size="LETTER"
                style={styles.page}
                orientation="landscape"
              >
                <React.Fragment
                  key={`reportDataTable-${i}-${
                    // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    pageIndex
                  }`}
                >
                  {/** biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX-- */}
                  {reportDataTable && reportDataTable[0] && (
                    <>
                      {data.reportData?.reportInfo &&
                        data.reportData.reportInfo.length > 0 && (
                          <View
                            key={`reportInfoKey-${
                              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                              i
                            }`}
                            style={styles.dataBox}
                          >
                            {data.reportData.reportInfo[i].logoImg ? (
                              <View
                                style={{
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                              >
                                <Image
                                  src={data.reportData.reportInfo[i].logoImg}
                                  style={styles.image}
                                />
                                <Text style={styles.headerTitle}>
                                  {data.reportData.reportInfo[i].accountName}
                                </Text>
                              </View>
                            ) : (
                              <View>
                                <Text style={styles.headerTitle}>
                                  {data.reportData.reportInfo[i].accountName}
                                </Text>
                              </View>
                            )}
                            <View style={styles.textContainer}>
                              <Text style={styles.headerTitle}>
                                {`${data.reportData.reportInfo[i].name}`}
                              </Text>
                              <Text style={styles.headerTitle}>
                                {`${data.reportData.reportInfo[i].commissionAmount}`}
                              </Text>
                              <Text style={styles.headerSubTitle}>
                                {`Created at: ${data.reportData.reportInfo[i].createdAt}`}
                              </Text>
                            </View>
                          </View>
                        )}
                      <View style={styles.table}>
                        <View style={[styles.row, styles.header]}>
                          {pageKeys.map((key, j) => (
                            <Text
                              key={`reportDataKey-${
                                // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                                j
                              }`}
                              style={[
                                styles.cell,
                                styles.reportHeaderCell,
                                styles[`cell-${key}`],
                              ]}
                              break
                            >
                              {key}
                            </Text>
                          ))}
                        </View>
                        {reportDataTable.map((row, j) => (
                          <View
                            key={`reportDataRow-${i}-${
                              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                              j
                            }`}
                            style={styles.row}
                          >
                            {row &&
                              pageKeys.map((key, k) => (
                                <Text
                                  key={`reportDataContent-${i}-${j}-${
                                    // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                                    k
                                  }`}
                                  style={[
                                    styles.reportCell,
                                    styles[`cell-${key}`],
                                  ]}
                                >
                                  {row[key]}
                                </Text>
                              ))}
                          </View>
                        ))}
                      </View>
                      {data.reportData?.totals &&
                        data.reportData.totals.length > 0 && (
                          <View
                            key={`reportTotalsKey-${
                              // biome-ignore lint/suspicious/noArrayIndexKey: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                              i
                            }`}
                            style={styles.dataBox}
                          >
                            {data.reportData.totals[i]?.[
                              'Commissions amount'
                            ] ? (
                              <Text style={styles.reportHeaderCell}>
                                {`Commissions amount ${data.reportData.totals[i]?.['Commissions amount']} `}
                              </Text>
                            ) : null}
                            {data.reportData.totals[i]?.['Premium amount'] ? (
                              <Text style={styles.reportHeaderCell}>
                                {`Premium amount ${data.reportData.totals[i]?.['Premium amount']}`}
                              </Text>
                            ) : null}
                          </View>
                        )}
                      {data.reportData.reportInfo[i]?.customTermsEnabled && (
                        <View style={styles.termsBox}>
                          <Text style={styles.termsText}>
                            {data.reportData.reportInfo[i].customTermsText}
                          </Text>
                        </View>
                      )}
                    </>
                  )}
                </React.Fragment>
              </Page>
            );
          });
        })}
    </Document>
  );
};

export default async (data) => {
  return await ReactPDF.renderToStream(
    <CommissionPayoutTemplate {...{ data }} />
  );
};
