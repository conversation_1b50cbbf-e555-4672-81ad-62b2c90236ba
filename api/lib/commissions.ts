import type { Prisma, report_data, statement_data } from '@prisma/client';
import { AccountIds, DEFAULT_FILTER } from 'common/constants';
import CommonFormatter from 'common/Formatter';
import { CompProfileMatcherState } from 'common/globalTypes';
import { numberOrDefault } from 'common/helpers';
import * as math from 'mathjs';
import 'reflect-metadata';

import Formatter from '@/lib/Formatter';
import { isNill, limitConcurrency, runInBatch } from '@/lib/helpers';
import { filterMatcher } from '@/lib/matcher';
import { prismaClient } from '@/lib/prisma';
import { findContactInclAncestors } from '@/lib/queries';
import { LRUCacheService } from '@/services/cache/lru';
import { DataStates } from '@/types';
import { TimerStats } from '@/lib/timerStats';
import type { AgentCommissionCalcLogItem } from '@/pages/api/data_processing/commissions/commissionCalContext';
import type { ScheduleDetail } from '@/services/advance-commission-schedule/interface';

// @ts-expect-error
export const checkIssueAge = (rule, statement) => {
  const result: { match: boolean; noMatchReason: string[] } = {
    match: true,
    noMatchReason: [],
  };
  const effectiveIssueAge = !isNill(statement.issue_age)
    ? statement.issue_age
    : statement.report?.issue_age;
  // TODO: Align issue_age_min/max and issue_age_start/end
  // Non-comp grids uses issue_age_min/max
  if (!isNill(rule?.issue_age_max) && !isNill(effectiveIssueAge)) {
    if (effectiveIssueAge > rule.issue_age_max) {
      result.noMatchReason.push('issue_age_max critera not met.');
      result.match = false;
    }
  }
  if (!isNill(rule?.issue_age_min) && !isNill(effectiveIssueAge)) {
    if (effectiveIssueAge < rule.issue_age_min) {
      result.noMatchReason.push('issue_age_min critera not met.');
      result.match = false;
    }
  }
  // Comp grids use issue_age_start/end
  if (!isNill(rule?.issue_age_start) && !isNill(effectiveIssueAge)) {
    if (effectiveIssueAge < rule.issue_age_start) {
      result.noMatchReason.push('issue_age_start critera not met.');
      result.match = false;
    }
  }
  if (!isNill(rule?.issue_age_end) && !isNill(effectiveIssueAge)) {
    if (effectiveIssueAge > rule.issue_age_end) {
      result.noMatchReason.push('issue_age_end critera not met.');
      result.match = false;
    }
  }
  return result;
};

export const getEffectiveFieldVal = (
  fieldId: string,
  statement: statement_data & { report?: report_data }
) => {
  let effectiveFieldVal = !isNill(statement[fieldId as keyof statement_data])
    ? statement[fieldId as keyof statement_data]
    : statement.report?.[fieldId as keyof report_data];
  if (typeof effectiveFieldVal === 'string') {
    effectiveFieldVal = effectiveFieldVal.trim().toLowerCase();
  }
  return effectiveFieldVal;
};

// @ts-expect-error
export const checkTextRule = (fieldId, rule, statement) => {
  const result = { match: true, noMatchReason: [] };
  let effectiveFieldVal = !isNill(statement[fieldId])
    ? statement[fieldId]
    : statement.report?.[fieldId];
  if (typeof effectiveFieldVal === 'string') {
    effectiveFieldVal = effectiveFieldVal.trim().toLowerCase();
  }
  if (!isNill(rule[fieldId])) {
    if (
      rule[fieldId] === DEFAULT_FILTER.BLANK_OPTION &&
      !isNill(effectiveFieldVal)
    ) {
      result.noMatchReason.push(
        // @ts-expect-error
        `${fieldId} critera not met (rule: ${rule[fieldId]}, data: ${effectiveFieldVal}.`
      );
      result.match = false;
    } else if (
      isNill(effectiveFieldVal) ||
      effectiveFieldVal !== rule[fieldId].toLowerCase()
    ) {
      result.noMatchReason.push(
        // @ts-expect-error
        `${fieldId} critera not met (rule: ${rule[fieldId]}, data: ${effectiveFieldVal}.`
      );
      result.match = false;
    }
  }
  return result;
};

export const checkArrayIdsToTextRule = (
  // @ts-expect-error
  dataFieldId,
  // @ts-expect-error
  ruleFieldId,
  // @ts-expect-error
  rule,
  // @ts-expect-error
  statement,
  // @ts-expect-error
  lookupData,
  // @ts-expect-error
  lookupField,
  checkReportData = false
) => {
  const result = { match: true, noMatchReason: [] };
  let effectiveFieldVal = !isNill(statement[dataFieldId])
    ? statement[dataFieldId]
    : statement.report?.[dataFieldId];
  if (typeof effectiveFieldVal === 'string') {
    effectiveFieldVal = effectiveFieldVal
      .trim()
      .toLowerCase()
      .replaceAll(' ', '');
  }
  let reportFieldVal = statement.report?.[dataFieldId];
  if (typeof reportFieldVal === 'string') {
    reportFieldVal = reportFieldVal.trim().toLowerCase().replaceAll(' ', '');
  }

  if (Array.isArray(rule[ruleFieldId]) && rule[ruleFieldId].length > 0) {
    if (
      effectiveFieldVal &&
      !rule[ruleFieldId]
        .map((id) =>
          lookupData[id]?.[lookupField]
            ?.trim()
            ?.toLowerCase()
            .replaceAll(' ', '')
        )
        .includes(effectiveFieldVal) &&
      (!checkReportData ||
        !rule[ruleFieldId]
          .map((id) =>
            lookupData[id]?.[lookupField]
              ?.trim()
              ?.toLowerCase()
              .replaceAll(' ', '')
          )
          .includes(reportFieldVal))
    ) {
      result.noMatchReason.push(
        // @ts-expect-error
        `${dataFieldId}/${ruleFieldId} critera not met (rule: ${rule[ruleFieldId]} (), data: ${effectiveFieldVal}.`
      );
      result.match = false;
    }
  }
  return result;
};

export const checkPayerRateApplicability = (
  // @ts-expect-error
  profile,
  // @ts-expect-error
  criteriaId,
  // @ts-expect-error
  effectiveDate
) => {
  const payerLevel =
    profile.agent_commission_schedule_profile?.payer_comp_grid_level;
  const _payerRates = payerLevel?.comp_grid_rates?.filter(
    // @ts-expect-error
    (rate) =>
      rate?.comp_grid_criterion_id === criteriaId &&
      (rate?.date_ranges?.length === 0 ||
        rate?.date_ranges?.some(
          // @ts-expect-error
          (range) =>
            (!range.start_date || range.start_date <= effectiveDate) &&
            (!range.end_date || range.end_date >= effectiveDate)
        ))
  );
  return _payerRates?.length > 0;
};

export const filterGridsByLevelName = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  compGridsRaw: any[],
  gridLevels: string[]
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
): any[] => {
  return compGridsRaw
    .map((grid) => {
      // @ts-expect-error
      const filteredCompGridLevels = grid.comp_grid_levels.filter((level) =>
        gridLevels.includes(level.name)
      );

      if (filteredCompGridLevels.length === 0) {
        return null;
      }

      // @ts-expect-error
      const updatedCompGridLevels = filteredCompGridLevels.map((level) => ({
        ...level,
        comp_grid_rates: level?.comp_grid_rates?.filter(
          // @ts-expect-error
          (rate) => rate.comp_grid_level_id === level.id
        ),
      }));

      return {
        ...grid,
        comp_grid_levels: updatedCompGridLevels,
      };
    })
    .filter((grid) => grid !== null);
};

export const initializeCompGridLevels = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  profileData: any = {},
  dataKey: string,
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  levelRates: any[] = []
) => {
  profileData[dataKey] = profileData[dataKey] || {};
  profileData[dataKey].comp_grid_rates =
    profileData[dataKey].comp_grid_rates || [];

  profileData[dataKey].comp_grid_rates = levelRates.flatMap((criterion) =>
    'comp_grid_rates' in criterion ? criterion.comp_grid_rates : []
  );
};

export const fetchCompGrids = async (
  // @ts-expect-error
  profileData,
  // @ts-expect-error
  matchedCompany,
  compGridCache: LRUCacheService = new LRUCacheService({ max: 300 })
) => {
  const whereCondition = matchedCompany
    ? {
        company_id: matchedCompany?.id,
        state: DataStates.ACTIVE,
      }
    : { state: DataStates.ACTIVE };

  // WorldChangers needs additional comp grid levels for comp calc
  const compGridsRaw = await compGridCache.cacheResult(
    `fetchCompGrids-${profileData.id}-${JSON.stringify(whereCondition)}`,
    () =>
      prismaClient.comp_grids.findMany({
        where: whereCondition,
        include: {
          comp_grid_criteria: {
            where: { state: DataStates.ACTIVE },
          },
          comp_grid_levels: {
            where: {
              state: DataStates.ACTIVE,
              name: {
                in: [
                  profileData.payer_grid_level_name,
                  profileData.payee_grid_level_name,
                  ...(String(profileData.account_id) ===
                  AccountIds.WORLD_CHANGERS
                    ? ['Agency', 'Agent']
                    : []),
                ].filter(Boolean),
              },
            },
            include: {
              comp_grid_rates: {
                where: { state: DataStates.ACTIVE },
                include: {
                  date_ranges: {
                    where: { state: DataStates.ACTIVE },
                  },
                },
              },
            },
          },
        },
      })
  );

  const gridIds = compGridsRaw.map((grid) => grid.id);
  const levelNames = [
    profileData.payer_grid_level_name,
    profileData.payee_grid_level_name,
    ...(String(profileData.account_id) === AccountIds.WORLD_CHANGERS
      ? ['Agency', 'Agent']
      : []),
  ].filter(Boolean);

  if (gridIds.length > 0) {
    const otherCompGridLevels = await compGridCache.cacheResult(
      `fetchOtherCompGridLevels-${profileData.id}-${JSON.stringify({
        gridIds,
        levelNames,
      })}`,
      () =>
        prismaClient.comp_grid_levels.findMany({
          where: {
            state: DataStates.ACTIVE,
            name: {
              in: levelNames,
            },
            related_comp_grids: {
              some: {
                state: DataStates.ACTIVE,
                id: { in: gridIds },
              },
            },
          },
          include: {
            comp_grid_rates: {
              where: { state: DataStates.ACTIVE },
              include: {
                date_ranges: {
                  where: { state: DataStates.ACTIVE },
                },
              },
            },
            related_comp_grids: {
              where: {
                id: { in: gridIds },
              },
              select: { id: true },
            },
          },
        })
    );

    if (otherCompGridLevels?.length > 0) {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const gridIdToLevelsMap = new Map<number, any[]>();
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      otherCompGridLevels.forEach((level) => {
        const { related_comp_grids, ...levelData } = level;
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        related_comp_grids?.forEach((grid) => {
          if (!gridIdToLevelsMap.has(grid.id)) {
            gridIdToLevelsMap.set(grid.id, []);
          }
          // @ts-expect-error
          gridIdToLevelsMap.get(grid.id).push(levelData);
        });
      });

      for (const grid of compGridsRaw) {
        const newLevels = gridIdToLevelsMap.get(grid.id);
        // @ts-expect-error
        if (newLevels?.length > 0) {
          const existingIds = new Set(
            grid.comp_grid_levels.map((level) => level.id)
          );
          // @ts-expect-error
          const uniqueLevels = newLevels.filter(
            (level) => !existingIds.has(level.id)
          );
          grid.comp_grid_levels = [...grid.comp_grid_levels, ...uniqueLevels];
        }
      }
    }
  }

  const compGrids = compGridsRaw.map((g) => {
    return {
      ...g,
      comp_grid_levels: g.comp_grid_levels.map((level) => ({
        ...level,
        comp_grid_rates: level?.comp_grid_rates?.filter(
          (rate) => rate.comp_grid_level_id === level.id
        ),
      })),
    };
  });

  return compGrids;
};

export const getCompGridCriteria = async (
  // @ts-expect-error
  profile,
  // @ts-expect-error
  rule,
  // @ts-expect-error
  lookupData,
  // @ts-expect-error
  matchedCompany,
  // @ts-expect-error
  compGridCache
) => {
  let compGridCriteria = [];

  const { agent_commission_schedule_profile: profileData } = profile;

  if (profileData?.single_carrier_mode) {
    compGridCriteria =
      Array.isArray(rule.comp_grid_criteria_id) &&
      rule.comp_grid_criteria_id.length > 0
        ? rule.comp_grid_criteria_id
            // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ?.map((cid: string) => lookupData['compGridCriteria'][cid])
            // @ts-expect-error
            .filter((r) => r)
        : // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          Object.values(lookupData['compGridCriteria']).filter(
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            (r: any) =>
              r.comp_grid_id ===
              profile.agent_commission_schedule_profile.comp_grid_id
          );
  } else if (profileData) {
    const compGridsRaw = await fetchCompGrids(
      profileData,
      matchedCompany,
      compGridCache
    );
    compGridCriteria = compGridsRaw.flatMap(
      (grid) => grid.comp_grid_criteria || []
    );
    const profileLevelKeys = [
      {
        dataKey: 'payee_comp_grid_level',
        sourceKey: 'payee_grid_level_name',
      },
      {
        dataKey: 'payer_comp_grid_level',
        sourceKey: 'payer_grid_level_name',
      },
    ];
    // WorldChangers needs additional comp grid levels for comp calc
    if (profileData.account_id === AccountIds.WORLD_CHANGERS) {
      profileData.agency_grid_level_name = 'Agency';
      profileData.agent_grid_level_name = 'Agent';
      profileLevelKeys.push(
        {
          dataKey: 'agency_comp_grid_level',
          sourceKey: 'agency_grid_level_name',
        },
        {
          dataKey: 'agent_comp_grid_level',
          sourceKey: 'agent_grid_level_name',
        }
      );
    }

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    profileLevelKeys.forEach(({ dataKey, sourceKey }) => {
      const compGridData = filterGridsByLevelName(compGridsRaw, [
        profileData[sourceKey],
      ]);
      const flatLevelsWithRates = compGridData.flatMap(
        (rate) => rate.comp_grid_levels || []
      );
      initializeCompGridLevels(profileData, dataKey, flatLevelsWithRates);
    });
  }

  return compGridCriteria;
};

// @ts-expect-error
export const profileCompanyCheck = (profile, statement) => {
  const { agent_commission_schedule_profile } = profile;
  if (agent_commission_schedule_profile.single_carrier_mode) {
    const profileCompanyName =
      profile.agent_commission_schedule_profile.company?.company_name;
    const firstValidatorCheck =
      isNill(profileCompanyName) ||
      [
        statement.carrier_name?.toLowerCase(),
        statement.writing_carrier_name?.toLowerCase(),
      ].includes(profileCompanyName.toLowerCase());
    const secondValidatorCheck =
      profileCompanyName &&
      [
        statement.carrier_name?.toLowerCase(),
        statement.writing_carrier_name?.toLowerCase(),
      ].includes(profileCompanyName.toLowerCase());
    return {
      firstValidatorCheck,
      secondValidatorCheck,
    };
  }
  const profileCompaniesName =
    profile.agent_commission_schedule_profile.companies;

  if (profileCompaniesName?.length === 0) {
    return {
      matchedCompany: null,
      firstValidatorCheck: true,
      secondValidatorCheck: true,
    };
  }
  const firstValidatorCheck = profileCompaniesName.some(
    (company: { company_name?: string }) => {
      return (
        isNill(company.company_name) ||
        company.company_name?.toLowerCase() ===
          statement.carrier_name?.toLowerCase() ||
        company.company_name?.toLowerCase() ===
          statement.writing_carrier_name?.toLowerCase()
      );
    }
  );
  const secondValidatorCheck = profileCompaniesName.some(
    (company: { company_name?: string }) => {
      return (
        company.company_name &&
        (company.company_name?.toLowerCase() ===
          statement.carrier_name?.toLowerCase() ||
          company.company_name?.toLowerCase() ===
            statement.writing_carrier_name?.toLowerCase())
      );
    }
  );
  const matchedCompany = profileCompaniesName.find(
    (company: { company_name?: string }) => {
      return (
        company.company_name?.toLowerCase() ===
          statement.carrier_name?.toLowerCase() ||
        company.company_name?.toLowerCase() ===
          statement.writing_carrier_name?.toLowerCase()
      );
    }
  );
  return {
    matchedCompany,
    firstValidatorCheck,
    secondValidatorCheck,
  };
};

export const getApplicableCompProfiles = async (
  // @ts-expect-error
  agent,
  // @ts-expect-error
  statement,
  // @ts-expect-error
  lookupData,
  // @ts-expect-error
  effectiveDate,
  useCompGrids = false,
  agentCommissionCalcLog = {},
  compProfileMatchContext = null,
  compGridCache: LRUCacheService = new LRUCacheService({ max: 300 }),
  timerStats: TimerStats = new TimerStats()
) => {
  const start = timerStats.start();
  const { compProfiles, agentStrId, statementContactStrId } = agent;
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let res;
  const matches = [];
  let alertMessage =
    compProfiles.length === 0 ? 'No applicable comp profiles' : '';
  console.time('getApplicableCompProfiles');
  for (const profile of compProfiles) {
    const { matchedCompany, firstValidatorCheck, secondValidatorCheck } =
      profileCompanyCheck(profile, statement);
    // TODO: Score profile and rules separately
    let currentMatchScore = 0;
    if (firstValidatorCheck) {
      if (secondValidatorCheck) {
        currentMatchScore += 1;
      }
      const effectiveEffectiveDate = !isNill(statement.effective_date)
        ? statement.effective_date
        : statement.report?.effective_date;
      if (
        profile.start_date &&
        effectiveEffectiveDate &&
        effectiveEffectiveDate < profile.start_date
      ) {
        // console.log(
        //   `Commission schedule date criteria not met: effective_date (${statement.effective_date}) < profile.start_date(${profile.start_date})`
        // );
        continue;
      } else {
        currentMatchScore += 1;
      }
      if (
        profile.end_date &&
        effectiveEffectiveDate &&
        effectiveEffectiveDate > profile.end_date
      ) {
        // console.log(
        //   `Commission schedule date criteria not met: effective_date (${statement.effective_date}) > profile.end_date(${profile.end_date})`
        // );
        continue;
      } else {
        currentMatchScore += 1;
      }

      console.time('getCompGridCriteria');
      const compGridCriteriaData = useCompGrids
        ? await limitConcurrency(
            async (rule) => {
              return await getCompGridCriteria(
                profile,
                rule,
                lookupData,
                matchedCompany,
                compGridCache
              );
            },
            profile.agent_commission_schedule_profile.schedules,
            30
          )
        : [];
      console.timeEnd('getCompGridCriteria');

      for (
        let idx = 0;
        idx < profile.agent_commission_schedule_profile.schedules.length;
        idx++
      ) {
        const rule = profile.agent_commission_schedule_profile.schedules[idx];
        let match = true;
        let matchedCriteriaId = null;
        let matchedCriteriaStr = null;
        const noMatchReason = [];

        const needsCompGridCriteria =
          useCompGrids &&
          !['payoutRate', 'keepRate', 'overrideSplit'].includes(
            rule.calculation_method
          );

        if (!useCompGrids) {
          const issueAgeCheck = checkIssueAge(rule, statement);
          if (!issueAgeCheck.match) {
            noMatchReason.push(...issueAgeCheck.noMatchReason);
            match = false;
          } else {
            currentMatchScore += 1;
          }

          const compensationTypeCheck = checkTextRule(
            'compensation_type',
            rule,
            statement
          );
          if (!compensationTypeCheck.match) {
            // const compensationTypeAlternativeCheck =
            //   checkCompensationTypeAlternative(rule, statement);
            // if (!compensationTypeAlternativeCheck.match) {
            //   noMatchReason.push(...compensationTypeCheck.noMatchReason);
            //   noMatchReason.push(
            //     ...compensationTypeAlternativeCheck.noMatchReason
            //   );
            //   match = false;
            // } else {
            //   currentMatchScore += 1;
            // }
            noMatchReason.push(...compensationTypeCheck.noMatchReason);
            match = false;
          } else {
            currentMatchScore += 1;
          }

          const transactionTypeCheck = checkTextRule(
            'transaction_type',
            rule,
            statement
          );
          if (!transactionTypeCheck.match) {
            noMatchReason.push(...transactionTypeCheck.noMatchReason);
            match = false;
          } else {
            currentMatchScore += 1;
          }

          const productsCheck = checkArrayIdsToTextRule(
            'product_name',
            'product_id',
            rule,
            statement,
            // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            lookupData['companyProducts'],
            'product_name'
          );
          if (!productsCheck.match) {
            noMatchReason.push(...productsCheck.noMatchReason);
            match = false;
          } else {
            currentMatchScore += 1;
          }

          const productOptionsCheck = checkArrayIdsToTextRule(
            'product_option_name',
            'product_option_id',
            rule,
            statement,
            // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            lookupData['companyProductOptions'],
            'name'
          );
          if (!productOptionsCheck.match) {
            noMatchReason.push(...productOptionsCheck.noMatchReason);
            match = false;
          } else {
            currentMatchScore += 1;
          }
        } else {
          // No scoring for comp grid criteria yet
          const compGridCriteria = compGridCriteriaData[idx];
          // Check if any of the compGridCriteria match the statement
          let compGridCriteriaMatch = false;
          for (const compGridCriterion of compGridCriteria) {
            const issueAgeCheck = checkIssueAge(compGridCriterion, statement);
            if (!issueAgeCheck.match) {
              continue;
            }
            const compensationTypeCheck = checkTextRule(
              'compensation_type',
              compGridCriterion,
              statement
            );
            if (!compensationTypeCheck.match) continue;
            const transactionTypeCheck = checkTextRule(
              'transaction_type',
              compGridCriterion,
              statement
            );
            if (!transactionTypeCheck.match) continue;
            const paymentModeCheck = checkTextRule(
              'payment_mode',
              compGridCriterion,
              statement
            );
            if (!paymentModeCheck.match) continue;
            const compGridProductName =
              compGridCriterion.comp_grid_product?.name?.trim()?.toLowerCase();
            const effectiveProductName = getEffectiveFieldVal(
              'product_name',
              statement
            );
            const isGridProductNameMatch =
              compGridProductName === effectiveProductName;
            if (isGridProductNameMatch) {
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              console.log('matchy matchy');
            }

            const productIds =
              // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              lookupData['compGridProducts'][
                compGridCriterion.grid_product_id
                // @ts-expect-error
              ]?.company_products?.map((cp) => cp.id) ?? [];
            const productsCheck = checkArrayIdsToTextRule(
              'product_name',
              'product_id',
              { product_id: productIds },
              statement,
              // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              lookupData['companyProducts'],
              'product_name',
              true
            );
            if (
              !isGridProductNameMatch &&
              (productIds?.length === 0 || !productsCheck.match)
            )
              continue;
            // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            console.log('compGridCriterion matched', matchedCriteriaStr);
            const validPayerRate = checkPayerRateApplicability(
              profile,
              compGridCriterion.id,
              effectiveDate
            );
            if (validPayerRate) {
              compGridCriteriaMatch = true;
              matchedCriteriaId = compGridCriterion.id;
              matchedCriteriaStr =
                CommonFormatter.compGridCriterion(compGridCriterion);
              matches.push({
                matchedCriteriaId,
                matchedCriteriaStr,
              });
            } else {
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              console.log('Payer rate not applicable', matchedCriteriaStr);
            }
          }

          if (matches.length > 1) {
            // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            console.warn('Found multiple matches');
          }
          if (
            needsCompGridCriteria &&
            compGridCriteria.length > 0 &&
            !compGridCriteriaMatch
          ) {
            alertMessage = 'No matching comp grid criteria found';
            if (compProfileMatchContext) {
              // @ts-expect-error
              compProfileMatchContext.addCompProfileMatchContextItem(
                agentStrId,
                profile?.agent_commission_schedule_profile?.str_id,
                {
                  state: CompProfileMatcherState.UNMATCHED,
                  message: alertMessage,
                  updateAt: new Date(),
                }
              );
            }
            match = false;
          } else if (needsCompGridCriteria && compGridCriteria.length === 0) {
            alertMessage = 'Missing comp grid data';
            if (compProfileMatchContext) {
              // @ts-expect-error
              compProfileMatchContext.addCompProfileMatchContextItem(
                agentStrId,
                profile?.agent_commission_schedule_profile?.str_id,
                {
                  state: CompProfileMatcherState.UNMATCHED,
                  message: alertMessage,
                  updateAt: new Date(),
                }
              );
            }
          }
        }

        if (rule.match_criteria) {
          let matchersMatch = true;
          for (const matcher of rule.match_criteria) {
            // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            console.log('Checking field matcher', JSON.stringify(matcher));
            if (
              !filterMatcher(matcher)(statement, {
                statementContactStrId,
                agentStrId,
              })
            ) {
              noMatchReason.push(
                `Criteria (${Formatter.fieldMatcher(matcher)}}) not met.`
              );
              matchersMatch = false;
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              console.log(
                `Criteria (${Formatter.fieldMatcher(matcher)}}) not met.`
              );
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            } else console.log('Matched matcher');
          }
          if (matchersMatch) {
            currentMatchScore += 0.5; // Giving half point for additional filters
          } else if (!matchersMatch) {
            match = false;
          }
        }

        if (match) {
          const matchMessage = `Match found: profile (${
            profile.agent_commission_schedule_profile.name
          }), rule (${JSON.stringify(rule)})`;
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.log(matchMessage);
          if (compProfileMatchContext) {
            // @ts-expect-error
            compProfileMatchContext.addCompProfileMatchContextItem(
              agentStrId,
              profile?.agent_commission_schedule_profile?.str_id,
              {
                state: CompProfileMatcherState.MATCHED,
                profileName: profile?.agent_commission_schedule_profile?.name,
                rule: `${JSON.stringify(rule)}`,
                updateAt: new Date(),
              }
            );
          }
          const currentMatch = {
            agentProfileConfig: profile,
            rule,
            matchedCriteriaId,
            matchedCriteriaStr,
            matchScore: currentMatchScore,
          };
          const previousMatchScore = res?.[0]?.matchScore ?? -1;
          if (currentMatchScore === previousMatchScore) {
            // @ts-expect-error
            res.push(currentMatch);
          } else if (currentMatchScore > previousMatchScore) {
            res = [currentMatch];
          }
          // } else {
          //   console.log(
          //     `Profile (${
          //       profile.agent_commission_schedule_profile.name
          //     }), rule (${JSON.stringify(
          //       rule
          //     )}) does not match: (${noMatchReason.join(', ')})`
          //   );
        }
        if (noMatchReason.length > 0) {
          alertMessage = noMatchReason.join(', ');
          if (compProfileMatchContext) {
            // @ts-expect-error
            compProfileMatchContext.addCompProfileMatchContextItem(
              agentStrId,
              profile?.agent_commission_schedule_profile?.str_id,
              {
                state: CompProfileMatcherState.UNMATCHED,
                message: alertMessage,
                updateAt: new Date(),
              }
            );
          }
        }
      }
      console.timeEnd('getApplicableCompProfiles');
    } else if (alertMessage.length === 0) {
      alertMessage = 'No comp profile match';
    }
  }

  if (alertMessage.length > 0 && !res && statement.id && agentStrId) {
    // @ts-expect-error
    agentCommissionCalcLog[statement.id] = {
      // @ts-expect-error
      ...(agentCommissionCalcLog[statement.id] ?? {}),
      [agentStrId]: [
        // @ts-expect-error
        ...(agentCommissionCalcLog[statement.id]?.[agentStrId] ?? []).filter(
          (log: AgentCommissionCalcLogItem) => log.alerts !== alertMessage
        ),
        {
          alerts: alertMessage,
          // biome-ignore lint/complexity/useDateNow: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          calculatedAt: new Date().getTime(),
        },
      ],
    };
  }

  // @ts-expect-error
  if (res?.length > 1) {
    const msg =
      'Found multiple matching agent commission schedule profile rules with the same specifity. This can lead to inconsistent and unexpected results.';
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.warn(msg);
    // Sentry.captureMessage(msg);
  }
  const duration = timerStats.end('getApplicableCompProfiles', start);
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `getApplicableCompProfiles took ${duration.toFixed(2)}s (total: ${timerStats.get('getApplicableCompProfiles').toFixed(2)}s)\n` +
      timerStats.logString('getApplicableCompProfiles')
  );
  return res?.[0];
};

// @ts-expect-error
export const validateCommissionSchedule = (scheduleConfig) => {
  const res: { isValid: boolean; errors: string[] } = {
    isValid: true,
    errors: [],
  };
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  scheduleConfig.commission_schedule?.forEach(
    (schedule: Required<ScheduleDetail>) => {
      if (Number.isNaN(Number.parseFloat(`${schedule.year}`))) {
        res.isValid = false;
        res.errors.push(
          `Invalid commission schedule year (${schedule.year}) in ${scheduleConfig.name} (${scheduleConfig.id}`
        );
      }
      if (Number.isNaN(Number.parseFloat(`${schedule.rate}`))) {
        res.isValid = false;
        res.errors.push(
          `Invalid commission schedule rate (${schedule.rate}) in ${scheduleConfig.name} (${scheduleConfig.id}`
        );
      }
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      schedule.breakdown.forEach((monthRate) => {
        try {
          math.number(math.fraction(monthRate));
        } catch {
          res.isValid = false;
          res.errors.push(
            `Invalid commission schedule rate breakdown (${schedule.breakdown}) in ${scheduleConfig.name} (${scheduleConfig.id}`
          );
        }
      });
      if (
        schedule.max_commission &&
        Number.isNaN(Number.parseFloat(`${schedule.max_commission}`))
      ) {
        res.isValid = false;
        res.errors.push(
          `Invalid commission schedule max_commission (${schedule.max_commission}) in ${scheduleConfig.name} (${scheduleConfig.id}`
        );
      }
    }
  );

  return res;
};

export const getApplicableCommissionSchedule = (
  // @ts-expect-error
  scheduleConfig,
  currentYear: number
) => {
  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let res;
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  JSON.parse(JSON.stringify(scheduleConfig.commission_schedule))
    .sort(
      (a: ScheduleDetail, b: ScheduleDetail) =>
        Number.parseInt(`${a.year}`) - Number.parseInt(`${b.year}`)
    )
    .forEach((schedule: ScheduleDetail) => {
      if (Number.parseInt(`${schedule.year}`) <= currentYear + 1) {
        res = schedule;
      }
    });
  return res;
};

const globalCache = new LRUCacheService({
  max: 300,
  ttl: 1000 * 60 * 10,
});

const fetchCompGridRatesByGridLevelIds = async (
  gridLevelIds: null | number[]
) => {
  gridLevelIds = [...new Set(gridLevelIds?.filter(Boolean))];
  if (!gridLevelIds?.length) return [];
  const compGridRates = await runInBatch({
    items: gridLevelIds,
    batchSize: 10,
    onBatch: async (gridLevelIds) => {
      return prismaClient.comp_grid_rates.findMany({
        where: { state: 'active', comp_grid_level_id: { in: gridLevelIds } },
        include: {
          comp_grid_level: true,
          date_ranges: {
            where: { state: 'active' },
          },
        },
      });
    },
  });
  return compGridRates;
};

// @ts-expect-error
export const fetchProfileCompanies = async (compProfiles) => {
  if (!compProfiles) return;
  // Separate profiles into single and multi-carrier modes
  const multiCarrierProfiles = compProfiles.filter(
    // @ts-expect-error
    (profile) =>
      !profile?.agent_commission_schedule_profile?.single_carrier_mode
  );

  if (multiCarrierProfiles.length > 0) {
    // Fetch all company mappings for multi-carrier profiles in a single query
    const mappings = await prismaClient.company_mappings.findMany({
      where: {
        record_id: {
          in: multiCarrierProfiles.map(
            (p: { agent_commission_schedule_profile: { id: string } }) =>
              p?.agent_commission_schedule_profile.id
          ),
        },
        record_type: 'agent_commission_schedule_profiles',
        account_id: {
          in: multiCarrierProfiles.map(
            (p: {
              agent_commission_schedule_profile: { account_id: string };
            }) => p?.agent_commission_schedule_profile.account_id
          ),
        },
        state: DataStates.ACTIVE,
      },
      select: {
        record_id: true,
        company_id: true,
      },
    });

    // Extract unique company IDs
    const companyIds = [...new Set(mappings.map((m) => m.company_id))];

    // Fetch all related company entities in a single query
    const companiesEntities = await prismaClient.companies.findMany({
      where: {
        id: { in: companyIds },
        state: DataStates.ACTIVE,
      },
      select: {
        id: true,
        company_name: true,
      },
    });

    // Map company entities back to their profiles
    const companyMap = mappings.reduce((map, m) => {
      // @ts-expect-error
      if (!map[m.record_id]) {
        // @ts-expect-error
        map[m.record_id] = [];
      }
      const company = companiesEntities.find((c) => c.id === m.company_id);
      if (company) {
        // @ts-expect-error
        map[m.record_id].push(company);
      }
      return map;
    }, {});

    // Assign companies to profiles
    for (const profile of multiCarrierProfiles) {
      profile.agent_commission_schedule_profile.companies =
        // @ts-expect-error
        companyMap[profile?.agent_commission_schedule_profile?.id] || [];
    }
  }

  // Assign empty companies for single-carrier mode profiles
  for (const profile of compProfiles) {
    if (profile.agent_commission_schedule_profile.single_carrier_mode) {
      profile.agent_commission_schedule_profile.companies = [];
    }
  }
};

export const getCompProfiles = async (
  contact_ids: number[],
  cache: LRUCacheService = globalCache,
  req?: { account_id: string | null },
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();
  const setsIds = contact_ids.filter((id) => !cache.get(`sets_${id}`));
  const profileIds = contact_ids.filter((id) => !cache.get(`profile_${id}`));
  const cachedSets = contact_ids
    .flatMap((id) => cache.get(`sets_${id}`))
    .filter((r) => r);
  const cachedProfiles = contact_ids
    .flatMap((id) => cache.get(`profile_${id}`))
    .filter((r) => r);

  const compProfileSetsPromise = setsIds.length
    ? prismaClient.contacts_agent_commission_schedule_profiles_sets.findMany({
        where: {
          state: 'active',
          agent_commission_schedule_profiles_sets: { state: 'active' },
          contact: { state: 'active', id: { in: setsIds } },
        },
        include: {
          agent_commission_schedule_profiles_sets: {
            where: { state: 'active' },
            include: {
              commission_profiles: {
                where: { state: 'active' },
                include: {
                  company: { where: { state: 'active' } },
                  payee_comp_grid_level: {
                    where: { state: 'active' },
                  },
                  payer_comp_grid_level: {
                    where: { state: 'active' },
                  },
                },
              },
            },
          },
        },
      })
    : [];

  const compProfilesPromise = profileIds.length
    ? prismaClient.contacts_agent_commission_schedule_profiles.findMany({
        where: {
          state: 'active',
          agent_commission_schedule_profile: { state: 'active' },
          contact: { state: 'active', id: { in: profileIds } },
        },
        include: {
          agent_commission_schedule_profile: {
            where: { state: 'active' },
            include: {
              company: { where: { state: 'active' } },
              payee_comp_grid_level: {
                where: { state: 'active' },
              },
              payer_comp_grid_level: {
                where: { state: 'active' },
              },
            },
          },
          contact: { where: { state: 'active' } },
        },
      })
    : [];
  const [compProfileSets, compProfiles] = await Promise.all([
    compProfileSetsPromise,
    compProfilesPromise,
  ]);

  const gridLevelIds = [
    ...new Set(
      compProfileSets.flatMap((set) =>
        // @ts-expect-error
        set.agent_commission_schedule_profiles_sets.commission_profiles.flatMap(
          (s) => [s.payee_comp_grid_level_id, s.payer_comp_grid_level_id]
        )
      )
    ),
    ...new Set(
      compProfiles.flatMap((p) => [
        // @ts-expect-error
        p.agent_commission_schedule_profile.payee_comp_grid_level_id,
        // @ts-expect-error
        p.agent_commission_schedule_profile.payer_comp_grid_level_id,
      ])
    ),
  ];

  // WorldChanges needs additional data to calculate agent_payout_rate
  let agencyGridLevels: Prisma.comp_grid_levelsGetPayload<{
    select: {
      id: true;
      name: true;
      comp_grid: true;
      related_comp_grids: true;
    };
  }>[];
  let agentGridLevels: Prisma.comp_grid_levelsGetPayload<{
    select: {
      id: true;
      name: true;
      comp_grid: true;
      related_comp_grids: true;
    };
  }>[];
  if (req?.account_id === AccountIds.WORLD_CHANGERS) {
    const select = {
      id: true,
      name: true,
      comp_grid: true,
      related_comp_grids: true,
    };
    [agencyGridLevels, agentGridLevels] = await Promise.all([
      prismaClient.comp_grid_levels.findMany({
        where: { state: DataStates.ACTIVE, type: 'Agency level' },
        select,
      }),
      prismaClient.comp_grid_levels.findMany({
        where: { state: DataStates.ACTIVE, name: 'Agent' },
        select,
      }),
    ]);
    gridLevelIds.push(
      ...(agencyGridLevels ?? []).map((l) => l.id).filter(Boolean)
    );
    gridLevelIds.push(
      ...(agentGridLevels ?? []).map((l) => l.id).filter(Boolean)
    );
  }

  // @ts-expect-error
  const compGridRates = await fetchCompGridRatesByGridLevelIds(gridLevelIds);
  const gridLevelRatesGroup = compGridRates.reduce((acc, rate) => {
    acc[rate.comp_grid_level_id] = [
      ...(acc[rate.comp_grid_level_id] ?? []),
      rate,
    ];
    return acc;
  }, {});

  // Add comp grid rates to comp profile sets and profiles
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  compProfileSets.forEach((set) => {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    set?.agent_commission_schedule_profiles_sets?.commission_profiles.forEach(
      (profile) => {
        if (profile.payee_comp_grid_level) {
          // @ts-expect-error
          profile.payee_comp_grid_level.comp_grid_rates =
            // @ts-expect-error
            gridLevelRatesGroup[profile.payee_comp_grid_level_id] ?? [];
        }
        if (profile.payer_comp_grid_level) {
          // @ts-expect-error
          profile.payer_comp_grid_level.comp_grid_rates =
            // @ts-expect-error
            gridLevelRatesGroup[profile.payer_comp_grid_level_id] ?? [];
        }
        // WorldChangers needs this data for agent_payout_rate calculation
        if (req?.account_id === AccountIds.WORLD_CHANGERS) {
          const agencyGridLevel = agencyGridLevels.find(
            (level) =>
              profile.comp_grid_id === level.comp_grid?.id ||
              level.related_comp_grids?.some(
                (grid) => grid.id === profile.comp_grid_id
              )
          );
          // @ts-expect-error
          profile.agency_comp_grid_level = {
            // @ts-expect-error
            comp_grid_rates: gridLevelRatesGroup?.[agencyGridLevel?.id] ?? [],
            agency_comp_grid_level_id: agencyGridLevel?.id ?? null,
          };
          const agentGridLevel = agentGridLevels.find(
            (level) =>
              profile.comp_grid_id === level.comp_grid?.id ||
              level.related_comp_grids?.some(
                (grid) => grid.id === profile.comp_grid_id
              )
          );
          // @ts-expect-error
          profile.agent_comp_grid_level = {
            // @ts-expect-error
            comp_grid_rates: gridLevelRatesGroup?.[agentGridLevel?.id] ?? [],
            agent_comp_grid_level_id: agentGridLevel?.id ?? null,
          };
        }
      }
    );
  });

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  compProfiles.forEach((profile) => {
    // @ts-expect-error
    const { payee_comp_grid_level, payer_comp_grid_level } =
      profile.agent_commission_schedule_profile;

    if (payee_comp_grid_level) {
      payee_comp_grid_level.comp_grid_rates =
        gridLevelRatesGroup[
          // @ts-expect-error
          profile.agent_commission_schedule_profile.payee_comp_grid_level_id
        ] ?? [];
    }

    if (payer_comp_grid_level) {
      payer_comp_grid_level.comp_grid_rates =
        gridLevelRatesGroup[
          // @ts-expect-error
          profile.agent_commission_schedule_profile.payer_comp_grid_level_id
        ] ?? [];
    }

    // WorldChangers needs this data for agent_payout_rate calculation
    if (req?.account_id === AccountIds.WORLD_CHANGERS) {
      const agencyGridLevel = agencyGridLevels.find(
        (level) =>
          // @ts-expect-error
          profile.agent_commission_schedule_profile.comp_grid_id ===
            level.comp_grid?.id ||
          level.related_comp_grids?.some(
            (grid) =>
              // @ts-expect-error
              grid.id === profile.agent_commission_schedule_profile.comp_grid_id
          )
      );
      // @ts-expect-error
      profile.agent_commission_schedule_profile.agency_comp_grid_level = {
        // @ts-expect-error
        comp_grid_rates: gridLevelRatesGroup?.[agencyGridLevel?.id] ?? [],
        agency_comp_grid_level_id: agencyGridLevel?.id ?? null,
      };
      const agentGridLevel = agentGridLevels.find(
        (level) =>
          // @ts-expect-error
          profile.agent_commission_schedule_profile.comp_grid_id ===
            level.comp_grid?.id ||
          level.related_comp_grids?.some(
            (grid) =>
              // @ts-expect-error
              grid.id === profile.agent_commission_schedule_profile.comp_grid_id
          )
      );
      // @ts-expect-error
      profile.agent_commission_schedule_profile.agent_comp_grid_level = {
        // @ts-expect-error
        comp_grid_rates: gridLevelRatesGroup?.[agentGridLevel?.id] ?? [],
        agent_comp_grid_level_id: agentGridLevel?.id ?? null,
      };
    }
  });

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  compProfileSets.forEach((set) => {
    const existingSets =
      cache.get(
        `sets_${set.contact_id}`
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ) ?? ([] as any);
    cache.set(`sets_${set.contact_id}`, [...existingSets, set]);
  });
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  compProfiles.forEach((profile) => {
    const existingProfiles =
      cache.get(
        `profile_${profile.contact_id}`
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ) ?? ([] as any);
    cache.set(`profile_${profile.contact_id}`, [...existingProfiles, profile]);
  });
  const res = {
    compProfileSets: [...compProfileSets, ...cachedSets],
    compProfiles: [...compProfiles, ...cachedProfiles],
  };
  const totalCompProfiles =
    res.compProfileSets.length + res.compProfiles.length;
  const { count, durationSec } =
    timerStats?.end(
      'getCompProfiles',
      // @ts-expect-error
      start,
      totalCompProfiles
    ) || {};
  const totalTimeSec = timerStats?.get('getCompProfiles');
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `🧮 getCompProfiles processed ${totalCompProfiles} profiles in ${durationSec.toFixed(2)}s ` +
      `(total time: ${totalTimeSec?.toFixed(2)}s and total comp profiles: ${count})\n` +
      timerStats?.logString('getCompProfiles')
  );
  return res;
};

export const getContactAncestorsWithCommissionProfiles = async (
  strId: string,
  // @ts-expect-error
  effectiveDate,
  // @ts-expect-error
  req,
  cache?: LRUCacheService,
  timerStats?: TimerStats
) => {
  const start = timerStats?.start();
  const contact = await findContactInclAncestors(
    strId,
    effectiveDate,
    timerStats
  );
  if (!contact) return null;
  // @ts-expect-error
  const ancestorsFlat = [];
  const currentContact = contact;
  ancestorsFlat.push({
    agentName: Formatter.contact(currentContact, {
      account_id: req.account_id,
    }),
    agentId: currentContact.id,
    agentStrId: currentContact.str_id,
    uplineIds: currentContact.parent_relationships
      .map((pRel: { parent?: { id: number } }) => pRel.parent?.id)
      .filter(Boolean),
    // CommissionProfiles: commissionProfilesMap[currentContact.str_id] ?? [],
  });
  let currentContacts = [{ parent: currentContact }];
  while (currentContacts.length > 0) {
    // @ts-expect-error
    const allParents = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    currentContacts.forEach((c) => {
      const parentsRelationships = c.parent?.parent_relationships?.filter(
        // @ts-expect-error
        (parentRelationship) => parentRelationship.parent
      );
      // Reduce over the array of parents to get the total of parents with a specified split
      const specifiedSplits = parentsRelationships.map(
        (pRel: { split_percentage: number }) =>
          numberOrDefault(+pRel.split_percentage, 0)
      );
      const specifiedSplitsTotal = specifiedSplits.reduce(
        (acc: number, cur: number) => acc + cur,
        0
      );
      const defaultSplit =
        (100 - specifiedSplitsTotal) / parentsRelationships.length;
      if (!parentsRelationships || parentsRelationships?.length === 0) return;
      allParents.push(...parentsRelationships);
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      parentsRelationships.forEach(
        (pRel: {
          split_percentage: string;
          parent: {
            id: number;
            str_id: string;
            parent_relationships: { parent: { id: number } }[];
          };
        }) => {
          // @ts-expect-error
          if (ancestorsFlat.find((a) => a.agentId === pRel.parent.id)) return;
          ancestorsFlat.push({
            agentName: Formatter.contact(pRel.parent, {
              account_id: req.account_id,
            }),
            agentId: pRel.parent.id,
            agentStrId: pRel.parent.str_id,
            hierarchySplitPercentage: +pRel.split_percentage || defaultSplit,
            uplineIds: pRel.parent.parent_relationships
              .map((pRel) => pRel.parent?.id)
              .filter(Boolean),
            // CommissionProfiles: commissionProfilesMap[parent.str_id] ?? [],
          });
        }
      );
    });
    // @ts-expect-error
    currentContacts = allParents;
  }

  const { compProfileSets, compProfiles } =
    ancestorsFlat?.length > 0
      ? await getCompProfiles(
          ancestorsFlat.map((a) => a.agentId),
          cache,
          req,
          timerStats
        )
      : { compProfileSets: [], compProfiles: [] };
  for (const ancestor of ancestorsFlat) {
    const _compProfileSets = compProfileSets.filter(
      (cps) =>
        // @ts-expect-error
        cps.contact_id === ancestor.agentId ||
        // @ts-expect-error
        (ancestor.uplineIds.includes(cps.contact_id) &&
          // @ts-expect-error
          cps.hierarchy_processing === 'downlines')
    );

    const uniqueCompProfileSetsByProfileId =
      flatCompProfileSets(_compProfileSets);

    // @ts-expect-error
    ancestor.commissionProfiles = [
      ...compProfiles.filter(
        (cp) =>
          // @ts-expect-error
          cp.contact.str_id === ancestor.agentStrId ||
          // @ts-expect-error
          (ancestor.uplineIds.includes(cp.contact.id) &&
            // @ts-expect-error
            cp.hierarchy_processing === 'downlines')
      ),
      ...uniqueCompProfileSetsByProfileId,
    ];

    // Fetch associated companies for each profile
    // @ts-expect-error
    await fetchProfileCompanies(ancestor?.commissionProfiles);
  }
  const duration = timerStats?.end(
    'getContactAncestorsWithCommissionProfiles',
    // @ts-expect-error
    start
  );
  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  console.info(
    `getContactAncestorsWithCommissionProfiles took ${duration.toFixed(2)}s (total: ${timerStats?.get('getContactAncestorsWithCommissionProfiles').toFixed(2)}s)\n` +
      timerStats?.logString('getContactAncestorsWithCommissionProfiles')
  );

  return ancestorsFlat;
};

// @ts-expect-error
export const flatCompProfileSets = (compProfileSets) => {
  if (!compProfileSets) return [];
  // @ts-expect-error
  const compProfileSetCompProfiles = compProfileSets.map((cps) => {
    const { agent_commission_schedule_profiles_sets, ...rest } = cps;
    return (
      (agent_commission_schedule_profiles_sets?.commission_profiles ?? [])
        // @ts-expect-error
        .map((cp) => ({
          ...rest,
          agent_commission_schedule_profile: cp,
          agent_commission_schedule_profile_id: cp.id,
        }))
    );
  });

  const uniqueCompProfileSetsByProfileId = [
    ...new Map(
      compProfileSetCompProfiles
        .flat()
        // @ts-expect-error
        .map((item) => [item.agent_commission_schedule_profile.id, item])
    ).values(),
  ];
  return uniqueCompProfileSetsByProfileId;
};

// @ts-expect-error
export const sumPolicyCommissionAmounts = (report_datum) => {
  // @ts-expect-error
  return report_datum.statement_data.reduce((acc, cur) => {
    return acc + +cur.commission_amount;
  }, 0);
};
