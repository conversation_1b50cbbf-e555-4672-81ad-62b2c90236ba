import { type ClassConstructor, plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { BadRequestException } from 'next-api-decorators';

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const validateData = async (cls: ClassConstructor<any>, data: any) => {
  const instance = plainToInstance(cls, data);
  const errors = await validate(instance);

  const errList: string[] = [];
  for (let index = 0; index < errors.length; index++) {
    let currentLevel = errors[index];
    // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    while (currentLevel.children && currentLevel.children.length) {
      currentLevel = currentLevel.children[0];
    }
    // @ts-expect-error
    errList.push(...Object.values(currentLevel.constraints));
  }
  if (errors.length) {
    throw new BadRequestException(errList.join(','));
  }
};
