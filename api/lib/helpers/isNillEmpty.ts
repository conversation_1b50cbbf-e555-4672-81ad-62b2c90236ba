import { isNill } from './isNill';

export const isNillEmpty = (value: unknown) => {
  return (
    isNill(value) ||
    (Array.isArray(value) && !value.length) ||
    (value instanceof Set && !value.size) ||
    (value instanceof Map && !value.size) ||
    (!(value instanceof Set) &&
      !(value instanceof Map) &&
      typeof value === 'object' &&
      //@ts-expect-error
      !Object.keys(value).length)
  );
};
