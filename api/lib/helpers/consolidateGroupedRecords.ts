import currency from 'currency.js';
import { isEqual } from 'lodash-es';

/**
 * Consolidates an object of grouped records into an array of records.
 * If a group has more than one record, it merges them into a single record.
 * If the same key in multiple records has different values, it sets the key's value to '***', except for 'agent_commissions',
 * where it adds up the values.
 *
 * @param {Object} groupedRecords - The object of grouped records to consolidate.
 * @returns {Array} The consolidated array of records.
 */
export const consolidateGroupedRecords = (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  groupedRecords: Record<string, any>
) => {
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Object.values(groupedRecords).forEach((group) => {
    if (group.length > 1) {
      const baseRecord = group[0];
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      group.slice(1).forEach((record: Record<string, any>) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.keys(record).forEach((key) => {
          if (record[key] !== baseRecord[key]) {
            if (key === 'agent_commissions') {
              if (record[key] && baseRecord[key]) {
                // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                Object.keys(record[key]).forEach((subKey) => {
                  if (baseRecord[key][subKey] !== undefined) {
                    baseRecord[key][subKey] = currency(
                      baseRecord[key][subKey]
                    ).add(record[key][subKey]).value;
                  } else {
                    baseRecord[key][subKey] = record[key][subKey];
                  }
                });
              }
              // TODO: Check math, it should be similar to the following, but we may need to
              // configure whether to use premium vs target_premium
              // https://github.com/Fintary/fintary/blob/main/api/lib/prisma.ts#L66
              // } else if (key === 'agent_payout_rate') {
              //   if (record[key] && baseRecord[key]) {
              //     Object.keys(record[key]).forEach((subKey) => {
              //       if (baseRecord.report && baseRecord.report[key])
              //         baseRecord[key] = baseRecord.report[key];
              //       baseRecord[key][subKey] =
              //         baseRecord.report?.commissionable_premium_amount;
              //       if (record.report && record.report[key])
              //         record[key][subKey] =
              //           record.report?.commissionable_premium_amount;
              //     });
              //   }
            } else if (key === 'commission_amount') {
              baseRecord[key] = currency(baseRecord[key]).add(
                record[key]
              ).value;
            } else if (key === 'contacts') {
              if (
                !isEqual([...baseRecord[key]].sort(), [...record[key]].sort())
              ) {
                baseRecord[key] = '***';
              }
            } else if (
              key === 'product_type' ||
              key === 'product_name' ||
              key === 'effective_date' ||
              key === 'premium_amount' ||
              key === 'payment_mode'
            ) {
              if (!baseRecord[key] && baseRecord.report[key])
                baseRecord[key] = baseRecord.report[key];
              if (!record[key] && record.report[key])
                record[key] = record.report[key];
              if (record[key] !== baseRecord[key]) {
                if (
                  record.report[key] !== baseRecord.report[key] &&
                  baseRecord[key] !== '***'
                ) {
                  baseRecord[key] = '***';
                } else {
                  if (key === 'premium_amount' && record.report[key] === null) {
                    baseRecord[key] =
                      // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                      record.report['commissionable_premium_amount'];
                  } else {
                    baseRecord[key] = record.report[key];
                  }
                }
              }
            } else if (
              key === 'payment_date' ||
              key === 'processing_date' ||
              key === 'period_date'
            ) {
              const baseDate = new Date(baseRecord[key]);
              const recordDate = new Date(record[key]);
              // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              if (isNaN(baseDate.getTime())) {
                baseRecord[key] = record[key];
              } else if (recordDate > baseDate) {
                baseRecord[key] = record[key];
              }
            } else if (key === 'customer_name') {
              // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              if (record.report && record.report?.customer_name) {
                baseRecord[key] = record.report.customer_name;
              } else {
                const baseProcessingDate = new Date(baseRecord.processing_date);
                const recordProcessingDate = new Date(record.processing_date);
                // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                if (isNaN(baseProcessingDate.getTime())) {
                  baseRecord[key] = record[key];
                } else if (recordProcessingDate > baseProcessingDate) {
                  baseRecord[key] = record[key];
                }
              }
            } else if (key === 'commission_rate') {
              baseRecord[key] = '';
            } else if (key === 'id') {
              baseRecord[key] = record[key];
            } else if (key === 'report') {
              baseRecord[key] = record[key];
            } else baseRecord[key] = '***';
          }
        });
      });
    }
  });
  const consolidatedData = Object.values(groupedRecords).map(
    (group) => group[0]
  );

  return consolidatedData;
};
