import { DEFAULT_FILTER } from 'common/constants';

export const formatFieldOptions = <T>(data: T[], targetKey: string): T[] => [
  ...new Set(
    data
      .map((item) => {
        // @ts-expect-error
        const val = item[targetKey];
        return val === null
          ? DEFAULT_FILTER.BLANK_OPTION
          : val === ''
            ? DEFAULT_FILTER.BLANK_OPTION
            : val;
      })
      .sort()
  ),
];
