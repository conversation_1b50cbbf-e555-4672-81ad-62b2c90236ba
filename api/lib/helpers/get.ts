export function get<T>(
  obj: Record<string, unknown> | null | undefined,
  path: string | Array<string | number>,
  defaultValue?: T
): T {
  // @ts-expect-error
  if (!obj || [null, undefined].includes(path)) return defaultValue as T;

  const keys: Array<string | number> = Array.isArray(path)
    ? path
    : path.replace(/\[(\d+)\]/g, '.$1').split('.');

  let result: unknown = obj;

  for (const key of keys) {
    if (typeof result !== 'object' || result === null || !(key in result)) {
      return defaultValue as T;
    }
    result = (result as Record<string, unknown>)[key];
  }

  return result as T;
}
