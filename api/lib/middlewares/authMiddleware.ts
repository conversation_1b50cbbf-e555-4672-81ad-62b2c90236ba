import { createMiddlewareDecorator } from 'next-api-decorators';

import type { ExtNextApiRequest, ExtNextApiResponse } from '@/types';
import { requestFilter } from '@/lib/middlewares';

// @ts-expect-error
export const WithAuth = (options) => {
  return createMiddlewareDecorator(
    // @ts-expect-error
    async (req: ExtNextApiRequest, res: ExtNextApiResponse, next) => {
      await requestFilter(req, res, options);
      next();
    }
  );
};
