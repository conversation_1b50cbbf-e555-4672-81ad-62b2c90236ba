import { createMiddlewareDecorator } from 'next-api-decorators';

import type { ExtNextApiRequest, ExtNextApiResponse, Roles } from '@/types';

export const AllowedRoles = (roles: Roles[]) => {
  return createMiddlewareDecorator(
    // @ts-expect-error
    async (req: ExtNextApiRequest, res: ExtNextApiResponse, next) => {
      const rolesList = roles.map((role) => role.toString());
      // @ts-expect-error
      if (!rolesList.includes(req.role_id)) {
        res.status(403).json({ success: false, statusText: 'Forbidden' });
        return;
      }
      next();
    }
  );
};
