import type { BaseQueryParamsModel } from './base-query.interface';

export interface ReconciliationQueryParamsModel extends BaseQueryParamsModel {
  carrier_name?: string | string[];
  writing_carrier_name?: string | string[];
  effective_date_start?: string | string[] | Date;
  effective_date_end?: string | string[] | Date;
  payment_date_first?: string | string[] | Date;
  payment_date_last?: string | string[] | Date;
  incl_dupes?: string;
  reconciled?: string;
}
