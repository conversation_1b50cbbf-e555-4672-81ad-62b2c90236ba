import type { NextApiRequest } from 'next';

import type { AppLoggerService } from '@/services/logger/appLogger';

export interface ExtNextApiRequest extends NextApiRequest {
  uid?: string | null;
  ouid?: string | null;
  account_id?: string | null;
  role_id?: string | null;
  timezone?: string | null;
  query: {
    [key: string]: string;
  };
  page?: number;
  limit?: number;
  logger: AppLoggerService;
  // @ts-expect-error
  fintaryAdmin: AuthResponse['fintaryAdmin'];
}
