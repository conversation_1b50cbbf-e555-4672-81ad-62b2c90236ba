import type { HierarchyDataAccess } from './hierarchy-data-access.interface';

export interface ContactSettings {
  uplineHierarchyAccessLevel: string;
  downlineHierarchyAccessLevel: string;
  directUplineDataAccess: HierarchyDataAccess;
  extendedUplineDataAccess: HierarchyDataAccess;
  directDownlineDataAccess: HierarchyDataAccess;
  extendedDownlineDataAccess: HierarchyDataAccess;
}

export type ChildRelationshipWithContact = { contact?: { id?: number } };
