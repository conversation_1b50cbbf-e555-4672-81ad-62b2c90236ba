import type { NextApiResponse } from 'next';

interface BaseResponse extends NextApiResponse {
  stats?: {
    page?: number;
    limit?: number;
    current_length?: number;
    total_length?: number;
  };
  type?: string;
}

type SuccessResponse = BaseResponse & {
  statusText: 'ok';
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data: any;
};

type ErrorResponse = BaseResponse & {
  statusText: 'error';
  error: string;
};

export type ExtNextApiResponse = SuccessResponse | ErrorResponse;
