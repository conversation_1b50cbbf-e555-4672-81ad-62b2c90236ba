import { injectable } from 'inversify';
import BigNumber from 'bignumber.js';

import { prismaClient } from '@/lib/prisma';
import { DataStates, type ExtAccountInfo } from '@/types';

@injectable()
export class SessionService {
  async check(account: ExtAccountInfo) {
    const authConfig = await prismaClient.account_configs.findFirst({
      where: {
        account_id: account.account_id,
        state: DataStates.ACTIVE,
        type: 'session',
      },
    });
    const user = await prismaClient.users.findFirst({
      where: {
        uid: account.uid,
      },
    });

    if (!authConfig) {
      return { session: null };
    }

    const config = authConfig.value as { session: number };
    if (!config?.session) {
      return { session: null };
    }
    const now = Date.now();
    const lastActivity = user?.last_visited_at.getTime() || now;
    // In minutes
    const sessionLeft =
      (config.session * 60 * 1000 - (now - lastActivity)) / 60 / 1000;

    return {
      session: config.session,
      lastActivity,
      sessionLeft:
        sessionLeft > 0 ? BigNumber(sessionLeft).dp(2).toNumber() : 0,
    };
  }
}
