import { inject, injectable } from 'inversify';
import { document_extract_method, PrismaClient } from '@prisma/client';
import {
  ProcessorStatuses,
  ProcessorSelectorStatuses,
  AccessTypes,
  ProcessorReviewStatuses,
  SortOrder,
} from 'common/globalTypes';

import prisma, { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';

@injectable()
export class ProcessorsService {
  private static readonly PROCESSOR_STATUSES = [
    ProcessorStatuses.NEW,
    ProcessorStatuses.PROCESSED,
  ];
  private static readonly MAX_PROCESSORS = 3;

  constructor(@inject(PrismaClient) private prisma: PrismaClient) {}

  async fetchProcessorsForAccountId({
    accountId,
    userId,
    isAdmin,
    query,
  }: {
    accountId: string;
    userId: string;
    isAdmin: boolean;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    query: Record<string, any>;
  }) {
    const baseWhere = {
      OR: [
        { account_id: accountId, state: DataStates.ACTIVE },
        { access: AccessTypes.GLOBAL, state: DataStates.ACTIVE },
        {
          reviewer_str_id: userId,
          status: ProcessorReviewStatuses.InReview,
          state: DataStates.ACTIVE,
        },
      ],
      state: DataStates.ACTIVE,
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;

    if (query.id) {
      baseWhere.str_id = { in: query.id.split(',') };
    }
    if (query.type) {
      baseWhere.type = { in: query.type.split(',') };
    }
    if (query.company_id) {
      baseWhere.company_id = query.company_id;
    }
    if (query.snapshot) {
      baseWhere.processor_status = { contains: query.snapshot };
    } else {
      baseWhere.processor_status = {
        in: [ProcessorStatuses.NEW, ProcessorStatuses.PROCESSED],
      };
    }
    if (query.status) {
      baseWhere.status = { in: query.status.split(',') };
    }
    if (query.q) {
      baseWhere.OR = [
        {
          companies_processors: {
            some: {
              company: {
                company_name: {
                  contains: query.q,
                  mode: 'insensitive',
                },
              },
            },
          },
        },
        {
          inner_name: {
            contains: query.q,
            mode: 'insensitive',
          },
          state: DataStates.ACTIVE,
        },
        {
          name: {
            contains: query.q,
            mode: 'insensitive',
          },
          state: DataStates.ACTIVE,
        },
      ];
    }

    const restrictedFields = {
      id: true,
      str_id: true,
      name: true,
      type: true,
      inner_name: true,
      method: true,
      status: true,
      access: true,
    };

    const unrestrictedFields = {
      company_id: true,
      created_at: true,
      created_by: true,
      document_str_id: true,
      extractionsid: true,
      file_type: true,
      id: true,
      str_id: true,
      notes: true,
      processor: true,
      processor_status: true,
      reviewed_at: true,
      reviewed_by: true,
      reviewer_id: true,
      reviewer_str_id: true,
      state: true,
      status: true,
      owner: true,
      updated_at: true,
      updated_by: true,
      created_proxied_by: true,
      suggest_for: true,
      extractions: {
        select: {
          created_at: true,
          document_id: true,
          id: true,
          str_id: true,
          method: true,
          method_options: true,
          output_format: true,
          result: true,
          status: true,
          updated_at: true,
        },
      },
      users_processors_created_byTousers: {
        select: { first_name: true, last_name: true, id: true, str_id: true },
      },
      users_processors_updated_byTousers: {
        select: { first_name: true, last_name: true, id: true, str_id: true },
      },
      users_processors_reviewed_byTousers: {
        select: { first_name: true, last_name: true },
      },
      companies_processors: {
        where: {
          state: DataStates.ACTIVE,
        },
        include: {
          company: {
            select: {
              id: true,
              str_id: true,
              company_name: true,
              alias_list: true,
            },
          },
        },
      },
    };

    let processors = await prisma.processors.findMany({
      where: baseWhere,
      select: {
        ...restrictedFields,
        ...(isAdmin ? unrestrictedFields : {}),
      },
      orderBy: { created_at: SortOrder.DESC },
      accountInject: false,
    });

    const processorIds = processors.map((p) => p.str_id).filter(Boolean);

    if (processorIds.length > 0) {
      const counts = await prisma.documents.groupBy({
        by: ['processor'],
        where: {
          processor: { in: processorIds },
          state: DataStates.ACTIVE,
        },
        _count: true,
        accountInject: false,
      });

      processors = processors.map((processor) => ({
        ...processor,
        documents:
          counts.find((c) => c.processor === processor.str_id)?._count || 0,
      }));
    }

    if (query.company_name) {
      const companyNameList = query.company_name.split(',');
      const filteredProcessors = processors.filter((processor) => {
        const companiesData =
          processor.companies_processors?.map((cp) => cp.company) || [];
        return companiesData.some((company) => {
          const itemCompanies = [
            company?.company_name || '',
            ...(company?.alias_list || []),
          ].filter(Boolean);
          return itemCompanies.some((companyName) =>
            companyNameList.includes(companyName)
          );
        });
      });
      return filteredProcessors;
    }

    return processors;
  }

  async getProcessorsByDocument({
    document_id,
    account_id,
    companies,
    method,
  }: {
    document_id: string;
    account_id: string;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    companies: any;
    method: string;
  }) {
    try {
      const documentData = await this.prisma.documents.findUnique({
        where: {
          str_id: document_id,
          account_id,
        },
        select: {
          filename: true,
          profiles: {
            select: {
              str_id: true,
              name: true,
              processors: {
                select: {
                  str_id: true,
                  name: true,
                },
              },
            },
          },
          companies: {
            select: {
              str_id: true,
              canonical_company: {
                select: {
                  str_id: true,
                },
              },
            },
          },
        },
      });

      if (!documentData) return [];

      // Processors attached to document profiles
      const processorIds = new Set(
        (documentData.profiles?.processors || [])
          .map((processor) => processor.str_id)
          .filter(Boolean)
      );

      // Map to store processor import methods
      const processorImportMethods = new Map<string, string>();

      if (documentData.companies?.str_id) {
        // Processors attached to company with their import_status
        const [companyProcessors, canonicalProcessors] = await Promise.all([
          prismaClient.companies_processors.findMany({
            where: {
              company_str_id: documentData.companies.str_id,
              state: DataStates.ACTIVE,
              processor: {
                processor_status: { in: ProcessorsService.PROCESSOR_STATUSES },
                OR: [{ access: 'global' }, { access: 'account', account_id }],
              },
            },
            select: {
              processor_str_id: true,
              import_status: true,
            },
            accountInject: false,
          }),
          documentData.companies.canonical_company?.str_id
            ? prismaClient.companies_processors.findMany({
                where: {
                  company_str_id:
                    documentData.companies.canonical_company.str_id,
                  state: DataStates.ACTIVE,
                  processor: {
                    processor_status: {
                      in: ProcessorsService.PROCESSOR_STATUSES,
                    },
                    OR: [
                      { access: 'global' },
                      { access: 'account', account_id },
                    ],
                  },
                },
                select: {
                  processor_str_id: true,
                  import_status: true,
                },
                accountInject: false,
              })
            : Promise.resolve([]),
        ]);

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        companyProcessors.forEach((p) => {
          if (p.processor_str_id) {
            processorIds.add(p.processor_str_id);
            if (p.import_status) {
              processorImportMethods.set(p.processor_str_id, p.import_status);
            }
          }
        });

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        canonicalProcessors.forEach((p) => {
          if (p.processor_str_id) {
            processorIds.add(p.processor_str_id);
            if (
              p.import_status &&
              !processorImportMethods.has(p.processor_str_id)
            ) {
              processorImportMethods.set(p.processor_str_id, p.import_status);
            }
          }
        });
      }

      let filteredProcessors = await prismaClient.processors.findMany({
        where: {
          processor_status: { in: ProcessorsService.PROCESSOR_STATUSES },
          str_id: { in: Array.from(processorIds) },
          ...(method === ProcessorSelectorStatuses.MULTI
            ? {
                method: {
                  notIn: [ProcessorSelectorStatuses.SPREADSHEET],
                },
              }
            : method === ProcessorSelectorStatuses.SPREADSHEET
              ? { method: ProcessorSelectorStatuses.SPREADSHEET }
              : { method: document_extract_method[method] }),
          OR: [{ access: 'global' }, { access: 'account', account_id }],
        },
        accountInject: false,
        select: {
          str_id: true,
          name: true,
          inner_name: true,
          processor: true,
          method: true,
          import_status: true,
        },
      });

      filteredProcessors = filteredProcessors.map((processor) => {
        const importMethod = processorImportMethods.get(processor.str_id);
        return {
          ...processor,
          import_status: importMethod || null,
        };
      });

      // If less than 3 processors, find processors by name
      if (filteredProcessors.length < ProcessorsService.MAX_PROCESSORS) {
        const additionalProcessors = await this.findProcessorsByName(
          companies,
          method,
          documentData.filename,
          account_id
        );

        const processorsMap = new Map();
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        [...filteredProcessors, ...additionalProcessors].forEach(
          (processor) => {
            if (!processorsMap.has(processor.str_id)) {
              processorsMap.set(processor.str_id, processor);
            }
          }
        );

        filteredProcessors = Array.from(processorsMap.values());
      }

      return filteredProcessors.slice(0, ProcessorsService.MAX_PROCESSORS);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        `Error fetching processors for document ${document_id}:`,
        error instanceof Error ? error.message : String(error)
      );
      throw error;
    }
  }

  private async findProcessorsByName(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    companies: any,
    method: string,
    filename: string,
    account_id: string
  ): Promise<
    {
      str_id: string;
      name: string;
      inner_name: string;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      processor: any;
      method: document_extract_method;
      import_status?: string | null;
    }[]
  > {
    if (!companies || !filename) return [];

    if (!method) {
      method = ProcessorSelectorStatuses.SPREADSHEET;
    }

    const processors = await prismaClient.processors.findMany({
      where: {
        processor_status: { in: ProcessorsService.PROCESSOR_STATUSES },
        ...(method === ProcessorSelectorStatuses.MULTI
          ? {
              method: {
                notIn: [ProcessorSelectorStatuses.SPREADSHEET],
              },
            }
          : method === ProcessorSelectorStatuses.SPREADSHEET
            ? { method: ProcessorSelectorStatuses.SPREADSHEET }
            : { method: document_extract_method[method] }),
        OR: [{ access: 'global' }, { access: 'account', account_id }],
      },
      accountInject: false,
      select: {
        str_id: true,
        inner_name: true,
        name: true,
        processor: true,
        method: true,
        import_status: true,
      },
    });

    if (!processors.length) return [];

    const companyNames = new Set(
      [
        companies.company_name?.toLowerCase() || '',
        ...(companies.alias_list || []).map((name) => name.toLowerCase()),
      ].filter(Boolean)
    );

    const matchedProcessors = processors.filter((processor) =>
      this.isProcessorMatch(processor, Array.from(companyNames), filename)
    );

    if (companies.str_id && matchedProcessors.length > 0) {
      const processorIds = matchedProcessors.map((p) => p.str_id);

      const processorRelations =
        await prismaClient.companies_processors.findMany({
          where: {
            company_str_id: companies.str_id,
            processor_str_id: { in: processorIds },
            state: DataStates.ACTIVE,
          },
          select: {
            processor_str_id: true,
            import_status: true,
          },
          accountInject: false,
        });

      const importMethodMap = new Map();
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      processorRelations.forEach((relation) => {
        if (relation.processor_str_id && relation.import_status) {
          importMethodMap.set(
            relation.processor_str_id,
            relation.import_status
          );
        }
      });

      return matchedProcessors.map((processor) => ({
        ...processor,
        import_status: importMethodMap.get(processor.str_id) || null,
      }));
    }

    return matchedProcessors;
  }

  private isProcessorMatch(
    processor: { inner_name: string; name: string },
    companyNames: string[],
    filename?: string
  ): boolean {
    const companyMatch = this.isCompanyNameMatch(processor, companyNames);
    if (companyMatch) return true;

    if (filename) {
      return this.isFilenameMatch(processor, filename);
    }

    return false;
  }

  private isFilenameMatch(
    processor: { inner_name: string; name: string },
    filename: string
  ): boolean {
    if (!processor.name || !filename) return false;

    const processorName = processor.name.toLowerCase();
    const filenameLower = filename
      .replace(/^\s*\d+\s+/, '')
      .split(/\d/)[0]
      .toLowerCase()
      .trim();

    if (processorName.includes(filenameLower)) return true;

    const abbr = filename
      .split('')
      .filter((char) => char === char.toUpperCase() && char.match(/[A-Z]/))
      .join('')
      .toLowerCase();

    if (abbr.length > 2 && processorName.includes(abbr.toLowerCase()))
      return true;

    return false;
  }

  private isCompanyNameMatch(
    processor: { inner_name: string },
    companyNames: string[]
  ): boolean {
    if (!processor.inner_name || !companyNames?.length) return false;

    const nameParts = processor.inner_name.split('_');
    const companyNameParts = processor.inner_name.endsWith('processor')
      ? nameParts.slice(0, -3)
      : nameParts.slice(0, -2);

    const processorName = companyNameParts.join(' ').toLowerCase();
    if (!processorName) return false;

    return companyNames.some(
      (companyName) =>
        companyName &&
        (companyName.includes(processorName) ||
          processorName.includes(companyName))
    );
  }
}
