import { injectable } from 'inversify';
import { z } from 'zod';
import * as Sentry from '@sentry/nextjs';

import { prismaClient } from '@/lib/prisma';

@injectable()
export class ShareFilter {
  // biome-ignore lint/complexity/noUselessConstructor: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor() {}
  async getAllGroupNames({
    modelName,
    ...rest
  }: {
    accountId: string;
    modelName: 'report_data' | 'statement_data';
  }) {
    try {
      const { accountId } = await z
        .object({ accountId: z.string() })
        .parseAsync(rest)
        .catch(() => {
          throw { code: 400, message: 'Invalid parameters' };
        });
      const model =
        modelName === 'report_data'
          ? prismaClient.report_data
          : prismaClient.statement_data;

      const data = await model.findMany({
        where: {
          account_id: accountId,
          AND: [{ group_name: { not: null } }, { group_name: { not: '' } }],
        },
        distinct: ['group_name'],
        select: {
          group_name: true,
        },
      });

      return data.map((item) => item.group_name);
    } catch (error) {
      if (error.code) throw error;

      Sentry.captureException(error);
    }
    return [];
  }
  async getAllTransactionTypes({
    modelName,
    ...rest
  }: {
    accountId: string;
    modelName: 'report_data' | 'statement_data';
  }) {
    try {
      const { accountId } = await z
        .object({ accountId: z.string() })
        .parseAsync(rest)
        .catch(() => {
          throw { code: 400, message: 'Invalid parameters' };
        });
      const model =
        modelName === 'report_data'
          ? prismaClient.report_data
          : prismaClient.statement_data;

      const data = await model.findMany({
        where: {
          account_id: accountId,
          AND: [
            { transaction_type: { not: null } },
            { transaction_type: { not: '' } },
          ],
        },
        distinct: ['transaction_type'],
        select: {
          transaction_type: true,
        },
      });

      return data.map((item) => item.transaction_type);
    } catch (error) {
      if (error.code) throw error;
      Sentry.captureException(error);
    }
    return [];
  }
}
