import { inject, injectable } from 'inversify';

import {
  isReceivableCommissionScheduleSet,
  type CommissionSchedule,
} from './types';
import type CommissionScheduleRecord from '@/persistence/commission-schedules/CommissionScheduleRecord';
import type ICommissionScheduleRepo from '@/persistence/commission-schedules/ICommissionScheduleRepo';
import { REPOSITORY_TYPES } from '@/constants';
import type { statement_data } from '@prisma/client';

export interface FindSchedulesByAccountInput {
  account_id: string;
}

export interface FindSchedulesByAccountOutput {
  commission_schedules: CommissionSchedule[];
}

export interface FindMatchingScheduleInput {
  statementData: statement_data;
  accountId: string;
}

@injectable()
export default class CommissionSchedulesService {
  @inject(REPOSITORY_TYPES.CommissionScheduleRepository)
  private readonly repo: ICommissionScheduleRepo;

  private mapCommissionScheduleRecord(
    record: CommissionScheduleRecord
  ): CommissionSchedule {
    const {
      str_id,
      account_id,
      uid,
      name,
      agent_grid_level,
      commission_schedule,
      carrier_company_id,
      paying_entity_company_id,
      delay,
      start_date,
      end_date,
      issue_age_start,
      issue_age_end,
      premium_min,
      premium_max,
      notes,
      product_type,
      product_name,
    } = record;

    if (!isReceivableCommissionScheduleSet(commission_schedule)) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Invalid commission schedule format.`, commission_schedule);
      throw new Error(`Invalid commission schedule format.`);
    }

    const result: CommissionSchedule = {
      str_id,
      account_id,
      uid,
      name,
      agent_grid_level,
      commission_schedule,
      carrier_company_id,
      paying_entity_company_id,
      delay,
      start_date,
      end_date,
      issue_age_start,
      issue_age_end,
      premium_min: premium_min?.toNumber(),
      premium_max: premium_max?.toNumber(),
      notes,
      product_type,
      product_name,
    };

    return result;
  }

  async findSchedulesByAccount(
    input: FindSchedulesByAccountInput
  ): Promise<FindSchedulesByAccountOutput> {
    const { account_id } = input;

    const str_ids = await this.repo.findByAccount(account_id);
    if (str_ids.length === 0) {
      return { commission_schedules: [] };
    }

    const records = await this.repo.getByStrId(str_ids);

    const commission_schedules = records.map((record) =>
      this.mapCommissionScheduleRecord(record)
    );

    return { commission_schedules };
  }
}
