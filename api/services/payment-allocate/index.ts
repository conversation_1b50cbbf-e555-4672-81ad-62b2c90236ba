import {
  Prisma,
  type report_data,
  type statement_data,
  virtual_type,
  type PrismaClient,
  type data_processing,
  data_processing_type,
} from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { inject, injectable } from 'inversify';

import { prismaTransaction<PERSON>andler } from '@/lib/prismaUtils';
import { prismaClient } from '@/lib/prisma';
import {
  DataProcessingStatuses,
  DataStates,
  type ExtAccountInfo,
  Tables,
} from '@/types';
import { AppLoggerService } from '@/services/logger/appLogger';
import { DataProcessService } from '@/services/data_processing';
import { limitConcurrency } from '@/lib/helpers';
import dayjs from '@/lib/dayjs';
import { GroupDedupeService } from '@/services/data_processing/group-dedupe';

type StatementWithDetails = Prisma.statement_dataGetPayload<{
  include: {
    details: true;
  };
}>;

@injectable()
export class PaymentAllocateService {
  logger = new AppLoggerService({
    defaultMeta: {
      service: 'PaymentAllocateService',
    },
  });

  @inject(DataProcessService) private dataProcessingService: DataProcessService;
  @inject(GroupDedupeService) private groupDedupeService: GroupDedupeService;

  async fullFillGroupedCommission(
    data: {
      statementId: number;
      targetAmount: Decimal;
      accountId: string;
    },
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    const { statementId, targetAmount, accountId } = data;

    const {
      _sum: { commission_amount: groupedAmount },
    } = await prisma.statement_data.aggregate({
      _sum: {
        commission_amount: true,
      },
      where: {
        parent_id: statementId,
        state: DataStates.GROUPED,
        account_id: accountId,
      },
    });

    const isFulfilled =
      // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      groupedAmount !== null && groupedAmount.eq(targetAmount);

    const updateData: Prisma.statement_dataUpdateInput = {
      premium_amount: targetAmount,
      commission_amount: groupedAmount,
      allocated_amount: groupedAmount,
    };

    if (isFulfilled) {
      updateData.commission_rate = '100';
      updateData.new_commission_rate = new Prisma.Decimal(1);
      updateData.commission_rate_percent = new Prisma.Decimal(100);
    }

    return await prisma.statement_data.update({
      where: { id: statementId },
      data: updateData,
    });
  }
  async getMatchedPolicy(
    statement: statement_data,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    let policy = null;
    if (statement.report_data_id) {
      policy = await prisma.report_data.findUnique({
        where: { id: statement.report_data_id },
      });
    } else if (statement.policy_id) {
      policy = await prisma.report_data.findFirst({
        where: {
          policy_id: statement.policy_id,
          state: DataStates.ACTIVE,
          account_id: statement.account_id,
        },
      });
    }
    if (!policy) {
      this.logger.warn(`Policy not found for statement ${statement.id}`, {
        statementId: statement.id,
      });
      return null;
    }

    if (!policy.premium_amount) {
      this.logger.warn(`Premium amount not found for policy ${policy.id}`, {
        policyId: policy.id,
      });
      return null;
    }
    return policy;
  }
  /**
   * Generate an array of period_date (YYYY-MM-01) for each term based on policy.effective_date and terms.
   * @param effectiveDate - The policy effective date (string, YYYY-MM-DD)
   * @param terms - Number of periods (months)
   * @returns Array of period_date strings (YYYY-MM-DD)
   */
  generatePeriodDates(effectiveDate: Date, terms: number): string[] {
    const dates: string[] = [];
    let date = dayjs(effectiveDate);
    for (let i = 0; i < terms; i++) {
      dates.push(date.format('YYYY-MM-01'));
      date = date.add(1, 'month');
    }
    return dates;
  }

  /**
   * Find missing period dates for a policy up to the latest statement's period_date.
   * @param policy - The policy object (must have effective_date and terms)
   * @param statements - Array of statement_data (must have period_date)
   * @returns Array of missing period_date strings (YYYY-MM-01)
   */
  findMissingPeriodDates(
    policy: { effective_date: Date; policy_term_months: number },
    statements: Array<{ period_date: Date }>
  ): string[] {
    const allPeriodDates = this.generatePeriodDates(
      policy.effective_date,
      policy.policy_term_months
    );

    // Find the latest statement's period_date
    const latestStatementDate =
      statements.length > 0
        ? statements.reduce((latest, statement) => {
            const statementDate = dayjs(statement.period_date);
            return statementDate.isAfter(latest) ? statementDate : latest;
          }, dayjs(statements[0].period_date))
        : dayjs(); // Fallback to current date if no statements

    const filteredPeriodDates = allPeriodDates.filter((date) =>
      dayjs(date).isSameOrBefore(latestStatementDate)
    );
    const statementDates = new Set(
      statements.map((s) => dayjs(s.period_date).format('YYYY-MM-01'))
    );
    return filteredPeriodDates.filter((date) => !statementDates.has(date));
  }

  async getAllocatableStatements(
    statement: Prisma.statement_dataGetPayload<{
      include: { children_data: true };
    }>,
    policy: report_data,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    const where: Prisma.statement_dataWhereInput = {
      policy_id: statement.policy_id,
      account_id: statement.account_id,
      is_virtual: false,
      AND: [
        {
          OR: [
            { allocated_amount: null },
            { commission_amount: null },
            {
              NOT: {
                allocated_amount: {
                  equals: prismaClient.statement_data.fields.commission_amount,
                },
              },
            },
          ],
        },
        { OR: [{ remain_amount: { not: 0 } }, { remain_amount: null }] },
        {
          OR: [
            { state: DataStates.ALLOCATED },
            {
              state: DataStates.ACTIVE,
            },
            {
              state: DataStates.GROUPED,
            },
          ],
        },
      ],
    };

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const rule = (policy.config as any)?.allied_payment_rule;

    if (rule?.mode === 'dental_only') {
      where.product_type = 'Dental';
    } else if (rule?.mode === 'vision_only') {
      where.product_type = 'Vision';
    } else if (rule?.mode === 'dental_vision') {
      where.product_type = { in: ['Dental', 'Vision'] };
    }

    const candidates = await prisma.statement_data.findMany({
      where,
      include: {
        details: { where: { state: { not: DataStates.DELETED } } },
      },
      orderBy: {
        period_date: 'asc',
      },
    });

    if (rule?.mode === 'dental_vision' && rule.priority?.length > 0) {
      candidates.sort((a, b) => {
        const aIndex = rule.priority.indexOf(a.product_type);
        const bIndex = rule.priority.indexOf(b.product_type);
        if (aIndex === -1) return 1;
        if (bIndex === -1) return -1;
        return aIndex - bIndex;
      });
    }
    return candidates;
  }

  async reset(accountId: string) {
    const allocatedStatements = await prismaClient.statement_data.findMany({
      where: {
        account_id: accountId,
        state: DataStates.ALLOCATED,
      },
      include: {
        details: {
          where: {
            virtual_type: virtual_type.partial_payment,
            is_virtual: true,
            state: { in: [DataStates.ACTIVE, DataStates.GROUPED] },
          },
        },
      },
    });

    for (const statement of allocatedStatements) {
      if (statement.details.length > 0) {
        // Delete all history, so that the partial payments can be deleted
        await prismaClient.history.deleteMany({
          where: {
            account_id: accountId,
            statement_data_id: statement.id,
          },
        });

        // Delete all partial payments
        await prismaClient.statement_data.deleteMany({
          where: {
            id: {
              in: statement.details.map((d) => d.id),
            },
          },
        });
      }

      // Reset the master statement to active
      await prismaClient.statement_data.update({
        where: {
          id: statement.id,
        },
        data: {
          state: DataStates.ACTIVE,
          allocated_amount: null,
          remain_amount: null,
        },
      });
    }

    await prismaClient.statement_data.updateMany({
      where: {
        account_id: accountId,
        state: DataStates.GROUPED,
      },
      data: {
        allocated_amount: null,
        remain_amount: null,
      },
    });

    await prismaClient.statement_data.updateMany({
      where: {
        account_id: accountId,
        is_virtual: true,
        virtual_type: virtual_type.grouped,
      },
      data: {
        allocated_amount: null,
        remain_amount: null,
        premium_amount: null,
        commission_rate: null,
        commission_rate_percent: null,
        new_commission_rate: null,
        report_data_id: null,
      },
    });
    await this.groupDedupeService.deleteOrphanVirtualRecords(
      Tables.COMMISSION_DATA,
      accountId
    );
  }

  async getPendingStatements(
    accountId: string,
    policy: Pick<
      report_data,
      'effective_date' | 'policy_term_months' | 'policy_id' | 'product_type'
    >
  ) {
    if (!policy.effective_date || !policy.policy_term_months) {
      this.logger.warn(
        `Policy ${policy.policy_id} has no effective date or terms, skipping grouped statements retrieval`
      );
      return [];
    }
    const where: Prisma.statement_dataWhereInput = {
      state: DataStates.ACTIVE,
      policy_id: policy.policy_id,
      OR: [
        {
          NOT: [
            {
              premium_amount: {
                equals: prismaClient.statement_data.fields.commission_amount,
              },
            },
          ],
        },
        { commission_amount: null },
        { premium_amount: null },
      ],
      account_id: accountId,
      is_virtual: true,
      virtual_type: virtual_type.grouped,
      product_type: policy.product_type,
      AND: [
        {
          period_date: {
            gte: dayjs(policy.effective_date).toDate(),
          },
        },
        {
          period_date: {
            lte: dayjs(policy.effective_date)
              .add(policy.policy_term_months, 'month')
              .endOf('month')
              .toDate(),
          },
        },
      ],
    };

    return await prismaClient.statement_data.findMany({
      where,
      orderBy: {
        period_date: 'asc',
      },
      include: {
        children_data: {
          where: {
            state: DataStates.GROUPED,
          },
          include: {
            details: { where: { state: { not: DataStates.DELETED } } },
          },
        },
        details: {
          where: { virtual_type: virtual_type.grouped },
        },
      },
    });
  }

  async processPolicyStatementAllocation(
    account: ExtAccountInfo,
    matchedPolicy: report_data,
    warnings: string[] = [],
    failedTasks: number[] = []
  ) {
    const onFail = (context: {
      error;
      data: Prisma.statement_dataGetPayload<{
        include: {
          children_data: { include: { details: true } };
          details: true;
        };
      }>;
    }) => {
      this.logger.error(
        `Failed to process statement ${context.data.id}`,
        context.error
      );
      failedTasks.push(context.data.id);
    };
    // Need to refetch to get the latest statement data
    const pendingStatements = await this.getPendingStatements(
      account.account_id,
      matchedPolicy
    );
    await limitConcurrency<
      Prisma.statement_dataGetPayload<{
        include: {
          children_data: { include: { details: true } };
          details: true;
        };
      }>
    >(
      async (
        statement: Prisma.statement_dataGetPayload<{
          include: {
            children_data: {
              include: {
                details: true;
              };
            };
            details: true;
          };
        }>
      ) => {
        await prismaTransactionHandler(prismaClient, async (client) => {
          const policy = matchedPolicy;

          if (!policy) {
            warnings.push(
              `Policy not found for statement ${statement.id}, skipping`
            );
            return null;
          }
          if (policy.premium_amount === null) {
            warnings.push(
              `Premium amount not set for policy ${policy.id}, skipping allocation for statement ${statement.id}`
            );
            return null;
          }

          const targetAmount = policy.premium_amount?.div(12).toDP(2);

          const candidates = await this.getAllocatableStatements(
            statement,
            policy,
            client
          );

          this.logger.info(`Processing statement ${statement.id} allocation`, {
            statementId: statement.id,
            policyId: statement.policy_id,
            targetAmount: targetAmount.toString(),
            childrenCount: statement.children_data.length,
            candidatesCount: candidates.length,
          });

          const { allocations, remainStatements } =
            await this.calculateAllocations(candidates, targetAmount, client);

          const candidateIds = new Set<number>(candidates.map((c) => c.id));

          await this.processAllocations(
            allocations,
            remainStatements,
            candidateIds,
            statement,
            client
          );

          await client.statement_data.update({
            where: { id: statement.id },
            data: {
              report_data_id: policy.id,
            },
          });
          await this.fullFillGroupedCommission(
            {
              statementId: statement.id,
              targetAmount,
              accountId: statement.account_id,
            },
            client
          );
        });
      },
      pendingStatements,
      1,
      { retries: 0, onFail }
    );
  }

  async getPolicies(accountId: string) {
    const statements = await prismaClient.statement_data.findMany({
      where: { account_id: accountId },
      select: { policy_id: true },
    });

    const policyIds = [
      ...new Set(statements.map((s) => s.policy_id).filter(Boolean)),
    ];

    if (policyIds.length === 0) {
      return [];
    }

    const policies = await prismaClient.report_data.findMany({
      where: {
        account_id: accountId,
        policy_id: { in: policyIds },
        policy_term_months: { not: null },
        effective_date: { not: null },
      },
      orderBy: [
        {
          policy_id: 'asc',
        },
        {
          effective_date: 'asc',
        },
      ],
    });

    // Group policies by policy_id
    const groupedPolicies: Record<string, typeof policies> = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    policies.forEach((policy) => {
      const policyId = policy.policy_id;
      if (policyId) {
        if (!groupedPolicies[policyId]) {
          groupedPolicies[policyId] = [];
        }
        groupedPolicies[policyId].push(policy);
      }
    });

    // Sort policies within each group according to allied_payment_rule
    const sortedGroups = Object.entries(groupedPolicies).map(
      ([policyId, policiesInGroup]) => {
        // Get the allied_payment_rule from the first policy in the group (assuming all policies in the same group have the same rule)
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const rule = (policiesInGroup[0]?.config as any)?.allied_payment_rule;

        if (rule?.mode === 'dental_vision' && rule?.priority?.length > 0) {
          // Sort by priority order specified in the rule
          policiesInGroup.sort((a, b) => {
            const aIndex = rule.priority.indexOf(a.product_type);
            const bIndex = rule.priority.indexOf(b.product_type);
            // If product_type not found in priority, put it at the end
            if (aIndex === -1) return 1;
            if (bIndex === -1) return -1;
            return aIndex - bIndex;
          });
        } else {
          // Default sorting by effective_date if no specific rule
          policiesInGroup.sort((a, b) => {
            if (!a.effective_date || !b.effective_date) return 0;
            return a.effective_date.getTime() - b.effective_date.getTime();
          });
        }

        return [policyId, policiesInGroup] as const;
      }
    );

    // Flatten the sorted groups back to a single array
    const result = sortedGroups.flatMap(
      ([_policyId, policiesInGroup]) => policiesInGroup
    );

    return result;
  }

  async createMissedPeriodStatements(
    policy: report_data,
    missedPeriods: Date[],
    allocatableStatements: statement_data[]
  ) {
    return await prismaClient.statement_data.createManyAndReturn({
      data: missedPeriods.map((period) => ({
        ...this.cloneStatement(allocatableStatements[0]),
        period_date: period,
        state: DataStates.ACTIVE,
        is_virtual: true,
        premium_amount: policy.premium_amount?.div(12).toDP(2),
        commission_amount: 0,
        allocated_amount: 0,
        remain_amount: 0,
        policy_id: policy.policy_id,
        account_id: policy.account_id,
        virtual_type: virtual_type.grouped,
      })),
    });
  }

  async getGroupedStatements(
    policy: report_data,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    if (!policy.effective_date || !policy.policy_term_months) {
      this.logger.warn(
        `Policy ${policy.policy_id} has no effective date or terms, skipping grouped statements retrieval`
      );
      return [];
    }
    return await prisma.statement_data.findMany({
      where: {
        account_id: policy.account_id,
        policy_id: policy.policy_id,
        is_virtual: true,
        virtual_type: virtual_type.grouped,
        state: DataStates.ACTIVE,
        product_type: policy.product_type,
        period_date: {
          gte: dayjs(policy.effective_date).toDate(),
          lte: dayjs(policy.effective_date)
            .add(policy.policy_term_months, 'month')
            .toDate(),
        },
      },
    });
  }
  async processWaterfallAllocation(account: ExtAccountInfo) {
    let dataProcessing: data_processing | null = null;
    const startTime = Date.now();
    const failedTasks: number[] = [];
    const warnings: string[] = [];
    try {
      dataProcessing = await this.dataProcessingService.create({
        account: { connect: { str_id: account.account_id } },
        type: data_processing_type.payment_allocation,
        status: DataProcessingStatuses.PROCESSING,
      });

      const policies = await this.getPolicies(account.account_id);

      if (policies.length === 0) {
        this.logger.warn(
          `No policies found for account ${account.account_id}, skipping allocation`
        );
      }
      for (const policy of policies) {
        this.logger.info(
          `Processing payment allocation for policy ${policy.policy_id}, id: ${policy.id}`
        );
        const groupedStatements = await this.getGroupedStatements(policy);
        const missed = this.findMissingPeriodDates(policy, groupedStatements);

        // Only create missed periods if there are allocatable statements for this policy
        if (missed.length > 0) {
          // Check if there are any allocatable statements for this policy
          const dummyStatement = {
            policy_id: policy.policy_id,
            account_id: policy.account_id,
            children_data: [],
          };

          const allocatableStatements = await this.getAllocatableStatements(
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            dummyStatement as any,
            policy,
            prismaClient
          );

          if (allocatableStatements.length > 0) {
            this.logger.info(
              `Creating ${missed.length} missed period statements for policy ${policy.policy_id}`,
              { policyId: policy.policy_id, missedPeriods: missed.length }
            );

            await this.createMissedPeriodStatements(
              policy,
              missed.map((m) => new Date(m)),
              allocatableStatements
            );
          } else {
            this.logger.info(
              `No allocatable statements found for policy ${policy.policy_id}, skipping creation of ${missed.length} missed periods`,
              { policyId: policy.policy_id, missedPeriods: missed.length }
            );
          }
        }

        await this.processPolicyStatementAllocation(
          account,
          policy,
          warnings,
          failedTasks
        );
      }
      await this.groupDedupeService.deleteOrphanVirtualRecords(
        Tables.COMMISSION_DATA,
        account.account_id
      );

      const endTime = Date.now();
      const hasErrors = failedTasks.length > 0;

      await this.dataProcessingService.updateTaskStatus({
        str_id: dataProcessing.str_id,
        status: hasErrors
          ? DataProcessingStatuses.ERROR
          : DataProcessingStatuses.COMPLETED,
        duration: endTime - startTime,
        stats: {
          warnings,
          ...(hasErrors ? { failed: failedTasks } : {}),
        },
      });
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error during waterfall allocation for account ${account.account_id}`,
        error
      );
      const endTime = Date.now();
      if (dataProcessing) {
        await this.dataProcessingService.updateTaskStatus({
          str_id: dataProcessing.str_id,
          status: DataProcessingStatuses.ERROR,
          duration: endTime - startTime,
          stats: {
            warnings,
            failed: failedTasks,
            error: error.message,
          },
        });
      }
      throw error;
    }
  }

  async processRemainStatements(
    remainStatements: StatementWithDetails[],
    client: PrismaClient | Prisma.TransactionClient
  ) {
    for (const statement of remainStatements) {
      await client.statement_data.updateMany({
        where: {
          id: statement.id,
        },
        data: {
          state: DataStates.ALLOCATED,
          parent_id: null,
          allocated_amount: new Prisma.Decimal(0),
          remain_amount: statement.commission_amount,
        },
      });
    }
  }
  async fullyConsumeStatement(
    statement: StatementWithDetails,
    parentStatement: StatementWithDetails,
    client: PrismaClient | Prisma.TransactionClient
  ) {
    await client.statement_data.update({
      where: { id: statement.id },
      data: {
        state: DataStates.GROUPED,
        parent_id: parentStatement.id,
        allocated_amount: statement.commission_amount,
        remain_amount: 0,
      },
    });
  }
  async processAllocations(
    allocations: Array<{ statement: StatementWithDetails; amount: Decimal }>,
    remainStatements: StatementWithDetails[],
    candidateIds: Set<number>,
    parentStatement: StatementWithDetails,
    client: PrismaClient | Prisma.TransactionClient
  ) {
    for (const { statement: stmt, amount } of allocations) {
      const isCandidate = candidateIds.has(stmt.id);
      const originalRemain = stmt.remain_amount ?? stmt.commission_amount;
      const consumedFully = amount?.eq(originalRemain);

      if (isCandidate || !consumedFully) {
        await this.generatePartialPayments(
          stmt,
          parentStatement,
          amount,
          client
        );
      } else {
        await this.fullyConsumeStatement(stmt, parentStatement, client);
      }
    }
    await this.processRemainStatements(remainStatements, client);
  }

  async calculateAllocations(
    statements: StatementWithDetails[],
    targetAmount: Decimal,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ): Promise<{
    allocations: Array<{ statement: StatementWithDetails; amount: Decimal }>;
    isFulfilled: boolean;
    remainStatements: StatementWithDetails[];
  }> {
    const allocations: Array<{
      statement: StatementWithDetails;
      amount: Decimal;
    }> = [];
    const remainStatements: StatementWithDetails[] = [];
    let cumulativeAmount = new Decimal(0);

    for (const currentStatement of statements) {
      if (cumulativeAmount.gte(targetAmount)) {
        remainStatements.push(
          ...statements.slice(statements.indexOf(currentStatement))
        );
        break;
      }

      // Get the latest remaining amount to avoid stale data issues
      const remainAmount = await this.getStatementRemainingAmount(
        currentStatement,
        prisma
      );
      if (remainAmount.lte(0)) {
        continue;
      }

      const needed = targetAmount.sub(cumulativeAmount);
      const amountToAllocate = Decimal.min(remainAmount, needed);

      allocations.push({
        statement: currentStatement,
        amount: amountToAllocate,
      });
      cumulativeAmount = cumulativeAmount.add(amountToAllocate);
    }

    return {
      allocations,
      isFulfilled: cumulativeAmount.gte(targetAmount),
      remainStatements,
    };
  }

  groupedTotalAmount(
    statement: Prisma.statement_dataGetPayload<{
      include: {
        children_data: true;
      };
    }>
  ) {
    const amountAllocated = statement.children_data.reduce(
      (acc, r) => acc.add(r.commission_amount ?? new Prisma.Decimal(0)),
      new Prisma.Decimal(0)
    );
    return amountAllocated;
  }

  cloneStatement(statement: statement_data) {
    const excludeFields = [
      'agent_commissions_status',
      'agent_commissions_status2',
      'agent_commissions_v2',
      'agent_commission_payout_rate',
      'agent_payout_rate',
      'allocated_amount',
      'children_data',
      'commission_rate',
      'commission_rate_percent',
      'master_id',
      'new_commission_rate',
      'contacts',
      'created_at',
      'created_by',
      'created_proxied_by',
      'details',
      'flags',
      'id',
      'import_id',
      'is_virtual',
      'reconciliation_method',
      'reconciliation_stats',
      'reconciliation_status',
      'remain_amount',
      'report_data_id',
      'virtual_type',
      'processing_status',
      'split_percentage',
      'state',
      'str_id',
      'sync_id',
      'updated_at',
      'updated_by',
    ];
    const copy = { ...statement };
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    excludeFields.forEach((field) => {
      delete copy[field];
    });
    return copy;
  }

  async generatePartialPayments(
    statement: Prisma.statement_dataGetPayload<{
      include: {
        details: true;
      };
    }>,
    parentStatement: StatementWithDetails,
    commissionAmount: Decimal,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    const remainingAmount = await this.getStatementRemainingAmount(
      statement,
      prisma
    );
    // Check if there's remaining amount to allocate
    if (remainingAmount.gte(commissionAmount)) {
      // Create a new virtual statement with the remaining amount
      const virtualStatement = this.cloneStatement(statement);
      virtualStatement.commission_amount = commissionAmount;
      return await prismaTransactionHandler(
        prisma,
        async (prisma: PrismaClient | Prisma.TransactionClient) => {
          const allocatedAmount =
            statement.allocated_amount?.add(commissionAmount) ??
            commissionAmount;
          await prisma.statement_data.update({
            where: { id: statement.id },
            data: {
              state: DataStates.ALLOCATED,
              allocated_amount: allocatedAmount,
              remain_amount: remainingAmount.sub(commissionAmount),
              parent_id: null,
            },
          });

          return await prisma.statement_data.create({
            data: {
              ...virtualStatement,
              commission_amount: commissionAmount,
              remain_amount: new Prisma.Decimal(0),
              allocated_amount: commissionAmount,
              state: DataStates.GROUPED,
              virtual_type: virtual_type.partial_payment,
              is_virtual: true,
              period_date: parentStatement.period_date,
              parent_id: parentStatement.id,
              master_id: statement.id,
            },
          });
        }
      );
    }
    return null;
  }

  async getStatementRemainingAmount(
    statement: statement_data,
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    // Need to refetch the statement to get the latest state
    const latest = await prisma.statement_data.findFirst({
      where: {
        id: statement.id,
        state: { not: DataStates.DELETED },
        account_id: statement.account_id,
      },
      include: {
        details: true,
      },
    });

    if (!latest) {
      return new Prisma.Decimal(0);
    }

    // If remain_amount is already set, use it directly
    if (latest.remain_amount !== null) {
      return latest.remain_amount;
    }

    // Otherwise, calculate based on partial payments
    const amountAllocated = latest.details
      .filter((r) => r.virtual_type === virtual_type.partial_payment)
      .reduce(
        (acc, detail) => acc.add(detail.commission_amount),
        new Prisma.Decimal(0)
      );
    return latest.commission_amount.sub(amountAllocated);
  }
}
