import { AccountIds } from 'common/constants';
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { appendFileSync } from 'fs';

import { container } from '@/ioc';
import { limitConcurrency } from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import dayjs from '@/lib/dayjs';
import {
  AcaSafeHarborType,
  type AuditLog,
  AutoActivityLogCreationOptionType,
  BillingCarrierTypeEnums,
  BillingType,
  CommissionPeriodType,
  FundingType,
  PolicyOriginationReason,
  PremiumPaymentFrequency,
  type ProductDetail,
} from '@/services/benefitPoint/interface';
import { AppLoggerService } from '@/services/logger/appLogger';
import { BenefitPointWorker } from '@/services/queue/worker/benefitPoint';

const tz = 'America/Los_Angeles';

export class MigrationService {
  private logs: {
    [productID: number]: {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      logs?: { productId: number; field: string; from: any; to: any }[];
    };
  } = {};
  private logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: 'MigrationService' },
  });
  async populateWorkerNameToEntities() {
    const configs = await prismaClient.account_configs.findMany({
      where: {
        type: 'dataSync',
        state: 'active',
      },
    });

    const entityTablesMap = {
      agents: ['contacts'],
      carriersAndProducts: ['companies', 'company_products'],
      policies: ['report_data'],
      agentHierarchy: ['contact_hierarchy'],
      agentLevel: ['contact_levels'],
      // PolicySplits
    };
    for (const config of configs) {
      const configItem = config.value as {
        worker: string;
        entities: string[];
      };
      for (const entity of configItem.entities) {
        const tables = entityTablesMap[entity];
        if (!tables) continue;
        for (const table of tables) {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          await (prismaClient[table] as any).updateMany({
            where: {
              account_id: config.account_id,
              sync_id: { not: null },
            },
            data: { sync_worker: configItem.worker },
          });
          this.logger.info(`Updated ${table} for ${entity}`, {
            account_id: config.account_id,
          });
        }
      }
    }
  }
  applyTimezoneOffset(datetime: Date) {
    return datetime ? dayjs.tz(datetime.toISOString(), tz).format() : null;
  }

  appendLogToFile(productId) {
    const logFilePath = 'restore.log';
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    this.logs[productId].logs.forEach((log) => {
      const { productId, field, from, to } = log;
      this.logger.info(
        `Restoring ${field} for product ${productId} from ${from} to ${to}`
      );
      // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      appendFileSync(logFilePath, JSON.stringify(log) + '\n');
    });
  }

  onFail(data) {
    this.logger.error(`Migration failed for ${data.data[0].productID}`, {
      data: data.data[0],
    });
    // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    appendFileSync('restore-failed.log', data.data[0].productID + '\n');
  }
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  addLog(log: { productId: number; field: string; from: any; to: any }) {
    const { productId } = log;
    this.logs[productId] = this.logs[productId] || {
      logs: [],
    };
    this.logs[productId].logs.push(log);
  }

  async restoreBenefitPointData() {
    const reportDataWithMemberCount = await prismaClient.$queryRawUnsafe<
      Array<{ id: string }>
    >(
      `SELECT id, policy_id, sync_id FROM report_data WHERE account_id = $1 AND config ? 'member_count'`,
      AccountIds.RISK_TAG
    );

    const service = container.get<BenefitPointWorker>(BenefitPointWorker);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    await service.setup({ account_id: AccountIds.RISK_TAG } as any);

    // Get product details & corresponding audit logs
    const items = await limitConcurrency(
      async (report) => {
        const details = await service.benefitPointService.getProductDetail(
          +report.sync_id
        );
        let logs = await service.benefitPointService.auditLog(+report.sync_id);
        const previousLogs = logs.filter(
          (r) => r.userName !== '<EMAIL>'
        );
        logs = logs
          .filter((r) => r.userName === '<EMAIL>')
          .filter((r) => {
            return dayjs(r.date).isBefore(dayjs('2025-07-09'));
          });
        return logs.length ? [details, logs, previousLogs] : null;
      },
      reportDataWithMemberCount,
      100
    );

    // Restore data
    await limitConcurrency(
      async (r) => {
        const logs = r[1];
        const previousLogs = r[2];
        const productDetail = r[0];
        let item = { ...productDetail };
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        logs.forEach((log) => {
          item = this.generateFromEntry(
            item as ProductDetail,
            log,
            previousLogs
          );
        });
        await service.benefitPointService.updateProductDetail(item);
        this.appendLogToFile(item.productID);
        return item;
      },
      items.filter(Boolean),
      100,
      { retries: 0, onFail: this.onFail.bind(this) }
    );
  }

  generateEstimatedCommissionFee(data: ProductDetail, comment: string) {
    // "Estimated Commission/Fee changed from '25.00' to 'None Selected'"
    const match = comment.match(
      /Estimated Commission\/Fee changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const [oldFee, _newFee] = match.slice(1);
    this.addLog({
      productId: data.productID,
      field: 'estimatedCommissionFee',
      from: data.additionalProductInfo.estimatedCommission,
      to: oldFee,
    });
    return {
      ...data,
      additionalProductInfo: {
        ...data.additionalProductInfo,
        estimatedCommission: oldFee,
      },
    };
  }

  generateNumberofEligibleEmployeeChange(data: ProductDetail, comment: string) {
    // "Number of Eligible Employees changed from '0' to 'None Selected'"
    const match = comment.match(
      /Number of Eligible Employees changed from '(\d+)' to '.*'/
    );
    if (!match) {
      return data;
    }
    this.addLog({
      productId: data.productID,
      field: 'numberOfEligibleEmployees',
      from: data.numberOfEligibleEmployees,
      to: match[1],
    });
    const oldCount = match[1];
    return {
      ...data,
      numberOfEligibleEmployees: +oldCount,
    };
  }

  generateFundingTypeChange(data: ProductDetail, comment: string) {
    // "Funding Type changed from 'Fully Funded' to 'None Selected'"
    const match = comment.match(/Funding Type changed from '(.*)' to '.*'/);
    if (!match) {
      return data;
    }
    const fundingTypeMap: Record<string, FundingType> = {
      'None Selected': FundingType.NONE_SELECTED,
      'Fully Funded': FundingType.FULLY_FUNDED,
      'Self Funded': FundingType.SELF_FUNDED,
      'Level Funded': FundingType.LEVEL_FUNDED,
    };
    const oldFundingType = match[1];
    this.addLog({
      productId: data.productID,
      field: 'fundingType',
      from: data.fundingType,
      to: fundingTypeMap[oldFundingType],
    });
    return {
      ...data,
      fundingType: fundingTypeMap[oldFundingType],
    };
  }
  generatePremiumPaymentFrequencyChange(data: ProductDetail, comment: string) {
    // "Premium Payment Frequency changed from 'per Month' to 'None Selected'"
    const match = comment.match(
      /Premium Payment Frequency changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const frequencyMap: Record<string, PremiumPaymentFrequency> = {
      'None Selected': PremiumPaymentFrequency.NONE_SELECTED,
      'per Month': PremiumPaymentFrequency.PER_MONTH,
      'per Year': PremiumPaymentFrequency.PER_YEAR,
      'per Week': PremiumPaymentFrequency.PER_WEEK,
      'One Time': PremiumPaymentFrequency.ONE_TIME,
      Quarterly: PremiumPaymentFrequency.QUARTERLY,
    };
    const [oldFrequency, _newFrequency] = match.slice(1);
    this.addLog({
      productId: data.productID,
      field: 'premiumPaymentFrequency',
      from: data.premiumPaymentFrequency,
      to: frequencyMap[oldFrequency],
    });
    return {
      ...data,
      premiumPaymentFrequency: frequencyMap[oldFrequency],
    };
  }

  generateEstimatedCommissionFeePeriod(data: ProductDetail, comment: string) {
    // "Estimated Commission/Fee Period changed from 'per Month' to 'None Selected'"
    const match = comment.match(
      /Estimated Commission\/Fee Period changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const periodMap: Record<string, CommissionPeriodType> = {
      'None Selected': CommissionPeriodType.NONE_SELECTED,
      'per Month': CommissionPeriodType.PER_MONTH,
      'per Year': CommissionPeriodType.PER_YEAR,
      'per Week': CommissionPeriodType.PER_WEEK,
      'One Time': CommissionPeriodType.ONE_TIME,
      'per Quarter': CommissionPeriodType.PER_QUARTER,
    };
    const [oldPeriod, _newPeriod] = match.slice(1);
    this.addLog({
      productId: data.productID,
      field: 'estimatedCommissionFeePeriod',
      from: data.additionalProductInfo.commissionPeriodType,
      to: periodMap[oldPeriod],
    });
    return {
      ...data,
      additionalProductInfo: {
        ...data.additionalProductInfo,
        commissionPeriodType: periodMap[oldPeriod],
      },
    };
  }

  generateParentChange(data: ProductDetail, comment: string) {
    const match = comment.match(
      /Prior Plan changed from 'BP Internal Plan\/Product ID: (\d+)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const [oldParent, _newParent] = match.slice(1);
    this.addLog({
      productId: data.productID,
      field: 'parentProductID',
      from: data.parentProductID,
      to: oldParent,
    });
    return { ...data, parentProductID: +oldParent };
  }
  generateCommissionSplitChange(data: ProductDetail, comment: string) {
    // "Commission Start Date changed from '01/01/2020' to 'None Selected'" -> {01/01/2020}
    const match = comment.match(
      /Commission Start Date changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const date = match[1];
    this.addLog({
      productId: data.productID,
      field: 'commissionStartOn',
      from: data.commissionInfo.commissionStartOn,
      to: this.applyTimezoneOffset(dayjs(date).toDate()),
    });
    return {
      ...data,
      commissionInfo: {
        ...data.commissionInfo,
        commissionStartOn: this.applyTimezoneOffset(dayjs(date).toDate()),
      },
    };
  }

  generatePolicyNumberChange(data: ProductDetail, comment: string) {
    // "Policy/Group Number changed from '20041815801' to ''"
    const match = comment.match(
      /Policy\/Group Number changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    this.addLog({
      productId: data.productID,
      field: 'policyNumber',
      from: data.policyNumber,
      to: match[1],
    });
    return { ...data, policyNumber: match[1] };
  }

  generateEstimatedMonthlyPremium(data: ProductDetail, comment: string) {
    // "Estimated Monthly Premium changed from '0.00' to 'None Selected'"
    const match = comment.match(
      /Estimated Monthly Premium changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    this.addLog({
      productId: data.productID,
      field: 'totalEstimatedMonthlyPremium',
      from: data.totalEstimatedMonthlyPremium,
      to: match[1],
    });
    return { ...data, totalEstimatedMonthlyPremium: match[1] };
  }

  generateOriginalEffectiveAsOfChange(data: ProductDetail, comment: string) {
    // "Original Effective Date changed from '01/01/2024' to 'None Selected'"
    const match = comment.match(
      /Original Effective Date changed from '(.*)' to '.*'/
    );

    if (!match) {
      return data;
    }
    const date = match[1];
    this.addLog({
      productId: data.productID,
      field: 'originalEffectiveAsOf',
      from: data.originalEffectiveAsOf,
      to: this.applyTimezoneOffset(dayjs(date).toDate()),
    });
    return {
      ...data,
      originalEffectiveAsOf: this.applyTimezoneOffset(dayjs(date).toDate()),
    };
  }

  generateBrokerOfRecordAsOfChange(data: ProductDetail, comment: string) {
    // "Broker of Record Date changed from '08/01/2019' to 'None Selected'"
    const match = comment.match(
      /Broker of Record Date changed from '(.*)' to '.*'/
    );

    if (!match) {
      return data;
    }
    const date = match[1];
    this.addLog({
      productId: data.productID,
      field: 'brokerOfRecordAsOf',
      from: data.brokerOfRecordAsOf,
      to: this.applyTimezoneOffset(dayjs(date).toDate()),
    });
    return {
      ...data,
      brokerOfRecordAsOf: this.applyTimezoneOffset(dayjs(date).toDate()),
    };
  }
  generateACAComplianceChange(data: ProductDetail, comment: string) {
    const match = comment.match(
      /ACA Safe Harbor Affordability changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const typeMap: Record<string, AcaSafeHarborType> = {
      'None Selected': AcaSafeHarborType.NONE_SELECTED,
      'W-2 Wages': AcaSafeHarborType.W_2_WAGES,
      'Rate of Pay': AcaSafeHarborType.RATE_OF_PAY,
      'Federal Poverty Level': AcaSafeHarborType.FEDERAL_POVERTY_LINE,
    };
    const oldType = typeMap[match[1]];

    this.addLog({
      productId: data.productID,
      field: 'acaSafeHarborType',
      from: data.acaSafeHarborType,
      to: oldType,
    });
    return oldType ? { ...data, acaSafeHarborType: oldType } : data;
  }
  generatePolicyOriginationReasonChange(data: ProductDetail, comment: string) {
    const match = comment.match(
      /Origination Reason changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const reasonMap: Record<string, PolicyOriginationReason> = {
      'None Selected': PolicyOriginationReason.NONE_SELECTED,
      New: PolicyOriginationReason.NEW,
      Replacement: PolicyOriginationReason.REPLACEMENT,
      Renewal: PolicyOriginationReason.RENEWAL,
      Prospective: PolicyOriginationReason.PROSPECTIVE,
    };
    const oldReason = reasonMap[match[1]];
    this.addLog({
      productId: data.productID,
      field: 'policyOriginationReason',
      from: data.policyOriginationReason,
      to: oldReason,
    });
    return oldReason
      ? { ...data, policyOriginationReason: oldReason }
      : { ...data };
  }
  generateBillingTypeChange(data: ProductDetail, comment: string) {
    const match = comment.match(/Billing Type changed from '(.*)' to '.*'/);
    if (!match) {
      return data;
    }
    const typeMap: Record<string, BillingType> = {
      'None Selected': BillingType.NONE_SELECTED,
      'Direct Bill': BillingType.DIRECT_BILL,
      'Agency Bill': BillingType.AGENCY_BILL,
    };
    const oldType = typeMap[match[1]];
    this.addLog({
      productId: data.productID,
      field: 'billingType',
      from: data.billingType,
      to: oldType,
    });
    return oldType ? { ...data, billingType: oldType } : data;
  }
  generateBillingCarrierTypeChange(data: ProductDetail, comment: string) {
    // "Commissions/Fees Paid By Type changed from 'Insurer/TPA' to 'None Selected'"
    const match = comment.match(
      /Commissions\/Fees Paid By Type changed from '(.*)' to '(.*)'/
    );
    if (!match) {
      return data;
    }
    const typeMap: Record<string, BillingCarrierTypeEnums> = {
      'None Selected': BillingCarrierTypeEnums.NONE_SELECTED,
      'Parent Company': BillingCarrierTypeEnums.PARENT_COMPANY,
      'Insurer/TPA': BillingCarrierTypeEnums.INSURER_OR_TPA,
      'Other Brokerage/GA': BillingCarrierTypeEnums.OTHER_BROKERAGE_OR_GA,
    };
    const newType = typeMap[match[1]];
    this.addLog({
      productId: data.productID,
      field: 'billingCarrierType',
      from: data.billingCarrierType,
      to: newType,
    });
    return newType ? { ...data, billingCarrierType: newType } : data;
  }

  // Removed
  generateCancellationAdditionalInfo(
    data: ProductDetail,
    comment: string,
    prevLogs: AuditLog[]
  ) {
    // Cancellation Additional Information removed
    const match = comment.match(/Cancellation Additional Information removed/);
    if (!match) {
      return data;
    }
    const prevLog = prevLogs.find((log) =>
      log.comment.includes('Cancellation Additional Information set to')
    );
    if (prevLog) {
      const prevMatch = prevLog.comment.match(
        /Cancellation Additional Information set to '(.*)'/
      );
      // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (prevMatch && prevMatch[1]) {
        this.addLog({
          productId: data.productID,
          field: 'cancellationAdditionalInformation',
          from: data.cancellationAdditionalInformation,
          to: prevMatch[1],
        });
        return { ...data, cancellationAdditionalInformation: prevMatch[1] };
      }
    }
    return data;
  }
  generateCancellationDate(
    data: ProductDetail,
    comment: string,
    prevLogs: AuditLog[]
  ) {
    // Handle "Cancellation Date removed"
    if (comment === 'Cancellation Date removed') {
      // Find the latest "Cancellation Date changed from" or "Cancellation Date set to"
      const latestLog = [...prevLogs].find((log) =>
        log.comment.match(
          /Cancellation Date (changed from '.*' to '.*'|set to '.*')/
        )
      );
      if (latestLog) {
        // Try to extract the latest value
        const match =
          latestLog.comment.match(
            /Cancellation Date changed from '.*' to '(.*)'/
          ) || latestLog.comment.match(/Cancellation Date set to '(.*)'/);
        // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (match && match[1]) {
          this.addLog({
            productId: data.productID,
            field: 'cancellationOn',
            from: data.cancellationOn,
            to: this.applyTimezoneOffset(dayjs(match[1]).toDate()),
          });
          return {
            ...data,
            cancellationOn: this.applyTimezoneOffset(dayjs(match[1]).toDate()),
          };
        }
      }
      return data;
    }

    return data;
  }

  generateCancellationReason(
    data: ProductDetail,
    comment: string,
    prevLogs: AuditLog[]
  ) {
    if (comment === 'Cancellation Reason removed') {
      // Find the latest "Cancellation Date changed from" or "Cancellation Date set to"
      const latestLog = [...prevLogs].find((log) =>
        log.comment.match(
          /Cancellation Reason (changed from '.*' to '.*'|set to '.*')/
        )
      );
      if (latestLog) {
        // Try to extract the latest value
        const match =
          latestLog.comment.match(
            /Cancellation Reason changed from '.*' to '(.*)'/
          ) || latestLog.comment.match(/Cancellation Reason set to '(.*)'/);
        // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (match && match[1]) {
          this.addLog({
            productId: data.productID,
            field: 'cancellationReason',
            from: data.cancellationReason,
            to: match[1],
          });
          return {
            ...data,
            cancellationReason: match[1],
          };
        }
      }
      return data;
    }
    return data;
  }

  generateReinstaementAdditionalInfo(
    data: ProductDetail,
    comment: string,
    prevLogs: AuditLog[]
  ) {
    // 'Reinstatement Additional Information removed'
    const match = comment.match(/Reinstatement Additional Information removed/);
    if (!match) {
      return data;
    }
    const prevLog = prevLogs.find((log) =>
      log.comment.includes('Reinstatement Additional Information set to')
    );
    if (prevLog) {
      const prevMatch = prevLog.comment.match(
        /Reinstatement Additional Information set to '(.*)'/
      );
      // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (prevMatch && prevMatch[1]) {
        this.addLog({
          productId: data.productID,
          field: 'reinstatementAdditionalInformation',
          from: data.reinstatementAdditionalInformation,
          to: prevMatch[1],
        });
        return { ...data, reinstatementAdditionalInformation: prevMatch[1] };
      }
    }
    return data;
  }

  generateAutoActivityLogCreationOptionTypeChange(
    data: ProductDetail,
    comment: string
  ) {
    // "Automatic Activity Log Creation for Renewal changed from '90 days' to 'None Selected'"
    const match = comment.match(
      /Automatic Activity Log Creation for Renewal changed from '(.*)' to '.*'/
    );
    if (!match) {
      return data;
    }
    const typeMap: Record<string, AutoActivityLogCreationOptionType> = {
      'None Selected': AutoActivityLogCreationOptionType.NONE_SELECTED,
      '90 days': AutoActivityLogCreationOptionType.NINETY_DAYS,
      '60 days': AutoActivityLogCreationOptionType.SIXTY_DAYS,
    };
    const newType = typeMap[match[1]];
    if (newType) {
      this.addLog({
        productId: data.productID,
        field: 'autoActivityLogCreationOptionType',
        from: data.autoActivityLogCreationOptionType,
        to: newType,
      });
      return {
        ...data,
        autoActivityLogCreationOptionType: newType,
      };
    }
    return data;
  }

  generateReinstatementDate(
    data: ProductDetail,
    comment: string,
    prevLogs: AuditLog[]
  ) {
    if (comment === 'Reinstatement Date removed') {
      // Find the latest "Reinstatement Date changed from" or "Reinstatement Date set to"
      const latestLog = [...prevLogs].find((log) =>
        log.comment.match(
          /Reinstatement Date (changed from '.*' to '.*'|set to '.*')/
        )
      );
      if (latestLog) {
        // Try to extract the latest value
        const match =
          latestLog.comment.match(
            /Reinstatement Date changed from '.*' to '(.*)'/
          ) || latestLog.comment.match(/Reinstatement Date set to '(.*)'/);
        // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (match && match[1]) {
          this.addLog({
            productId: data.productID,
            field: 'reinstatementOn',
            from: data.reinstatementOn,
            to: this.applyTimezoneOffset(dayjs(match[1]).toDate()),
          });
          return {
            ...data,
            reinstatementOn: this.applyTimezoneOffset(dayjs(match[1]).toDate()),
          };
        }
      }
      return data;
    }

    return data;
  }

  generateReinstatementReason(
    data: ProductDetail,
    comment: string,
    prevLogs: AuditLog[]
  ) {
    if (comment === 'Reinstatement Reason removed') {
      // Find the latest "Reinstatement Reason changed from" or "Reinstatement Reason set to"
      const latestLog = [...prevLogs].find((log) =>
        log.comment.match(
          /Reinstatement Reason (changed from '.*' to '.*'|set to '.*')/
        )
      );
      if (latestLog) {
        // Try to extract the latest value
        const match =
          latestLog.comment.match(
            /Reinstatement Reason changed from '.*' to '(.*)'/
          ) || latestLog.comment.match(/Reinstatement Reason set to '(.*)'/);
        // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (match && match[1]) {
          this.addLog({
            productId: data.productID,
            field: 'reinstatementReason',
            from: data.reinstatementReason,
            to: match[1],
          });
          return {
            ...data,
            reinstatementReason: match[1],
          };
        }
      }
      return data;
    }
    return data;
  }

  generateFromEntry(
    data: ProductDetail,
    auditLog: AuditLog,
    previousLogs: AuditLog[]
  ): Partial<ProductDetail> {
    const generators = [
      this.generateBillingCarrierTypeChange,
      this.generateBillingTypeChange,
      this.generatePolicyOriginationReasonChange,
      this.generateBrokerOfRecordAsOfChange,
      this.generateOriginalEffectiveAsOfChange,
      this.generatePolicyNumberChange,
      this.generateCommissionSplitChange,
      this.generateEstimatedCommissionFeePeriod,
      this.generatePremiumPaymentFrequencyChange,
      this.generateFundingTypeChange,
      this.generateEstimatedCommissionFee,
      this.generateEstimatedMonthlyPremium,
      this.generateNumberofEligibleEmployeeChange,
      this.generateCancellationAdditionalInfo,
      // This.generateCancellationDate,
      // this.generateCancellationReason,
      this.generateReinstaementAdditionalInfo,
      // This.generateReinstatementDate,
      // this.generateReinstatementReason,
      this.generateACAComplianceChange,
      this.generateParentChange,
      this.generateAutoActivityLogCreationOptionTypeChange,
    ];

    return generators.reduce(
      (result, fn) => fn.call(this, result, auditLog.comment, previousLogs),
      data
    );
  }
}
