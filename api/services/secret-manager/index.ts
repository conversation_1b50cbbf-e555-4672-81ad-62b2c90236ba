import { SecretManagerServiceClient } from '@google-cloud/secret-manager';

const client = new SecretManagerServiceClient();

export const getSecret = async (
  secretId: string,
  version = 'latest'
): Promise<string> => {
  const projectId = process.env.PROJECT_ID;
  if (!projectId) {
    throw new Error('Project ID not set in environment (PROJECT_ID)');
  }

  const [accessResponse] = await client.accessSecretVersion({
    name: `projects/${projectId}/secrets/${secretId}/versions/${version}`,
  });

  const payload = accessResponse.payload?.data?.toString();
  if (!payload) {
    throw new Error(`Secret ${secretId} not found or empty`);
  }
  return payload;
};
