import { injectable } from 'inversify';
import currency from 'currency.js';
import type { SnapshotData } from 'common/globalTypes';

import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';

interface ReportData {
  id: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  [key: string]: any;
}

type ReportDataPatch = ReportData[];

interface AgentCommissions {
  [agentId: string]: number;
  total: number;
}

@injectable()
export class SavedReportsService implements ISavedReportsService {
  async getReportSnapshotDataIds(
    reportStrId: string,
    accountId: string
  ): Promise<number[]> {
    const report = await prismaClient.saved_reports.findUnique({
      where: {
        str_id: reportStrId,
        account_id: accountId,
      },
    });

    if (report?.snapshot_data) {
      const snapshotData = report.snapshot_data as unknown as SnapshotData;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const ids = snapshotData.data.data.map((item: any) => item.id);
      return ids;
    }

    return [];
  }

  async patchReportData(
    reportId: number | null,
    reportStrId: string | null,
    accountId: string,
    data: ReportDataPatch,
    uid: string,
    ouid: string
  ): Promise<number | null> {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let whereObj;

    if (reportId) {
      whereObj = {
        id: reportId,
        account_id: accountId,
      };
    } else if (reportStrId) {
      whereObj = {
        str_id: reportStrId,
        account_id: accountId,
      };
    } else {
      return null;
    }

    const reportToUpdate = await prismaClient.saved_reports.findUnique({
      where: whereObj,
    });

    // Create a new history record
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const queryObj: any = {
      account_id: reportToUpdate.account_id,
      created_at: new Date(),
      created_proxied_by: ouid,
      access: reportToUpdate.access,
      name: reportToUpdate.name,
      notes: reportToUpdate.notes,
      page: reportToUpdate.page,
      type: reportToUpdate.type,
      users_white_list: reportToUpdate.users_white_list,
      status: reportToUpdate.status,
      snapshot_data: reportToUpdate.snapshot_data,
      saved_reports_history_saved_reports: {
        connect: {
          str_id: reportToUpdate.str_id,
        },
      },
      rep_created_byTousers: {
        connect: {
          uid: uid,
        },
      },
    };

    if (reportToUpdate.reviewed_by) {
      queryObj.rep_reviewed_byTousers = {
        connect: {
          uid: reportToUpdate.reviewed_by,
        },
      };
    }
    await prismaClient.saved_reports_history.create({
      data: queryObj,
    });

    if (
      reportToUpdate?.snapshot_data &&
      typeof reportToUpdate.snapshot_data === 'object'
    ) {
      const snapshotData = (
        reportToUpdate.snapshot_data as unknown as SnapshotData
      ).data.data;

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      data.forEach((patch) => {
        const rowToUpdate = snapshotData.find((row) => +row.id === +patch.id);
        if (rowToUpdate) {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          Object.keys(patch).forEach((key) => {
            if (key !== 'id') {
              rowToUpdate[key] = patch[key];
            }
          });
        }
      });

      // Recalculate totals based on the updated snapshotData
      const totals = {
        fees: 0,
        agent_commissions: {
          total: 0,
        } as AgentCommissions,
        commission_amount: 0,
        commission_paid_amount: 0,
        customer_paid_premium_amount: 0,
        commissionable_premium_amount: 0,
      };
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      snapshotData.forEach((row) => {
        if (row.fees) totals.fees = currency(totals.fees).add(row.fees).value;
        if (row.agent_commissions) {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          Object.keys(row.agent_commissions).forEach((agentId) => {
            totals.agent_commissions[agentId] = currency(
              totals.agent_commissions[agentId] ?? 0
            ).add(row.agent_commissions[agentId]).value;
          });
          totals.agent_commissions.total = Object.keys(totals.agent_commissions)
            .filter((key) => key !== 'total')
            .reduce((sum, agentId) => {
              return currency(sum).add(totals.agent_commissions[agentId]).value;
            }, 0);
        }
        if (row.commission_amount)
          totals.commission_amount = currency(totals.commission_amount).add(
            row.commission_amount
          ).value;
        if (row.commission_paid_amount)
          totals.commission_paid_amount = currency(
            totals.commission_paid_amount
          ).add(row.commission_paid_amount).value;
        if (row.customer_paid_premium_amount)
          totals.customer_paid_premium_amount = currency(
            totals.customer_paid_premium_amount
          ).add(row.customer_paid_premium_amount).value;
        if (row.commissionable_premium_amount)
          totals.commissionable_premium_amount = currency(
            totals.commissionable_premium_amount
          ).add(row.commissionable_premium_amount).value;
      });

      const updatedSnapshotData = {
        ...reportToUpdate.snapshot_data,
        data: {
          ...(reportToUpdate.snapshot_data as unknown as SnapshotData).data,
          data: snapshotData,
          totals: totals,
        },
      };

      const updatedReport = await prismaClient.saved_reports.update({
        where: whereObj,
        data: {
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
          snapshot_data: updatedSnapshotData,
        },
      });

      return updatedReport.id;
    }

    return null;
  }

  async deleteSavedReports(
    savedReportsIds: number[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<boolean> {
    const promises = savedReportsIds.map((_id) => {
      const deletionData = {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      };

      return prismaClient.$transaction(async (prisma) => {
        await prisma.saved_reports.update({
          where: { id: _id, account_id: String(accountId) },
          data: deletionData,
        });

        // Cascade delete accounting_transactions linked to the report
        await prisma.accounting_transactions.updateMany({
          where: {
            saved_report_id: _id,
            account_id: accountId,
          },
          data: deletionData,
        });
      });
    });

    await Promise.all(promises);

    return true;
  }
}

export interface ISavedReportsService {
  getReportSnapshotDataIds(
    reportStrId: string,
    accountId: string
  ): Promise<number[]>;
  patchReportData(
    reportId: number | null,
    reportStrId: string | null,
    accountId: string,
    data: ReportDataPatch,
    uid: string,
    ouid: string
  ): Promise<number | null>;
  deleteSavedReports(
    savedReportsIds: number[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<boolean>;
}
