import { describe, it, expect, vi, beforeEach } from 'vitest';

import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';
import { SavedReportsService } from './index';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    $transaction: vi.fn(),
    saved_reports: {
      update: vi.fn(),
    },
    accounting_transactions: {
      updateMany: vi.fn(),
    },
  },
}));

describe('SavedReportsService', () => {
  let service: SavedReportsService;
  const mockAccountId = 'acc123';
  const mockUserId = 'user456';
  const mockOperatingUserId = 'ouser789';
  const mockDate = new Date('2025-06-12T10:00:00Z');
  const mockReportIds = [1, 2, 3];

  beforeEach(() => {
    vi.resetAllMocks();
    service = new SavedReportsService();
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.$transaction as any).mockImplementation(async (callback) => {
      const prismaMock = {
        saved_reports: {
          update: prismaClient.saved_reports.update,
        },
        accounting_transactions: {
          updateMany: prismaClient.accounting_transactions.updateMany,
        },
      };
      await callback(prismaMock);
    });
  });

  describe('deleteSavedReports', () => {
    it('Should mark reports as deleted when deleting with valid ids', async () => {
      const deletionData = {
        state: DataStates.DELETED,
        updated_at: mockDate,
        updated_by: mockUserId,
        updated_proxied_by: mockOperatingUserId,
      };

      const result = await service.deleteSavedReports(
        mockReportIds,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(prismaClient.$transaction).toHaveBeenCalledTimes(
        mockReportIds.length
      );
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      mockReportIds.forEach((id) => {
        expect(prismaClient.saved_reports.update).toHaveBeenCalledWith({
          where: { id, account_id: String(mockAccountId) },
          data: deletionData,
        });
      });
      expect(result).toBe(true);
    });

    it('Should delete linked accounting transactions when deleting reports', async () => {
      const deletionData = {
        state: DataStates.DELETED,
        updated_at: mockDate,
        updated_by: mockUserId,
        updated_proxied_by: mockOperatingUserId,
      };

      await service.deleteSavedReports(
        mockReportIds,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      mockReportIds.forEach((id) => {
        expect(
          prismaClient.accounting_transactions.updateMany
        ).toHaveBeenCalledWith({
          where: {
            saved_report_id: id,
            account_id: mockAccountId,
          },
          data: deletionData,
        });
      });
    });

    it('Should return true without processing when empty array is provided', async () => {
      const result = await service.deleteSavedReports(
        [],
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(prismaClient.$transaction).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('Should throw error when database operation fails', async () => {
      const mockError = new Error('Database error');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.$transaction as any).mockRejectedValueOnce(mockError);

      await expect(
        service.deleteSavedReports(
          [1],
          mockAccountId,
          mockUserId,
          mockOperatingUserId
        )
      ).rejects.toThrow('Database error');
    });
  });
});
