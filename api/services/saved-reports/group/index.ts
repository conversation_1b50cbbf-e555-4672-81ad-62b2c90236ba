import { injectable } from 'inversify';
import type { JsonValue } from '@prisma/client/runtime/library';
import {
  AgentCommissionsStatuses,
  CompReportViewTypes,
  type ConflictingReportData,
  ReportGroupsStatuses,
  SavedReportStatuses,
} from 'common/globalTypes';
import type { Prisma } from '@prisma/client';
import { type SnapshotData, TransactionStatuses } from 'common/globalTypes';
import currency from 'currency.js';
import BigNumber from 'bignumber.js';
import { transactionTypes } from 'common/constants';
import {
  AGENT_COMMISSION_THIS_PERIOD_COLUMN,
  TOTAL_COMMISSION_COLUMN,
  TOTAL_PREMIUM_COLUMN,
} from 'common/constants/excel-export';

import { calculateSkipAndTake } from '@/prisma';
import { prismaClient } from '@/lib/prisma';
import {
  DataStates,
  DownloadTypes,
  Roles,
  savedReportsGroupsTemplates,
} from '@/types';
import { AccountService, type IAccountService } from '@/services/account';
import { container } from '@/ioc';
import { type IReportService, ReportService } from '@/services/report';
import { limitConcurrency } from '@/lib/helpers/limitConcurrency';
import { type IUserService, UserService } from '@/services/user';
import { type IPdfService, PdfService } from '@/services/pdfService';
import { EmailerService } from '@/services/emailer';
import { type IStatementService, StatementService } from '@/services/statement';
import {
  AccountingService,
  type IAccountingService,
} from '@/services/accounting';
import type { ExportParams } from '@/services/export-report/types';
import { CompReportSettingsService } from '@/services/settings/comp_reports';
import { isReportInSelectedData } from '@/services/export-report/utils';
import Formatter from '@/lib/Formatter';
import type { Decimal } from '@prisma/client/runtime/library';
import { AccountingTransactionsType } from 'common/constants/accounting_transactions';

type WhereClause = Prisma.saved_report_groupsWhereUniqueInput;

interface Contact {
  id: number;
  str_id: string;
  first_name: string;
  last_name: string;
  email: string;
  status: string;
  payable_status: string;
  user_str_id: string;
  balance: Decimal | number;
}

interface SavedReport {
  id: number;
  str_id: string;
  name: JsonValue;
  status: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  snapshot_data: any;
  contact: Contact;
  notes: string;
  current_balance?: number;
  accounting_transactions?: Array<{
    amount: Decimal;
    status: string;
    contact_id: number;
  }>;
}

interface ReportGroup {
  data: SavedReport[];
  count: number;
}

interface BulkEditBody {
  report_notes?: string;
  status?: SavedReportStatuses;
}

interface ReportDataToApprove {
  reportId: number;
  reportStatus: SavedReportStatuses;
  savedReportsGroupId: number;
  reportName: JsonValue;
  contactStrId: string;
  includesPaidCommission: boolean;
  hasConflictingReport: boolean;
  conflictingReportsData: ConflictingReportData[];
}

@injectable()
export class SavedReportsGroupsService implements ISavedReportsGroupsService {
  private statementService: StatementService;
  private accountingService: AccountingService;
  private compReportSettingsService: CompReportSettingsService;

  constructor(
    statementsService?: StatementService,
    accountingService?: AccountingService,
    compReportSettingsService?: CompReportSettingsService
  ) {
    this.statementService =
      statementsService ?? container.get(StatementService);
    this.accountingService =
      accountingService ?? container.get(AccountingService);
    this.compReportSettingsService =
      compReportSettingsService ?? container.get(CompReportSettingsService);
  }

  // TODO: Add unit tests for this service method
  async getReportGroupsDetailsData(
    accountId: string,
    groupId?: string,
    page?: string,
    limit?: string,
    agentStatus?: string,
    agentPayableStatus?: string,
    hideEmpty?: string,
    q?: string,
    currentBalance?: string
  ): Promise<ReportGroup> {
    const { take, skip } = calculateSkipAndTake({
      page,
      limit,
    });

    const where: WhereClause = {
      account_id: accountId,
      state: DataStates.ACTIVE,
      str_id: undefined,
    };
    if (groupId) {
      where.str_id = groupId;
    }

    const findOperation = prismaClient.saved_report_groups.findUnique({
      where,
      include: {
        saved_reports: {
          where: {
            state: DataStates.ACTIVE,
            snapshot_data: hideEmpty
              ? { path: ['data', 'count'], not: 0 }
              : undefined,
            contact: {
              status: agentStatus || undefined,
              payable_status: agentPayableStatus || undefined,
            },
            OR: q
              ? [
                  { name: { contains: q, mode: 'insensitive' } },
                  { notes: { contains: q, mode: 'insensitive' } },
                ]
              : undefined,
          },
          skip,
          take,
          orderBy: {
            created_at: 'desc',
          },
          include: {
            contact: {
              select: {
                id: true,
                str_id: true,
                first_name: true,
                last_name: true,
                email: true,
                status: true,
                payable_status: true,
                user_str_id: true,
                balance: true,
              },
            },
            accounting_transactions: {
              where: {
                state: DataStates.ACTIVE,
                status: TransactionStatuses.DRAFT,
                type: AccountingTransactionsType.COMP_REPORT,
              },
              select: {
                amount: true,
                status: true,
                contact_id: true,
              },
            },
          },
        },
      },
    });

    const countOperation = prismaClient.saved_reports.count({
      where: {
        state: DataStates.ACTIVE,
        saved_report_group: {
          account_id: accountId,
          str_id: groupId,
        },
      },
    });

    const [data, count] = await Promise.all([findOperation, countOperation]);

    if (!data?.saved_reports) {
      return { data: [], count: 0 };
    }

    const reportsWithCurrentBalance = calculateCurrentBalanceAndFilter(
      data.saved_reports,
      currentBalance
    );

    return { data: reportsWithCurrentBalance, count: count };
  }

  async bulkEditReportGroupsDetailsData(
    ids: number[],
    updateData: BulkEditBody
  ): Promise<Prisma.BatchPayload | null> {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let savedReportsOp;
    const data: { notes?: string; status?: string } = {};

    if (updateData.report_notes) {
      data.notes = updateData.report_notes;
    }

    if (updateData.status) {
      data.status = updateData.status;
    }

    if (Object.keys(data).length > 0) {
      savedReportsOp = prismaClient.saved_reports.updateMany({
        where: {
          id: {
            in: ids,
          },
        },
        data: data,
      });

      const resp = await savedReportsOp;
      return resp;
    }

    return null;
  }

  async deleteReportGroupReports(
    ids: number[],
    report_group_str_id: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<{ reportGroupDeleted: boolean }> {
    if (ids.length === 0) {
      return { reportGroupDeleted: false };
    }

    await prismaClient.saved_reports.updateMany({
      where: {
        id: {
          in: ids,
        },
        account_id: accountId,
      },
      data: {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });

    const reportGroup = await prismaClient.saved_report_groups.findUnique({
      where: {
        account_id: accountId,
        str_id: report_group_str_id,
      },
      include: {
        saved_reports: true,
      },
    });

    if (!reportGroup) {
      return { reportGroupDeleted: false };
    }

    const allReportsDeleted = reportGroup.saved_reports.every(
      (report) => report.state === DataStates.DELETED
    );

    if (allReportsDeleted) {
      await prismaClient.saved_report_groups.update({
        where: {
          account_id: accountId,
          str_id: report_group_str_id,
        },
        data: {
          state: DataStates.DELETED,
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
        },
      });

      return {
        reportGroupDeleted: true,
      };
    }

    return {
      reportGroupDeleted: false,
    };
  }

  async rejectCompReports(
    reportsStrIds: string[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<{ count: number }> {
    const updatedReports = await prismaClient.saved_reports.updateMany({
      where: {
        str_id: {
          in: reportsStrIds,
        },
        account_id: accountId,
      },
      data: {
        state: DataStates.DELETED,
        updated_by: uid,
        updated_proxied_by: ouid,
        updated_at: new Date(),
      },
    });

    const reportData = await prismaClient.saved_reports.findMany({
      where: {
        str_id: {
          in: reportsStrIds,
        },
        account_id: accountId,
      },
      select: {
        id: true,
        saved_report_group_id: true,
      },
    });

    const reportIds = reportData.map((report) => report.id);

    await prismaClient.accounting_transactions.updateMany({
      where: {
        saved_report_id: { in: reportIds },
        account_id: accountId,
      },
      data: {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });

    return updatedReports;
  }

  async approveCompReports(
    reportsStrIds: string[],
    previewOnly: boolean,
    accountId: string,
    uid: string,
    ouid: string
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any> {
    const reportDataToApprove = await prismaClient.saved_reports.findMany({
      where: {
        str_id: {
          in: reportsStrIds,
        },
        account_id: accountId,
      },
      select: {
        id: true,
        status: true,
        state: true,
        name: true,
        snapshot_data: true,
        contact_id: true,
        saved_report_group_id: true,
      },
    });

    const allStatementIds: number[] = Array.from(
      new Set(
        reportDataToApprove.flatMap((report) => {
          const snapshotData = (report.snapshot_data as unknown as SnapshotData)
            .data;
          return (
            snapshotData.data
              // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              .filter((statement) => statement && statement.id)
              .map((statement) => Number(statement.id))
          );
        })
      )
    );

    const agentCommissionStatuses = await prismaClient.statement_data.findMany({
      where: {
        id: {
          in: allStatementIds,
        },
      },
      select: {
        id: true,
        agent_commissions_status2: true,
      },
    });

    const approvalStatusByReport = await Promise.all(
      reportDataToApprove.map(async (report) => {
        const snapshotData = (report.snapshot_data as unknown as SnapshotData)
          .data;
        const contactStrId = snapshotData.contactStrId;
        const statementIds = snapshotData.data
          // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          .filter((statement) => statement && statement.id)
          .map((statement) => Number(statement.id));

        // Skip reports with no valid statements
        if (statementIds.length === 0) {
          return null;
        }

        const response = {
          reportId: report.id,
          reportName: report.name,
          reportStatus: report.status as SavedReportStatuses,
          savedReportsGroupId: report.saved_report_group_id,
          contactStrId: contactStrId,
          includesPaidCommission: false,
          hasConflictingReport: false,
          conflictingReportsData: [] as ConflictingReportData[],
        };

        response.includesPaidCommission = checkPaidCommission(
          statementIds,
          contactStrId,
          agentCommissionStatuses
        );

        response.hasConflictingReport = await checkConflictingReports(
          report.contact_id,
          reportsStrIds,
          accountId,
          allStatementIds,
          response.conflictingReportsData
        );

        return response;
      })
    ).then((results) => results.filter((result) => result !== null));

    if (previewOnly) {
      return approvalStatusByReport;
    } else {
      const reportsToApprove = approvalStatusByReport.filter(
        (report) =>
          !report.includesPaidCommission &&
          !report.hasConflictingReport &&
          report.conflictingReportsData.length === 0
      );
      await approveCompReportsHandler(reportsToApprove, accountId, uid, ouid);
      return reportsToApprove;
    }
  }

  async deleteCompReportsGroups(
    savedReportGroupIds: number[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<boolean> {
    const promises = savedReportGroupIds.map((_id) => {
      const deletionData = {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      };

      return prismaClient.$transaction(async (prisma) => {
        await prisma.saved_report_groups.update({
          where: { id: _id, account_id: accountId },
          data: deletionData,
        });

        // Get all reports ids in the group
        const reports = await prisma.saved_reports.findMany({
          where: {
            saved_report_group_id: _id,
            account_id: accountId,
          },
          select: {
            id: true,
          },
        });

        const reportIds = reports.map((report) => report.id);

        // Cascade soft delete for saved_reports and transactions
        await prisma.saved_reports.updateMany({
          where: {
            id: { in: reportIds },
            account_id: accountId,
          },
          data: deletionData,
        });

        await prisma.accounting_transactions.updateMany({
          where: {
            saved_report_id: { in: reportIds },
            account_id: accountId,
          },
          data: deletionData,
        });
      });
    });

    await Promise.all(promises);

    return true;
  }

  async setPayedCompReportsGroups(
    reportsStrIds: string[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<boolean> {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const reportDataToUpdate: any = await prismaClient.saved_reports.findMany({
      where: {
        account_id: accountId,
        str_id: {
          in: reportsStrIds,
        },
        state: DataStates.ACTIVE,
      },
      select: {
        id: true,
        contact_id: true,
        snapshot_data: true,
        saved_report_group_id: true,
      },
    });

    const statementService = this.statementService;
    const accountingService = this.accountingService;

    const groupedReports = reportDataToUpdate.reduce((acc, report) => {
      const contactStrId = report.snapshot_data?.data?.contactStrId;

      if (!contactStrId) return acc;

      const statementsIds = (report.snapshot_data?.data?.data ?? []).map(
        (row) => row.id
      );
      const reportBalance = report.snapshot_data?.data?.reportBalance;
      const reportTransactionId = report.snapshot_data?.data?.transactionId;
      const contactId = report.contact_id;

      if (!acc[contactStrId]) {
        acc[contactStrId] = [];
      }

      acc[contactStrId].push({
        reportId: report.id,
        statementsIds,
        reportBalance,
        contactId,
        reportTransactionId,
      });

      return acc;
    }, {});

    for (const contactStrId in groupedReports) {
      const reports = groupedReports[contactStrId];
      const contactId = reports[0].contactId;
      const reportsTotalBalance = reports.reduce(
        (acc, report) =>
          currency(acc).add(currency(Number(report.reportBalance))).value,
        0
      );

      const statementsIds = reports.flatMap((report) => report.statementsIds);
      await statementService.updateStatementPayoutStatusByIds(
        statementsIds,
        AgentCommissionsStatuses.PAID,
        accountId,
        contactStrId,
        uid,
        ouid
      );

      const payedTransaction =
        await prismaClient.accounting_transactions.create({
          data: {
            account_id: accountId,
            contact_id: contactId,
            created_by: uid,
            created_proxied_by: ouid,
            date: new Date(),
            status: TransactionStatuses.SETTLEMENT,
            amount: -reportsTotalBalance,
            notes: 'Paid transaction',
            type: transactionTypes.COMP_REPORT_PAYMENT,
            created_at: new Date(),
          },
        });

      const payedTransactionId = payedTransaction.id;

      for (const report of reports) {
        await prismaClient.saved_reports.update({
          where: { id: report.reportId },
          data: {
            status: SavedReportStatuses.PAID,
            reviewed_at: new Date(),
            updated_by: uid,
            updated_proxied_by: ouid,
            updated_at: new Date(),
          },
        });

        if (report.reportTransactionId) {
          await accountingService.payTransactionWithDetails(
            report.reportTransactionId,
            payedTransactionId,
            report.reportId,
            accountId,
            contactId,
            uid,
            ouid
          );
        }
      }
    }

    const savedReportGroupIds: number[] = Array.from(
      new Set(
        reportDataToUpdate.map(
          (report) => report.saved_report_group_id as number
        )
      )
    );

    await setReportGroupsStatus(savedReportGroupIds, accountId, uid, ouid);

    return true;
  }

  async getExportParamsForSavedReportGroup(
    id: string,
    accountId: string,
    roleId: string,
    exportOptions: ExportOptions
  ): Promise<Omit<ExportParams, 'res' | 'req'>> {
    const groupData = await prismaClient.saved_report_groups.findUnique({
      where: {
        str_id: id,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      select: {
        id: true,
        template: true,
        saved_reports: {
          select: {
            id: true,
            snapshot_data: true,
            name: true,
            page: true,
          },
        },
        account: {
          select: {
            white_label_mode: true,
            logo_url: true,
            name: true,
          },
        },
        page: true,
        created_at: true,
      },
    });

    const compReportSettings =
      await this.compReportSettingsService.getCompReportSettings(accountId);

    let table:
      | 'companies'
      | 'mappings'
      | 'processors'
      | 'report_data'
      | 'statement_data'
      | 'reconciliation_data'
      | 'extractions'
      | 'documents' = 'report_data';
    if (groupData.saved_reports[0].page === 'policies') {
      table = 'report_data';
    } else if (groupData.saved_reports[0].page === 'commissions') {
      table = 'statement_data';
    } else if (groupData.saved_reports[0].page === 'reconciliation') {
      table = 'reconciliation_data';
    }

    let summaryResults = [];
    if (groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT) {
      summaryResults = groupData.saved_reports
        .filter((report) =>
          isReportInSelectedData(report, exportOptions.selected_data)
        )
        .map((report) => {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          const snapshotData = report.snapshot_data as any;
          return {
            'Report name': report.name,
            'Start date': Formatter.date(
              snapshotData?.data?.fieldOptions?.processing_date_start
            ),
            'End date': Formatter.date(
              snapshotData?.data?.fieldOptions?.processing_date_end
            ),
            [AGENT_COMMISSION_THIS_PERIOD_COLUMN]:
              snapshotData?.data?.totals?.agent_commissions[
                snapshotData?.data?.contactStrId
              ] ??
              Object.values(
                snapshotData?.data?.totals?.agent_commissions
              )?.[0] ??
              0,
            [TOTAL_COMMISSION_COLUMN]:
              snapshotData?.data?.totals?.commission_amount ?? 0,
            [TOTAL_PREMIUM_COLUMN]:
              snapshotData?.data?.totals?.premium_amount ?? 0,
          };
        });
    }

    let roleForExport = roleId;
    if (exportOptions.view === CompReportViewTypes.PRODUCER_VIEW) {
      roleForExport = Roles.PRODUCER.toString();
    } else if (exportOptions.view === CompReportViewTypes.ADMIN_VIEW) {
      roleForExport = Roles.ACCOUNT_ADMIN.toString();
    }

    return {
      exportOptions,
      groupData: {
        ...groupData,
        template: groupData.template as savedReportsGroupsTemplates,
        created_at: groupData.created_at.toISOString(),
      },
      summaryResults,
      table,
      roleForExport,
      compReportSettings,
    };
  }

  // TODO: Add unit tests for this service method
  async changeReportGroup(
    reportStrIds: string[],
    reportGroupStrId: string,
    uid: string,
    ouid: string
  ): Promise<boolean> {
    const reportGroupId = await prismaClient.saved_report_groups.findUnique({
      where: {
        str_id: reportGroupStrId,
      },
      select: {
        id: true,
      },
    });

    if (!reportGroupId) {
      throw new Error(`Report group with str_id ${reportGroupStrId} not found`);
    }

    await prismaClient.saved_reports.updateMany({
      where: {
        str_id: {
          in: reportStrIds,
        },
      },
      data: {
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
        saved_report_group_id: reportGroupId.id,
      },
    });

    return true;
  }
}

interface ReportWithTransactions extends SavedReport {
  accounting_transactions: Array<{
    amount: Decimal;
    status: string;
    contact_id: number;
  }>;
}

interface ReportWithCurrentBalance extends SavedReport {
  current_balance: number;
  accounting_transactions: Array<{
    amount: Decimal;
    status: string;
    contact_id: number;
  }>;
}

const calculateCurrentBalanceAndFilter = (
  reports: ReportWithTransactions[],
  currentBalanceThreshold?: string
): ReportWithCurrentBalance[] => {
  const transactionsByContactId = new Map<
    number,
    Array<{
      amount: Decimal;
      status: string;
      contact_id: number;
    }>
  >();

  for (const report of reports) {
    const contactId = report.contact.id;
    const existingTransactions = transactionsByContactId.get(contactId) || [];
    existingTransactions.push(...(report.accounting_transactions || []));
    transactionsByContactId.set(contactId, existingTransactions);
  }

  const reportsWithCurrentBalance = reports.map(
    (report): ReportWithCurrentBalance => {
      const validatedBalance = getValidatedBalance(report.contact.balance);
      const fallbackBalance = report.snapshot_data?.data?.currentBalance || 0;
      const baseBalance = validatedBalance || fallbackBalance;

      const contactTransactions =
        transactionsByContactId.get(report.contact.id) || [];
      const transactionSum = calculateTransactionSum(contactTransactions);
      const currentBalance = new BigNumber(baseBalance)
        .plus(transactionSum)
        .toNumber();

      return {
        ...report,
        contact: {
          ...report.contact,
          balance: validatedBalance,
        },
        current_balance: currentBalance,
      };
    }
  );

  return filterByThreshold(reportsWithCurrentBalance, currentBalanceThreshold);
};

const getValidatedBalance = (
  balance: Decimal | number | null | undefined
): number => {
  if (balance == null) return 0;
  const numericBalance = Number(balance);
  return Number.isNaN(numericBalance) ? 0 : numericBalance;
};

const calculateTransactionSum = (
  transactions: Array<{ amount: Decimal }>
): number => {
  return transactions.reduce((sum, transaction) => {
    return new BigNumber(sum)
      .plus(new BigNumber(transaction.amount?.toString() || '0'))
      .toNumber();
  }, 0);
};

const filterByThreshold = (
  reports: ReportWithCurrentBalance[],
  thresholdStr?: string
): ReportWithCurrentBalance[] => {
  if (!thresholdStr?.trim()) {
    return reports;
  }

  const threshold = parseFloat(thresholdStr);
  if (Number.isNaN(threshold)) {
    return reports;
  }

  return reports.filter((report) => report.current_balance >= threshold);
};

const checkPaidCommission = (
  statementIds: number[],
  contactStrId: string,
  agentCommissionStatuses: {
    id: number;
    agent_commissions_status2: JsonValue;
  }[]
): boolean => {
  return statementIds.some((statementId) => {
    const agentCommissionStatus = agentCommissionStatuses.find(
      (status) => status.id === statementId
    );
    return (
      // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      agentCommissionStatus &&
      agentCommissionStatus.agent_commissions_status2 &&
      agentCommissionStatus.agent_commissions_status2[
        contactStrId
      ]?.toLowerCase() === AgentCommissionsStatuses.PAID.toLowerCase()
    );
  });
};

const checkConflictingReports = async (
  contactId: number,
  reportsStrIds: string[],
  accountId: string,
  allStatementIds: number[],
  conflictingReportsData: ConflictingReportData[]
): Promise<boolean> => {
  const batchSize = 50;
  const batchOffsets: number[] = [];
  let skip = 0;

  // Generate batch offsets for concurrent processing
  while (true) {
    const count = await prismaClient.saved_reports.count({
      where: {
        contact_id: contactId,
        str_id: {
          notIn: reportsStrIds,
        },
        state: DataStates.ACTIVE,
        account_id: accountId,
      },
      skip,
      take: batchSize,
    });

    if (count === 0) break;

    batchOffsets.push(skip);
    skip += batchSize;

    if (count < batchSize) break;
  }

  const checkConflictBatchTask = async (offset: number) => {
    const batch = await prismaClient.saved_reports.findMany({
      where: {
        contact_id: contactId,
        str_id: {
          notIn: reportsStrIds,
        },
        state: DataStates.ACTIVE,
        account_id: accountId,
      },
      select: {
        id: true,
        str_id: true,
        name: true,
        snapshot_data: true,
      },
      skip: offset,
      take: batchSize,
    });

    const batchResults = [];
    for (const conflictingReport of batch) {
      const conflictingSnapshotData = (
        conflictingReport.snapshot_data as unknown as SnapshotData
      ).data;
      const conflictingStatementIds = conflictingSnapshotData.data
        // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        .filter((statement) => statement && statement.id)
        .map((statement) => Number(statement.id));

      const hasConflict = conflictingStatementIds.some((id) =>
        allStatementIds.includes(id)
      );

      batchResults.push({
        hasConflict,
        conflictData: hasConflict
          ? {
              reportId: conflictingReport.id,
              reportStrId: conflictingReport.str_id,
              reportName: conflictingReport.name,
            }
          : null,
      });
    }

    return batchResults;
  };

  const allResults = await limitConcurrency(
    checkConflictBatchTask,
    batchOffsets,
    10
  );

  const conflicts = allResults.flatMap((batchResults) =>
    batchResults.filter((result) => result.hasConflict && result.conflictData)
  );
  conflictingReportsData.push(
    ...conflicts.map((result) => result.conflictData)
  );
  const hasAnyConflict = conflicts.length > 0;

  return hasAnyConflict;
};

const approveCompReportsHandler = async (
  reportsToApprove: ReportDataToApprove[],
  accountId: string,
  uid: string,
  ouid: string
) => {
  const accountService = container.get<IAccountService>(AccountService);
  const reportService = container.get<IReportService>(ReportService);
  const userService = container.get<IUserService>(UserService);
  const pdfService = container.get<IPdfService>(PdfService);
  const emailerService = container.get<EmailerService>(EmailerService);
  const statementService = container.get<IStatementService>(StatementService);
  const accountingService =
    container.get<IAccountingService>(AccountingService);

  const autoShareCommisionReportSettings =
    await accountService.isAutoShareCommissionReportSettings(
      accountId,
      prismaClient
    );

  for (const report of reportsToApprove) {
    const reportId = report.reportId;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const snapshotData: any = await prismaClient.saved_reports.findFirst({
      where: {
        account_id: accountId,
        id: reportId,
        state: DataStates.ACTIVE,
      },
      include: {
        account: {
          select: {
            white_label_mode: true,
            logo_url: true,
            name: true,
          },
        },
      },
    });

    await prismaClient.saved_reports.update({
      where: { id: +reportId },
      data: {
        status: SavedReportStatuses.APPROVED,
        reviewed_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
        updated_at: new Date(),
      },
    });

    if (autoShareCommisionReportSettings.auto_share_commission_report) {
      if (snapshotData?.snapshot_data?.data?.contactUser) {
        const contactUserId = snapshotData.snapshot_data.data.contactUser;

        reportService.updateReportAccess(
          +reportId,
          accountId,
          contactUserId,
          snapshotData.users_white_list,
          'user_list'
        );

        if (autoShareCommisionReportSettings.auto_email_commission_report) {
          const userInfo = await userService.getUserByStrId(contactUserId);
          if (userInfo && userInfo.user_contact?.length > 0) {
            // Only for commissions reports when approved, automatically upload the pdf commission payout report to storage
            await pdfService.generateAndStorageCommissionPayoutPdf(
              snapshotData,
              accountId,
              userInfo
            );
            await emailerService.sendEmail(
              userInfo.email,
              'Commission payout report',
              `<div>
                          Hi ${userInfo.first_name} ${userInfo.last_name},<br/>
                          <p>A new payout report has been generated.</p>
                          <a href="https://app.fintary.com/downloads?download_type=${DownloadTypes.COMMISSION_PAYOUT}&file_name=${encodeURIComponent(snapshotData.name)}">Click here to review</a>
                        </div>`
            );
          }
        }
      }
    }

    const ids = (snapshotData?.snapshot_data?.data?.data ?? [])
      // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      .filter((row) => row && row.id)
      .map((row) => row.id);

    await statementService.updateStatementPayoutStatusByIds(
      ids,
      AgentCommissionsStatuses.APPROVED,
      accountId,
      snapshotData?.snapshot_data?.data?.contactStrId,
      uid,
      ouid
    );

    if (snapshotData?.snapshot_data?.data?.transactionId)
      await accountingService.approveTransactionWithDetails(
        accountId,
        snapshotData.snapshot_data.data.transactionId,
        uid,
        ouid
      );
  }

  const savedReportGroupIds = new Set(
    reportsToApprove.map((report) => report.savedReportsGroupId)
  );

  await setReportGroupsStatus(
    Array.from(savedReportGroupIds),
    accountId,
    uid,
    ouid
  );
};

const setReportGroupsStatus = async (
  reportGroupIds: number[],
  accountId: string,
  uid: string,
  ouid: string
) => {
  const reportGroupReports = await prismaClient.saved_report_groups.findMany({
    where: {
      id: {
        in: reportGroupIds,
      },
      account_id: accountId,
    },
    include: {
      saved_reports: {
        where: {
          state: DataStates.ACTIVE,
        },
        select: {
          id: true,
          status: true,
        },
      },
    },
  });

  for (const group of reportGroupReports) {
    const allSameStatus = group.saved_reports.every(
      (report) => report.status === group.saved_reports[0].status
    );

    const newState = allSameStatus
      ? group.saved_reports[0].status
      : ReportGroupsStatuses.IN_PROGRESS;

    await prismaClient.saved_report_groups.update({
      where: { id: group.id },
      data: {
        status: newState,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });
  }
};

export interface ExportOptions {
  export_type: string;
  amount_due_threshold: string;
  individual_reports: boolean;
  view: CompReportViewTypes;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  selected_data: any[];
  grouping: string;
}

export interface ISavedReportsGroupsService {
  getExportParamsForSavedReportGroup(
    id: string,
    accountId: string,
    roleId: string,
    exportOptions: ExportOptions
  ): Promise<Omit<ExportParams, 'res' | 'req'>>;
  getReportGroupsDetailsData(
    accountId: string,
    groupId?: string,
    page?: string,
    limit?: string,
    agentStatus?: string,
    agentPayableStatus?: string,
    hideEmpty?: string,
    q?: string,
    currentBalance?: string
  ): Promise<ReportGroup>;
  bulkEditReportGroupsDetailsData(
    ids: number[],
    updateData: BulkEditBody
  ): Promise<Prisma.BatchPayload | null>;
  deleteReportGroupReports(
    ids: number[],
    report_group_str_id: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<{ reportGroupDeleted: boolean }>;
  rejectCompReports(
    reportsStrIds: string[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<{ count: number }>;
  approveCompReports(
    reportsStrIds: string[],
    previewOnly: boolean,
    accountId: string,
    uid: string,
    ouid: string
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  deleteCompReportsGroups(
    savedReportGroupIds: number[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<boolean>;
  setPayedCompReportsGroups(
    reportsStrIds: string[],
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<boolean>;
  changeReportGroup(
    reportStrIds: string[],
    reportGroupStrId: string,
    uid: string,
    ouid: string
  ): Promise<boolean>;
}
