import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { CompReportViewTypes } from 'common/globalTypes';

import { prismaClient } from '@/lib/prisma';
import { type ExportOptions, SavedReportsGroupsService } from './index';
import { DataStates, Roles } from '@/types';

vi.mock('@/ioc', () => ({
  container: {
    get: vi.fn(() => ({
      // Mock all service methods to return minimal responses
      isAutoShareCommissionReportSettings: vi.fn().mockResolvedValue({
        auto_share_commission_report: false,
        auto_email_commission_report: false,
      }),
      updateReportAccess: vi.fn().mockResolvedValue(undefined),
      getUserByStrId: vi.fn().mockResolvedValue(null),
      generateAndStorageCommissionPayoutPdf: vi
        .fn()
        .mockResolvedValue(undefined),
      sendEmail: vi.fn().mockResolvedValue(undefined),
      updateStatementPayoutStatusByIds: vi.fn().mockResolvedValue(undefined),
      approveTransactionWithDetails: vi.fn().mockResolvedValue(undefined),
      payTransactionWithDetails: vi.fn().mockResolvedValue(undefined),
      getCompReportSettings: vi.fn().mockResolvedValue({}),
    })),
  },
}));

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    $transaction: vi.fn(),
    saved_report_groups: {
      update: vi.fn(),
      findUnique: vi.fn(),
    },
    saved_reports: {
      findMany: vi.fn(),
      updateMany: vi.fn(),
      count: vi.fn(),
    },
    statement_data: {
      findMany: vi.fn(),
      count: vi.fn(),
      update: vi.fn(),
      createMany: vi.fn(),
    },
    accounting_transactions: {
      updateMany: vi.fn(),
    },
  },
}));

vi.mock('./index', async () => {
  const actual = await vi.importActual('./index');
  return {
    ...actual,
  };
});

vi.mock('@/lib/helpers/limitConcurrency', () => ({
  limitConcurrency: vi.fn(async (taskFn, items, _concurrency) => {
    const results = [];
    for (const item of items) {
      results.push(await taskFn(item));
    }
    return results;
  }),
}));

describe('SavedReportsGroupsService', () => {
  let service: SavedReportsGroupsService;
  const mockAccountId = 'acc123';
  const mockUserId = 'user456';
  const mockOperatingUserId = 'ouser789';
  const mockDate = new Date('2025-06-12T10:00:00Z');
  const mockGroupIds = [1, 2, 3];
  const mockReportIds = [101, 102, 103];

  beforeEach(() => {
    vi.resetAllMocks();
    service = new SavedReportsGroupsService();
    vi.useFakeTimers();
    vi.setSystemTime(mockDate);

    // Mock transaction implementation
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.$transaction as any).mockImplementation(async (callback) => {
      const prismaMock = {
        saved_report_groups: {
          update: prismaClient.saved_report_groups.update,
        },
        saved_reports: {
          findMany: prismaClient.saved_reports.findMany,
          updateMany: prismaClient.saved_reports.updateMany,
        },
        accounting_transactions: {
          updateMany: prismaClient.accounting_transactions.updateMany,
        },
      };
      await callback(prismaMock);
    });

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.saved_reports.findMany as any).mockResolvedValue(
      mockReportIds.map((id) => ({ id }))
    );
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  describe('deleteCompReportsGroups', () => {
    it('Given valid ids, should mark report groups as deleted', async () => {
      const deletionData = {
        state: DataStates.DELETED,
        updated_at: mockDate,
        updated_by: mockUserId,
        updated_proxied_by: mockOperatingUserId,
      };

      const result = await service.deleteCompReportsGroups(
        mockGroupIds,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(prismaClient.$transaction).toHaveBeenCalledTimes(
        mockGroupIds.length
      );
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      mockGroupIds.forEach((id) => {
        expect(prismaClient.saved_report_groups.update).toHaveBeenCalledWith({
          where: { id, account_id: mockAccountId },
          data: deletionData,
        });
      });
      expect(result).toBe(true);
    });

    it('Given valid ids, should find and delete all reports associated with each group', async () => {
      const deletionData = {
        state: DataStates.DELETED,
        updated_at: mockDate,
        updated_by: mockUserId,
        updated_proxied_by: mockOperatingUserId,
      };

      await service.deleteCompReportsGroups(
        mockGroupIds,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      mockGroupIds.forEach((groupId) => {
        expect(prismaClient.saved_reports.findMany).toHaveBeenCalledWith({
          where: {
            saved_report_group_id: groupId,
            account_id: mockAccountId,
          },
          select: {
            id: true,
          },
        });
      });

      expect(prismaClient.saved_reports.updateMany).toHaveBeenCalledWith({
        where: {
          id: { in: mockReportIds },
          account_id: mockAccountId,
        },
        data: deletionData,
      });
    });

    it('Given valid ids, should delete all accounting transactions linked to the reports', async () => {
      const deletionData = {
        state: DataStates.DELETED,
        updated_at: mockDate,
        updated_by: mockUserId,
        updated_proxied_by: mockOperatingUserId,
      };

      await service.deleteCompReportsGroups(
        mockGroupIds,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(
        prismaClient.accounting_transactions.updateMany
      ).toHaveBeenCalledWith({
        where: {
          saved_report_id: { in: mockReportIds },
          account_id: mockAccountId,
        },
        data: deletionData,
      });
    });

    it('Given an empty array, should return true without processing', async () => {
      const result = await service.deleteCompReportsGroups(
        [],
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(prismaClient.$transaction).not.toHaveBeenCalled();
      expect(result).toBe(true);
    });

    it('Given a database error, should throw error when database operation fails', async () => {
      const mockError = new Error('Database error');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.$transaction as any).mockRejectedValueOnce(mockError);

      await expect(
        service.deleteCompReportsGroups(
          [1],
          mockAccountId,
          mockUserId,
          mockOperatingUserId
        )
      ).rejects.toThrow('Database error');
    });
  });

  describe('getExportParamsForSavedReportGroup', () => {
    const mockAccountId = 'acc123';
    const mockRoleId = '1';
    const mockGroupStrId = 'group789';
    const mockExportOptions: ExportOptions = {
      export_type: 'csv',
      amount_due_threshold: '1000',
      individual_reports: false,
      view: CompReportViewTypes.ADMIN_VIEW,
      selected_data: [],
      grouping: 'none',
    };

    const mockGroupData = {
      id: 1,
      template: 'commission_payout',
      saved_reports: [
        {
          id: 101,
          snapshot_data: {
            data: {
              fieldOptions: {
                processing_date_start: '2024-01-01',
                processing_date_end: '2024-01-31',
              },
              totals: {
                agent_commissions: { agent1: 500 },
                commission_amount: 500,
                premium_amount: 10000,
              },
              contactStrId: 'agent1',
            },
          },
          name: 'Report 1',
          page: 'policies',
        },
      ],
      account: {
        white_label_mode: false,
        logo_url: 'logo.png',
        name: 'Test Account',
      },
      page: 'policies',
      created_at: new Date('2024-01-01T00:00:00Z'),
    };

    const mockCompReportSettings = { setting: 'value' };

    beforeEach(() => {
      vi.resetAllMocks();
      service = new SavedReportsGroupsService(undefined, undefined, {
        getCompReportSettings: vi
          .fn()
          .mockResolvedValue(mockCompReportSettings),
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any);

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_report_groups.findUnique as any).mockImplementation(
        () => Promise.resolve(mockGroupData)
      );
    });

    it('Given a group with commission_payout template and policies page, should return correct export params', async () => {
      const result = await service.getExportParamsForSavedReportGroup(
        mockGroupStrId,
        mockAccountId,
        mockRoleId,
        mockExportOptions
      );

      expect(prismaClient.saved_report_groups.findUnique).toHaveBeenCalledWith({
        where: {
          str_id: mockGroupStrId,
          account_id: mockAccountId,
          state: 'active',
        },
        select: expect.any(Object),
      });
      expect(result.table).toBe('report_data');
      expect(result.groupData).toMatchObject({
        ...mockGroupData,
        created_at: expect.anything(),
      });
      expect(result.compReportSettings).toEqual(mockCompReportSettings);
      expect(result.roleForExport).toBe(mockRoleId);
      expect(result.summaryResults.length).toBe(1);
      expect(result.summaryResults[0]['Report name']).toBe('Report 1');
    });

    it('Given a group with commissions page, should set table to statement_data', async () => {
      const groupData = {
        ...mockGroupData,
        saved_reports: [
          { ...mockGroupData.saved_reports[0], page: 'commissions' },
        ],
      };
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_report_groups.findUnique as any).mockResolvedValue(
        groupData
      );

      const result = await service.getExportParamsForSavedReportGroup(
        mockGroupStrId,
        mockAccountId,
        mockRoleId,
        mockExportOptions
      );

      expect(result.table).toBe('statement_data');
    });

    it('Given a group with reconciliation page, should set table to reconciliation_data', async () => {
      const groupData = {
        ...mockGroupData,
        saved_reports: [
          { ...mockGroupData.saved_reports[0], page: 'reconciliation' },
        ],
      };
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_report_groups.findUnique as any).mockResolvedValue(
        groupData
      );

      const result = await service.getExportParamsForSavedReportGroup(
        mockGroupStrId,
        mockAccountId,
        mockRoleId,
        mockExportOptions
      );

      expect(result.table).toBe('reconciliation_data');
    });

    it('Given exportOptions.view is producer_view, should set roleForExport to producer', async () => {
      const options = {
        ...mockExportOptions,
        view: CompReportViewTypes.PRODUCER_VIEW,
      };
      const result = await service.getExportParamsForSavedReportGroup(
        mockGroupStrId,
        mockAccountId,
        mockRoleId,
        options
      );
      expect(result.roleForExport).toBe(Roles.PRODUCER.toString());
    });

    it('Given exportOptions.view is admin_view, should set roleForExport to account_admin', async () => {
      const options = {
        ...mockExportOptions,
        view: CompReportViewTypes.ADMIN_VIEW,
      };
      const result = await service.getExportParamsForSavedReportGroup(
        mockGroupStrId,
        mockAccountId,
        mockRoleId,
        options
      );
      expect(result.roleForExport).toBe('1'); // Roles.ACCOUNT_ADMIN.toString()
    });

    it('Given groupData.template is not commission_payout, should return empty summaryResults', async () => {
      const groupData = { ...mockGroupData, template: 'other_template' };
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_report_groups.findUnique as any).mockResolvedValue(
        groupData
      );

      const result = await service.getExportParamsForSavedReportGroup(
        mockGroupStrId,
        mockAccountId,
        mockRoleId,
        mockExportOptions
      );
      expect(result.summaryResults).toEqual([]);
    });

    it('Given null groupData, should throw', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_report_groups.findUnique as any).mockResolvedValue(
        null
      );

      await expect(
        service.getExportParamsForSavedReportGroup(
          mockGroupStrId,
          mockAccountId,
          mockRoleId,
          mockExportOptions
        )
      ).rejects.toThrow();
    });
  });

  describe('approveCompReports', () => {
    const mockReportsStrIds = ['report1', 'report2'];
    const mockAccountId = 'acc123';
    const mockUserId = 'user456';
    const mockOperatingUserId = 'ouser789';
    const mockReportData = [
      {
        id: 1,
        status: 'pending',
        state: 'active',
        name: 'Report 1',
        snapshot_data: {
          data: {
            contactStrId: 'contact1',
            data: [
              { id: '101', statement: 'data1' },
              { id: '102', statement: 'data2' },
            ],
          },
        },
        contact_id: 1,
        saved_report_group_id: 10,
      },
      {
        id: 2,
        status: 'pending',
        state: 'active',
        name: 'Report 2',
        snapshot_data: {
          data: {
            contactStrId: 'contact2',
            data: [{ id: '103', statement: 'data3' }],
          },
        },
        contact_id: 2,
        saved_report_group_id: 11,
      },
    ];

    const mockAgentCommissionStatuses = [
      { id: 101, agent_commissions_status2: { contact1: 'pending' } },
      { id: 102, agent_commissions_status2: { contact1: 'pending' } },
      { id: 103, agent_commissions_status2: { contact2: 'pending' } },
    ];

    beforeEach(() => {
      vi.resetAllMocks();
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_reports.findMany as any).mockResolvedValue(
        mockReportData
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.statement_data.findMany as any).mockResolvedValue(
        mockAgentCommissionStatuses
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_reports.count as any).mockResolvedValue(0);
    });

    it('Given valid report IDs, should fetch reports and statement data', async () => {
      await service.approveCompReports(
        mockReportsStrIds,
        true,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(prismaClient.saved_reports.findMany).toHaveBeenCalledWith({
        where: {
          str_id: {
            in: mockReportsStrIds,
          },
          account_id: mockAccountId,
        },
        select: {
          id: true,
          status: true,
          state: true,
          name: true,
          snapshot_data: true,
          contact_id: true,
          saved_report_group_id: true,
        },
      });

      expect(prismaClient.statement_data.findMany).toHaveBeenCalledWith({
        where: {
          id: {
            in: [101, 102, 103],
          },
        },
        select: {
          id: true,
          agent_commissions_status2: true,
        },
      });
    });

    it('Given reports with invalid statements, should filter them out from statement IDs', async () => {
      const reportWithInvalidData = [
        {
          ...mockReportData[0],
          snapshot_data: {
            data: {
              contactStrId: 'contact1',
              data: [
                { id: '101', statement: 'data1' },
                null, // Invalid statement
                { id: undefined, statement: 'data2' }, // Invalid ID
                { id: '102', statement: 'data3' },
              ],
            },
          },
        },
      ];

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_reports.findMany as any).mockResolvedValue(
        reportWithInvalidData
      );

      await service.approveCompReports(
        ['report1'],
        true,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(prismaClient.statement_data.findMany).toHaveBeenCalledWith({
        where: {
          id: {
            in: [101, 102], // Only valid IDs should be included
          },
        },
        select: {
          id: true,
          agent_commissions_status2: true,
        },
      });
    });

    it('Given reports with no valid statements, should filter them out from results', async () => {
      const reportWithNoValidData = [
        {
          ...mockReportData[0],
          snapshot_data: {
            data: {
              contactStrId: 'contact1',
              data: [
                null, // Invalid statement
                { id: undefined, statement: 'data2' }, // Invalid ID
              ],
            },
          },
        },
        mockReportData[1], // Valid report
      ];

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_reports.findMany as any).mockResolvedValue(
        reportWithNoValidData
      );

      const result = await service.approveCompReports(
        mockReportsStrIds,
        true,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      // Should only return 1 report (the valid one)
      expect(result).toHaveLength(1);
      expect(result[0].reportId).toBe(2);
    });

    it('Given previewOnly=true, should return approval status without processing', async () => {
      const result = await service.approveCompReports(
        mockReportsStrIds,
        true,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        reportId: 1,
        reportName: 'Report 1',
        reportStatus: 'pending',
        savedReportsGroupId: 10,
        contactStrId: 'contact1',
        includesPaidCommission: false,
        hasConflictingReport: false,
        conflictingReportsData: [],
      });
    });

    it('Given database error, should throw error', async () => {
      const mockError = new Error('Database error');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_reports.findMany as any).mockRejectedValueOnce(
        mockError
      );

      await expect(
        service.approveCompReports(
          mockReportsStrIds,
          true,
          mockAccountId,
          mockUserId,
          mockOperatingUserId
        )
      ).rejects.toThrow('Database error');
    });

    it('Given empty reports array, should return empty results', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_reports.findMany as any).mockResolvedValue([]);
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.statement_data.findMany as any).mockResolvedValue([]);

      const result = await service.approveCompReports(
        [],
        true,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(result).toEqual([]);
    });

    it('Given checkConflictingReports finds no conflicts, should process efficiently', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.saved_reports.count as any).mockResolvedValue(0);

      const result = await service.approveCompReports(
        mockReportsStrIds,
        true,
        mockAccountId,
        mockUserId,
        mockOperatingUserId
      );

      expect(result).toHaveLength(2);
      expect(result[0].hasConflictingReport).toBe(false);
      expect(result[1].hasConflictingReport).toBe(false);
    });
  });
});
