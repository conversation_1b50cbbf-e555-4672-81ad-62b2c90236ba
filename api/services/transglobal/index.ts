import axios, { type AxiosInstance } from 'axios';
import { injectable } from 'inversify';

@injectable()
export class TransGlobalService {
  private _client: AxiosInstance;

  get client() {
    if (!this._client) {
      this._client = axios.create({
        baseURL:
          'https://pznpagt2xzvwyhvhnn34ppbhnu0hratq.lambda-url.us-west-2.on.aws',
        headers: {
          Authorization: `Bearer ${process.env.TRANSGLOBAL_KEY}`,
        },
      });
    }
    return this._client;
  }

  async fetchContacts() {
    const response = await this.client.get('/ipipeline_agents');
    return response.data;
  }

  async fetchAgentHierarchy() {
    const response = await this.client.get('/ipipeline_hierarchies');
    return response.data;
  }

  async fetchCarriersAndProducts() {
    const response = await this.client.get('/ipipeline_carrier_product_new');
    return response.data;
  }

  async fetchPolicies(params: { after_commit_timestamp: string }) {
    const response = await this.client.get(`/ipipeline_policies`, { params });
    return response.data;
  }

  async fetchPolicySplits(params: { after_policy_date?: string }) {
    const response = await this.client.get(
      '/ipipeline_policy_split_percentage_after_policy_date',
      { params }
    );
    return response.data;
  }

  async fetchAgentLevel() {
    const response = await this.client.get('/ipipeline_agent_levels');
    return response.data;
  }
}
