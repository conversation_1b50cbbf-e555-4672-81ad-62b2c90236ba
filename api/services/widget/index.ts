import { faker } from '@faker-js/faker';
import type { accounting_transactions, contacts, Prisma } from '@prisma/client';
import * as Sentry from '@sentry/nextjs';
import BigNumber from 'bignumber.js';
import {
  DEFAULT_FILTER,
  e2eTestWidgetName,
  FieldTypes,
  WidgetGroup,
} from 'common/constants';
import { numberOrDefault } from 'common/helpers';
import { findBestMatchKey } from 'common/helpers/stringSimilarity';
import { inject, injectable, type interfaces } from 'inversify';
import type { DefaultArgs } from '@prisma/client/runtime/library';

import { SERVICE_TYPES } from '@/constants';
import { container } from '@/ioc';
import Formatter from '@/lib/Formatter';
import { getDateBucket, runInBatch, sortInfinityLast } from '@/lib/helpers';
import { get } from '@/lib/helpers/get';
import { matchesFilter } from '@/lib/matcher';
import prisma, { prismaClient } from '@/lib/prisma';
import {
  createAgentListQuery,
  generateAccountingTransactionsWhere,
  generateContactWhere,
  generateCustomersWhere,
  generateReportWhere,
  generateReportWhereCommissionProcessingDate,
  generateReportWhereWithoutDataFilter,
  generateStatementWhere,
  generateWhereBase,
} from '@/lib/prismaUtils';
import { AccountService } from '@/services/account';
import { AccountingTransactionsService } from '@/services/accounting-transactions';
import type { LRUCacheService } from '@/services/cache/lru';
import { ContactService } from '@/services/contact';
import { ReconciliationService } from '@/services/reconciliation';
import { ReportService } from '@/services/report';
import { StatementService } from '@/services/statement';
import { UserService } from '@/services/user';
import { StatusType } from '../api_key/validator';
import type {
  SaveWidgetRequest,
  ServiceCall,
  UpdateWidgetRequest,
  Widget,
} from './types';
import dayjs from '@/lib/dayjs';
import { calculateCommissionFields } from './utils';
import { generateDateClauses } from '@/lib/helpers/generateDateClauses';
import { type WidgetDefinition, DEFAULT_FIELD_VALUE } from 'common/dto/widgets';
import { AccountConfigService } from '@/services/account/config';

@injectable()
export class WidgetService implements IWidgetService {
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private userData: any;
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private accountSettings: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: will address in refactor
  private reconciliationsData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private reportData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private reportDataWithoutDateFilter: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private reportDataCommissionProcessingDate: any;
  private accountingTransactions: (accounting_transactions & {
    contact: contacts | null;
  })[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private customersData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private statementDataByNoDate: Record<string, any>[];
  private statementWhere: Prisma.statement_dataWhereInput;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private mockStatementData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private mockReportData: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private mockReportDataCommissionProcessingDate: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private compensationTypes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private statementTags: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private policyPolicyStatuses: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private policyProductTypes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private commissionProductTypes: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private contactGroups: any;
  private params: {
    role_id;
    uid;
    account_id;
    agent_group;
    unselected_agent;
    agent;
    compensation_type;
    tag;
    policy_status;
    product_type;
    startDate;
    endDate;
    contact_str_id;
    include_blanks;
    timezone;
  };
  private serviceCalls: ServiceCall[] = [];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private contacts: any[];
  @inject(UserService) private userService: UserService;
  @inject(ContactService) private contactService: ContactService;
  @inject(AccountService) private accountService: AccountService;
  @inject(ReconciliationService)
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: will address in refactor
  private reconciliationService: ReconciliationService;
  @inject(ReportService) private reportService: ReportService;
  @inject(StatementService) private statementService: StatementService;
  @inject(AccountingTransactionsService)
  private accountingTransactionsService: AccountingTransactionsService;
  @inject(AccountConfigService)
  private accountConfigService: AccountConfigService;

  private lruServiceFactory = container.get<
    interfaces.SimpleFactory<
      LRUCacheService,
      ConstructorParameters<typeof LRUCacheService>
    >
  >(SERVICE_TYPES.LRUCacheServiceFactory);
  private cacheService = this.lruServiceFactory({
    max: 1000,
  });
  private statementSelectFields = {
    commission_amount: true,
    agent_commissions: true,
    policy_id: true,
    contacts: true,
    carrier_name: true,
    customer_name: true,
    agent_name: true,
    writing_carrier_name: true,
    processing_date: true,
    geo_state: true,
    effective_date: true,
    report: {
      select: {
        product_sub_type: true,
        product_type: true,
        customer_name: true,
        geo_state: true,
        premium_amount: true,
        writing_carrier_name: true,
      },
    },
    premium_amount: true,
    commissionable_premium_amount: true,
    compensation_type: true,
    tags: true,
    account_type: true,
    bill_mode: true,
    carrier_rate: true,
    commission_basis: true,
    commission_rate: true,
    group_name: true,
    notes: true,
    payment_mode: true,
    payment_status: true,
    premium_type: true,
    product_option_name: true,
    product_sub_type: true,
    reconciliation_method: true,
    reconciliation_status: true,
    statement_number: true,
    status: true,
    type: true,
    reconciliation_stats: true,
    standardized_customer_name: true,
    transaction_type: true,
    invoice_date: true,
    payment_date: true,
    product_name: true,
    product_type: true,
    period_date: true,
    issue_age: true,
    member_count: true,
    commission_paid_amount: true,
    commission_rate_percent: true,
    customer_paid_premium_amount: true,
    fees: true,
    new_carrier_rate: true,
    new_commission_rate: true,
    split_percentage: true,
  };
  init(params) {
    this.params = {
      role_id: params.role_id,
      uid: params.uid,
      account_id: params.account_id,
      agent_group: params.agent_group,
      unselected_agent: params.unselected_agent,
      agent: params.agent,
      compensation_type: params.compensation_type,
      tag: params.tag,
      policy_status: params.policy_status,
      product_type: params.product_type,
      startDate: params.startDate,
      endDate: params.endDate,
      contact_str_id: params.contact_str_id,
      include_blanks: params.include_blanks,
      timezone: params.timezone ?? 'UTC',
    };
  }
  getPieOptions(name, data, countOnly = false) {
    return {
      tooltip: {
        trigger: 'item',
        position: [0, 0],
        formatter: countOnly ? `{b0}: {c0} ({d0}%)` : `{b0}: \${c0} ({d0}%)`,
      },
      series: [
        {
          center: ['50%', '55%'],
          name,
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          emphasis: {
            label: {
              show: true,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: true,
          },
          data,
        },
      ],
    };
  }

  getTimeSeriesOptions(
    data,
    opts: { pct?: boolean; type?: 'bar' | 'line'; smooth?: boolean } = {},
    nameMapToDisplay: Map<string, string>
  ) {
    const { pct, type, smooth } = {
      pct: false,
      type: 'bar',
      smooth: true,
      ...opts,
    };
    const sorted = Object.entries(data).sort(
      (a, b) => new Date(a[0]).getTime() - new Date(b[0]).getTime()
    );
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const xAxis = sorted.map(([k, v]) => nameMapToDisplay.get(k) ?? k);
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const yAxis = sorted.map(([k, v]) => {
      if (typeof v !== 'number') {
        return v;
      }
      return (+v)?.toFixed(2);
    });
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const yAxisTotal = sorted.reduce((acc, [k, v]) => acc + +(v ?? 0), 0);
    // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const yAxisPct = sorted.map(([k, v]) => (+v / yAxisTotal) * 100);
    return {
      grid: { containLabel: true },
      tooltip: {
        trigger: 'item',
      },
      xAxis: {
        type: 'category',
        data: xAxis,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 25,
          margin: 10,
          formatter: (value) => {
            if (!value) return '';
            return value.length > 16 ? `${value.substring(0, 15)}...` : value;
          },
        },
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: pct ? yAxisPct : yAxis,
          type: type,
          smooth: smooth,
        },
      ],
    };
  }

  getSeriesOptions(data) {
    // Define fixed colors for each category
    const colorMapping = {
      Medical: '#92cc75',
      Ancillary: '#fac858',
      '401k': '#ed6666',
      Other: '#e97ccc',
      Individual: '#5570c6',
      Medicare: '#73c0de',
      null: '#3ba272',
    };

    const stackSet = new Set();
    const categories = Object.keys(data);

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    categories.forEach((item) => {
      const itemData = data[item];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.keys(itemData).forEach((key) => {
        if (key !== 'sum') {
          stackSet.add(key);
        }
      });
    });

    // Convert the set to an array
    const stackedTypes = Array.from(stackSet);

    // Prepare the series data
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const series = stackedTypes.map((type: any) => ({
      name: type,
      type: 'bar',
      stack: 'stack', // Stack all bars with the same stack ID
      data: categories.map((c) => {
        if (typeof data[c][type] === 'number') {
          return data[c][type]?.toFixed(2) || 0;
        }
        return data[c][type];
      }),
      itemStyle: {
        color: colorMapping[type],
      },
    }));

    return {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: stackedTypes,
      },
      xAxis: {
        data: categories,
        axisLabel: {
          show: true,
          interval: 0,
          rotate: 25,
          margin: 10,
          formatter: (value) => {
            if (!value) return '';
            return value.length > 16 ? `${value.substring(0, 15)}...` : value;
          },
        },
      },
      yAxis: {
        type: 'value',
      },
      series: series,
    };
  }

  async storeWidgetToDB(model: SaveWidgetRequest) {
    const slug = encodeURIComponent(
      model.name.replace(/ /g, '-')?.toLowerCase()
    );
    try {
      const widget = await prisma.widgets.create({
        data: {
          name: model.name,
          account_id: model.access === 'global' ? null : model.account_id,
          slug: slug,
          spec: model.spec,
          access: model.access,
        },
      });
      return widget;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        `Error storing widget to DB with data: ${JSON.stringify(model)}`,
        error
      );
      throw new Error(error);
    }
  }

  async getInsightsPreviewData(accountId: string) {
    // Get all widgets for the account
    const widgets = await this.getWidgetsByAccountId(accountId, []);
    this.mockStatementData = this.createRandomStatementData(500);
    this.mockReportData = this.createRandomReportData(500);
    this.mockReportDataCommissionProcessingDate =
      this.createReportDataCommissionProcessingDate(500);
    const result = [];
    for (const widget of widgets) {
      try {
        const customWidget = await this.createMockCustomWidget(
          accountId,
          widget.spec
        );
        if (customWidget) {
          result.push({ ...customWidget, id: widget.id });
        }
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error('Error creating widget', error, widget.name);
      }
    }

    return {
      widgets: result.filter((widget) => widget !== null),
    };
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async getAccountWidgetList(account_id: string): Promise<any> {
    try {
      const widgets = await prisma.widgets.findMany({
        where: {
          OR: [
            // Either shared or matching account_id
            { access: 'global' },
            { account_id: account_id },
          ],
          state: StatusType.Enum.active, // Must be active in both cases
        },
        accountInject: false,
        select: {
          id: true,
          name: true,
          access: true,
          slug: true,
        },
      });
      if (widgets == null || widgets.length === 0) {
        return null;
      }
      return widgets;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting shared widgets', error);
      throw new Error(error);
    }
  }

  async updateWidgetInDB(model: UpdateWidgetRequest) {
    try {
      const widget = await prisma.widgets.update({
        where: {
          id: model.id,
        },
        data: {
          name: model.name,
          spec: model.spec,
          access: model.access,
        },
      });
      return widget;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        `Error storing widget to DB with data: ${JSON.stringify(model)}`,
        error
      );
      throw new Error(error);
    }
  }

  async deleteWidgetInDB(id: string) {
    try {
      // Check if widget exists
      const widget = await prisma.widgets.findUnique({
        where: {
          id,
        },
      });
      if (!widget) {
        throw new Error(`Widget with id ${id} not found`);
      }
      // Hard delete if widget name is e2eTestWidgetName
      // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (widget.name == e2eTestWidgetName) {
        await prisma.widgets.delete({
          where: {
            id,
          },
        });
        return;
      }
      await prisma.widgets.update({
        where: {
          id,
        },
        data: {
          state: 'deleted',
        },
      });
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error deleting widget in DB with id: ${id}`, error);
      throw new Error(error);
    }
  }

  async getWidgetsByAccountId(
    accountId: string,
    sharedWidgetsIds: number[]
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any> {
    try {
      const filterContition =
        // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        sharedWidgetsIds == null || sharedWidgetsIds.length == 0
          ? {
              access: 'global',
            }
          : {
              id: {
                in: sharedWidgetsIds,
              },
            };
      const widgets = await prisma.widgets.findMany({
        where: {
          OR: [
            {
              account_id: accountId,
              state: 'active',
            },
            filterContition,
          ],
        },
        accountInject: false,
      });
      if (widgets == null || widgets.length === 0) {
        return null;
      }
      return widgets;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Error getting widgets by account id: ${accountId}`, error);
      throw new Error(error);
    }
  }

  async getCustomers(where: Prisma.customersWhereInput) {
    const commonWhere = {
      account_id: this.params.account_id,
      state: 'active',
    };
    return prismaClient.customers.findMany({
      where,
      include: {
        contacts: {
          where: {
            ...commonWhere,
          },
        },
        accounting_transaction_details: {
          where: {
            ...commonWhere,
          },
        },
        report_data: {
          where: {
            ...commonWhere,
          },
        },
      },
    });
  }

  async loadAllDataFromServices() {
    // Check if data is in cache
    const cacheKey = JSON.stringify(this.params);
    const cachedData = this.cacheService.get(`widgetsData-${cacheKey}`);
    if (cachedData) {
      return cachedData;
    }
    const agentGroups =
      Array.isArray(this.params.agent_group) &&
      this.params.agent_group.length > 0
        ? this.params.agent_group
        : this.params.agent_group && !Array.isArray(this.params.agent_group)
          ? [this.params.agent_group]
          : undefined;
    const userData = await this.userService.getUserByUid(this.params.uid);
    const isAdminOrDataSpecialist = this.userService.isAdminOrDataSpecialist(
      userData.account_user_roles
    );

    const whereBase = generateWhereBase(this.params.account_id, userData);
    const contacts = this.params.contact_str_id
      ? [this.params.contact_str_id]
      : agentGroups
        ? await this.contactService.getContactIdsByGroupName(
            whereBase,
            agentGroups
          )
        : undefined;

    let agentList = createAgentListQuery(contacts, this.params.agent);
    // For producer, only show their own data
    if (this.params.contact_str_id) {
      agentList = [{ str_id: this.params.contact_str_id }];
    }
    const unSelectedAgentList = this.params.unselected_agent
      ?.split(',')
      .map((id) => {
        return { str_id: id };
      });
    const reportWhere = generateReportWhere({
      account_id: this.params.account_id,
      userData: userData,
      startDate: this.params.startDate,
      endDate: this.params.endDate,
      policy_status: this.params.policy_status,
      product_type: this.params.product_type,
      agentList: agentList,
      compensation_type: this.params.compensation_type,
      includeBlankDate: this.params.include_blanks,
      unSelectedAgentList,
    });
    const reportWhereWithoutDateFilter = generateReportWhereWithoutDataFilter({
      account_id: this.params.account_id,
      userData: userData,
      policy_status: this.params.policy_status,
      product_type: this.params.product_type,
      compensation_type: this.params.compensation_type,
      agentList: agentList,
      unSelectedAgentList,
    });
    const reportWhereCommissionProcessingDate =
      generateReportWhereCommissionProcessingDate({
        account_id: this.params.account_id,
        userData: userData,
        startDate: this.params.startDate,
        endDate: this.params.endDate,
        product_type: this.params.product_type,
        compensation_type: this.params.compensation_type,
        agentList: agentList,
        includeBlankDate: this.params.include_blanks,
        unSelectedAgentList,
      });
    this.statementWhere = generateStatementWhere({
      account_id: this.params.account_id,
      userData: userData,
      product_type: this.params.product_type,
      compensation_type: this.params.compensation_type,
      tag: this.params.tag,
      agentList: agentList,
      unSelectedAgentList,
    });

    const accountingTransactionsWhere = generateAccountingTransactionsWhere({
      account_id: this.params.account_id,
      agentList: agentList,
      unSelectedAgentList,
    });

    const contactsWhere = generateContactWhere({
      account_id: this.params.account_id,
      agentList: agentList,
      unSelectedAgentList,
    });

    const customersWhere = generateCustomersWhere({
      account_id: this.params.account_id,
      agentList: agentList,
      unSelectedAgentList,
    });

    const reportInclude: Prisma.report_dataInclude<DefaultArgs> = {
      statement_data: {
        select: {
          commission_amount: true,
        },
      },
    };

    const result = await Promise.all([
      this.getAgentCommissions(whereBase),
      this.getAccountById(),
      this.getContactGroups(this.params.account_id),
      this.getAgentCommissionsNames(whereBase),
      this.getReportData(reportWhere, reportInclude),
      this.getReportDataComisionProcessingDate(
        reportWhereCommissionProcessingDate
      ),
      this.getStatementData(
        { ...this.statementWhere },
        'processing_date',
        this.params.startDate,
        this.params.endDate,
        this.params.include_blanks
      ),
      this.getStatementDataGroupByPolicyAgentName(whereBase),
      this.getStatementDataGroupByCompensationType(whereBase),
      this.getReportDataGroupByPolicyStatus(whereBase),
      this.getReportDataGroupByProductType(whereBase),
      this.getStatementDataGroupByProductType(whereBase),
      this.getStatementTags(whereBase),
      this.getAccountingTransactions(accountingTransactionsWhere),
      this.getContacts(contactsWhere),
      this.getReportData(reportWhereWithoutDateFilter, reportInclude),
      this.getCustomers(customersWhere),
      this.getStatementDataByNoDate({ ...this.statementWhere }),
    ]);

    this.cacheService.set(
      `widgetsData-${cacheKey}-isAdminOrDataSpecialist`,
      isAdminOrDataSpecialist
    );
    return result;
  }

  setAllDataFromServices(result) {
    if (!result) {
      return;
    }
    this.accountSettings = result[1];
    this.contactGroups = result[2];
    this.reportData = result[4];
    this.reportDataCommissionProcessingDate = result[5];
    this.compensationTypes = result[8];
    this.policyPolicyStatuses = result[9];
    this.policyProductTypes = result[10];
    this.commissionProductTypes = result[11];
    this.statementTags = result[12];
    this.accountingTransactions = result[13];
    this.contacts = result[14];
    this.reportDataWithoutDateFilter = result[15];
    this.customersData = result[16];
    this.statementDataByNoDate = result[17];
  }

  async getWidgets(
    accountId: string,
    widgetsToLoad: string[],
    loadFilters: boolean = true
  ) {
    const result: Widget[] = [];
    let producerOnlyData = false;
    // If this.params.contact_str_id is set, and this.params.agent is set and agent list does not include this.params.contact_str_id, return empty array
    if (this.params.contact_str_id && this.params.agent) {
      // Convert agent to array if it is not
      const agentList = Array.isArray(this.params.agent)
        ? this.params.agent
        : [this.params.agent];
      if (!agentList.includes(this.params.contact_str_id)) {
        producerOnlyData = true;
      }
    }
    const serviceData = await this.loadAllDataFromServices();
    this.setAllDataFromServices(serviceData);

    for (const widgetId of widgetsToLoad) {
      if (widgetId == null) {
        continue;
      }
      const widget = await this.getWidgetSettingsById(widgetId);
      if (!widget || widget?.state === 'deleted') {
        continue;
      }
      const customWidget = await this.createCustomWidget(
        accountId,
        widget.spec
      );
      if (customWidget) {
        result.push({ ...customWidget, id: widget.id, access: widget.access });
      }
    }

    return {
      widgets: producerOnlyData ? [] : result,
      dashboard_filter_by_agent: this.cacheService.get(
        `widgetsData-${JSON.stringify(this.params)}-isAdminOrDataSpecialist`
      ),
      filters: loadFilters ? this.buildFilterValues() : undefined,
    };
  }

  buildFilterValues() {
    const compensationTypes = this.compensationTypes;
    const tags = this.statementTags;
    const policyPolicyStatuses = this.policyPolicyStatuses;
    const policyProductTypes = this.policyProductTypes;
    const commissionProductTypes = this.commissionProductTypes;

    const compensationType = [
      DEFAULT_FILTER.BLANK_OPTION,
      ...compensationTypes.map((data) => data.compensation_type).sort(),
    ];

    const tag = [
      DEFAULT_FILTER.BLANK_OPTION,
      ...tags.map((data) => data.tag).sort(),
    ];

    const policyStatus = [
      DEFAULT_FILTER.BLANK_OPTION,
      ...new Set([...policyPolicyStatuses.map((row) => row.policy_status)]),
    ]
      .filter((val) => val)
      .sort();

    const productType = [
      DEFAULT_FILTER.BLANK_OPTION,
      ...new Set([
        ...policyProductTypes.map((row) => row.product_type),
        ...commissionProductTypes.map((row) => row.product_type),
      ]),
    ]
      .filter((val) => val)
      .sort();

    const agentGroup = this.contactGroups.map((row) => {
      return {
        id: row.id,
        name: row.name,
      };
    });

    return {
      compensationType,
      policyStatus,
      productType,
      agentGroup,
      tag,
    };
  }

  async getWidgetById(accountId: string, widgetId: string) {
    const result = await this.getWidgets(accountId, [widgetId], false);
    return result?.[0] ?? null;
  }

  async getWidgetSettingsById(id: string) {
    const widget = await prisma.widgets.findUnique({
      where: {
        id,
      },
    });
    return widget;
  }

  async createWidget(accountId: string, definition: WidgetDefinition) {
    const result = await this.loadAllDataFromServices();
    this.setAllDataFromServices(result);
    return this.createCustomWidget(accountId, definition);
  }

  async createCustomWidget(
    accountId: string,
    definition: WidgetDefinition
  ): Promise<Widget | null> {
    // biome-ignore lint/suspicious/noExplicitAny: Waiting for refactor
    let source: Record<string, any>[] | null = null;
    switch (definition?.dataSource) {
      case 'commissions':
        if (definition?.filterByDate === 'none') {
          source = this.statementDataByNoDate;
        } else {
          source = await this.getStatementData(
            { ...this.statementWhere },
            definition?.filterByDate || 'processing_date',
            this.params.startDate,
            this.params.endDate,
            this.params.include_blanks
          );
        }
        break;
      case 'policies':
        if (definition?.filterByDate === 'commission_processing_date') {
          source = this.reportDataCommissionProcessingDate;
        } else if (definition?.filterByDate === 'none') {
          source = this.reportDataWithoutDateFilter;
        } else {
          source = this.reportData;
        }

        source = source.map((row) => {
          const commissionFields = calculateCommissionFields(
            row.statement_data
          );

          const item = {
            ...row,
            ...commissionFields,
          };

          return item;
        });
        break;
      case 'agentPayouts':
        source = this.accountingTransactions.map((transaction) => {
          return {
            ...transaction,
            contact_name: transaction.contact
              ? `${Formatter.contact(transaction.contact)}`
              : null,
            date_ymd: transaction.date
              ? dayjs(transaction.date)
                  .tz(this.params.timezone)
                  .format('YYYY-MM-DD')
              : null,
          };
        });
        break;
      case 'contacts':
        // TODO: Add filterByDate logic for contacts
        // if (definition?.filterByDate === 'start_date') {
        //   source = this.contacts;
        // } else if (definition?.filterByDate === 'end_date') {
        //   source = this.contacts;
        // } else if (definition?.filterByDate === 'none') {
        //   source = this.contacts;
        // } else {
        //   source = this.contacts;
        // }
        source = this.contacts;
        source = source.map((contact) => {
          return {
            ...contact,
            name: Formatter.contact(contact),
          };
        });
        break;
      case 'customers':
        source = this.customersData.map((customer) => {
          const item = {
            ...customer,
          };

          const aggregationSelectors = definition?.aggregationSelectors;
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          aggregationSelectors?.forEach((selector) => {
            switch (selector.field) {
              case 'contacts_balance':
                item.contacts_balance = customer.contacts.reduce(
                  (acc, contact) => acc + +contact.balance,
                  0
                );
                break;
              case 'accounting_transaction_details_amount':
                item.accounting_transaction_details_amount =
                  customer.accounting_transaction_details.reduce(
                    (acc, transaction) => acc + +transaction.amount,
                    0
                  );
                break;
              case 'report_data_commissionable_premium_amount':
                item.report_data_commissionable_premium_amount =
                  customer.report_data.reduce(
                    (acc, policy) =>
                      acc + +policy.commissionable_premium_amount,
                    0
                  );
                break;
              case 'report_data_premium_amount':
                item.report_data_premium_amount = customer.report_data.reduce(
                  (acc, policy) => acc + +policy.premium_amount,
                  0
                );
                break;
              case 'report_data_commissions_expected':
                item.report_data_commissions_expected =
                  customer.report_data.reduce(
                    (acc, policy) => acc + +policy.commissions_expected,
                    0
                  );
                break;
              case 'report_data_customer_paid_premium_amount':
                item.report_data_customer_paid_premium_amount =
                  customer.report_data.reduce(
                    (acc, policy) => acc + +policy.customer_paid_premium_amount,
                    0
                  );
                break;
              case 'report_data_split_percentage':
                item.report_data_split_percentage = customer.report_data.reduce(
                  (acc, policy) => acc + +policy.split_percentage,
                  0
                );
                break;
              case 'report_data_issue_age':
                item.report_data_issue_age = customer.report_data.reduce(
                  (acc, policy) => acc + +policy.issue_age,
                  0
                );
                break;
              case 'report_data_policy_term_months':
                item.report_data_policy_term_months =
                  customer.report_data.reduce(
                    (acc, policy) => acc + +policy.policy_term_months,
                    0
                  );
                break;
              default:
                break;
            }
          });

          return item;
        });
        break;
      default:
        break;
    }

    if (!source) {
      return null;
    }
    let result = null;

    source = this.filterMatcher(source, definition.filters);

    try {
      switch (definition.type) {
        case 'box':
          result = await this.createBoxWidget(accountId, definition, source);
          break;
        case 'chart-donut':
          result = await this.createDonutChartWidget(definition, source);
          break;
        case 'chart-line':
          result = await this.createLineChartWidget(definition, source);
          break;
        case 'chart-bar':
          result = await this.createBarChartWidget(definition, source);
          break;
        case 'table':
          result = await this.createTableWidget(definition, source);
          break;
        default:
          return null;
      }
    } catch (e) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error creating widget', e);
      Sentry.captureException(e);
    }
    return { ...result, spec: definition, rowCount: source.length ?? 0 };
  }

  createRandomReportData = (number) => {
    return Array.from({ length: number }, () => ({
      policy_id: faker.string.uuid(),
      policy_status: faker.helpers.arrayElement([
        'Active',
        'Cancelled',
        'Expired',
        'Pending',
      ]),
      product_type: faker.helpers.arrayElement([
        'Medical',
        'Ancillary',
        '401k',
        'Other',
        'Individual',
        'Medicare',
      ]),
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };

  createRandomReconciliationData = (number) => {
    return Array.from({ length: number }, () => ({
      policy_id: faker.string.uuid(),
      policy_status: faker.helpers.arrayElement([
        'Active',
        'Cancelled',
        'Expired',
        'Pending',
      ]),
      product_type: faker.helpers.arrayElement([
        'Medical',
        'Ancillary',
        '401k',
        'Other',
        'Individual',
        'Medicare',
      ]),
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };

  createReportDataCommissionProcessingDate = (number) => {
    return Array.from({ length: number }, () => ({
      policy_id: faker.string.uuid(),
      policy_status: faker.helpers.arrayElement([
        'Active',
        'Cancelled',
        'Expired',
        'Pending',
      ]),
      product_type: faker.helpers.arrayElement([
        'Medical',
        'Ancillary',
        '401k',
        'Other',
        'Individual',
        'Medicare',
      ]),
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };

  createRandomStatementData = (number) => {
    return Array.from({ length: number }, () => ({
      agent_name: faker.person.fullName(),
      agent_commissions: faker.number.int(1000),
      commission_amount: faker.number.int(1000),
      contacts: Array.from(
        { length: faker.number.int({ min: 1, max: 10 }) },
        () => faker.lorem.word()
      ),
      carrier_name: faker.company.name(),
      customer_name: faker.person.fullName(),
      writing_carrier_name: faker.company.name(),
      processing_date: faker.date.past(),
      geo_state: faker.location.state(),
      effective_date: faker.date.past(),
      report: {
        product_sub_type: faker.lorem.word(),
      },
      premium_amount: faker.number.int(1000),
      commissionable_premium_amount: faker.number.int(1000),
      compensation_type: faker.lorem.word(),
      tags: faker.lorem.word(),
    }));
  };
  async createMockCustomWidget(
    accountId: string,
    definition: WidgetDefinition
  ) {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let source;
    switch (definition?.dataSource) {
      case 'commissions':
        source = this.mockStatementData;
        break;
      case 'policies':
        source = this.mockReportData;
        if (definition.filterByDate === 'commission_processing_date') {
          source = this.mockReportDataCommissionProcessingDate;
        }
        break;
      default:
        break;
    }
    if (!source) {
      return null;
    }
    let result = null;

    source = this.filterMatcher(source, definition.filters);

    switch (definition.type) {
      case 'box':
        result = await this.createBoxWidget(accountId, definition, source);
        break;
      case 'chart-donut':
        result = await this.createDonutChartWidget(definition, source);
        break;
      case 'chart-line':
        result = await this.createLineChartWidget(definition, source);
        break;
      case 'chart-bar':
        result = await this.createBarChartWidget(definition, source);
        break;
      case 'table':
        result = await this.createTableWidget(definition, source);
        break;
      default:
        return null;
    }
    return { ...result, spec: definition };
  }

  filterMatcher(source, filters) {
    if (!filters || filters.length === 0) {
      return source;
    }
    source = source.filter((row) => matchesFilter(row, filters));
    return source;
  }

  mergeAgentCommissions(data) {
    const agentCommissions = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((item) => {
      if (item.agent_commissions) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.entries(item.agent_commissions).forEach(([key, value]) => {
          if (!agentCommissions[key]) {
            agentCommissions[key] = 0;
          }
          agentCommissions[key] += numberOrDefault(value);
        });
      }
    });
    return agentCommissions;
  }

  async calculateCommissionsBasedOnAgentType(
    data,
    calculationType,
    initialValue,
    agentType
  ) {
    const agent_commissions = this.mergeAgentCommissions(data);

    const agentsInAgentCommissionsWithoutTotal = Object.keys(
      agent_commissions
    ).filter((key) => key !== 'total');
    const agentsList =
      await this.contactService.getFilteredAgentListByAgentType(
        agentsInAgentCommissionsWithoutTotal,
        agentType
      );
    if (calculationType === 'sum') {
      return agentsList.reduce((acc, cur) => {
        const agentCommissions = agent_commissions[cur];
        if (agentCommissions) {
          return acc + numberOrDefault(agentCommissions);
        }
        return acc;
      }, 0);
    }
    if (calculationType === 'aggregate') {
      return agentsList.reduce((acc, cur) => {
        const agentCommissions = agent_commissions[cur];
        if (agentCommissions) {
          const curValue = Object.entries(agentCommissions ?? {})
            // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            .filter(([k, v]) => k !== 'total')
            // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            .reduce((acc, [k, v]) => acc + +v, 0);
          return curValue + acc;
        }
        return acc;
      }, initialValue);
    }
  }

  async createBoxWidget(
    accountId: string,
    definition: WidgetDefinition,
    source
  ): Promise<Widget> {
    const data = source;
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let value;
    const { dataField, aggregationMethod, resultFormatter } =
      this.getPropsFromDefinition(definition);
    const featureFlags = await this.accountConfigService.getConfigByType(
      accountId,
      'feature-flags'
    );
    let agentIds: string[] = [];
    if (
      !Array.isArray(featureFlags) &&
      typeof featureFlags === 'object' &&
      featureFlags?.agentIds &&
      Array.isArray(featureFlags.agentIds) &&
      featureFlags?.includeExcludedAgents
    ) {
      agentIds = featureFlags.agentIds as string[];
    }
    if (definition.customCode) {
      value = await this.executeCustomCode(
        data,
        definition.customCode,
        this,
        agentIds
      );
    } else {
      switch (aggregationMethod) {
        case 'Sum':
          if (dataField === 'agent_commissions.agents_only') {
            value = await this.calculateCommissionsBasedOnAgentType(
              data,
              'sum',
              0,
              'Agent'
            );
          } else if (dataField === 'agent_commissions.sales_reps_only') {
            value = await this.calculateCommissionsBasedOnAgentType(
              data,
              'sum',
              0,
              'Sales rep'
            );
          } else {
            value = data.reduce((acc, cur) => {
              let fieldValue = numberOrDefault(get(cur, dataField));
              if (dataField === 'agent_commissions') {
                fieldValue = numberOrDefault(cur.agent_commissions?.total);
              }
              return acc + fieldValue;
            }, 0);
          }
          break;
        case 'Average':
          value = data.reduce((acc, cur) => {
            return acc + numberOrDefault(get(cur, dataField));
          }, 0);
          value = value / data.length;
          break;
        case 'Count':
          value = data.length;
          break;
        case 'Aggregate':
          value = data.reduce((acc, cur) => {
            const curValue = Object.entries(get(cur, dataField) ?? {})
              // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              .filter(([k, v]) => k !== 'total')
              // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              .reduce((acc, [k, v]) => acc + +v, 0);

            if (
              dataField === 'agent_commissions' &&
              typeof featureFlags === 'object' &&
              !Array.isArray(featureFlags) &&
              featureFlags?.includeExcludedAgents
            ) {
              const adjCommission = agentIds.reduce((acc, agentId) => {
                return acc + numberOrDefault(cur.agent_commissions?.[agentId]);
              }, 0);
              return curValue - adjCommission + acc;
            }
            return curValue + acc;
          }, 0);
          break;
        default:
          break;
      }
    }
    if (resultFormatter === FieldTypes.CURRENCY) {
      value = Formatter.currency(value);
    }
    return {
      widgetGroup: WidgetGroup.BOX,
      displayName: definition.name,
      value: value,
      type: 'h5',
      enabled: true,
    };
  }

  async executeCustomCode(
    dataSource,
    customCode,
    context = null,
    agentIds = []
  ) {
    if (!dataSource || !customCode) {
      return null;
    }

    try {
      // Remove comments (both single-line and multi-line)
      const codeWithoutComments = customCode
        .replace(/\/\/.*|\/\*[\s\S]*?\*\//g, '')
        .trim();
      // Check if code without comments have await keyword
      const isAsync = codeWithoutComments.includes('await');
      // Conditionally create the function based on the isAsync flag
      const code = isAsync
        ? new Function(
            'data',
            'ALEVO_EXCLUDED_AGENTS',
            'numberOrDefault',
            'getDateBucket',
            'sortInfinityLast',
            `return (async () => { ${codeWithoutComments} })();`
          )
        : new Function(
            'data',
            'ALEVO_EXCLUDED_AGENTS',
            'numberOrDefault',
            'getDateBucket',
            'sortInfinityLast',
            `return ${codeWithoutComments};`
          );

      // Execute the function with the provided context (`this` value)
      const result = isAsync
        ? await code.call(
            context,
            dataSource,
            agentIds,
            numberOrDefault,
            getDateBucket,
            sortInfinityLast
          )
        : code.call(
            context,
            dataSource,
            agentIds,
            numberOrDefault,
            getDateBucket
          );
      return result;
    } catch {
      return null;
    }
  }

  getPropsFromDefinition(definition) {
    let dataField = Array.isArray(definition.dataField)
      ? definition.dataField[0]
      : definition.dataField;
    let aggregationMethod = definition.calculation;
    let resultFormatter = definition.resultFormatter;
    if (
      definition.aggregationSelectors?.length > 0 &&
      definition.aggregationSelectors[0]?.field !== DEFAULT_FIELD_VALUE
    ) {
      dataField = definition.aggregationSelectors[0]?.field;
      aggregationMethod =
        definition.aggregationSelectors[0]?.aggregation_method;
      resultFormatter = definition.aggregationSelectors[0]?.formatter;
    }
    if (
      definition.aggregationSelectors &&
      definition.aggregationSelectors.length > 0
    ) {
      // If dataFieldsExpression is present, we should use the first field from aggregationSelectors
      dataField ||= definition.aggregationSelectors[0]?.field;
      aggregationMethod ||=
        definition.aggregationSelectors[0]?.aggregation_method;
      resultFormatter ||= definition.aggregationSelectors[0]?.formatter;
    }

    return { dataField, aggregationMethod, resultFormatter };
  }

  async createDonutChartWidget(definition, source): Promise<Widget> {
    const { dataField, aggregationMethod, resultFormatter } =
      this.getPropsFromDefinition(definition);
    const data = source;
    const isNumberFormatted = this.isNumberFormat(resultFormatter);
    const isCurrencyFormatted = this.isCurrencyFormat(resultFormatter);
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let groupedData;

    const nameMapToDisplay = new Map();

    if (definition.customCode) {
      groupedData = await this.executeCustomCode(
        data,
        definition.customCode,
        this
      );
    } else {
      switch (aggregationMethod) {
        case 'Sum':
          groupedData = data.reduce((acc, cur) => {
            if (definition.uniqueKey) {
              nameMapToDisplay.set(
                get(cur, definition.uniqueKey)?.toString(),
                get(cur, definition.groupBy)
              );
            }
            const valueGroup = get<string>(
              cur,
              definition.uniqueKey ?? definition.groupBy
            );
            const requireSimilarityGroupBy = definition.similarityGroupBy;
            const bestMatchKey = requireSimilarityGroupBy
              ? findBestMatchKey(acc, valueGroup)
              : valueGroup;

            if (definition.dataFieldsExpression) {
              const value = this.calculateValueFromExpression(
                definition.dataFieldsExpression,
                cur
              );
              // Find similar vaueGroup in acc[valueGroup] and add the value
              acc[bestMatchKey] =
                (acc[bestMatchKey] ?? 0) + BigNumber(value).toNumber();
            } else {
              acc[bestMatchKey] =
                (acc[bestMatchKey] ?? 0) +
                BigNumber(get(cur, dataField) || 0).toNumber();
            }
            return acc;
          }, {});
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          Object.keys(groupedData).forEach((key) => {
            groupedData[key] = (+groupedData[key])?.toFixed(2);
          });
          break;
        case 'Count':
          groupedData = data.reduce((acc, cur) => {
            if (definition.uniqueKey) {
              nameMapToDisplay.set(
                get(cur, definition.uniqueKey)?.toString(),
                get(cur, definition.groupBy)
              );
            }
            const valueGroup = get<string>(
              cur,
              definition.uniqueKey ?? definition.groupBy
            );
            const requireSimilarityGroupBy = definition.similarityGroupBy;
            const bestMatchKey = requireSimilarityGroupBy
              ? findBestMatchKey(acc, valueGroup)
              : valueGroup;
            if (!acc[bestMatchKey]) {
              acc[bestMatchKey] = 1;
            } else {
              acc[bestMatchKey] =
                BigNumber(acc[bestMatchKey] ?? 0).toNumber() + 1;
            }
            return acc;
          }, {});
          break;
        default:
          break;
      }
    }
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let contactNameIdMap;
    if (definition.groupBy === 'contacts') {
      contactNameIdMap = await this.getContactNameIdMap(groupedData);
      groupedData = Object.entries(groupedData).reduce((acc, [k, v]) => {
        const realName = this.convertContactsKeyToName(k, contactNameIdMap);
        acc[realName] = v;
        return acc;
      }, {});
    }

    if (definition.sortingField) {
      groupedData = this.sortingPick(groupedData, definition.sortingField);
    }

    const chartData = Object.entries(groupedData || {}).map(([k, v]) => {
      return {
        name: nameMapToDisplay.get(k) ?? k,
        value: isNumberFormatted
          ? +v
          : isCurrencyFormatted
            ? (+v)?.toFixed(2)
            : v,
      };
    });
    return {
      widgetGroup: WidgetGroup.CHART,
      displayName: definition.name,
      value: this.getPieOptions(
        definition.displayName,
        chartData,
        isNumberFormatted
      ),
      type: 'chart-donut',
      enabled: true,
    };
  }

  sortingPick(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    groupedData: Record<string, any>,
    sortingField: {
      order: 'asc' | 'desc';
      limit: number;
      field?: string;
    }
  ) {
    const { order, limit, field } = sortingField;

    const entries = Object.entries(groupedData).map(([key, value]) => {
      const sortValue =
        field && typeof value === 'object' && value !== null
          ? value[field]
          : value;

      return {
        key,
        value: value,
        numericValue: this.parseNumericValue(sortValue),
      };
    });

    entries.sort((a, b) => {
      if (order === 'asc') {
        return a.numericValue - b.numericValue;
      } else {
        return b.numericValue - a.numericValue;
      }
    });

    const topN = entries.slice(0, limit);

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const result: Record<string, any> = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    topN.forEach((entry) => {
      result[entry.key] = groupedData[entry.key];
    });
    return result;
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  parseNumericValue(value: any): number {
    if (typeof value === 'number') return value;
    if (typeof value !== 'string') return 0;

    const numericString = value.replace(/[^0-9.-]/g, '');
    const parsed = parseFloat(numericString);

    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    return isNaN(parsed) ? 0 : parsed;
  }

  async getContactNameIdMap(groupedData) {
    const contactIds = Object.keys(groupedData);
    const flattenedContactIds = contactIds.flatMap((contactId) =>
      contactId.split(',')
    );
    const result =
      await this.contactService.getBatchContactNamesByStrIds(
        flattenedContactIds
      );
    const contactNameIdMap = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    result.forEach((item) => {
      // Separate by ::
      const contactId = item.split('::')[0];
      const contactName = item.split('::')[1];
      contactNameIdMap[contactId] = contactName;
    });
    return contactNameIdMap;
  }

  async getContactNameIdMapFromNested(groupedData) {
    const topLevelKeys = Object.keys(groupedData);

    const allContactIds = topLevelKeys.flatMap((key) =>
      Object.keys(groupedData[key])
    );

    const flattenedContactIds = allContactIds.flatMap((contactId) =>
      contactId.split(',')
    );

    const result =
      await this.contactService.getBatchContactNamesByStrIds(
        flattenedContactIds
      );
    const contactNameIdMap = {};
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    result.forEach((item) => {
      const [contactId, contactName] = item.split('::');
      contactNameIdMap[contactId] = contactName;
    });

    return contactNameIdMap;
  }

  isNumberFormat(formatter) {
    return formatter === 'number';
  }

  isCurrencyFormat(formatter) {
    return formatter === FieldTypes.CURRENCY;
  }

  calculateValueFromExpression(dataFieldsExpression, cur) {
    const fields = dataFieldsExpression.split(/[+*/-]/);
    const operator = dataFieldsExpression.match(/[+*/-]/)[0];

    if (operator === '*' || operator === '/') {
      const initialValue =
        operator === '*' ? 1 : numberOrDefault(cur[fields[0]]);
      return fields.reduce((result, field, index) => {
        if (operator === '*' && index === 0)
          return result * numberOrDefault(cur[field]);
        if (operator === '/' && index === 0) return result;
        return operator === '*'
          ? result * numberOrDefault(cur[field])
          : result / numberOrDefault(cur[field]);
      }, initialValue);
    } else {
      return fields.reduce((sum, field) => {
        return operator === '+'
          ? sum + numberOrDefault(cur[field])
          : sum - numberOrDefault(cur[field]);
      }, 0);
    }
  }

  async buildResultMap(definition, source) {
    const data = source;
    let resultMap = {};
    if (definition.customCode) {
      resultMap = await this.executeCustomCode(
        data,
        definition.customCode,
        this
      );
    }
    const timePeriod = definition.timePeriod || 'month';
    let total = 0;
    const { dataField, aggregationMethod } =
      this.getPropsFromDefinition(definition);
    const nameMapToDisplay = new Map();

    const handlers = {
      SumAccumulate: (value, dateBucket) => {
        total += value;
        resultMap[dateBucket] = value + total;
      },
      Aggregate: (value, dateBucket) => {
        total += value;
        resultMap[dateBucket] = value + total;
      },
      Sum: (value, dateBucket) => {
        resultMap[dateBucket] = (resultMap[dateBucket] ?? 0) + value;
      },
      Count: (_value, dateBucket) => {
        resultMap[dateBucket] =
          BigNumber(resultMap[dateBucket] || 0).toNumber() + 1;
      },
      CountAccumulate: (_value, dateBucket) => {
        resultMap[dateBucket] = (resultMap[dateBucket] ?? 0) + 1 + total;
        total += 1;
      },
    };

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data?.forEach((row) => {
      const valueGroup = get<string>(
        row,
        definition.uniqueKey ?? definition.groupBy
      );
      if (definition.uniqueKey) {
        nameMapToDisplay.set(
          get(row, definition.uniqueKey)?.toString(),
          get(row, definition.groupBy)
        );
      }
      const dateBucket = getDateBucket(valueGroup, timePeriod) || valueGroup;
      if (dateBucket) {
        const value = definition.dataFieldsExpression
          ? this.calculateValueFromExpression(
              definition.dataFieldsExpression,
              row
            )
          : BigNumber(get(row, dataField) || 0).toNumber();
        handlers[aggregationMethod]?.(value, dateBucket);
      }
    });
    if (definition.groupBy === 'contacts') {
      const contactNameIdMap = await this.getContactNameIdMap(resultMap);
      resultMap = Object.entries(resultMap).map(([k, v]) => {
        const realName = this.convertContactsKeyToName(k, contactNameIdMap);
        return [realName, v];
      });
    }

    return { resultMap, nameMapToDisplay };
  }

  async createLineChartWidget(definition, source): Promise<Widget> {
    const { resultMap, nameMapToDisplay } = await this.buildResultMap(
      definition,
      source
    );
    return {
      widgetGroup: WidgetGroup.CHART,
      displayName: definition.name,
      value: this.getTimeSeriesOptions(
        resultMap,
        { type: 'line' },
        nameMapToDisplay
      ),
      type: 'chart-line',
      enabled: true,
    };
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async createStackedBarChartWidget(definition: WidgetDefinition, source: any) {
    const data = source;
    const { dataField } = this.getPropsFromDefinition(definition);
    const timePeriod = definition.timePeriod || 'month';
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let resultMap: any = Object.entries(
      data.reduce((result, row) => {
        let legendKey = get<string>(row, definition.column);
        let groupByKey = get<string>(row, definition.groupBy);
        if (definition.groupBy.includes('date')) {
          const dateBucket = getDateBucket(groupByKey, timePeriod);
          if (dateBucket) {
            groupByKey = dateBucket;
          }
        }
        const dataFieldKey = get(row, dataField);
        if (!result[groupByKey]) {
          result[groupByKey] = { sum: 0 };
        }
        if (!legendKey) {
          legendKey = DEFAULT_FILTER.BLANK_OPTION;
        }
        if (!result[groupByKey][legendKey]) {
          result[groupByKey][legendKey] = 0;
        }
        if (dataField) {
          result[groupByKey][legendKey] += numberOrDefault(dataFieldKey);
          result[groupByKey].sum += numberOrDefault(dataFieldKey);
        } else {
          result[groupByKey][legendKey] += 1;

          result[groupByKey].sum += 1;
        }

        return result;
      }, {})
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    ).sort((a: any, b: any) => b[1].sum - a[1].sum);
    if (definition.columnLimit) {
      resultMap = resultMap.slice(0, definition.columnLimit);
    }
    resultMap = resultMap.reduce((r, [groupByKey, data]) => {
      r[groupByKey] = data;
      return r;
    }, {});
    if (definition.groupBy === 'contacts') {
      const contactNameIdMap = await this.getContactNameIdMap(resultMap);
      resultMap = Object.entries(resultMap).map(([k, v]) => {
        const realName = this.convertContactsKeyToName(k, contactNameIdMap);
        return [realName, v];
      });
    }

    if (definition.column === 'contacts') {
      const contactNameIdMap =
        await this.getContactNameIdMapFromNested(resultMap);
      resultMap = Object.entries(resultMap).reduce((acc, [y, innerObj]) => {
        acc[y] = Object.entries(innerObj).reduce((innerAcc, [xx, value]) => {
          const realName = contactNameIdMap[xx] || xx;
          innerAcc[realName] = value;
          return innerAcc;
        }, {});
        return acc;
      }, {});
    }
    return resultMap;
  }

  convertContactsKeyToName(key, contactNameIdMap) {
    const contactIds = key.split(',');
    const contactNames = contactIds.map(
      (contactId) => contactNameIdMap[contactId]
    );
    return contactNames.join(',');
  }

  async createBarChartWidget(definition, source): Promise<Widget> {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let groupedData;
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let nameMapToDisplayGroupedData;
    if (definition.column) {
      groupedData = await this.createStackedBarChartWidget(definition, source);
    } else {
      const { resultMap, nameMapToDisplay } = await this.buildResultMap(
        definition,
        source
      );
      groupedData = resultMap;
      nameMapToDisplayGroupedData = nameMapToDisplay;
      if (definition.sortingField) {
        groupedData = this.sortingPick(groupedData, definition.sortingField);
      }
    }
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let options;
    if (this.isMultiLevelObj(groupedData)) {
      options = this.getSeriesOptions(groupedData);
    } else {
      options = this.getTimeSeriesOptions(
        groupedData,
        { type: 'bar' },
        nameMapToDisplayGroupedData
      );
    }
    return {
      widgetGroup: WidgetGroup.CHART,
      displayName: definition.name,
      value: options,
      type: 'chart-bar',
      enabled: true,
    };
  }

  isMultiLevelObj(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return false;
    }

    for (const key in obj) {
      if (typeof obj[key] === 'object' && obj[key] !== null) {
        return true;
      }
    }

    return false;
  }

  async createTableWidget(definition, source): Promise<Widget> {
    // Extract dataField, for each if it contains a '.' then it is a lookup
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let dataColumns;
    if (definition.aggregationSelectors?.length > 0) {
      dataColumns = definition.aggregationSelectors?.map((selector) => {
        const field = selector.field;
        if (field.includes('.')) {
          const fieldParts = field.replace('?', '').split('.');
          return fieldParts[0];
        }
        return field;
      });
    } else {
      dataColumns = [definition.dataFieldsExpression];
    }

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let tableArray: any = [[definition.groupBy, ...dataColumns]];
    if (definition.dataFieldsExpression) {
      tableArray = [[definition.groupBy, definition.dataFieldsExpression]];
    }
    let resultMap = {};
    if (definition.customCode) {
      resultMap = await this.executeCustomCode(
        source,
        definition.customCode,
        this
      );
      if (!resultMap) {
        return null;
      }
      tableArray = resultMap;
    } else {
      const nameMapToDisplay = new Map();
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      source.forEach((row) => {
        const key = get<string>(
          row,
          definition.uniqueKey ?? definition.groupBy
        );
        if (definition.uniqueKey) {
          nameMapToDisplay.set(
            get(row, definition.uniqueKey)?.toString(),
            get(row, definition.groupBy)
          );
        }
        const requireSimilarityGroupBy = definition.similarityGroupBy;
        const bestMatchKey = requireSimilarityGroupBy
          ? findBestMatchKey(resultMap, key)
          : key;
        if (!resultMap[bestMatchKey]) {
          resultMap[bestMatchKey] = {};
        }

        if (definition.dataFieldsExpression) {
          const value = this.calculateValueFromExpression(
            definition.dataFieldsExpression,
            row
          );
          resultMap[bestMatchKey] = value;
        } else {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          definition.aggregationSelectors.forEach((selector) => {
            const field = selector.field;
            const fieldParts = field.replace('?', '').split('.');
            const subKey = fieldParts[0];
            const fieldLookupValue = get(row, field);

            if (!resultMap[bestMatchKey][`${subKey}-subtotal`]) {
              resultMap[bestMatchKey][`${subKey}-subtotal`] = 0;
            }
            if (resultMap[bestMatchKey].Count === undefined) {
              resultMap[bestMatchKey].Count = 0;
            }
            resultMap[bestMatchKey][`${subKey}-subtotal`] +=
              numberOrDefault(fieldLookupValue);
            resultMap[bestMatchKey].Count += 1;
            switch (selector.aggregation_method) {
              case 'Sum':
                resultMap[bestMatchKey][subKey] =
                  (resultMap[bestMatchKey][subKey] ?? 0) +
                  numberOrDefault(fieldLookupValue);
                break;
              case 'Count':
                resultMap[bestMatchKey][subKey] = resultMap[bestMatchKey].Count;
                break;
              case 'Average':
                resultMap[bestMatchKey][subKey] =
                  resultMap[bestMatchKey][`${subKey}-subtotal`] /
                  resultMap[bestMatchKey].Count;
                break;
              default:
                break;
            }
          });
        }
      });
      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let contactNameIdMap;
      if (definition.groupBy === 'contacts') {
        contactNameIdMap = await this.getContactNameIdMap(resultMap);
        resultMap = Object.entries(resultMap).reduce((acc, [k, v]) => {
          const realName = this.convertContactsKeyToName(k, contactNameIdMap);
          acc[realName] = v;
          return acc;
        }, {});
      }

      if (definition.sortingField) {
        resultMap = this.sortingPick(resultMap, definition.sortingField);
      }

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.entries(resultMap).forEach(async ([k, v]) => {
        const key = nameMapToDisplay.get(k) ?? k;

        if (definition.dataFieldsExpression) {
          let value = v;
          if (definition.resultFormatter === FieldTypes.CURRENCY) {
            value = Formatter.currency(v);
          } else if (definition.resultFormatter === FieldTypes.PERCENTAGE) {
            value = Formatter.asPercentage(v);
          }

          tableArray.push([key, value]);
        } else {
          tableArray.push([
            key,
            ...definition.aggregationSelectors.map((selector) => {
              const field = selector.field;
              const fieldParts = field.replace('?', '').split('.');
              const subKey = fieldParts[0];
              const value = v[subKey];
              if (selector.formatter === FieldTypes.CURRENCY) {
                return Formatter.currency(value);
              }
              if (selector.formatter === 'percent') {
                return Formatter.asPercentage(value);
              }
              if (selector.formatter === 'number') {
                return Number(value);
              }
              return value;
            }),
          ]);
        }
      });
    }

    if (!Array.isArray(tableArray[0])) {
      return null;
    }

    return {
      widgetGroup: WidgetGroup.TABLE,
      displayName: definition.name,
      data: tableArray,
      type: 'table-array',
      enabled: true,
    };
  }

  async getAgentCommissions(whereBase) {
    try {
      const agentCommissions = await this.statementService.getAgentCommissions(
        whereBase,
        this.params.startDate,
        this.params.endDate,
        this.params.include_blanks
      );
      return agentCommissions;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting agent commissions', error);
      return null;
    }
  }

  async getAccountById() {
    try {
      const accountSettings = await this.accountService.getAccountById(
        this.params.account_id
      );
      return accountSettings;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting account settings', error);
      return null;
    }
  }

  async getContactGroups(account_id) {
    try {
      const contactGroups =
        await this.contactService.getContactGroups(account_id);
      return contactGroups;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting contact groups', error);
      return null;
    }
  }

  async getAgentCommissionsNames(whereBase) {
    try {
      const commissionAgentNames =
        await this.statementService.getStatementDataGroupByPolicyAgentName(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return commissionAgentNames;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting agent commissions names', error);
      return null;
    }
  }

  async getReportData(
    reportWhere: Prisma.report_dataWhereInput,
    includeStatement?: Prisma.report_dataInclude<DefaultArgs>
  ) {
    try {
      const reportData = await this.reportService.getReportData(
        reportWhere,
        true,
        includeStatement
      );
      return reportData;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting report data', error);
      return null;
    }
  }

  async getReportDataComisionProcessingDate(
    reportWhereCommissionProcessingDate: Prisma.report_dataWhereInput
  ) {
    try {
      const reportDataCommissionProcessingDate =
        await this.reportService.getReportDataComisionProcessingDate(
          reportWhereCommissionProcessingDate,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return reportDataCommissionProcessingDate;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        'Error getting report data commission processing date',
        error
      );
      return null;
    }
  }

  async getStatementData(
    statementWhereClause: Prisma.statement_dataWhereInput,
    dateField: string,
    startDate?: Date,
    endDate?: Date,
    includeBlankDate?: boolean
  ) {
    const finalWhereClause = { ...statementWhereClause };
    if (Array.isArray(finalWhereClause.AND)) {
      finalWhereClause.AND = [
        ...finalWhereClause.AND,
        generateDateClauses(dateField, startDate, endDate, includeBlankDate),
      ];
    }

    try {
      const statementData = await this.statementService.getStatementData(
        finalWhereClause,
        null,
        this.statementSelectFields
      );
      return statementData;
    } catch (_error) {
      return null;
    }
  }

  async getStatementDataByNoDate(
    statementWhereClause: Prisma.statement_dataWhereInput
  ) {
    try {
      const statementData = await this.statementService.getStatementData(
        statementWhereClause,
        null,
        this.statementSelectFields
      );
      return statementData;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting statement data', error);
      return null;
    }
  }

  async getStatementDataGroupByPolicyAgentName(whereBase) {
    try {
      const policyAgentNames =
        await this.reportService.getReportDataGroupByPolicyAgentName(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return policyAgentNames;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        'Error getting statement data group by policy agent name',
        error
      );
      return null;
    }
  }

  async getStatementDataGroupByCompensationType(whereBase) {
    try {
      const compensationTypes =
        await this.statementService.getStatementDataGroupByCompensationType(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return compensationTypes;
    } catch {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting statement data group by compensation type');
      return null;
    }
  }

  async getStatementTags(whereBase) {
    const statementData = await this.statementService.getStatementData(
      whereBase,
      undefined,
      { tags: true }
    );
    if (!statementData) {
      return null;
    }
    const statementTags = new Set();
    statementData.map((row) => {
      if (row.tags) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        row.tags.forEach((tag) => {
          statementTags.add(tag);
        });
      }
    });
    return Array.from(statementTags).map((tag) => {
      return {
        tag: tag,
      };
    });
  }

  async getReportDataGroupByPolicyStatus(whereBase) {
    try {
      const policyPolicyStatuses =
        await this.reportService.getReportDataGroupByPolicyStatus(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return policyPolicyStatuses;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting report data group by policy status', error);
      return null;
    }
  }

  async getReportDataGroupByProductType(whereBase) {
    try {
      const policyProductTypes =
        await this.reportService.getReportDataGroupByProductType(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return policyProductTypes;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error getting report data group by product type', error);
      return null;
    }
  }

  async getStatementDataGroupByProductType(whereBase) {
    try {
      const commissionProductTypes =
        await this.statementService.getStatementDataGroupByProductType(
          whereBase,
          this.params.startDate,
          this.params.endDate,
          this.params.include_blanks
        );
      return commissionProductTypes;
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        'Error getting statement data group by product type',
        error
      );
      return null;
    }
  }

  async executeServiceCalls() {
    const results = await runInBatch({
      items: this.serviceCalls,
      onBatch: async (items) => {
        await Promise.all(
          items.map((call) => call.service[call.method](...call.params))
        );
      },
      batchSize: 50,
    });

    // Map the results to the corresponding variables dynamically
    this.serviceCalls.forEach((call, index) => {
      // Set results[index] to the object of the call.resultKey
      this[call.resultKey] = results[index];
    });
  }

  async getAccountingTransactions(
    where: Prisma.accounting_transactionsWhereInput
  ) {
    const accountingTransactions =
      await this.accountingTransactionsService.getAccountingTransactions(
        where,
        {
          contact: true,
        }
      );
    return accountingTransactions;
  }

  async getContacts(where: Prisma.contactsWhereInput) {
    const contacts = await this.contactService.getContacts({
      where,
    });
    return contacts;
  }
}

export interface IWidgetService {
  getWidgets(
    accountId: string,
    widgetsToLoad: string[],
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customizedWidgets: any,
    loadFilters: boolean
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getWidgetById(accountId: string, widgetId: string): Promise<Widget | null>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  storeWidgetToDB(model: SaveWidgetRequest): Promise<any>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  updateWidgetInDB(model: UpdateWidgetRequest): Promise<any>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  deleteWidgetInDB(id: string): Promise<any>;
  getWidgetsByAccountId(
    accountId: string,
    sharedWidgetsIds: number[]
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getAccountWidgetList(account_id: string): Promise<any>;
}
