import BigNumber from 'bignumber.js';
import { WidgetFieldTypes } from 'common/dto/widgets';
import type { statement_data } from '@prisma/client';

export function calculateCommissionFields(
  statement_data?: Partial<statement_data>[]
) {
  const result: { [key in WidgetFieldTypes]?: number } = {};

  result[WidgetFieldTypes.COMMISSION_AMOUNT_COUNT] =
    statement_data?.length || 0;

  result[WidgetFieldTypes.COMMISSION_AMOUNT_SUM] =
    statement_data
      ?.reduce(
        (acc, curr) => acc.plus(curr.commission_amount?.toNumber?.() ?? 0),
        BigNumber(0)
      )
      ?.toNumber() || 0;

  result[WidgetFieldTypes.COMMISSION_AMOUNT_NEGATIVE_COUNT] =
    statement_data?.filter(
      (statement) => statement.commission_amount?.toNumber?.() < 0
    ).length || 0;

  result[WidgetFieldTypes.COMMISSION_AMOUNT_NEGATIVE_SUM] =
    statement_data
      ?.filter((statement) => statement.commission_amount?.toNumber?.() < 0)
      .reduce(
        (acc, curr) => acc.plus(curr.commission_amount?.toNumber?.() ?? 0),
        BigNumber(0)
      )
      ?.toNumber() || 0;

  result[WidgetFieldTypes.COMMISSION_AMOUNT_POSITIVE_COUNT] =
    statement_data?.filter(
      (statement) => statement.commission_amount?.toNumber?.() > 0
    ).length || 0;

  result[WidgetFieldTypes.COMMISSION_AMOUNT_POSITIVE_SUM] =
    statement_data
      ?.filter((statement) => statement.commission_amount?.toNumber?.() > 0)
      .reduce(
        (acc, curr) => acc.plus(curr.commission_amount?.toNumber?.() ?? 0),
        BigNumber(0)
      )
      ?.toNumber() || 0;

  return result;
}
