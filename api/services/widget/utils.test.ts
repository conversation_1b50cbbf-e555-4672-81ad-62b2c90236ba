import { describe, it, expect } from 'vitest';
import { WIDGET_FIELDS } from 'common/dto/widgets';
import { Decimal } from '@prisma/client/runtime/library';

import { calculateCommissionFields } from './utils';

describe('calculateCommissionFields', () => {
  it('Should return correct counts and sums for positive and negative commission amounts', () => {
    const statement_data = [
      { commission_amount: new Decimal(100) },
      { commission_amount: new Decimal(-50) },
      { commission_amount: new Decimal(200) },
      { commission_amount: new Decimal(-30) },
      { commission_amount: null },
      { commission_amount: undefined },
    ];

    const result = calculateCommissionFields(statement_data);

    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_COUNT]).toBe(6);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_SUM]).toBe(220);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_COUNT]).toBe(2);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_SUM]).toBe(-80);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_COUNT]).toBe(2);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_SUM]).toBe(300);
  });

  it('Should handle empty statement_data', () => {
    const statement_data = [];

    const result = calculateCommissionFields(statement_data);

    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_COUNT]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_SUM]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_COUNT]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_SUM]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_COUNT]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_SUM]).toBe(0);
  });

  it('Should handle statement_data with null or undefined commission_amount', () => {
    const result = calculateCommissionFields(null);

    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_COUNT]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_SUM]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_COUNT]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_NEGATIVE_SUM]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_COUNT]).toBe(0);
    expect(result[WIDGET_FIELDS.COMMISSION_AMOUNT_POSITIVE_SUM]).toBe(0);
  });
});
