import type { WidgetGroup } from 'common/constants';
import type { WidgetDefinition } from 'common/dto/widgets';

export type Widget = {
  id?: number;
  widgetGroup: WidgetGroup;
  displayName?: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  value?: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data?: any;
  type: string;
  enabled: boolean;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  formatters?: any;
  order?: number;
  rowCount?: number;
  access?: string;
};

export type SaveWidgetRequest = {
  name: string;
  spec: WidgetDefinition;
  access: string;
  account_id: string;
};

export type UpdateWidgetRequest = {
  id: string;
  name: string;
  access?: string;
  spec: WidgetDefinition;
};

export type ServiceCall = {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  service: any;
  method: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  params: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  resultKey: any;
};
