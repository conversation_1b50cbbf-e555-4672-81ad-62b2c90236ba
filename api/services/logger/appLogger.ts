import { LoggingWinston } from '@google-cloud/logging-winston';
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { AsyncLocalStorage } from 'async_hooks';
import { nanoid } from 'nanoid';
import winston from 'winston';

import type { ExtAccountInfo } from '@/types';

export const asyncLocalStorage = new AsyncLocalStorage<{
  traceId: string;
  account?: ExtAccountInfo;
}>();

const { combine, timestamp, align, printf } = winston.format;

export class AppLoggerService {
  private logger: winston.Logger;
  private readonly isGcloudLogging: boolean;
  private readonly MAX_LOG_SIZE = 256 * 800; // 256KB in bytes
  private isChildLogger: boolean = false;

  constructor(
    options?: Parameters<typeof winston.createLogger>[0] & {
      childLogger?: winston.Logger;
      isChildLogger?: boolean;
    }
  ) {
    const transports: winston.transport[] = [new winston.transports.Console()];
    const level = process.env.LOG_LEVEL || 'info';
    this.isGcloudLogging = !!process.env.GCLOUD_LOGGING;

    if (this.isGcloudLogging) {
      transports.push(new LoggingWinston({ level: 'debug' }));
    }
    this.isChildLogger = !!options?.isChildLogger;

    this.logger =
      options?.childLogger ??
      winston.createLogger({
        level: level,
        format: combine(
          timestamp(),
          winston.format.json(),
          printf((info) => {
            const baseLog = `[${info.timestamp}] ${info.level.toUpperCase()} - TraceID: ${info.traceId}`;
            const parentTrace = info.parentTraceId
              ? ` (Parent: ${info.parentTraceId})`
              : '';
            const message = `Message: ${info.message} ${info.durationMs ? `(Duration: ${info.durationMs}ms)` : ''}`;
            const details =
              level === 'debug'
                ? `\nDetails: ${JSON.stringify(info, null, 2)}`
                : '';

            return `${baseLog}${parentTrace}\n${message}${details}\n`;
          }),
          align()
        ),
        transports: transports,
        ...(options ?? {}),
      });
  }

  // Gcloud has 256K size limit for logs
  private truncateLogBody(
    message: string,
    meta?: Record<string, unknown>
  ): [string, Record<string, unknown> | undefined, boolean] {
    if (!this.isGcloudLogging) return [message, meta, false];

    const logBody = { message, ...meta };
    const jsonString = JSON.stringify(logBody);

    if (jsonString.length <= this.MAX_LOG_SIZE) {
      return [message, meta, false];
    }

    // Calculate available space for truncated content
    const truncatedMark = '...(truncated)';
    const reservedSpace = truncatedMark.length + 20; // Extra space for _truncated and originalSize fields
    const availableSpace = this.MAX_LOG_SIZE - reservedSpace;

    // Truncate the message first
    const truncatedMessage = message.slice(0, availableSpace / 2);
    const truncatedMeta = { ...meta };

    // Gradually truncate meta fields until it fits
    while (
      JSON.stringify({ message: truncatedMessage, ...truncatedMeta }).length >
      availableSpace
    ) {
      const metaEntries = Object.entries(truncatedMeta);
      if (metaEntries.length === 0) break;

      const lastKey = metaEntries[metaEntries.length - 1][0];
      const lastValue = truncatedMeta[lastKey];

      if (typeof lastValue === 'string') {
        truncatedMeta[lastKey] =
          lastValue.slice(0, lastValue.length / 2) + truncatedMark;
      } else {
        delete truncatedMeta[lastKey];
      }
    }

    // Add truncation information
    truncatedMeta._truncated = true;
    truncatedMeta.originalSize = jsonString.length;

    return [truncatedMessage + truncatedMark, truncatedMeta, true];
  }

  private logWithLevel(
    level: string,
    message: string,
    meta?: Record<string, unknown>
  ) {
    // Extract and format error stack if present
    let formattedMeta = { ...meta };
    if (meta?.error instanceof Error) {
      const error = meta.error as Error;
      delete formattedMeta.error;
      formattedMeta = {
        ...formattedMeta,
        stack: error.stack,
        name: error.name,
        message: error.message,
      };
    }

    const [truncatedMessage, truncatedMeta, isTruncated] = this.truncateLogBody(
      message,
      formattedMeta
    );

    this.logger.log(level, truncatedMessage, {
      traceId: this.isChildLogger
        ? meta?.traceId || nanoid()
        : this.getTraceId(),
      ...truncatedMeta,
    });

    // If truncated, log full message to console
    if (isTruncated) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const consoleMethod = console[level];
      if (typeof consoleMethod === 'function') {
        consoleMethod('Full log (truncated in GCloud):', message, {
          traceId: this.isChildLogger ? meta?.traceId : this.getTraceId(),
          ...formattedMeta,
        });
      }
    }
  }

  // Create a child logger with a new traceId
  getChildLogger(meta?: Record<string, unknown>) {
    return new AppLoggerService({
      isChildLogger: true,
      childLogger: this.logger.child({
        traceId: nanoid(),
        parentTraceId: this.getTraceId(),
        ...meta,
      }),
    });
  }

  getTraceId() {
    const store = asyncLocalStorage.getStore() as { traceId: string };
    return store?.traceId || nanoid();
  }

  log(level: string, message: string, meta?: Record<string, unknown>) {
    this.logWithLevel(level, message, meta);
  }

  error(message: string | Error, meta?: Record<string, unknown> | Error) {
    if (meta instanceof Error) {
      this.logWithLevel(
        'error',
        message instanceof Error ? message.message : message,
        { error: meta }
      );
    } else if (message instanceof Error) {
      // Error object passed as message
      const error = message;
      this.logWithLevel('error', error.message, {
        ...(meta || {}),
        error: error,
      });
    } else {
      // Regular string message
      this.logWithLevel('error', message, meta);
    }
  }

  warn(message: string, meta?: Record<string, unknown>) {
    this.logWithLevel('warn', message, meta);
  }

  info(message: string, meta?: Record<string, unknown>) {
    this.logWithLevel('info', message, meta);
  }

  debug(message: string, meta?: Record<string, unknown>) {
    this.logWithLevel('debug', message, meta);
  }

  profile(message: string) {
    this.logger.profile(message, { traceId: this.getTraceId() });
  }
}
