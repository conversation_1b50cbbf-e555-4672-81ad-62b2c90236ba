import type { Prisma } from '@prisma/client';
import type { ReportDataQueryDto } from 'common/dto/report_data/dto';
import Formatter from 'common/Formatter';
import { dateOrDefault, setNextDay } from 'common/helpers';
import { inject, injectable } from 'inversify';
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import url from 'url';
import { customViewDefault } from 'common/constants/account_role_settings';
import { DEFAULT_FILTER } from 'common/constants';

import FieldValuesCache from '@/lib/field-values/FieldValuesCache';
import normalizeFieldValues from '@/lib/field-values/normalizeFieldValues';
import { filterFieldOptions, loadFieldFilters } from '@/lib/helpers';
import prisma, { prismaClient } from '@/lib/prisma';
import { ContactService } from '@/services/contact';
import { ShareFilter } from '@/services/share/filter';
import { StatementFilterService } from '@/services/statement/filter';
import { type ExtAccountInfo, type ExtNextApiRequest, Roles } from '@/types';

const filterFieldsCache = new FieldValuesCache('report_data');
const queryFieldValues = async (
  account_id: string,
  filterList: string[],
  where: Prisma.report_dataWhereInput
) => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const queries = filterList.map<Promise<[string, any[]]>>(
    async (fieldName) => {
      const values = await filterFieldsCache.get(
        account_id,
        fieldName,
        where,
        async () => {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          const records: any[] = await prismaClient.report_data.findMany({
            select: { [fieldName]: true },
            where,
          });

          return normalizeFieldValues(
            records.map((record) => record[fieldName])
          );
        }
      );

      return [fieldName, values];
    }
  );

  const resultEntries = await Promise.all(queries);

  return Object.fromEntries(resultEntries);
};
@injectable()
class ReportDataFilterService {
  @inject(ShareFilter)
  private shareFilter: ShareFilter;

  @inject(StatementFilterService)
  filterService: StatementFilterService;

  @inject(ContactService)
  contactService: ContactService;

  async getFilters(
    req: ExtNextApiRequest,
    query: ReportDataQueryDto,
    account: ExtAccountInfo,
    currentRecord: { id: number; parent_id: number } = null
  ) {
    const userData = await this.getProducerUserData(
      account,
      query.producer_view
    );

    const filterService = this.filterService;
    const contactService = this.contactService;

    const parsedUrl = url.parse(req.url);
    const queryParamNames = parsedUrl?.query
      ?.split('&')
      ?.map((param) => param.split('=')[0]);

    const contactStrId = userData?.user_contact[0].str_id;
    let contactAndChildrenStrIds: string[] = [];
    if (contactStrId) {
      const policyAccountSettings =
        await prisma.account_role_settings.findUnique({
          where: {
            account_id_role_id_custom_view_name: {
              account_id: String(req.account_id),
              role_id: parseInt(req.role_id),
              custom_view_name: customViewDefault,
            },
          },
          select: {
            agent_settings: true,
          },
        });

      const { agent_settings } = policyAccountSettings ?? {};
      const directDownlineDataAccess =
        agent_settings?.directDownlineDataAccess?.policiesConfig;
      const extendedDownlineDataAccess =
        agent_settings?.extendedDownlineDataAccess?.policiesConfig;
      const payoutLevelsDataAccess =
        agent_settings?.extendedDownlineDataAccess?.payoutLevels;

      contactAndChildrenStrIds =
        await filterService.getContactAndChildrenStrIds(
          extendedDownlineDataAccess,
          directDownlineDataAccess,
          contactStrId
        );

      // If the user has no children or no hierarchy config is in place, add the contact itself.
      if (contactAndChildrenStrIds.length === 0)
        contactAndChildrenStrIds.push(contactStrId);

      if (payoutLevelsDataAccess && Array.isArray(payoutLevelsDataAccess)) {
        const childrenGridLevelsStrIds =
          await contactService.getChildenContactStrIdListByGridLevel(
            contactStrId,
            payoutLevelsDataAccess
          );

        // The payoutLevelsDataAccess setting can be combined with the extendedDownlineDataAccess/directDownlineDataAccess settings, so we need to merge the two results
        contactAndChildrenStrIds = Array.from(
          new Set([...contactAndChildrenStrIds, ...childrenGridLevelsStrIds])
        );
      }
    }

    const globalWhere = ReportDataFilterService.createGlobalWhere({
      query,
      userData,
      account_id: account.account_id,
      currentRecord,
      contactAndChildrenStrIds,
    });
    const { where, filterList, additionalFilterFields } =
      ReportDataFilterService.applyReportDataFilter({
        query,
        // Copy globalWhere to avoid modifying the original object
        where: JSON.parse(JSON.stringify(globalWhere)),
        queryParams: queryParamNames,
      });
    return { globalWhere, where, filterList, additionalFilterFields };
  }

  async getReportQuerySelects(account: ExtAccountInfo) {
    let selectStatement = {};
    const selectedFields = {
      str_id: true,
      sync_id: true,
      account_id: true,
      created_at: true,
      created_by: true,
      updated_at: true,
      updated_by: true,
      state: true,
      document_id: true,
      config: true,
      import_id: true,
      transaction_type: true,
      group_name: true,
    };

    const reportAccountSettings =
      await prismaClient.account_role_settings.findUnique({
        where: {
          account_id_role_id_custom_view_name: {
            account_id: account.account_id,
            role_id: parseInt(account.role_id),
            custom_view_name: customViewDefault,
          },
        },
        select: {
          pages_settings: true,
        },
      });
    if (
      Array.isArray(
        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        reportAccountSettings?.pages_settings?.['policies']?.fields
      ) &&
      // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      reportAccountSettings.pages_settings?.['policies']?.fields.length > 0
    ) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      reportAccountSettings.pages_settings?.['policies']?.fields.forEach(
        (item: string) => {
          selectedFields[item] = true;
        }
      );
      selectStatement = { ...selectedFields };
    } else {
      const policy_fields = await prismaClient.fields.findMany({
        where: {
          model: 'reports',
          state: 'active',
        },
        select: {
          key: true,
        },
      });
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      policy_fields.forEach((field: { key: string }) => {
        selectedFields[field.key] = true;
      });
      selectStatement = {
        ...selectedFields,
        id: true,
        report_contacts: true,
        statement_data: true,
        children_report_data: true,
      };
    }
    return selectStatement;
  }

  static loadFilterOption(req: ExtNextApiRequest, excludeNullDates = false) {
    const {
      query: { q = '', id = '' },
    } = req;

    const globalWhere = {
      AND: [
        { account_id: String(req.account_id) },
        { OR: [{ state: 'active' }] },
        {
          OR: [
            { agent_name: { contains: q, mode: 'insensitive' } },
            { aggregation_id: { contains: q, mode: 'insensitive' } },
            { customer_name: { contains: q, mode: 'insensitive' } },
            { document_id: { contains: q, mode: 'insensitive' } },
            { notes: { contains: q, mode: 'insensitive' } },
            { policy_id: { contains: q, mode: 'insensitive' } },
            { policy_status: { contains: q, mode: 'insensitive' } },
            { product_name: { contains: q, mode: 'insensitive' } },
            { product_sub_type: { contains: q, mode: 'insensitive' } },
            { product_type: { contains: q, mode: 'insensitive' } },
            { writing_carrier_name: { contains: q, mode: 'insensitive' } },
            { account_type: { contains: q, mode: 'insensitive' } },
          ],
        },
        { effective_date: undefined },
        { str_id: undefined },
      ],
    };

    if (excludeNullDates) {
      globalWhere.AND[3].effective_date = { not: null };
    }

    if (id) {
      globalWhere.AND[4].str_id = id;
      globalWhere.AND[1].OR = [
        { state: 'active' },
        { state: 'duplicate' },
        { state: 'grouped' },
      ];
    }

    return JSON.parse(JSON.stringify(globalWhere));
  }

  async getReportTags(condition) {
    const reportData = await prismaClient.report_data.findMany({
      where: condition,
      select: {
        tags: true,
      },
    });
    const reportTags = new Set();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    reportData.forEach((row) => {
      if (row.tags) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        row.tags.forEach((tag) => {
          reportTags.add(tag);
        });
      }
    });
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const result: any = [DEFAULT_FILTER.BLANK_OPTION];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Array.from(reportTags).forEach((tag) => {
      result.push(tag);
    });
    return result;
  }
  async getFilterValues(
    req: ExtNextApiRequest,
    query: ReportDataQueryDto,
    account: ExtAccountInfo
  ) {
    const groupNames = await this.shareFilter.getAllGroupNames({
      accountId: account.account_id,
      modelName: 'report_data',
    });

    const transactionTypes = await this.shareFilter.getAllTransactionTypes({
      accountId: account.account_id,
      modelName: 'report_data',
    });

    const { globalWhere, filterList, where, additionalFilterFields } =
      await this.getFilters(req, query, account);

    req.logger.profile('Getting field values for report_data filter ');
    let _fieldOptions = await queryFieldValues(
      req.account_id,
      filterList,
      where
    );
    req.logger.profile('Getting field values for report_data filter ');

    if (additionalFilterFields.length > 0) {
      req.logger.profile(
        'Getting additional field values for report_data filter '
      );
      const additionalFieldValues = await queryFieldValues(
        req.account_id,
        additionalFilterFields,
        globalWhere as Prisma.report_dataWhereInput
      );
      _fieldOptions = { ..._fieldOptions, ...additionalFieldValues };
      req.logger.profile(
        'Getting additional field values for report_data filter '
      );
    }
    const startItem = await prismaClient.report_data.findFirst({
      where: ReportDataFilterService.loadFilterOption(req, true),
      select: {
        effective_date: true,
      },
      // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      orderBy: [{ ['effective_date']: 'asc' }],
    });
    const endItem = await prismaClient.report_data.findFirst({
      where: ReportDataFilterService.loadFilterOption(req, true),
      select: {
        effective_date: true,
      },
      // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      orderBy: [{ ['effective_date']: 'desc' }],
    });
    let fieldOptions = {
      document_id: undefined,
      import_id: undefined,
      contacts: undefined,
      tags: undefined,
      effective_date_start: startItem?.effective_date,
      effective_date_end: endItem?.effective_date,
      group_name: groupNames,
      transaction_type: transactionTypes,
      ..._fieldOptions,
    };

    fieldOptions.contacts = Array.from(
      new Set(fieldOptions.contacts?.flat() ?? [])
    );

    fieldOptions.tags = await this.getReportTags(globalWhere);

    req.logger.profile('Getting contacts for report_data filter ');
    // Add contact names to filters
    const contactIds = fieldOptions.contacts;
    let contacts = await prismaClient.contacts.findMany({
      where: { str_id: { in: contactIds } },
      select: {
        str_id: true,
        first_name: true,
        last_name: true,
        email: true,
      },
    });
    if (req.query?.unselected_contacts?.length > 0) {
      contacts = contacts.filter((contact) => {
        return !req.query.unselected_contacts.includes(contact.str_id);
      });
    }
    const contactsMap = Object.fromEntries(
      contacts.map((contact) => [contact.str_id, Formatter.contact(contact)])
    );
    fieldOptions.contacts = [
      { id: '-1', name: DEFAULT_FILTER.BLANK_OPTION },
      ...contactIds.map((id) => ({
        id,
        name: contactsMap[id] || id,
      })),
    ];
    fieldOptions.contacts.sort((a, b) => a.name.localeCompare(b.name));
    req.logger.profile('Getting contacts for report_data filter ');

    req.logger.profile('Getting documents for report_data filter ');
    // Add document name to filters
    const documentIds = fieldOptions.document_id;
    const documents = await prismaClient.documents.findMany({
      where: { str_id: { in: documentIds } },
      select: {
        str_id: true,
        filename: true,
      },
    });
    const documentsMap = Object.fromEntries(
      documents.map((document) => [document.str_id, document.filename])
    );
    fieldOptions.document_id =
      documentIds?.map((id) => ({
        id,
        name: documentsMap[id] || id,
      })) ?? [];
    fieldOptions.document_id.sort((a, b) => a.name.localeCompare(b.name));
    req.logger.profile('Getting documents for report_data filter ');
    const excludedKeys = ['effective_date_start', 'effective_date_end'];
    const selectStatement = await this.getReportQuerySelects(account);
    fieldOptions = filterFieldOptions(
      selectStatement,
      fieldOptions,
      excludedKeys
    ) as typeof fieldOptions;
    return fieldOptions;
  }

  async getProducerUserData(
    account: ExtAccountInfo,
    producer_view: boolean = false
  ) {
    if (producer_view) {
      account.role_id = Roles.PRODUCER.toString();
    }

    if (+account.role_id !== Roles.PRODUCER) return null;

    if (producer_view) {
      return await prismaClient.users.findFirst({
        where: {
          account_user_roles: {
            some: {
              role_id: Roles.PRODUCER,
              account_id: account.account_id,
              state: 'active',
            },
          },
        },
        select: {
          user_contact: {
            select: {
              str_id: true,
            },
          },
        },
      });
    } else {
      return await prismaClient.users.findUnique({
        where: {
          uid: String(account.uid),
        },
        select: {
          user_contact: {
            select: {
              str_id: true,
            },
          },
        },
      });
    }
  }

  static createGlobalWhere(data: {
    query: ReportDataQueryDto;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    userData: any;
    account_id: string;
    currentRecord: { id: number; parent_id: number } | null;
    contactAndChildrenStrIds: string[];
  }) {
    const { id, incl_dupes, incl_linked, q } = data.query;
    const { account_id, currentRecord, contactAndChildrenStrIds } = data;

    const globalWhere = {
      AND: [
        // Index 0
        { account_id: String(account_id) },
        // Index 1
        {
          contacts:
            contactAndChildrenStrIds.length > 0
              ? { hasSome: contactAndChildrenStrIds }
              : undefined,
        },
        // Index 2
        { OR: [{ state: 'active' }] },
        // Index 3
        {
          OR: [
            { agent_name: { contains: q, mode: 'insensitive' } },
            { agent_id: { contains: q, mode: 'insensitive' } },
            { customer_name: { contains: q, mode: 'insensitive' } },
            { dba: { contains: q, mode: 'insensitive' } },
            { group_id: { contains: q, mode: 'insensitive' } },
            { policy_id: { contains: q, mode: 'insensitive' } },
            { policy_status: { contains: q, mode: 'insensitive' } },
            { product_name: { contains: q, mode: 'insensitive' } },
            { product_type: { contains: q, mode: 'insensitive' } },
            { document_id: { contains: q, mode: 'insensitive' } },
            { import_id: { contains: q, mode: 'insensitive' } },
            { writing_carrier_name: { contains: q, mode: 'insensitive' } },
            { account_type: { contains: q, mode: 'insensitive' } },
          ],
        },
        // Index 4
        { OR: [], id: undefined, str_id: undefined },
      ],
    };

    if (id || incl_dupes || incl_linked) {
      const orstates = [{ state: 'active' }];
      if (incl_dupes) {
        orstates.push({ state: 'duplicate' });
      }
      if (incl_linked) {
        orstates.push({ state: 'grouped' });
      }

      if (id) {
        if (/^\d+$/.test(id)) {
          globalWhere.AND[4].id = +id;
        } else {
          if (currentRecord) {
            if (currentRecord.parent_id) {
              globalWhere.AND[4].OR.push({ id: currentRecord.parent_id });
              globalWhere.AND[4].OR.push({
                parent_id: currentRecord.parent_id,
              });
            } else {
              globalWhere.AND[4].OR.push({ parent_id: currentRecord.id });
            }
            globalWhere.AND[4].OR.push({ str_id: id });
          } else {
            globalWhere.AND[4].str_id = id;
          }
        }
        orstates.push({ state: 'duplicate' });
        orstates.push({ state: 'grouped' });
      }
      globalWhere.AND[2] = {
        OR: orstates,
      };
    }
    return globalWhere;
  }

  static applyReportDataFilter(data: {
    query: ReportDataQueryDto;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    where: any;
    queryParams: string[];
  }) {
    let filterList = [
      'agent_name',
      'contacts',
      'document_id',
      'policy_status',
      'product_name',
      'product_type',
      'writing_carrier_name',
      'account_type',
      'tags',
      'group_name',
      'transaction_type',
    ];

    const additionalFilterFields: string[] = [];
    const { where, queryParams, query } = data;
    const filteredQueryParamNames: string[] =
      queryParams?.filter((param) => filterList.includes(param)) ?? [];

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    [
      'writing_carrier_name',
      'product_name',
      'product_type',
      'agent_name',
      'policy_status',
      'document_id',
      'import_id',
      'account_type',
      'group_name',
      'transaction_type',
    ].forEach((field) => {
      const value = data.query[field];
      if (!value) return;
      const filters = loadFieldFilters(value, field);
      if (filters.length > 0) {
        where.AND.push({ OR: filters });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push(field);
          filterList = filterList.filter((item) => item !== field);
        }
      }
    });

    if (query.tags?.length > 0) {
      if (query.tags.includes(DEFAULT_FILTER.BLANK_OPTION)) {
        query.tags = query.tags.filter(
          (tag) => tag !== DEFAULT_FILTER.BLANK_OPTION
        );
        where.AND.push({
          OR: [
            {
              tags: { isEmpty: true },
            },
            {
              tags: {
                hasSome: Array.isArray(query.tags) ? query.tags : [query.tags],
              },
            },
          ],
        });
      } else {
        where.AND.push({
          OR: [
            {
              tags: {
                hasSome: Array.isArray(query.tags) ? query.tags : [query.tags],
              },
            },
          ],
        });
        if (filteredQueryParamNames.length > 0) {
          additionalFilterFields.push('tags');
          filterList = filterList.filter((item) => item !== 'tags');
        }
      }
    }
    if (query.contacts === '-1') {
      where.AND.push({
        AND: [
          {
            contacts: { isEmpty: true },
          },
        ],
      });
    } else if (query.contacts?.length > 0) {
      where.AND.push({
        OR: [
          query.contacts?.length > 0
            ? {
                contacts: {
                  hasSome: Array.isArray(query.contacts)
                    ? query.contacts
                    : [query.contacts],
                },
              }
            : {},
        ],
      });
      if (filteredQueryParamNames.length > 0) {
        additionalFilterFields.push('contacts');
        filterList = filterList.filter((item) => item !== 'contacts');
      }
    }
    const effectiveDateStart = dateOrDefault(
      query.effective_date_start,
      undefined
    );
    const effectiveDateEnd = setNextDay(
      dateOrDefault(query.effective_date_end, undefined)
    );
    where.AND = [
      ...where.AND,
      {
        AND: [
          {
            effective_date: effectiveDateStart
              ? { gte: effectiveDateStart }
              : undefined,
          },
          {
            effective_date: effectiveDateEnd
              ? { lt: effectiveDateEnd }
              : undefined,
          },
        ],
      },
    ];

    return { where, filterList, additionalFilterFields };
  }
}

export default ReportDataFilterService;
