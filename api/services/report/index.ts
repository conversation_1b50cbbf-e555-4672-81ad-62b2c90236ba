import { inject, injectable } from 'inversify';
import type { Prisma } from '@prisma/client';
import type { DefaultArgs } from '@prisma/client/runtime/library';

import { prismaClient, prisma } from '@/lib/prisma';
import type IReportDataRepo from '@/persistence/report-data/IReportDataRepo';
import type ReportDataRecord from '@/persistence/report-data/ReportDataRecord';
import { REPOSITORY_TYPES } from '@/constants';
import type { ContactsSplit, ReportData } from './types';
import {
  isContactsSplitJson,
  mapContactsSplitFromJson,
} from './contacts-split';
import { calculateSkipAndTake } from '@/prisma';
import { DataStates, type PaginationInput } from '@/types';
import { generateDateClauses } from '@/lib/helpers/generateDateClauses';

export interface GetReportDataByIdInput {
  account_id: string;
  str_ids: string[];
}

export interface GetReportDataByIdOutput {
  report_data: ReportData[];
}

export interface FindReportDataByAccountInput {
  account_id: string;
}
export interface FindReportDataByAccountOutput {
  report_data: ReportData[];
}

export interface UpdateReportCompProfileInput {
  account_id: string;
  report_data_str_id: string;
  comp_profile_str_id: string | null;
}

@injectable()
export class ReportService implements IReportService {
  @inject(REPOSITORY_TYPES.ReportDataRepository)
  private readonly repo: IReportDataRepo;

  async updateCustomerId({
    str_ids,
    customer_id,
    account_id,
  }: {
    str_ids: string[];
    customer_id: number;
    account_id: string;
  }) {
    await prismaClient.report_data.updateMany({
      where: {
        str_id: {
          in: str_ids,
        },
        account_id,
      },
      data: {
        customer_id,
      },
    });
  }

  async getCustomerNames({
    accountId,
    page,
    limit,
  }: { accountId: string } & PaginationInput) {
    const { take, skip } = calculateSkipAndTake({ page, limit });
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const where: any = {
      state: 'active',
      account_id: accountId,
      OR: [
        {
          customer: {
            is: null,
          },
        },
        {
          customer: {
            state: 'deleted',
          },
        },
      ],
    };
    const data = await prismaClient.report_data.findMany({
      select: {
        customer_name: true,
        str_id: true,
        group_id: true,
        policy_id: true,
        internal_id: true,
      },
      where,
      take,
      skip,
    });
    const total = await prismaClient.report_data.count({
      where,
    });
    return { total, data };
  }

  private mapReportDataRecord(record: ReportDataRecord): ReportData {
    const {
      id,
      str_id,
      account_id,
      uid,
      contacts_split,
      company_id,
      product_type,
      product_name,
      product_option_name,
      effective_date,
      issue_age,
      transaction_type,
      payment_mode,
      writing_carrier_name,
    } = record;

    let mappedContactsSplit: ContactsSplit | null;
    if (contacts_split === null) {
      mappedContactsSplit = null;
    } else if (isContactsSplitJson(contacts_split)) {
      mappedContactsSplit = mapContactsSplitFromJson(contacts_split);
    } else {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Unknown format for contacts_split.`, contacts_split);
      throw new Error(`Unknown format for contacts_split.`);
    }

    const result: ReportData = {
      id,
      str_id,
      account_id,
      uid,
      contacts_split: mappedContactsSplit,
      company_id,
      product_type,
      product_name,
      product_option_name,
      effective_date,
      issue_age,
      transaction_type,
      payment_mode,
      writing_carrier_name,
    };
    return result;
  }

  async getReportDataById(
    input: GetReportDataByIdInput
  ): Promise<GetReportDataByIdOutput> {
    const { account_id, str_ids } = input;

    const records = await this.repo.getByStrId(str_ids);
    const report_data = records
      .filter(
        ({ account_id: record_account_id }) => record_account_id === account_id
      )
      .map((record) => this.mapReportDataRecord(record));

    return { report_data };
  }

  async findReportDatabyAccount(
    input: FindReportDataByAccountInput
  ): Promise<FindReportDataByAccountOutput> {
    const { account_id } = input;

    const str_ids = await this.repo.findByAccount(account_id);
    if (str_ids.length === 0) {
      return { report_data: [] };
    }

    const records = await this.repo.getByStrId(str_ids);
    const report_data = records.map((record) =>
      this.mapReportDataRecord(record)
    );

    return { report_data };
  }

  async updateReportCompProfile(
    input: UpdateReportCompProfileInput
  ): Promise<void> {
    const { account_id, report_data_str_id, comp_profile_str_id } = input;

    // Verify access
    const [record] = await this.repo.getByStrId([report_data_str_id]);
    if (record?.account_id !== account_id) {
      throw new Error(
        `Unautherized access to report data. (${report_data_str_id})`
      );
    }

    await this.repo.update(report_data_str_id, {
      commission_profile_id: comp_profile_str_id,
    });
  }

  async getReportData(
    reportWhere: Prisma.report_dataWhereInput,
    accountInject: boolean = true,
    includeStatement?: Prisma.report_dataInclude<DefaultArgs>
  ) {
    try {
      const reportData = await prismaClient.report_data.findMany({
        where: reportWhere,
        accountInject,
        orderBy: [{ effective_date: 'asc' }],
        include: includeStatement,
      });

      return reportData;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getReportCurrentStatus(reportId: number, accountId: string) {
    try {
      const report = await prismaClient.saved_reports.findFirst({
        where: { id: reportId, account_id: accountId },
        select: { status: true },
      });
      return report.status;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getReportDataGroupByPolicyAgentName(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    const andClauses: Prisma.report_dataWhereInput = generateDateClauses(
      'effective_date',
      startDate,
      endDate,
      includeBlankDate
    );
    try {
      const reportData = await prisma.report_data.groupBy({
        by: ['agent_name'],
        where: {
          ...whereBase,
          AND: [{ agent_name: { not: null } }, { agent_name: { not: '' } }],
          ...andClauses,
        },
        select: { agent_name: true },
      });
      return reportData;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getReportDataComisionProcessingDate(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    reportWhereCommissionProcessingDate: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    try {
      const dateClauses: Prisma.statement_dataWhereInput = generateDateClauses(
        'processing_date',
        startDate,
        endDate,
        includeBlankDate
      );
      const reportData = await prismaClient.report_data.findMany({
        where: reportWhereCommissionProcessingDate,
        include: {
          statement_data: {
            where: {
              ...dateClauses,
            },
            select: {
              commission_amount: true,
            },
          },
        },
        orderBy: [{ effective_date: 'asc' }],
      });

      return reportData;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getReportDataGroupByPolicyStatus(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    try {
      const dateClauses: Prisma.report_dataWhereInput = generateDateClauses(
        'effective_date',
        startDate,
        endDate,
        includeBlankDate
      );
      const reportData = await prismaClient.report_data.groupBy({
        by: ['policy_status'],
        where: {
          ...whereBase,
          ...dateClauses,
        },
      });
      return reportData;
    } catch (error) {
      throw new Error(error);
    }
  }

  async getReportDataGroupByProductType(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    try {
      const dateClauses: Prisma.report_dataWhereInput = generateDateClauses(
        'effective_date',
        startDate,
        endDate,
        includeBlankDate
      );
      const reportData = await prismaClient.report_data.groupBy({
        by: ['product_type'],
        where: {
          ...whereBase,
          ...dateClauses,
        },
      });
      return reportData;
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateReportAccess(
    reportId: number,
    accountId: string,
    contactUserId: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    userWhiteList: any,
    access: string
  ) {
    return await prismaClient.saved_reports.update({
      where: { id: reportId, account_id: accountId },
      data: {
        access: access,
        users_white_list: [...userWhiteList, contactUserId],
      },
    });
  }

  async updateReportPayoutRates(
    statementId: number,
    uid: string,
    accountId: string,
    ouid: string
  ): Promise<boolean> {
    const statementData = await prismaClient.statement_data.findUnique({
      where: {
        id: statementId,
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      select: {
        id: true,
        report_data_id: true,
        agent_payout_rate: true,
      },
    });

    if (!statementData) throw new Error('Commission not found');

    if (
      !statementData.agent_payout_rate ||
      Object.keys(statementData.agent_payout_rate).length === 0
    )
      throw new Error('Agent payout rate not set for this commission');

    if (!statementData.report_data_id)
      throw new Error("Commission doesn't have a linked policy");

    const newRates = Object.fromEntries(
      Object.entries(statementData.agent_payout_rate).map(([key, value]) => [
        key,
        // TODO: Remove the times 100 when the commissions agent_payout_rate is normalized in the db
        +value * 100,
      ])
    );

    await prismaClient.report_data.update({
      where: {
        id: statementData.report_data_id,
        account_id: accountId,
      },
      data: {
        agent_payout_rate_override: newRates,
        updated_by: uid,
        updated_at: new Date(),
        updated_proxied_by: ouid,
      },
    });
    return true;
  }
}

export interface IReportService {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getReportData(reportWhere: any): Promise<any>;
  getReportDataComisionProcessingDate(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    reportWhereCommissionProcessingDate: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getReportDataGroupByPolicyAgentName(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getReportDataGroupByPolicyStatus(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getReportDataGroupByProductType(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getReportCurrentStatus(reportId: number, accountId: string): Promise<any>;
  updateReportAccess(
    reportId: number,
    accountId: string,
    contactUserId: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    userWhiteList: any,
    access: string
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  updateReportPayoutRates(
    statementId: number,
    uid: string,
    accountId: string,
    ouid: string
  ): Promise<boolean>;
}
