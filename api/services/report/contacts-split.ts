import type { ContactsSplit } from './types';

export type ContactsSplitJson = Record<string, string>;

export const isContactsSplitJson = (
  value: unknown
): value is ContactsSplitJson => {
  if (typeof value !== 'object' || value === null) {
    return false;
  }

  return Array.from(Object.entries(value)).every(
    ([k, v]) => typeof k === 'string' && typeof v === 'string'
  );
};

export const mapContactsSplitFromJson = (
  json: ContactsSplitJson
): ContactsSplit => {
  return { ...json };
};
