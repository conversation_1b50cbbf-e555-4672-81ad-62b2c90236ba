export interface ReportData {
  id: number;
  str_id: string;
  account_id: string;
  uid: string;
  company_id: number | null;
  product_type: string | null;
  product_name: string | null;
  product_option_name: string | null;
  effective_date: Date | null;
  issue_age: number | null;
  transaction_type: string | null;
  payment_mode: string | null;
  writing_carrier_name: string | null;
  contacts_split: ContactsSplit | null;
}

export interface ContactsSplit {
  [index: string]: string;
}
