import { describe, it, expect, vi, beforeEach } from 'vitest';

import { FieldsService } from '@/services/fields';
import { prismaClient } from '@/lib/prisma';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    fields: {
      findMany: vi.fn(),
    },
  },
}));

describe('FieldsService.getEntityFieldsTypes', () => {
  let fieldsService: FieldsService;

  beforeEach(() => {
    fieldsService = new FieldsService();
    vi.resetAllMocks();
  });

  it('Should return field types when fields exist for the entity', async () => {
    const mockFields = [
      { key: 'name', type: 'string' },
      { key: 'age', type: 'number' },
      { key: 'active', type: 'boolean' },
      { key: 'metadata', type: 'json' },
    ];

    vi.mocked(prismaClient.fields.findMany).mockResolvedValue(
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      mockFields as any
    );

    const result = await fieldsService.getEntityFieldsTypes('users');

    expect(prismaClient.fields.findMany).toHaveBeenCalledWith({
      where: { model: 'users' },
      select: { type: true, key: true },
    });

    expect(result).toEqual([
      { key: 'name', type: 'string' },
      { key: 'age', type: 'number' },
      { key: 'active', type: 'boolean' },
      { key: 'metadata', type: 'json' },
    ]);
  });

  it('Should return null when no fields are found', async () => {
    vi.mocked(prismaClient.fields.findMany).mockResolvedValue([]);

    const result = await fieldsService.getEntityFieldsTypes('unknown_entity');

    expect(prismaClient.fields.findMany).toHaveBeenCalledWith({
      where: { model: 'unknown_entity' },
      select: { type: true, key: true },
    });

    expect(result).toBeNull();
  });

  it('Should handle empty array of fields', async () => {
    vi.mocked(prismaClient.fields.findMany).mockResolvedValue([]);

    const result = await fieldsService.getEntityFieldsTypes('users');

    expect(prismaClient.fields.findMany).toHaveBeenCalledWith({
      where: { model: 'users' },
      select: { type: true, key: true },
    });

    expect(result).toBeNull();
  });

  it('Should handle database errors gracefully', async () => {
    const dbError = new Error('Database connection failed');
    vi.mocked(prismaClient.fields.findMany).mockRejectedValue(dbError);

    await expect(fieldsService.getEntityFieldsTypes('users')).rejects.toThrow(
      'Database connection failed'
    );

    expect(prismaClient.fields.findMany).toHaveBeenCalledWith({
      where: { model: 'users' },
      select: { type: true, key: true },
    });
  });
});
