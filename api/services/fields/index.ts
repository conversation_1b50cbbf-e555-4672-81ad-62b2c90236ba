import { injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';

interface FieldTypes {
  key: string;
  type: string;
}
@injectable()
export class FieldsService implements IFieldsService {
  async getEntityFieldsTypes(dataEntity: string): Promise<FieldTypes[] | null> {
    const fields = await prismaClient.fields.findMany({
      where: { model: dataEntity },
      select: { type: true, key: true },
    });
    if (fields && fields.length > 0) {
      return fields.map((field) => ({ key: field.key, type: field.type }));
    }
    return null;
  }
}

export interface IFieldsService {
  getEntityFieldsTypes(dataEntity: string): Promise<FieldTypes[] | null>;
}
