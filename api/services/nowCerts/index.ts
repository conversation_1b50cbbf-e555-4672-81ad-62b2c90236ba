import axios, { type AxiosInstance } from 'axios';
import { inject, injectable } from 'inversify';

import { ConfigService } from '@/services/config';
import type {
  <PERSON><PERSON>ist,
  CarrierDetailList,
  InsuredDetailList,
  ListResponse,
  LoginResult,
  PolicyAgent,
  PolicyDetailList,
} from '@/services/nowCerts/interface';

@injectable()
export class NowCertsService {
  endpoint: string = 'https://api.nowcerts.com/api/';

  username: string;

  password: string;

  token: string;
  @inject(ConfigService) configService: ConfigService;
  private _client: AxiosInstance;

  loadConfig(config: { username: string; password: string }) {
    this.username = config.username;
    this.password = config.password;
  }

  get client(): AxiosInstance {
    if (this._client) {
      return this._client;
    }
    this._client = axios.create({ baseURL: this.endpoint });
    this._client.interceptors.request.use(async (config) => {
      const token = await this.getToken();
      config.headers.set('authorization', `Bearer ${token}`);
      return config;
    });
    return this._client;
  }
  async getToken() {
    if (this.token) {
      return this.token;
    }
    const { access_token } = await this.login();
    this.token = access_token;
    return access_token;
  }

  async login() {
    const client = axios.create({ baseURL: this.endpoint });
    const search = new URLSearchParams({
      username: this.username,
      password: this.password,
      grant_type: 'password',
      client_id: 'ngAuthApp',
    });
    const { data } = await client.post<LoginResult>(
      '/token',
      search.toString(),
      {
        headers: {
          'Content-Type': 'text/plain',
        },
      }
    );
    return data;
  }

  async getAgentList(params: {
    $count?: boolean;
    $orderby?: string;
    $skip?: number;
    $top?: number;
  }) {
    const { data } = await this.client<ListResponse<AgentList>>(
      '/AgentList()',
      {
        params: {
          $orderBy: 'firstName asc',
          $skip: 0,
          $top: 100,
          $count: true,
          ...params,
        },
      }
    );
    return data;
  }

  async getPolicyAgents(policyDatabaseIds: string[]) {
    const { data } = await this.client.post<PolicyAgent[]>(
      '/Policy/PolicyAgents',
      {
        PolicyDatabaseId: policyDatabaseIds,
      }
    );
    return data;
  }

  async getPolicyDetailList(params: {
    $filter?: string;
    $count?: boolean;
    $orderby?: string;
    $skip?: number;
    $top?: number;
  }) {
    const { data } = await this.client<ListResponse<PolicyDetailList>>(
      '/PolicyDetailList()',
      {
        params: {
          $orderby: 'number asc',
          $skip: 0,
          $top: 100,
          $count: true,
          ...params,
        },
      }
    );
    return data;
  }

  async getInsuredDetailList(params: {
    $count?: boolean;
    $orderby?: string;
    $skip?: number;
    $top?: number;
    $filter?: string;
  }) {
    const { data } = await this.client<ListResponse<InsuredDetailList>>(
      '/InsuredDetailList',
      {
        params: {
          $orderby: 'changeDate asc',
          $skip: 0,
          $top: 100,
          $count: true,
          ...params,
        },
      }
    );
    return data;
  }

  async getCarrierDetailList(params: {
    $count?: boolean;
    $orderby?: string;
    $skip?: number;
    $top?: number;
    $filter?: string;
  }) {
    const { data } = await this.client<ListResponse<CarrierDetailList>>(
      '/CarrierDetailList()',
      {
        params: {
          $orderBy: 'changeDate asc',
          $skip: 0,
          $top: 100,
          $count: true,
          ...params,
        },
      }
    );
    return data;
  }
}
