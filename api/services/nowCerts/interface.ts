export interface LoginResult {
  '.expires': string;
  '.issued': string;
  access_token: string;
  agencyId: string;
  agencyName: string;
  as_client_id: string;
  contactId: string;
  email: string;
  expires_in: number;
  isAgencyOwner: string;
  refresh_token: string;
  token_type: string;
  userDisplayName: string;
  userId: string;
  userName: string;
  userType: string;
}

export interface AgentList {
  active: boolean;
  addressLine1: string | null;
  addressLine2: string | null;
  assignCommissionIfCSR: boolean;
  cellPhone: string | null;
  changeDate: string;
  city: string | null;
  county: string | null;
  email: string;
  fax: string | null;
  firstName: string;
  id: string;
  isDefaultAgent: boolean;
  isSuperVisior: boolean;
  lastName: string;
  npnNumber: string | null;
  phone: string | null;
  primaryOfficeName: string | null;
  primaryRole: string;
  state: string;
  useAgentIfNotDefault: boolean;
  userDisplayName: string;
  userId: string | null;
  zipCode: string | null;
}

export interface ListResponse<T> {
  '@odata.context': string;
  '@odata.count'?: number;
  value: T[];
}

export interface LineOfBusiness {
  databaseId: string;
  lineOfBusinessClassId: string;
  lineOfBusinessClassName: string;
  lineOfBusinessId: string;
  lineOfBusinessName: string;
  policyDatabaseId: string;
}

export interface PolicyDetailList {
  active: boolean;
  additionalPolicyStatus: string;
  billingType: string;
  bindDate: string;
  binderId: string | null;
  businessSubType: string;
  businessType: string;
  cancellationDate: string | null;
  carrierNAIC: string | null;
  carrierName: string;
  changeDate: string;
  createDate: string;
  databaseId: string;
  description: string | null;
  effectiveDate: string;
  expirationDate: string;
  inceptionDate: string;
  insuredCommercialName: string;
  insuredDatabaseId: string;
  insuredEmail: string;
  insuredFirstName: string | null;
  insuredLastName: string | null;
  insuredType: string;
  isQuote: boolean;
  lastChangeUserId: string;
  lastChangeUserName: string;
  lineOfBusinesses: LineOfBusiness[];
  mgaName: string | null;
  number: string;
  percentageChange: number;
  policyTerm: number;
  policyTermFormatted: string;
  primaryOfficeDatabaseId: string | null;
  productName: string | null;
  referralSourceDatabaseId: string | null;
  referralSourceName: string;
  reinstatementDate: string | null;
  status: string;
  totalAgencyCommission: number | null;
  totalNonPremium: number | null;
  totalPremium: number;
}

export interface CarrierDetailList {
  active: boolean;
  addressLine1: string | null;
  addressLine2: string | null;
  cellPhone: string;
  changeDate: string;
  city: string | null;
  createDate: string;
  customerId: string | null;
  description: string | null;
  eMail: string | null;
  eMail2: string | null;
  eMail3: string | null;
  fax: string;
  fein: string;
  id: string;
  insuredId: string;
  naic: string | null;
  name: string;
  phone: string;
  smsPhone: string | null;
  state: string | null;
  website: string | null;
  zipCode: string | null;
}

export interface PolicyAgent {
  databaseId: string;
  firstName: string;
  lastName: string;
  order: number;
  policyDatabaseId: string;
}

export interface InsuredDetailList {
  acquisitionDate: string;
  active: boolean;
  addressLine1: string;
  addressLine2: string;
  agentOfRecordDate: string;
  cellPhone: string;
  changeDate: string;
  city: string;
  clientType: string;
  coInsured_DateOfBirth: string;
  coInsured_FirstName: string;
  coInsured_LastName: string;
  coInsured_MiddleName: string;
  commercialName: string;
  createDate: string;
  customerId: string;
  dateOfBirth: string;
  dba: string;
  description: string;
  drInformation: string;
  eMail: string;
  eMail2: string;
  eMail3: string;
  fax: string;
  fein: string;
  firstName: string;
  greetingName: string;
  id: string;
  insuredId: string;
  insuredType: string;
  isSuperVisior: boolean;
  lastChangeUserId: string;
  lastChangeUserName: string;
  lastName: string;
  medicareHICN: string;
  middleName: string;
  naics: string;
  partAEffectiveDate: string;
  partBEffectiveDate: string;
  personNotes: string;
  phone: string;
  preferredLanguage: string;
  prospectType: string;
  referralSourceCompanyName: string;
  rxInformation: string;
  sicCode: string;
  sicDescription: string;
  smsPhone: string;
  state: string;
  type: string;
  userDisplayName: string;
  website: string;
  yearBusinessStarted: string;
  yearsInBusiness: number;
  zipCode: string;
}

export type PolicyStatus =
  | 'Active'
  | 'Cancelled'
  | 'Expired'
  | 'Fintary'
  | 'Non-Renewal'
  | 'Pending Cancel'
  | 'Renewed'
  | 'Renewing'
  | 'Replaced';
