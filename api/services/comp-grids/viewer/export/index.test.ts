import { describe, it, expect, vi, beforeEach } from 'vitest';

import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';
import {
  CompGridsViewerExportService,
  type ICompGridsViewerExportService,
} from './index';
import { fetchCompGridRatesWithRawQuery, runInBatch } from '@/lib/helpers';

vi.mock('@/lib/helpers', () => ({
  fetchCompGridRatesWithRawQuery: vi.fn(),
  runInBatch: vi.fn(),
}));

vi.mock('@/lib/prisma', () => {
  const mockPrismaClient = {
    comp_grids: {
      findMany: vi.fn(),
    },
    comp_grid_levels: {
      findMany: vi.fn(),
    },
  };

  return {
    prismaClient: mockPrismaClient,
    default: mockPrismaClient,
  };
});

describe('CompGridsViewerExportService', () => {
  let compGridsViewerExportService: ICompGridsViewerExportService;

  beforeEach(() => {
    vi.clearAllMocks();
    compGridsViewerExportService = new CompGridsViewerExportService();

    // Mock Prisma client methods
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.comp_grids.findMany as any).mockResolvedValue([]);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (prismaClient.comp_grid_levels.findMany as any).mockResolvedValue([]);

    // Mock helper functions
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (runInBatch as any).mockImplementation(async ({ items, onBatch }) => {
      if (items.length === 0) return [];
      return await onBatch(items);
    });

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (fetchCompGridRatesWithRawQuery as any).mockResolvedValue([]);
  });

  const mockCompGridsData = [
    {
      id: 1,
      str_id: 'comp-grid-1',
      name: 'Test Comp Grid 1',
      rate_fields: ['carrier_rate', 'house_rate'],
      company: {
        id: 1,
        str_id: 'company-1',
        company_name: 'Insurance Company A',
      },
      comp_grid_levels: [
        {
          id: 201,
          str_id: 'level-1',
          name: 'Agent Level',
          order_index: 1,
        },
        {
          id: 202,
          str_id: 'level-2',
          name: 'Manager Level',
          order_index: 2,
        },
      ],
      related_comp_grid_levels: [],
      comp_grid_criteria: [
        {
          id: 101,
          str_id: 'criterion-1',
          comp_grid_id: 1,
          compensation_type: 'Commission',
          filter_date_field: null,
          issue_age_start: null,
          issue_age_end: 65,
          premium_min: null,
          premium_max: null,
          policy_year_start: 1,
          policy_year_end: 5,
          transaction_type: null,
          payment_mode: null,
          comp_grid_product: {
            id: 1,
            name: 'Life Product',
            type: 'Life Insurance',
            company_products: [],
          },
          date_ranges: [],
          company: {
            id: 1,
            str_id: 'company-1',
            company_name: 'Insurance Company A',
          },
        },
        {
          id: 102,
          str_id: 'criterion-2',
          comp_grid_id: 1,
          compensation_type: 'Bonus',
          filter_date_field: null,
          issue_age_start: 25,
          issue_age_end: null,
          premium_min: null,
          premium_max: null,
          policy_year_start: 1,
          policy_year_end: 10,
          transaction_type: null,
          payment_mode: null,
          comp_grid_product: {
            id: 2,
            name: 'Health Product',
            type: 'Health Insurance',
            company_products: [],
          },
          date_ranges: [],
          company: {
            id: 1,
            str_id: 'company-1',
            company_name: 'Insurance Company A',
          },
        },
      ],
      comp_grid_products: [],
    },
    {
      id: 2,
      str_id: 'comp-grid-2',
      name: 'Test Comp Grid 2',
      rate_fields: null,
      company: {
        id: 2,
        str_id: 'company-2',
        company_name: 'Insurance Company B',
      },
      comp_grid_levels: [],
      related_comp_grid_levels: [],
      comp_grid_criteria: [
        {
          id: 103,
          str_id: 'criterion-3',
          comp_grid_id: 2,
          compensation_type: 'Override',
          filter_date_field: null,
          issue_age_start: 30,
          issue_age_end: 70,
          premium_min: null,
          premium_max: null,
          policy_year_start: null,
          policy_year_end: null,
          transaction_type: null,
          payment_mode: null,
          comp_grid_product: null,
          date_ranges: [],
          company: {
            id: 2,
            str_id: 'company-2',
            company_name: 'Insurance Company B',
          },
        },
      ],
      comp_grid_products: [],
    },
  ];

  const mockGridCriteriaRatesData = [
    {
      id: 1,
      str_id: 'rate-1',
      carrier_rate: 0.75,
      house_rate: 0.25,
      rate: 1.0,
      comp_grid_criterion_id: 101,
      comp_grid_level_id: 201,
      comp_grid_level: {
        id: 201,
        str_id: 'level-1',
        name: 'Agent Level',
      },
      date_ranges: [],
    },
    {
      id: 2,
      str_id: 'rate-2',
      carrier_rate: 0.8,
      house_rate: 0.2,
      rate: 1.0,
      comp_grid_criterion_id: 102,
      comp_grid_level_id: 201,
      comp_grid_level: {
        id: 201,
        str_id: 'level-1',
        name: 'Agent Level',
      },
      date_ranges: [],
    },
    {
      id: 3,
      str_id: 'rate-3',
      carrier_rate: 0.6,
      house_rate: 0.4,
      rate: 1.0,
      comp_grid_criterion_id: 101,
      comp_grid_level_id: 202,
      comp_grid_level: {
        id: 202,
        str_id: 'level-2',
        name: 'Manager Level',
      },
      date_ranges: [],
    },
    {
      id: 4,
      str_id: 'rate-4',
      carrier_rate: 0.9,
      house_rate: 0.1,
      rate: 1.0,
      comp_grid_criterion_id: 103,
      comp_grid_level_id: 202,
      comp_grid_level: {
        id: 202,
        str_id: 'level-2',
        name: 'Manager Level',
      },
      date_ranges: [],
    },
  ];

  // Mock data for tests without levels
  const mockCompGridsDataWithoutLevels = mockCompGridsData.map((grid) => ({
    ...grid,
    comp_grid_levels: undefined,
    related_comp_grid_levels: undefined,
  }));

  describe('getCompGridsViewerExportData', () => {
    it('given valid data with comp grid levels, should return complete export data structure', async () => {
      // Mock the comp grids data
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );

      // Mock the rates data
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (fetchCompGridRatesWithRawQuery as any).mockResolvedValue(
        mockGridCriteriaRatesData
      );

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123',
          undefined,
          ['level-1', 'level-2']
        );

      expect(result).toBeDefined();
      expect(result.data).toHaveLength(2);

      // Check first comp grid structure
      const firstGrid = result.data[0];
      expect(firstGrid.carrier).toEqual({
        str_id: 'company-1',
        name: 'Insurance Company A',
      });
      expect(firstGrid.comp_grid).toEqual({
        id: 1,
        str_id: 'comp-grid-1',
        name: 'Test Comp Grid 1',
        rate_fields: ['carrier_rate', 'house_rate'],
      });
      expect(firstGrid.criteria).toHaveLength(2);

      // Check criteria structure with levels
      const firstCriterion = firstGrid.criteria[0];
      expect(firstCriterion.criterion_id).toBe(101);
      expect(firstCriterion.issue_age_start).toBe(0); // Should be set to 0 due to business logic
      expect(firstCriterion.levels).toHaveLength(2);

      // Check levels and rates
      const firstLevel = firstCriterion.levels[0];
      expect(firstLevel.level_id).toBe(201);
      expect(firstLevel.name).toBe('Agent Level');
      expect(firstLevel.rates).toHaveLength(1); // Only rates for this criterion
      expect(firstLevel.rates[0].comp_grid_criterion_id).toBe(101);
    });

    it('given valid data without comp grid levels, should return data structure with empty levels', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsDataWithoutLevels
      );

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123'
        );

      expect(result).toBeDefined();
      expect(result.data).toHaveLength(2);

      // Check first comp grid structure
      const firstGrid = result.data[0];
      expect(firstGrid.carrier).toEqual({
        str_id: 'company-1',
        name: 'Insurance Company A',
      });
      expect(firstGrid.comp_grid).toEqual({
        id: 1,
        str_id: 'comp-grid-1',
        name: 'Test Comp Grid 1',
        rate_fields: ['carrier_rate', 'house_rate'],
      });
      expect(firstGrid.criteria).toHaveLength(2);

      // Check criteria structure without levels
      const firstCriterion = firstGrid.criteria[0];
      expect(firstCriterion.criterion_id).toBe(101);
      expect(firstCriterion.issue_age_start).toBe(0); // Should be set to 0 due to business logic
      expect(firstCriterion.levels).toHaveLength(0); // No levels when not requested

      // Verify fetchCompGridRatesWithRawQuery was not called
      expect(fetchCompGridRatesWithRawQuery).not.toHaveBeenCalled();
    });

    it('given company filter, should call prisma with correct where clause', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue([]);

      await compGridsViewerExportService.getCompGridsViewerExportData(
        'account-123',
        ['company-1', 'company-2']
      );

      expect(prismaClient.comp_grids.findMany).toHaveBeenCalledWith({
        where: {
          account_id: 'account-123',
          state: DataStates.ACTIVE,
          company: {
            state: DataStates.ACTIVE,
            str_id: { in: ['company-1', 'company-2'] },
          },
        },
        select: expect.any(Object),
      });
    });

    it('given comp grid levels filter, should include levels in select with correct where clause', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue([]);
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (fetchCompGridRatesWithRawQuery as any).mockResolvedValue([]);

      await compGridsViewerExportService.getCompGridsViewerExportData(
        'account-123',
        undefined,
        ['level-1', 'level-2']
      );

      expect(prismaClient.comp_grids.findMany).toHaveBeenCalledWith({
        where: {
          account_id: 'account-123',
          state: DataStates.ACTIVE,
          company: {
            state: DataStates.ACTIVE,
          },
        },
        select: expect.objectContaining({
          comp_grid_levels: expect.objectContaining({
            where: {
              str_id: { in: ['level-1', 'level-2'] },
              state: DataStates.ACTIVE,
            },
          }),
          related_comp_grid_levels: expect.objectContaining({
            where: {
              str_id: { in: ['level-1', 'level-2'] },
              state: DataStates.ACTIVE,
            },
          }),
        }),
      });
    });

    it('given no comp grid levels filter, should not fetch comp grid levels', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsDataWithoutLevels
      );

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123'
        );

      expect(fetchCompGridRatesWithRawQuery).not.toHaveBeenCalled();
      expect(result.data[0].criteria[0].levels).toHaveLength(0);
    });

    it('given empty comp grid levels array, should not fetch comp grid levels', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsDataWithoutLevels
      );

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123',
          undefined,
          []
        );

      expect(fetchCompGridRatesWithRawQuery).not.toHaveBeenCalled();
      expect(result.data[0].criteria[0].levels).toHaveLength(0);
    });

    it('given issue_age_start null and issue_age_end has value, should set issue_age_start to 0', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grid_levels.findMany as any).mockResolvedValue([]);

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123'
        );

      const firstCriterion = result.data[0].criteria[0];
      expect(firstCriterion.issue_age_start).toBe(0); // Was null, issue_age_end is 65
    });

    it('given issue_age_start has value, should preserve original value', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grid_levels.findMany as any).mockResolvedValue([]);

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123'
        );

      const secondCriterion = result.data[0].criteria[1];
      expect(secondCriterion.issue_age_start).toBe(25); // Original value preserved
    });

    it('given comp_grid_product is null, should set product_type to null', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grid_levels.findMany as any).mockResolvedValue([]);

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123'
        );

      const thirdCriterion = result.data[1].criteria[0];
      expect(thirdCriterion.product_type).toBeNull();
    });

    it('given rate_fields is null, should default to empty array', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123'
        );

      const secondGrid = result.data[1];
      expect(secondGrid.comp_grid.rate_fields).toEqual([]);
    });

    it('given levels with rates for different criteria, should filter rates correctly', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (fetchCompGridRatesWithRawQuery as any).mockResolvedValue(
        mockGridCriteriaRatesData
      );

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123',
          undefined,
          ['level-1', 'level-2']
        );

      const firstCriterion = result.data[0].criteria[0]; // Criterion 101
      const agentLevel = firstCriterion.levels[0]; // Level 201
      const managerLevel = firstCriterion.levels[1]; // Level 202

      // Agent level should have 1 rate for criterion 101
      expect(agentLevel.rates).toHaveLength(1);
      expect(agentLevel.rates[0].comp_grid_criterion_id).toBe(101);

      // Manager level should have 1 rate for criterion 101
      expect(managerLevel.rates).toHaveLength(1);
      expect(managerLevel.rates[0].comp_grid_criterion_id).toBe(101);
    });

    it('given empty comp grids data, should return empty data array', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue([]);

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123'
        );

      expect(result.data).toHaveLength(0);
    });

    it('given database error on comp grids fetch, should propagate the error', async () => {
      const error = new Error('Database connection failed');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockRejectedValue(error);

      await expect(
        compGridsViewerExportService.getCompGridsViewerExportData('account-123')
      ).rejects.toThrow('Database connection failed');
    });

    it('given database error on comp grid rates fetch, should propagate the error', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );
      const error = new Error('Rates fetch failed');
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (fetchCompGridRatesWithRawQuery as any).mockRejectedValue(error);

      await expect(
        compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123',
          undefined,
          ['level-1']
        )
      ).rejects.toThrow('Rates fetch failed');
    });

    it('given no company filter, should not include company filter in where clause', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue([]);

      await compGridsViewerExportService.getCompGridsViewerExportData(
        'account-123'
      );

      expect(prismaClient.comp_grids.findMany).toHaveBeenCalledWith({
        where: {
          account_id: 'account-123',
          state: DataStates.ACTIVE,
          company: {
            state: DataStates.ACTIVE,
          },
        },
        select: expect.any(Object),
      });
    });

    it('given complete data, should include all required fields in response', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.comp_grids.findMany as any).mockResolvedValue(
        mockCompGridsData
      );
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (fetchCompGridRatesWithRawQuery as any).mockResolvedValue(
        mockGridCriteriaRatesData
      );

      const result =
        await compGridsViewerExportService.getCompGridsViewerExportData(
          'account-123',
          undefined,
          ['level-1']
        );

      const exportItem = result.data[0];

      // Check all required fields are present
      expect(exportItem).toHaveProperty('carrier');
      expect(exportItem).toHaveProperty('comp_grid');
      expect(exportItem).toHaveProperty('criteria');

      expect(exportItem.carrier).toHaveProperty('str_id');
      expect(exportItem.carrier).toHaveProperty('name');

      expect(exportItem.comp_grid).toHaveProperty('id');
      expect(exportItem.comp_grid).toHaveProperty('str_id');
      expect(exportItem.comp_grid).toHaveProperty('name');
      expect(exportItem.comp_grid).toHaveProperty('rate_fields');

      const criterion = exportItem.criteria[0];
      expect(criterion).toHaveProperty('criterion_id');
      expect(criterion).toHaveProperty('criterion_str_id');
      expect(criterion).toHaveProperty('product_type');
      expect(criterion).toHaveProperty('compensation_type');
      expect(criterion).toHaveProperty('issue_age_start');
      expect(criterion).toHaveProperty('issue_age_end');
      expect(criterion).toHaveProperty('policy_year_start');
      expect(criterion).toHaveProperty('policy_year_end');
      expect(criterion).toHaveProperty('levels');

      const level = criterion.levels[0];
      expect(level).toHaveProperty('level_id');
      expect(level).toHaveProperty('level_str_id');
      expect(level).toHaveProperty('name');
      expect(level).toHaveProperty('rates');

      const rate = level.rates[0];
      expect(rate).toHaveProperty('comp_grid_criterion_id');
      expect(rate).toHaveProperty('comp_grid_level_id');
      expect(rate).toHaveProperty('carrier_rate');
      expect(rate).toHaveProperty('house_rate');
      expect(rate).toHaveProperty('rate');
    });
  });
});
