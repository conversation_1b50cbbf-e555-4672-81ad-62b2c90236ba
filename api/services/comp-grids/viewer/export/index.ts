import { injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';
import { fetchCompGridRatesWithRawQuery, runInBatch } from '@/lib/helpers';
import { DataStates } from '@/types';
import type {
  CompGridCriterion,
  CompGridExportItem,
  CompGridLevel,
  CompGridRate,
  CompGridRateWithLevel,
  CompGridsViewerExportResponse,
  CompGridWithRelations,
} from './types';

@injectable()
export class CompGridsViewerExportService
  implements ICompGridsViewerExportService
{
  async getCompGridsViewerExportData(
    accountId: string,
    companyStrIds?: string[],
    compGridLevelsStrIds?: string[]
  ): Promise<CompGridsViewerExportResponse> {
    const compGrids = await this.fetchCompGrids(
      accountId,
      companyStrIds,
      compGridLevelsStrIds
    );

    const gridCriteriaRateMap = new Map<number, CompGridRateWithLevel[]>();

    if (compGridLevelsStrIds?.length) {
      const allCriteriaIds = compGrids.flatMap((compGrid) =>
        compGrid.comp_grid_criteria.map((criteria) => criteria.id)
      );

      // Fetch rates using the same approach as the comp grid viewer
      const gridCriteriaRate = await runInBatch({
        items: allCriteriaIds,
        batchSize: 100,
        name: 'export-grid-criteria-rate',
        onBatch: async (ids) => {
          const effectiveDate = new Date();

          return fetchCompGridRatesWithRawQuery({
            prismaClient,
            accountId,
            criteriaIds: ids,
            effectiveDate,
          });
        },
      });

      for (const rate of gridCriteriaRate) {
        const rates =
          gridCriteriaRateMap.get(rate.comp_grid_criterion_id) || [];
        const existingRate = rates.find(
          (r) => r.comp_grid_level.id === rate.comp_grid_level.id
        );
        if (existingRate) {
          if (existingRate.id < rate.id) {
            const newRates = rates.map((r) =>
              r.id === existingRate.id ? rate : r
            );
            gridCriteriaRateMap.set(rate.comp_grid_criterion_id, newRates);
          }
        } else {
          gridCriteriaRateMap.set(rate.comp_grid_criterion_id, [
            ...rates,
            rate,
          ]);
        }
      }
    }

    const exportData = this.processExportData(compGrids, gridCriteriaRateMap);

    return {
      data: exportData,
    };
  }

  private async fetchCompGrids(
    accountId: string,
    companyStrIds?: string[],
    compGridLevelsStrIds?: string[]
  ): Promise<CompGridWithRelations[]> {
    const companyWhere = companyStrIds?.length
      ? { str_id: { in: companyStrIds } }
      : {};
    const shouldFetchLevels = compGridLevelsStrIds?.length;
    const levelFilter = shouldFetchLevels
      ? {
          str_id: { in: compGridLevelsStrIds },
          state: DataStates.ACTIVE,
        }
      : { state: DataStates.ACTIVE };

    return await prismaClient.comp_grids.findMany({
      where: {
        account_id: accountId,
        state: DataStates.ACTIVE,
        company: {
          state: DataStates.ACTIVE,
          ...companyWhere,
        },
      },
      select: {
        id: true,
        str_id: true,
        company_id: true,
        name: true,
        rate_fields: true,
        company: {
          select: {
            id: true,
            str_id: true,
            company_name: true,
          },
        },
        comp_grid_levels: shouldFetchLevels
          ? {
              where: levelFilter,
              select: {
                id: true,
                str_id: true,
                order_index: true,
                name: true,
              },
            }
          : false,
        related_comp_grid_levels: shouldFetchLevels
          ? {
              where: levelFilter,
              select: {
                id: true,
                str_id: true,
                order_index: true,
                name: true,
              },
            }
          : false,
        comp_grid_criteria: {
          where: {
            state: DataStates.ACTIVE,
          },
          select: {
            id: true,
            str_id: true,
            comp_grid_id: true,
            compensation_type: true,
            filter_date_field: true,
            issue_age_end: true,
            issue_age_start: true,
            premium_min: true,
            premium_max: true,
            policy_year_end: true,
            policy_year_start: true,
            transaction_type: true,
            payment_mode: true,
            comp_grid_product: {
              where: { state: DataStates.ACTIVE },
              select: {
                id: true,
                name: true,
                type: true,
                company_products: {
                  where: { state: DataStates.ACTIVE },
                  select: {
                    id: true,
                    product_name: true,
                    product_type: true,
                  },
                },
              },
            },
            date_ranges: {
              where: { state: DataStates.ACTIVE },
              select: {
                id: true,
                start_date: true,
                end_date: true,
                name: true,
              },
            },
            company: {
              select: {
                id: true,
                str_id: true,
                company_name: true,
              },
            },
          },
        },
        comp_grid_products: {
          where: { state: DataStates.ACTIVE },
          select: {
            id: true,
            name: true,
            type: true,
          },
        },
      },
    });
  }

  private processExportData(
    compGrids: CompGridWithRelations[],
    gridCriteriaRateMap: Map<number, CompGridRateWithLevel[]>
  ): CompGridExportItem[] {
    return compGrids.map((compGrid) => {
      const compGridLevels = compGrid.comp_grid_levels || [];
      const relatedCompGridLevels = compGrid.related_comp_grid_levels || [];

      const uniqueLevels = new Map();
      for (const level of [...compGridLevels, ...relatedCompGridLevels]) {
        uniqueLevels.set(level.id, level);
      }

      const mergedLevels = Array.from(uniqueLevels.values()).sort((a, b) => {
        const orderA = a.order_index ?? Number.MAX_SAFE_INTEGER;
        const orderB = b.order_index ?? Number.MAX_SAFE_INTEGER;
        return orderA - orderB;
      });

      const processedCriteria = this.processCriteria(
        compGrid.comp_grid_criteria,
        mergedLevels,
        gridCriteriaRateMap
      );

      const rateFields = compGrid.rate_fields || [];
      const processedRateFields = Array.isArray(rateFields)
        ? rateFields.map((field) => (field === 'total_rate' ? 'rate' : field))
        : [];

      return {
        carrier: {
          str_id: compGrid.company.str_id,
          name: compGrid.company.company_name,
        },
        comp_grid: {
          id: compGrid.id,
          str_id: compGrid.str_id,
          name: compGrid.name,
          rate_fields: processedRateFields,
        },
        criteria: processedCriteria,
      };
    });
  }

  private processCriteria(
    criteria: CompGridWithRelations['comp_grid_criteria'],
    compGridLevels: Array<{ id: number; str_id?: string; name?: string }>,
    gridCriteriaRateMap: Map<number, CompGridRateWithLevel[]>
  ): CompGridCriterion[] {
    return criteria.map((criterion) => {
      const issueAgeStart = this.calculateIssueAgeStart(criterion);
      const levelsForCriterion = this.processLevelsForCriterion(
        criterion.id,
        compGridLevels,
        gridCriteriaRateMap
      );

      return {
        criterion_id: criterion.id,
        criterion_str_id: criterion.str_id,
        product_type: criterion.comp_grid_product?.type || null,
        product_name: criterion.comp_grid_product?.name || null,
        compensation_type: criterion.compensation_type,
        issue_age_start: issueAgeStart,
        issue_age_end: criterion.issue_age_end,
        policy_year_start: criterion.policy_year_start,
        policy_year_end: criterion.policy_year_end,
        levels: levelsForCriterion,
      };
    });
  }

  private calculateIssueAgeStart(
    criterion: CompGridWithRelations['comp_grid_criteria'][0]
  ): number | null {
    return criterion.issue_age_start === null &&
      criterion.issue_age_end !== null
      ? 0
      : criterion.issue_age_start;
  }

  private processLevelsForCriterion(
    criterionId: number,
    compGridLevels: Array<{ id: number; str_id?: string; name?: string }>,
    gridCriteriaRateMap: Map<number, CompGridRateWithLevel[]>
  ): CompGridLevel[] {
    return compGridLevels.map((level) => {
      const allRatesForCriterion = gridCriteriaRateMap.get(criterionId) || [];
      const ratesForThisLevelAndCriterion: CompGridRate[] = allRatesForCriterion
        .filter((rate) => rate.comp_grid_level.id === level.id)
        .map((rate) => ({
          comp_grid_criterion_id: rate.comp_grid_criterion_id,
          comp_grid_level_id: rate.comp_grid_level_id,
          carrier_rate: rate.carrier_rate,
          house_rate: rate.house_rate,
          rate: rate.rate,
        }));

      return {
        level_id: level.id,
        level_str_id: level.str_id,
        name: level.name,
        rates: ratesForThisLevelAndCriterion,
      };
    });
  }
}

export interface ICompGridsViewerExportService {
  getCompGridsViewerExportData(
    accountId: string,
    companyStrIds?: string[],
    compGridLevelsStrIds?: string[]
  ): Promise<CompGridsViewerExportResponse>;
}
