import type { Prisma } from '@prisma/client';

type CompGridWithAllRelations = Prisma.comp_gridsGetPayload<{
  select: {
    id: true;
    str_id: true;
    company_id: true;
    name: true;
    rate_fields: true;
    company: {
      select: {
        id: true;
        str_id: true;
        company_name: true;
      };
    };
    comp_grid_levels: {
      select: {
        id: true;
        str_id: true;
        order_index: true;
        name: true;
      };
    };
    related_comp_grid_levels: {
      select: {
        id: true;
        str_id: true;
        order_index: true;
        name: true;
      };
    };
    comp_grid_criteria: {
      select: {
        id: true;
        str_id: true;
        comp_grid_id: true;
        compensation_type: true;
        filter_date_field: true;
        issue_age_end: true;
        issue_age_start: true;
        premium_min: true;
        premium_max: true;
        policy_year_end: true;
        policy_year_start: true;
        transaction_type: true;
        payment_mode: true;
        comp_grid_product: {
          select: {
            id: true;
            name: true;
            type: true;
            company_products: {
              select: {
                id: true;
                product_name: true;
                product_type: true;
              };
            };
          };
        };
        date_ranges: {
          select: {
            id: true;
            start_date: true;
            end_date: true;
            name: true;
          };
        };
        company: {
          select: {
            id: true;
            str_id: true;
            company_name: true;
          };
        };
      };
    };
    comp_grid_products: {
      select: {
        id: true;
        name: true;
        type: true;
      };
    };
  };
}>;

type CompGridWithoutLevels = Omit<
  CompGridWithAllRelations,
  'comp_grid_levels' | 'related_comp_grid_levels'
> & {
  comp_grid_levels?: undefined;
  related_comp_grid_levels?: undefined;
};

export type CompGridWithRelations =
  | CompGridWithAllRelations
  | CompGridWithoutLevels;

export type CompGridLevelWithRates = Prisma.comp_grid_levelsGetPayload<{
  include: {
    comp_grid_rates: {
      select: {
        comp_grid_criterion_id: true;
        comp_grid_level_id: true;
        carrier_rate: true;
        house_rate: true;
        rate: true;
      };
    };
  };
}>;

export interface CompGridRateWithLevel {
  id: number;
  str_id?: string;
  carrier_rate?: Prisma.Decimal;
  house_rate?: Prisma.Decimal;
  rate?: Prisma.Decimal;
  comp_grid_criterion_id: number;
  comp_grid_level_id: number;
  comp_grid_level: {
    id: number;
    str_id?: string;
    name?: string;
  };
  date_ranges?: Array<{
    id: number;
    start_date?: Date;
    end_date?: Date;
    name?: string;
  }>;
}

export interface CompGridRate {
  comp_grid_criterion_id: number;
  comp_grid_level_id: number;
  carrier_rate?: Prisma.Decimal;
  house_rate?: Prisma.Decimal;
  rate?: Prisma.Decimal;
}

export interface CompGridLevel {
  level_id: number;
  level_str_id?: string;
  name?: string;
  rates: CompGridRate[];
}

export interface CompGridCriterion {
  criterion_id: number;
  criterion_str_id?: string;
  product_type?: string;
  product_name?: string;
  compensation_type?: string;
  issue_age_start?: number;
  issue_age_end?: number;
  policy_year_start?: number;
  policy_year_end?: number;
  levels: CompGridLevel[];
}

export interface CarrierInfo {
  str_id?: string;
  name?: string;
}

export interface CompGridInfo {
  id: number;
  str_id?: string;
  name?: string;
  rate_fields: Prisma.JsonValue;
}

export interface CompGridExportItem {
  carrier: CarrierInfo;
  comp_grid: CompGridInfo;
  criteria: CompGridCriterion[];
}

export interface CompGridsViewerExportResponse {
  data: CompGridExportItem[];
}
