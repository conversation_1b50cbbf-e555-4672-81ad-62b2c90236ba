import { captureException } from '@sentry/nextjs';
import { inject, injectable } from 'inversify';

import { Config } from '@/lib/decorators';
import { ConfigService } from '@/services/config';
import { prismaClient } from '@/lib/prisma';
import { DocumentFileService } from '@/services/documents/fileService';
import { EmailerService } from '@/services/emailer';
import { NanonetsValidator, type ProcessDocumentType } from './validator';

interface NanonetsResult {
  message: string;
  result: Array<{
    message: string;
    request_file_id: string;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    prediction: any[];
    page: number;
    input: string;
  }>;
}

@injectable()
export class NanonetsService {
  @inject(ConfigService) configService: ConfigService;
  @inject(DocumentFileService) private documentFileService: DocumentFileService;
  @inject(EmailerService) private emailerService: EmailerService;

  @Config('NANONETS_MODEL_ID')
  private modelId: string;
  @Config('NANONETS_API_KEY')
  private apiKey: string;

  private readonly BASE_URL = 'https://app.nanonets.com/api/v2';
  private validator: NanonetsValidator;
  private authHeader: string;

  constructor(@inject(NanonetsValidator) validator: NanonetsValidator) {
    this.validator = validator;
  }

  private async initService() {
    if (!this.authHeader) {
      this.authHeader = `Basic ${Buffer.from(`${this.apiKey}:`).toString('base64')}`;
    }
  }

  private get uploadUrl() {
    return `${this.BASE_URL}/OCR/Model/${this.modelId}/LabelFile/`;
  }

  async processDocument(params: ProcessDocumentType) {
    try {
      await this.initService();
      const validatedData = this.validator.validateProcessParams(params);

      const document = await prismaClient.documents.findFirst({
        where: {
          id: validatedData.documentId,
          account_id: validatedData.accountId,
        },
      });

      if (!document) {
        throw new Error('Document not found');
      }

      const fileBuffer =
        await this.documentFileService.getFileFromStorage(document);
      const ocrResult = await this.submitOcrJob(fileBuffer, document.filename);
      const processedResult = this.processNanonetsData(ocrResult);

      return processedResult;
    } catch (error) {
      captureException(error);
      throw error;
    }
  }

  private async submitOcrJob(
    fileBuffer: Buffer,
    filename: string
  ): Promise<NanonetsResult> {
    try {
      const form = new FormData();
      const blob = new Blob([fileBuffer], { type: 'application/pdf' });
      form.append('file', blob, filename);

      const response = await fetch(`${this.uploadUrl}`, {
        method: 'POST',
        body: form,
        headers: {
          Authorization: this.authHeader,
        },
      });

      if (!response.ok) {
        if (response.status === 402) {
          await this.emailerService.sendEmail(
            '<EMAIL>',
            'Nanonets out of credits - document processing blocked',
            'Nanonets is out of credits. This blocks document processing.<br/><br/>Get more details<br/>https://app.nanonets.com/user/apps<br/>'
          );
        }
        throw new Error(`Nanonets error: ${response.status}`);
      }

      return (await response.json()) as NanonetsResult;
    } catch (error) {
      captureException(error);
      throw error;
    }
  }

  private processNanonetsData = (ocrResult: NanonetsResult) => {
    if (!Array.isArray(ocrResult.result)) {
      throw new Error('Input must be an array');
    }

    return ocrResult.result.flatMap((page) => {
      const pagePredictions = [];
      let currentBlock = [];
      let currentRow = null;
      let currentRowData = [];

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      page.prediction.forEach((eachLine) => {
        if (!eachLine.cells || !eachLine.cells.length) {
          return;
        }

        if (
          currentBlock.length > 0 &&
          eachLine.cells.length > 0 &&
          eachLine.cells[0].row < currentRow
        ) {
          pagePredictions.push(currentBlock);
          currentBlock = [];
          currentRow = null;
        }

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        eachLine.cells.forEach((cell) => {
          if (currentRow !== cell.row) {
            if (currentRowData.length > 0) {
              currentBlock.push([...currentRowData]);
            }
            currentRow = cell.row;
            currentRowData = [];
          }
          currentRowData.push(cell.text?.replace(/\n/g, ' ') || '');
        });
      });

      if (currentRowData.length > 0) {
        currentBlock.push([...currentRowData]);
      }
      if (currentBlock.length > 0) {
        pagePredictions.push(currentBlock);
      }

      if (pagePredictions.length === 0) {
        return {
          page: page.page + 1,
          prediction: [],
        };
      }

      return {
        page: page.page + 1,
        prediction: Array.isArray(pagePredictions[0][0])
          ? pagePredictions
          : [pagePredictions],
      };
    });
  };
}
