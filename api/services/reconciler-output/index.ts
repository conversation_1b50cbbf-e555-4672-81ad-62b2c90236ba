import type {
  Prisma,
  PrismaClient,
  report_data,
  statement_data,
} from '@prisma/client';
import { injectable } from 'inversify';
import { keyBy } from 'lodash-es';
import * as pgformat from 'pg-format';

import { runInBatch } from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import type { ExtAccountInfo } from '@/types';

@injectable()
export class ReconcilerOutputService {
  async getIncrementalStatements(
    query: { account_id: string; reconciler_id?: number; document_id?: string },
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    const { document_id } = query;
    const sql = `
       SELECT s.id  FROM statement_data s
      LEFT JOIN  reconciler_output ro ON ro.statement_id = s.id 
      LEFT JOIN reconcilers rs ON rs.id = ro.reconciler_id
      WHERE s.account_id = ${pgformat.literal(query.account_id)}
      AND s.state = 'active'
      AND ( ro.statement_id is NULL OR s.updated_at > ro.updated_at ${pgformat.string('OR ro.reconciler_hash != rs.hash')})
      ${document_id ? pgformat.string(`AND s.document_id = ${pgformat.literal(document_id)}`) : ''}
      `;

    const statementIds = (await prisma.$queryRawUnsafe(
      sql
    )) as statement_data[];
    const data = await runInBatch({
      items: statementIds,
      onBatch: async (batch) => {
        const ids = batch.map((d) => d.id);
        return await prisma.statement_data.findMany({
          where: { id: { in: ids } },
        });
      },
      batchSize: 100,
    });

    const relatedReportIds = data.map((d) => d.report_data_id).filter((r) => r);
    const relatedStatements = await prisma.statement_data.findMany({
      where: { report_data_id: { in: relatedReportIds }, state: 'active' },
    });

    // Return uniq statement
    return Object.values(keyBy([...data, ...relatedStatements], 'id'));
  }

  async getIncrementalReports(
    query: {
      account_id: string;
      reconciler_id?: number;
      related_report_ids?: number[];
    },
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    const columns = await prisma.$queryRaw<
      Array<{ column_name: string }>
    >`SELECT column_name FROM information_schema.columns WHERE table_name = 'report_data'`;

    const excludedFields = ['embedding', 'embedding_info'];
    const sourceAlias = 'r';
    const columNames = columns
      .map((c) => c.column_name)
      .filter((r) => !excludedFields.includes(r))
      .map((r) => `${sourceAlias}.${r}`);

    const sql = pgformat(
      `
      SELECT %s FROM report_data r
      LEFT JOIN  reconciler_output ro ON ro.report_id= r.id 
      LEFT JOIN reconcilers rs ON rs.id = ro.reconciler_id
      WHERE r.account_id = %L
      AND r.state in ('active', 'grouped')
      AND (ro.report_id is NULL OR r.updated_at > ro.updated_at OR ro.reconciler_hash != rs.hash OR ro.reconciler_hash is NULL)
    `,
      columNames.join(','),
      query.account_id
    );

    const data = (await prisma.$queryRawUnsafe(sql)) as report_data[];

    let related: report_data[] = [];

    if (query.related_report_ids?.length) {
      related = await prisma.report_data.findMany({
        where: {
          id: { in: query.related_report_ids },
          state: { in: ['active', 'grouped'] },
        },
      });
    }
    return Object.values(keyBy([...data, ...related], 'id'));
  }

  async save(params: {
    account: ExtAccountInfo;
    data: {
      reconciler_id: number;
      reconciler_hash: string;
      account_id: string;
      gemerated_Key: string;
      statement_id?: number;
      report_id?: number;
    };
  }) {
    const { data, account } = params;
    const record = await prismaClient.reconciler_output.findFirst({
      where: {
        reconciler_id: data.reconciler_id,
        account_id: data.account_id,
        statement_id: data.statement_id,
        report_id: data.report_id,
        state: 'active',
      },
    });
    if (record) {
      await prismaClient.reconciler_output.update({
        where: {
          id: record.id,
        },
        data: {
          ...data,
          updated_at: new Date(),
          updated_by: account.uid,
          updated_proxied_by: account.ouid,
        },
      });
    } else {
      await prismaClient.reconciler_output.create({
        data: {
          ...data,
          updated_at: new Date(),
          updated_by: account.uid,
          updated_proxied_by: account.ouid,
        },
      });
    }
  }
}
