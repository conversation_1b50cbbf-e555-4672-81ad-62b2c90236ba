import axios from 'axios';
import { injectable } from 'inversify';

export interface IGChatService {
  notify(message: string): Promise<void>;
}

@injectable()
export class GChatService implements IGChatService {
  endpoint: string;

  constructor(endpoint: string) {
    if (!endpoint) {
      throw new Error('GChat endpoint is required');
    }
    this.endpoint = endpoint;
  }

  async notify(message) {
    await axios({
      baseURL: this.endpoint,
      method: 'POST',
      data: { text: message },
    });
  }
}
