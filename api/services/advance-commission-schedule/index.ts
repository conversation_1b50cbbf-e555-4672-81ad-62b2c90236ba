import type {
  advance_commission_schedules,
  companies,
  Prisma,
  report_data,
  statement_data,
} from '@prisma/client';
import { virtual_type } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { CompensationTypes } from 'common/globalTypes';
import type {
  CreateAdvanceCommissionSchedulesDTO,
  UpdateAdvanceCommissionSchedulesDTO,
  DeleteAdvanceCommissionSchedulesDTO,
} from 'common/dto/advance-commission-schedules';
import {
  CommissionScheduleType,
  type CommissionScheduleTypes,
  PaymentDateBasis,
  PremiumAmountBasis,
} from 'common/constants/commission-schedule';
import { inject, injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';
import dayjs from '@/lib/dayjs';
import type { ScheduleDetail } from '@/services/advance-commission-schedule/interface';
import { AppLoggerService } from '@/services/logger/appLogger';
import { BigNumber } from 'bignumber.js';
import { GroupingCalculator } from '@/services/data_processing/grouping-calculator';
import { DataStates, type PolicyConfig } from 'common/types/common';
import { prismaTransactionHandler } from '@/lib/prismaUtils';

type PolicyWithStatements = Prisma.report_dataGetPayload<{
  include: {
    statement_data: true;
  };
}>;
@injectable()
export class AdvancedCommissionService {
  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: 'AdvancedCommissionService' },
  });

  @inject(GroupingCalculator)
  groupingCalculator: GroupingCalculator;

  private excludeFields = [
    'agent_commissions_status',
    'agent_commissions_status2',
    'agent_commissions_v2',
    'agent_commission_payout_rate',
    'children_data',
    'created_at',
    'created_by',
    'created_proxied_by',
    'details',
    'flags',
    'id',
    'import_id',
    'is_virtual',
    'master_id',
    'parent_id',
    'processing_status',
    'reconciliation_method',
    'reconciliation_stats',
    'reconciliation_status',
    'virtual_type',
    'state',
    'str_id',
    'updated_at',
    'updated_by',
  ];

  async getPolicyById(id: number) {
    return await prismaClient.report_data.findFirst({
      where: { id },
      include: {
        statement_data: {
          where: { state: { not: DataStates.DELETED } },
          orderBy: { payment_date: 'asc' },
        },
      },
    });
  }

  async processStatement(policyId: number, _accountId: string): Promise<void> {
    const policy = await this.getPolicyById(policyId);
    if (!policy) {
      this.logger.warn(
        `No policy found for policy with ID: ${policy.id}. Skipping processing.`
      );
      return;
    }
    if (
      (policy.config as PolicyConfig)?.advanced_commission_schedules === false
    ) {
      this.logger.info(
        `Skip advance commission schedules for policy: ${policy.policy_id} because it is disabled in policy config.`
      );
      return;
    }
    const schedule = await this.findMatchingSchedule({
      policy,
    });

    const statementData = policy?.statement_data?.[0];

    if (!schedule) {
      this.logger.warn(
        `No schedule found for policy: ${statementData.policy_id}`
      );
      return;
    }

    if (await this.hasExistingSchedules(statementData.id)) {
      this.logger.warn(
        `Existing schedules found for master_id: ${statementData.id}. Skipping processing.`
      );
      return;
    }

    const scheduledLines = this.prepareScheduledLines({
      statementData,
      schedule,
      policy,
    });
    await this.runTransaction(statementData, scheduledLines);
  }

  async hasExistingSchedules(masterId: number): Promise<boolean> {
    const existingSchedules = await prismaClient.statement_data.count({
      where: {
        master_id: masterId,
        virtual_type: virtual_type.scheduled,
      },
    });
    return existingSchedules > 0;
  }

  getPremiumAmount(data: {
    statementData: statement_data;
    schedule: advance_commission_schedules;
    policy?: report_data;
  }): Decimal {
    const { statementData, schedule, policy } = data;
    const originalPremium = new Decimal(statementData.premium_amount);
    if (
      schedule.premium_amount_basis ===
      PremiumAmountBasis.COMMISSION_PREMIUM_AMOUNT_12
    ) {
      return statementData.premium_amount.div(12);
    } else if (
      schedule.premium_amount_basis ===
      PremiumAmountBasis.POLICY_ANNUALIZED_REVENUE
    ) {
      return policy?.premium_amount;
    } else if (
      schedule.premium_amount_basis === PremiumAmountBasis.POLICY_TARGET_PREMIUM
    ) {
      return policy?.commissionable_premium_amount;
    }
    return originalPremium;
  }

  getPaymentDate(data: {
    schedule: advance_commission_schedules;
    policy?: report_data;
  }) {
    const { schedule, policy } = data;
    if (schedule.payment_date_basis === PaymentDateBasis.EFFECTIVE_DATE) {
      return policy.effective_date;
    } else if (
      schedule.payment_date_basis === PaymentDateBasis.FIRST_PAYMENT_DATE
    ) {
      return policy?.first_payment_date;
    }
    return null;
  }

  getCommissionAmount(data: {
    premiumAmount?: Decimal;
    scheduleType: CommissionScheduleTypes;
    rate?: number;
  }) {
    const { scheduleType, premiumAmount, rate } = data;
    if (scheduleType === CommissionScheduleType.NINE_MONTH) {
      return premiumAmount.mul(0.75).div(9);
    } else if (scheduleType === CommissionScheduleType.SIX_MONTH) {
      return premiumAmount.mul(0.5).div(6);
    }
    return premiumAmount.mul(BigNumber(rate).div(100).toNumber());
  }

  generateSchedulesByType(scheduleType: CommissionScheduleTypes) {
    const breakdown = Array.from<number>({ length: 12 }).fill(0);
    if (scheduleType === CommissionScheduleType.NINE_MONTH) {
      const rate = BigNumber(100).div(12).dp(2);
      breakdown.fill(rate.toNumber(), 0, 9);
    } else if (scheduleType === CommissionScheduleType.SIX_MONTH) {
      const rate = BigNumber(100).div(12).dp(2);
      breakdown.fill(rate.toNumber(), 0, 6);
    }
    return breakdown;
  }

  prepareScheduledLines(data: {
    statementData: statement_data;
    schedule: advance_commission_schedules;
    policy?: report_data;
  }): Omit<statement_data, 'id' | 'str_id' | 'created_at' | 'updated_at'>[] {
    const { statementData, schedule } = data;
    const premiumAmount = this.getPremiumAmount(data);
    const paymentDateBasis = this.getPaymentDate(data);

    const scheduledLines = [];
    const copy = { ...statementData };
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    this.excludeFields.forEach((field) => {
      delete copy[field];
    });

    const scheduleDetails: ScheduleDetail[] = (
      schedule.schedules as ScheduleDetail[]
    )?.length
      ? (schedule.schedules as ScheduleDetail[])
      : [
          {
            breakdown: this.generateSchedulesByType(
              schedule.schedule_type as CommissionScheduleTypes
            ),
          },
        ];

    scheduleDetails.forEach((scheduleItem, year) => {
      const breakdowns = scheduleItem.breakdown;

      const paymentCount = breakdowns.filter((r) => r).length;
      if (paymentCount === 0) {
        return [];
      }

      breakdowns.forEach((rate, index) => {
        if (BigNumber(rate).isZero()) {
          return;
        }

        const paymentDate = dayjs(paymentDateBasis)
          .add(year, 'year')
          .add(index + 1 + (schedule.delay || 0), 'months');
        const commissionAmount = this.getCommissionAmount({
          scheduleType: schedule.schedule_type as CommissionScheduleTypes,
          rate,
          premiumAmount,
        });
        const rates = this.groupingCalculator.getCommissionRate({
          commission_amount: commissionAmount,
          premium_amount: premiumAmount,
        });

        const scheduledLine = {
          ...copy,
          ...rates,
          commission_amount: commissionAmount,
          virtual_type: virtual_type.scheduled,
          is_virtual: true,
          master_id: statementData.id,
          premium_amount: premiumAmount,
          payment_date: paymentDate.toDate(),
          // Set processing_date to be the same as payment_date
          processing_date: paymentDate.toDate(),
          // Set period_date to be the first day of the month of payment_date
          period_date: paymentDate.startOf('month').toDate(),
        };
        scheduledLines.push(scheduledLine);
      });
    });

    return scheduledLines;
  }

  async runTransaction(
    originalStatement: statement_data,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    scheduledLines: any[]
  ) {
    await prismaClient.$transaction(async (prisma) => {
      await prisma.statement_data.update({
        where: { id: originalStatement.id },
        data: {
          compensation_type: CompensationTypes.ADVANCED,
        },
      });

      await prisma.statement_data.createMany({
        data: scheduledLines,
      });
    });
  }

  async list(accountId: string) {
    const schedules = await prismaClient.advance_commission_schedules.findMany({
      where: { account_id: accountId },
      include: {
        companies: true,
      },
      orderBy: { id: 'desc' },
    });

    return schedules.map((schedule) => {
      const { companies, ...rest } = schedule;
      return {
        ...rest,
        company_ids: companies.map((c) => c.company_id),
      };
    });
  }
  async create(
    data: CreateAdvanceCommissionSchedulesDTO & {
      account_id: string;
    }
  ) {
    const { company_ids, ...rest } = data;
    return await prismaClient.advance_commission_schedules.create({
      data: {
        ...rest,
        companies: {
          create: company_ids.map((company_id) => ({
            company: {
              connect: {
                id: company_id,
              },
            },
          })),
        },
      },
    });
  }

  async update(
    data: UpdateAdvanceCommissionSchedulesDTO & { account_id: string }
  ) {
    const { id, company_ids, ...rest } = data;
    const result = await prismaClient.advance_commission_schedules.update({
      where: { id },
      data: {
        ...rest,
        companies: {
          deleteMany: {},
          create: company_ids.map((company_id) => ({
            company: {
              connect: {
                id: company_id,
              },
            },
          })),
        },
      },
      include: {
        companies: {
          include: {
            company: true,
          },
        },
      },
    });

    const { companies, ...schedule } = result;
    return {
      ...schedule,
      companies: companies.map((c) => c.company),
    };
  }

  async delete(
    data: DeleteAdvanceCommissionSchedulesDTO & { account_id: string }
  ) {
    return await prismaClient.$transaction(async (prisma) => {
      await prisma.advance_commission_schedules_companies.deleteMany({
        where: {
          advance_commission_schedule_id: { in: data.ids },
        },
      });
      return await prisma.advance_commission_schedules.deleteMany({
        where: { id: { in: data.ids }, account_id: data.account_id },
      });
    });
  }

  async findSchedules(query: {
    companyId?: number;
    companyName?: string;
    accountId: string;
  }) {
    const { companyId, accountId } = query;
    const where: Prisma.advance_commission_schedulesWhereInput = {
      account_id: accountId,
    };
    if (companyId) {
      where.companies = {
        some: {
          company_id: companyId,
        },
      };
    }

    const schedules = await prismaClient.advance_commission_schedules.findMany({
      where,
      include: {
        companies: {
          include: {
            company: true,
          },
        },
      },
    });

    return schedules.map((schedule) => {
      const { companies, ...rest } = schedule;
      return {
        ...rest,
        companies: companies.map((c) => c.company),
      };
    });
  }

  async findMatchingSchedule(query: { policy: PolicyWithStatements }) {
    const { policy: reportData } = query;
    const accountId = reportData.account_id;
    if (!reportData.company_id && !reportData.writing_carrier_name) {
      return null;
    }
    const schedules = await this.findSchedules({
      accountId,
      companyId: reportData.company_id,
    });

    if (schedules.length === 0) {
      return null;
    }

    for (const schedule of schedules) {
      if (this.isScheduleMatch(schedule, reportData)) {
        return schedule;
      }
    }

    return null;
  }

  async stop(reportId: number) {
    const policy = await this.getPolicyById(reportId);
    if (!policy) {
      this.logger.warn(
        `No policy found for report ID: ${reportId}. Skipping stop processing.`
      );
      return;
    }
    return prismaTransactionHandler(
      prismaClient,
      async (tx: Prisma.TransactionClient) => {
        await tx.report_data.update({
          where: { id: reportId },
          data: {
            config: {
              ...(policy.config as object),
              advanced_commission_schedules: false,
            },
          },
        });
        await tx.statement_data.updateMany({
          where: {
            report_data_id: policy.id,
            is_virtual: true,
            virtual_type: virtual_type.scheduled,
          },
          data: {
            state: DataStates.DELETED,
          },
        });
      }
    );
  }

  isScheduleMatch(
    schedule: advance_commission_schedules & {
      companies: companies[];
    },
    policy: PolicyWithStatements
  ): boolean {
    if (
      schedule.companies.length > 0 &&
      !schedule.companies.some(
        (c) =>
          c.id === policy.company_id ||
          policy.writing_carrier_name === c.company_name
      )
    ) {
      return false;
    }
    if (
      schedule.payment_date_basis === PaymentDateBasis.FIRST_PAYMENT_DATE &&
      policy.statement_data.length === 0
    ) {
      this.logger.warn(
        `No statement data found for policy: ${policy.policy_id} with first payment date checking. Skipping`
      );
      return false;
    }
    if (
      schedule.product_type &&
      schedule.product_type !== policy.product_type
    ) {
      return false;
    }

    return true;
  }
}
