import { describe, it, expect, vi, beforeEach } from 'vitest';
import { type statement_data, virtual_type } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { CompensationTypes } from 'common/globalTypes';
import { AccountIds } from 'common/constants';

import { prismaClient } from '@/lib/prisma';
import { AdvancedCommissionService } from './index';
import { container } from '@/ioc';

vi.mock('@/lib/prisma', () => ({
  default: {},
  prismaClient: {
    statement_data: {
      count: vi.fn(),
      update: vi.fn(),
      createMany: vi.fn(),
      findMany: vi.fn(),
    },
    $transaction: vi.fn(),
  },
}));

const mockEligibleStatement: statement_data = {
  id: 1,
  policy_id: 'POLICY-123',
  carrier_name: 'National Life Insurance Co.',
  writing_carrier_name: 'National Life Insurance Co.',
  payment_mode: 'Monthly',
  transaction_type: 'Annualized',
  premium_amount: new Decimal(1200),
  commission_amount: new Decimal(900),
  processing_date: new Date('2023-01-15'),
  payment_date: new Date('2023-04-15'),
  str_id: 'stmt-1',
  report_data_id: 1,
  account_id: AccountIds.BROKERS_ALLIANCE,
  uid: null,
  state: 'active',
  processing_status: null,
  created_at: new Date(),
  created_by: null,
  created_proxied_by: null,
  updated_at: new Date(),
  compensation_type: 'FYC',
  contacts: [],
  premium_type: 'policy',
  tags: [],
  type: null,
  virtual_type: null,
} as statement_data;

describe('AdvancedCommissionService', () => {
  let service: AdvancedCommissionService;

  beforeEach(() => {
    service = container.get<AdvancedCommissionService>(
      AdvancedCommissionService
    );
    vi.resetAllMocks();
  });

  describe('hasExistingSchedules', () => {
    it('Should return true if count is greater than 0', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.statement_data.count as any).mockResolvedValue(1);
      const result = await service.hasExistingSchedules(1);
      expect(result).toBe(true);
      expect(prismaClient.statement_data.count).toHaveBeenCalledWith({
        where: {
          master_id: 1,
          virtual_type: virtual_type.scheduled,
        },
      });
    });

    it('Should return false if count is 0', async () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.statement_data.count as any).mockResolvedValue(0);
      const result = await service.hasExistingSchedules(1);
      expect(result).toBe(false);
    });
  });

  describe('runTransaction', () => {
    it('Should call prisma.$transaction and perform update and createMany', async () => {
      const mockUpdate = vi.fn();
      const mockCreateMany = vi.fn();
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (prismaClient.$transaction as any).mockImplementation(
        async (callback) => {
          await callback({
            statement_data: {
              update: mockUpdate,
              createMany: mockCreateMany,
            },
          });
        }
      );

      const scheduledLines = [{ commission_amount: '100.00' }];
      await service.runTransaction(mockEligibleStatement, scheduledLines);

      expect(prismaClient.$transaction).toHaveBeenCalledOnce();
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { id: mockEligibleStatement.id },
        data: { compensation_type: CompensationTypes.ADVANCED },
      });
      expect(mockCreateMany).toHaveBeenCalledWith({
        data: scheduledLines,
      });
    });
  });
  describe('stop', () => {
    it('Should do nothing if policy is not found', async () => {
      vi.spyOn(service, 'getPolicyById').mockResolvedValue(null);
      const loggerSpy = vi.spyOn(service.logger, 'warn');

      await service.stop(1);

      expect(loggerSpy).toHaveBeenCalledWith(
        'No policy found for report ID: 1. Skipping stop processing.'
      );
      expect(prismaClient.$transaction).not.toHaveBeenCalled();
    });

    it('Should update policy config and mark virtual statements as deleted', async () => {
      const mockPolicy = {
        id: 1,
        config: { some_other_config: true },
      };
      // biome-ignore lint/suspicious/noExplicitAny: test mock
      vi.spyOn(service, 'getPolicyById').mockResolvedValue(mockPolicy as any);

      const mockTx = {
        report_data: { update: vi.fn() },
        statement_data: { updateMany: vi.fn() },
      };
      // biome-ignore lint/suspicious/noExplicitAny: test mock
      (prismaClient.$transaction as any).mockImplementation(async (callback) =>
        callback(mockTx)
      );

      await service.stop(1);

      expect(service.getPolicyById).toHaveBeenCalledWith(1);
      expect(prismaClient.$transaction).toHaveBeenCalled();
      expect(mockTx.report_data.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          config: {
            some_other_config: true,
            advanced_commission_schedules: false,
          },
        },
      });
      expect(mockTx.statement_data.updateMany).toHaveBeenCalledWith({
        where: {
          report_data_id: 1,
          is_virtual: true,
          virtual_type: virtual_type.scheduled,
        },
        data: {
          state: 'deleted',
        },
      });
    });

    it('Should handle policy with no config', async () => {
      const mockPolicy = {
        id: 1,
        config: null,
      };
      // biome-ignore lint/suspicious/noExplicitAny: test mock
      vi.spyOn(service, 'getPolicyById').mockResolvedValue(mockPolicy as any);

      const mockTx = {
        report_data: { update: vi.fn() },
        statement_data: { updateMany: vi.fn() },
      };
      // biome-ignore lint/suspicious/noExplicitAny: test mock
      (prismaClient.$transaction as any).mockImplementation(async (callback) =>
        callback(mockTx)
      );

      await service.stop(1);

      expect(mockTx.report_data.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: {
          config: {
            advanced_commission_schedules: false,
          },
        },
      });
    });
  });

  describe('generateSchedulesByType', () => {
    it('Given a nine month schedule type, should generate a breakdown for 9 months', () => {
      const result = service.generateSchedulesByType('nine_month');
      expect(result).toEqual([
        8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 0, 0, 0,
      ]);
    });

    it('Given a six month schedule type, should generate a breakdown for 6 months', () => {
      const result = service.generateSchedulesByType('six_month');
      expect(result).toEqual([
        8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 0, 0, 0, 0, 0, 0,
      ]);
    });

    it('Given an unsupported schedule type, should return an array of zeros', () => {
      // biome-ignore lint/suspicious/noExplicitAny: test mock
      const result = service.generateSchedulesByType('UNSUPPORTED' as any);
      expect(result).toEqual([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
    });
  });

  describe('getCommissionAmount', () => {
    it('Given a nine month schedule type, should calculate the commission amount correctly', () => {
      const result = service.getCommissionAmount({
        scheduleType: 'nine_month',
        premiumAmount: new Decimal(1200),
      });
      expect(result.toNumber()).toBe(100);
    });

    it('Given a six month schedule type, should calculate the commission amount correctly', () => {
      const result = service.getCommissionAmount({
        scheduleType: 'six_month',
        premiumAmount: new Decimal(1200),
      });
      expect(result.toNumber()).toBe(100);
    });

    it('Given a custom schedule type with a rate, should calculate the commission amount correctly', () => {
      const result = service.getCommissionAmount({
        scheduleType: 'custom',
        premiumAmount: new Decimal(1200),
        rate: 10,
      });
      expect(result.toNumber()).toBe(120);
    });

    it('Given a custom schedule type with a zero rate, should return zero', () => {
      const result = service.getCommissionAmount({
        scheduleType: 'custom',
        premiumAmount: new Decimal(1200),
        rate: 0,
      });
      expect(result.toNumber()).toBe(0);
    });
  });

  describe('getPaymentDate', () => {
    it('Given an effective date basis, should return the policy effective date', () => {
      const effectiveDate = new Date('2023-01-01');
      const result = service.getPaymentDate({
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: { payment_date_basis: 'effective_date' } as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: { effective_date: effectiveDate } as any,
      });
      expect(result).toEqual(effectiveDate);
    });

    it('Given a first payment date basis, should return the policy first payment date', () => {
      const firstPaymentDate = new Date('2023-02-01');
      const result = service.getPaymentDate({
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: { payment_date_basis: 'first_payment_date' } as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: { first_payment_date: firstPaymentDate } as any,
      });
      expect(result).toEqual(firstPaymentDate);
    });

    it('Given an unsupported payment date basis, should return null', () => {
      const result = service.getPaymentDate({
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: { payment_date_basis: 'unsupported' } as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: {} as any,
      });
      expect(result).toBeNull();
    });

    it('Given a first payment date basis and no first payment date, should return null', () => {
      const result = service.getPaymentDate({
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: { payment_date_basis: 'first_payment_date' } as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: { first_payment_date: null } as any,
      });
      expect(result).toBeNull();
    });
  });
  describe('prepareScheduledLines', () => {
    const mockStatementData = { ...mockEligibleStatement };
    const mockPolicy = {
      id: 1,
      effective_date: new Date('2023-01-01'),
    };
    const mockSchedule = {
      id: 1,
      schedule_type: 'nine_month',
      schedules: [],
      delay: 0,
    };

    beforeEach(() => {
      vi.spyOn(service, 'getPremiumAmount').mockReturnValue(new Decimal(100));
      vi.spyOn(service, 'getPaymentDate').mockReturnValue(
        new Date('2023-01-01')
      );
      vi.spyOn(service, 'getCommissionAmount').mockImplementation(
        ({ rate }) => new Decimal(rate)
      );
      vi.spyOn(service.groupingCalculator, 'getCommissionRate').mockReturnValue(
        {
          commission_rate: '10',
          new_commission_rate: new Decimal(0.1),
          commission_rate_percent: new Decimal(10),
        }
      );
    });

    it('Given a schedule with a schedule_type, should generate scheduled lines correctly', () => {
      const schedule = { ...mockSchedule, schedule_type: 'nine_month' };
      vi.spyOn(service, 'generateSchedulesByType').mockReturnValue([
        8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 8.33, 0, 0, 0,
      ]);

      const result = service.prepareScheduledLines({
        statementData: mockStatementData,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: schedule as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: mockPolicy as any,
      });

      expect(result).toHaveLength(9);
      expect(service.generateSchedulesByType).toHaveBeenCalledWith(
        'nine_month'
      );
      expect(result[0].commission_amount.toNumber()).toBe(8.33);
      expect(result[0].payment_date).toEqual(
        new Date('2023-02-01T00:00:00.000Z')
      );
      expect(result[8].payment_date).toEqual(
        new Date('2023-10-01T00:00:00.000Z')
      );
      expect(result[0].virtual_type).toBe(virtual_type.scheduled);
      expect(result[0].is_virtual).toBe(true);
      expect(result[0].master_id).toBe(mockStatementData.id);
    });

    it('Given a schedule with custom breakdown, should use it to generate lines', () => {
      const schedule = {
        ...mockSchedule,
        schedule_type: 'custom',
        schedules: [{ breakdown: [10, 20, 0, 30] }],
      };
      const generateSchedulesByTypeSpy = vi.spyOn(
        service,
        'generateSchedulesByType'
      );

      const result = service.prepareScheduledLines({
        statementData: mockStatementData,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: schedule as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: mockPolicy as any,
      });

      expect(result).toHaveLength(3);
      expect(generateSchedulesByTypeSpy).not.toHaveBeenCalled();
      expect(result[0].commission_amount.toNumber()).toBe(10);
      expect(result[1].commission_amount.toNumber()).toBe(20);
      expect(result[2].commission_amount.toNumber()).toBe(30);
      expect(result[0].payment_date).toEqual(
        new Date('2023-02-01T00:00:00.000Z')
      );
      expect(result[1].payment_date).toEqual(
        new Date('2023-03-01T00:00:00.000Z')
      );
      expect(result[2].payment_date).toEqual(
        new Date('2023-05-01T00:00:00.000Z')
      );
    });

    it('Given a schedule with a delay, should offset payment dates correctly', () => {
      const schedule = {
        ...mockSchedule,
        schedule_type: 'custom',
        schedules: [{ breakdown: [10] }],
        delay: 3,
      };

      const result = service.prepareScheduledLines({
        statementData: mockStatementData,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: schedule as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: mockPolicy as any,
      });

      expect(result).toHaveLength(1);
      expect(result[0].payment_date).toEqual(
        new Date('2023-05-01T00:00:00.000Z')
      );
    });

    it('Given a multi-year schedule, should calculate payment dates for subsequent years', () => {
      const schedule = {
        ...mockSchedule,
        schedule_type: 'custom',
        schedules: [{ breakdown: [10] }, { breakdown: [20] }],
      };

      const result = service.prepareScheduledLines({
        statementData: mockStatementData,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: schedule as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: mockPolicy as any,
      });

      expect(result).toHaveLength(2);
      expect(result[0].payment_date).toEqual(
        new Date('2023-02-01T00:00:00.000Z')
      );
      expect(result[1].payment_date).toEqual(
        new Date('2024-02-01T00:00:00.000Z')
      );
    });

    it('Given a schedule with no payments, should return an empty array', () => {
      const schedule = {
        ...mockSchedule,
        schedule_type: 'custom',
        schedules: [{ breakdown: [0, 0, 0] }],
      };

      const result = service.prepareScheduledLines({
        statementData: mockStatementData,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        schedule: schedule as any,
        // biome-ignore lint/suspicious/noExplicitAny: test mock
        policy: mockPolicy as any,
      });

      expect(result).toHaveLength(0);
    });
  });
});
