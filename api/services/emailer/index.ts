import { injectable, inject } from 'inversify';
import * as Sen<PERSON> from '@sentry/nextjs';
import sgMail from '@sendgrid/mail';

import { ConfigService } from '@/services/config';
import { getEmailTemplate } from './email-template';

@injectable()
export class EmailerService implements IEmailerService {
  private secret: string;

  constructor(@inject(ConfigService) private configService: ConfigService) {
    this.secret = this.configService.get('SENDGRID_API_KEY');
    sgMail.setApiKey(this.secret);
  }

  private async _convertStreamToBase64(
    stream: NodeJS.ReadableStream
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      stream.on('data', (chunk) => chunks.push(chunk));
      stream.on('end', () => resolve(Buffer.concat(chunks).toString('base64')));
      stream.on('error', reject);
    });
  }

  private async _sendEmail(
    to: string[] | string,
    subject: string,
    html: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    attachments: any[] = []
  ): Promise<void> {
    const msg = {
      to,
      from: {
        email: '<EMAIL>',
        name: 'Fintary',
      },
      subject,
      html,
      attachments,
    };

    try {
      await sgMail.send(msg);
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.log(`Email sent to ${to}`);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`An error occurred when sending sendgrid email: ${error}`);
      Sentry.captureException(error);
    }
  }

  async sendEmail(
    to: string | string[],
    subject: string,
    html: string,
    fileStream?: NodeJS.ReadableStream,
    fileName?: string,
    fileType?: string
  ): Promise<void> {
    const attachments = [];

    if (fileStream && fileName && fileType) {
      const fileBase64 = await this._convertStreamToBase64(fileStream);
      attachments.push({
        content: fileBase64,
        filename: fileName,
        type: fileType,
        disposition: 'attachment',
      });
    }

    await this._sendEmail(to, subject, html, attachments);
  }

  async sendUserInviteEmail({
    email,
    accountName,
    roleName,
    inviteToken,
    isNewUser,
    accountId,
  }: {
    email: string;
    accountName: string | null;
    roleName: string;
    inviteToken: string | null;
    isNewUser: boolean;
    accountId?: string;
  }) {
    const frontEndUrl = process.env.FE_URL ?? 'https://app.fintary.com';

    const params = new URLSearchParams({
      inviteToken,
      email,
      inviteCode: 'TRYFINTARY2025',
      loginType: isNewUser ? 'signUp' : 'signIn',
    });

    const actionUrl = `${frontEndUrl}/sign-in?${params.toString()}`;
    const actionText = isNewUser ? 'Sign up' : 'Sign in';

    const { subject, body } = getEmailTemplate({
      accountId,
      accountName,
      actionText,
      actionLink: actionUrl,
      roleName,
    });
    await this.sendEmail(email, subject, body);
  }
}

export interface IEmailerService {
  sendEmail(
    to: string | string[],
    subject: string,
    html: string,
    pdfStream?: NodeJS.ReadableStream,
    filename?: string,
    fileType?: string
  ): Promise<void>;
  sendUserInviteEmail(param: {
    email: string;
    accountName: string | null;
    roleName: string;
    inviteToken: string | null;
    isNewUser: boolean;
    accountId?: string;
  }): Promise<void>;
}
