import { beforeEach, describe, expect, test, vi } from 'vitest';
import { Container } from 'inversify';
import sgMail from '@sendgrid/mail';
import * as Sentry from '@sentry/nextjs';

import { EmailerService } from '.';
import { ConfigService } from '../config';

describe('EmailerService', () => {
  let container: Container;

  beforeEach(() => {
    container = new Container({ defaultScope: 'Singleton' });
    container.bind<ConfigService>(ConfigService).toSelf();
    container.bind<EmailerService>(EmailerService).toSelf();

    vi.spyOn(sgMail, 'send').mockResolvedValue([
      {
        statusCode: 0,
        body: undefined,
        headers: undefined,
      },
      {},
    ]);
    vi.spyOn(sgMail, 'setApiKey');
    vi.spyOn(Sentry, 'captureException');
  });

  describe('sendEmail()', () => {
    test('sends an email successfully without attachments', async () => {
      const service = container.get<EmailerService>(EmailerService);
      const to = '<EMAIL>';
      const subject = 'Test Subject';
      const html = '<p>Test Email</p>';

      await service.sendEmail(to, subject, html);

      expect(sgMail.send).toHaveBeenCalledWith({
        to,
        from: { email: '<EMAIL>', name: 'Fintary' },
        subject,
        html,
        attachments: [],
      });
      expect(sgMail.send).toHaveBeenCalledTimes(1);
    });

    test('captures an error with Sentry when email sending fails', async () => {
      const service = container.get<EmailerService>(EmailerService);
      const to = '<EMAIL>';
      const subject = 'Test Subject';
      const html = '<p>Test Email</p>';

      const error = new Error('SendGrid error');
      vi.spyOn(sgMail, 'send').mockRejectedValueOnce(error);

      await service.sendEmail(to, subject, html);

      expect(Sentry.captureException).toHaveBeenCalledWith(error);
      expect(Sentry.captureException).toHaveBeenCalledTimes(1);
    });

    test('sends an email with attachments', async () => {
      const service = container.get<EmailerService>(EmailerService);
      const to = '<EMAIL>';
      const subject = 'Test Subject';
      const html = '<p>Test Email</p>';
      const fileStream = {
        on: vi.fn((event, callback) => {
          if (event === 'data') callback(Buffer.from('file content'));
          if (event === 'end') callback();
        }),
      } as unknown as NodeJS.ReadableStream;
      const fileName = 'test.pdf';
      const fileType = 'application/pdf';

      await service.sendEmail(
        to,
        subject,
        html,
        fileStream,
        fileName,
        fileType
      );

      expect(sgMail.send).toHaveBeenCalledWith({
        to,
        from: { email: '<EMAIL>', name: 'Fintary' },
        subject,
        html,
        attachments: [
          {
            content: Buffer.from('file content').toString('base64'),
            filename: fileName,
            type: fileType,
            disposition: 'attachment',
          },
        ],
      });
    });

    test('skips attachments if incomplete data is provided', async () => {
      const service = container.get<EmailerService>(EmailerService);
      const to = '<EMAIL>';
      const subject = 'Test Subject';
      const html = '<p>Test Email</p>';

      await service.sendEmail(
        to,
        subject,
        html,
        undefined,
        undefined,
        undefined
      );

      expect(sgMail.send).toHaveBeenCalledWith({
        to,
        from: { email: '<EMAIL>', name: 'Fintary' },
        subject,
        html,
        attachments: [],
      });
    });

    test('_convertStreamToBase64 converts a readable stream to a base64 string', async () => {
      const service = container.get<EmailerService>(EmailerService);
      const fileStream = {
        on: vi.fn((event, callback) => {
          if (event === 'data') callback(Buffer.from('test content'));
          if (event === 'end') callback();
        }),
      } as unknown as NodeJS.ReadableStream;

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const result = await (service as any)._convertStreamToBase64(fileStream);

      expect(result).toEqual(Buffer.from('test content').toString('base64'));
    });

    test('_convertStreamToBase64 rejects when the stream emits an error', async () => {
      const service = container.get<EmailerService>(EmailerService);
      const fileStream = {
        on: vi.fn((event, callback) => {
          if (event === 'error') callback(new Error('Stream error'));
        }),
      } as unknown as NodeJS.ReadableStream;

      await expect(
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        (service as any)._convertStreamToBase64(fileStream)
      ).rejects.toThrow('Stream error');
    });
  });
});
