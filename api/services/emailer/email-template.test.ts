import { describe, it, expect } from 'vitest';
import { AccountIds } from 'common/constants';

import { getEmailTemplate } from './email-template';

describe('getEmailTemplate', () => {
  it('should return WORLD_CHANGERS template with replaced action link and text', () => {
    const result = getEmailTemplate({
      accountId: AccountIds.WORLD_CHANGERS,
      accountName: 'World Changers',
      actionText: 'Activate Now',
      actionLink: 'https://example.com/activate',
      roleName: 'Member',
    });

    expect(result.subject).toBe('Access Your TWC Commissions Hub');
    expect(result.body).toContain('https://example.com/activate');
    expect(result.body).toContain('Activate Now');
    expect(result.body).toContain('<EMAIL>');
  });

  it('should return default template if accountId is not WORLD_CHANGERS', () => {
    const result = getEmailTemplate({
      accountId: 'some_other_id',
      accountName: 'Acme Corp',
      actionText: 'Join Now',
      actionLink: 'https://example.com/join',
      roleName: 'Admin',
    });

    expect(result.subject).toBe('Join Acme Corp on Fintary');
    expect(result.body).toContain('Acme Corp');
    expect(result.body).toContain('Admin');
    expect(result.body).toContain('https://example.com/join');
    expect(result.body).toContain('Join Now');
    expect(result.body).toContain('<EMAIL>');
  });

  it('should handle missing accountId and use default template', () => {
    const result = getEmailTemplate({
      accountName: 'Beta Org',
      actionText: 'Accept Invite',
      actionLink: 'https://example.com/invite',
      roleName: 'User',
    });

    expect(result.subject).toBe('Join Beta Org on Fintary');
    expect(result.body).toContain('Beta Org');
    expect(result.body).toContain('User');
    expect(result.body).toContain('https://example.com/invite');
    expect(result.body).toContain('Accept Invite');
  });

  it('should handle missing accountName in default template', () => {
    const result = getEmailTemplate({
      accountName: '',
      actionText: 'Accept',
      actionLink: 'https://example.com/accept',
      roleName: 'Viewer',
    });

    expect(result.subject).toBe('Join Fintary');
    expect(result.body).toContain('Viewer');
    expect(result.body).toContain('https://example.com/accept');
    expect(result.body).toContain('Accept');
  });
});
