import { AccountIds } from 'common/constants';

export const EmailTemplate: {
  [accountId: string]: {
    subject?: string;
    body?: string;
  };
} = {
  [AccountIds.WORLD_CHANGERS]: {
    subject: 'Access Your TWC Commissions Hub',
    body: `
     <div>
        <p>You’ve been invited to The World Changers Commissions Hub — where you’ll be able to view your commission statements, track earnings, and stay on top of your cash flow.</p>
        <p><a href="{{action_link}}">{{action_text}}</a> to activate your access.</p>
        <p>Questions? Email us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
      </div>
    `,
  },
};

export const getEmailTemplate = ({
  actionLink,
  actionText,
  accountName,
  accountId,
  roleName,
}: {
  accountId?: string;
  accountName: string;
  actionText: string;
  actionLink: string;
  roleName: string;
}) => {
  const template = EmailTemplate[accountId];
  switch (accountId) {
    case AccountIds.WORLD_CHANGERS: {
      template.body = template.body
        .replace('{{action_link}}', actionLink)
        .replace('{{action_text}}', actionText);
      return template;
    }
    default:
      return {
        subject: `Join ${accountName ? `${accountName} on ` : ''}Fintary`,
        body: `
          <div>
            <p>You've been invited to join <strong>${accountName}</strong> as a <strong>${roleName}</strong> in Fintary.</p>
            <a href="${actionLink}">${actionText}</a>
            <p>If you have any questions, contact <NAME_EMAIL>.</p>
          </div>
        `,
      };
  }
};
