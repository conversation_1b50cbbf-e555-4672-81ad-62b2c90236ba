import { inject, injectable } from 'inversify';
import { DocumentProcessorServiceClient } from '@google-cloud/documentai';
import { captureException } from '@sentry/nextjs';

import { ConfigService } from '@/services/config';
import { storage } from '@/lib/firebase-admin';
import { Config } from '@/lib/decorators';
import { getMimeType } from '@/lib/helpers';

@injectable()
export class GoogleDocumentAIService {
  @inject(ConfigService) configService: ConfigService;

  private clientService: DocumentProcessorServiceClient;

  private location: string;

  private name: string;

  @Config('PROJECT_ID')
  private projectId: string;

  @Config('DOCUMENT_AI_PROCESSOR_ID')
  private processorId: string;

  async initGoogleAIClient() {
    this.clientService = new DocumentProcessorServiceClient();
    this.location = 'us';
    this.name = `projects/${this.projectId}/locations/${this.location}/processors/${this.processorId}`;
  }

  getTableData = (table, text) => {
    const headerRow = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    table.headerRows[0].cells.forEach((headerCell) => {
      const headerCellText = this.getText(headerCell.layout.textAnchor, text);
      headerRow.push(headerCellText.trim());
    });
    const dataRows = [headerRow];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    table.bodyRows.forEach((row) => {
      const bodyRow = [];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      row.cells.forEach((bodyCell) => {
        const bodyCellText = this.getText(bodyCell.layout.textAnchor, text);
        bodyRow.push(bodyCellText.trim());
      });
      dataRows.push(bodyRow);
    });
    return dataRows;
  };

  getText = (textAnchor, text) => {
    if (!textAnchor.textSegments || textAnchor.textSegments.length === 0) {
      return '';
    }

    const startIndex = textAnchor.textSegments[0].startIndex || 0;
    const endIndex = textAnchor.textSegments[0].endIndex;

    return text.substring(startIndex, endIndex);
  };

  async getDocumentResults(outputPath: string) {
    const [files] = await storage.getFiles({
      prefix: outputPath,
    });

    const tableData = [];
    const fullText = [];
    for await (const fileInfo of files) {
      const [file] = await fileInfo.download();
      try {
        const document = JSON.parse(Buffer.from(file).toString('utf8'));
        fullText.push(document.text);
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        document.pages.forEach((page) => {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          page.tables.forEach((table) => {
            tableData.push(this.getTableData(table, document.text));
          });
        });
      } catch (error) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(error);
        captureException(error);
      }
    }

    return {
      fulltext: fullText.join(' '),
      tables: tableData,
    };
  }

  async processDocument(filePath: string) {
    try {
      await this.initGoogleAIClient();
      const mimeType = getMimeType(filePath);
      const [_u, accountId] = filePath.split('/');
      const outputPath = `output/${accountId}/${Date.now()}`;
      const [operation] = await this.clientService.batchProcessDocuments({
        name: this.name,
        inputDocuments: {
          gcsDocuments: {
            documents: [
              {
                gcsUri: `gs://${storage.name}/${filePath}`,
                mimeType,
              },
            ],
          },
        },
        documentOutputConfig: {
          gcsOutputConfig: {
            gcsUri: `gs://${storage.name}/${outputPath}`,
          },
        },
      });

      await operation.promise();

      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.log('Document processing complete.');
      const response = await this.getDocumentResults(outputPath);

      return {
        data: response,
        status: 'success',
      };
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(error);
      captureException(error);
      return { status: 'error', error: error.message || error };
    }
  }
}
