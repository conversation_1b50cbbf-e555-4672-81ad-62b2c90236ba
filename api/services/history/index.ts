import { injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';

@injectable()
export class HistoryService {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  postHistoryData = async (body: any[]) => {
    const data = await prismaClient.history.createMany({
      data: body,
      skipDuplicates: true,
    });
    return data;
  };
}
