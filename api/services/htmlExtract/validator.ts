import { injectable } from 'inversify';
import { z } from 'zod';

const documentId = z.number();
const accountId = z.string();

export const ProcessHtmlExtractSchema = z.object({
  documentId,
  accountId,
});

export type ProcessHtmlExtractType = z.infer<typeof ProcessHtmlExtractSchema>;

@injectable()
export class HtmlExtractValidator {
  validateProcessParams(params: ProcessHtmlExtractType) {
    return ProcessHtmlExtractSchema.parse(params);
  }
}
