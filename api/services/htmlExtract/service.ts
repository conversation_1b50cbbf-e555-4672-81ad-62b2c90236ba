import { inject, injectable } from 'inversify';
import { captureException } from '@sentry/nextjs';
import * as cheerio from 'cheerio';

import { HtmlExtractValidator, type ProcessHtmlExtractType } from './validator';
import { prismaClient } from '@/lib/prisma';
import { DocumentFileService } from '@/services/documents/fileService';

@injectable()
export class HtmlExtractService {
  @inject(DocumentFileService) private documentFileService: DocumentFileService;
  @inject(HtmlExtractValidator) private validator: HtmlExtractValidator;

  async processDocument(params: ProcessHtmlExtractType) {
    try {
      const validatedData = this.validator.validateProcessParams(params);

      const document = await prismaClient.documents.findFirst({
        where: {
          id: validatedData.documentId,
          account_id: validatedData.accountId,
        },
      });

      if (!document) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error('Document not found');
        throw new Error('Document not found');
      }

      const fileBuffer =
        await this.documentFileService.getFileFromStorage(document);
      const htmlContent = fileBuffer.toString('utf-8');

      const extractedData = await this.getHtmlContent(htmlContent);

      return extractedData;
    } catch (error) {
      captureException(error);
      throw new Error(`Error in processing document: ${error.message}`);
    }
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private async getHtmlContent(htmlContent: any): Promise<any[]> {
    try {
      const $ = cheerio.load(htmlContent);

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const data: any[] = [];

      $('table').each((tableIndex, table) => {
        const headers = this.extractHeaders(table, $);
        const tableData = this.extractTableData(table, headers, $);

        data.push({ tableIndex, data: tableData });
      });

      return data;
    } catch (error) {
      captureException(error);
      throw new Error('Error processing HTML content');
    }
  }

  private extractHeaders(table: cheerio.Element, $: cheerio.Root): string[] {
    const headers: string[] = [];
    $(table)
      .find('tr:first-child th')
      .each((_, th) => {
        headers.push($(th).text().trim());
      });
    return headers;
  }

  private extractTableData(
    table: cheerio.Element,
    headers: string[],
    $: cheerio.Root
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): any[] {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const tableData: any[] = [];
    $(table)
      .find('tr')
      // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      .each((rowIndex, row) => {
        const rowData: { [key: string]: string } = {};
        $(row)
          .find('td')
          .each((colIndex, cell) => {
            const key = headers[colIndex] || `${colIndex + 1}`;
            rowData[key] = $(cell).text().trim();
          });

        if (Object.keys(rowData).length > 0) {
          tableData.push(rowData);
        }
      });
    return tableData;
  }
}
