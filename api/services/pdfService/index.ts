import { injectable } from 'inversify';

import Formatter from '@/lib/Formatter';
import { getStorageSignedUrl } from '@/lib/helpers';
import CommissionPayoutTemplate from '@/lib/templates/CommissionPayoutTemplate';
import { exportTotalRows } from '@/pages/api/export/exportFields';
import { storage } from '@/lib/firebase-admin';
import { Roles } from '@/types';
import { getExportData } from '@/pages/api/export/base';

@injectable()
export class PdfService implements IPdfService {
  async generateAndStorageCommissionPayoutPdf(
    snapshotData,
    accountId,
    userInfo
  ) {
    const formatData = await getExportData({
      data: snapshotData.snapshot_data.data.data,
      table: 'statement_data',
      roleId: Roles.PRODUCER.toString(),
      accountId,
    });
    const totalRow = await exportTotalRows(formatData);
    const commissionAmount = +Object.values(
      snapshotData.snapshot_data?.data?.totals?.agent_commissions
    )?.[0];
    let logoImg = '';
    if (snapshotData.account.white_label_mode) {
      logoImg = await getStorageSignedUrl(snapshotData.account.logo_url);
    }
    const pdfData = {
      reportData: {
        data: [],
        totals: [],
        reportInfo: [],
      },
    };
    pdfData.reportData.data.push(formatData);
    pdfData.reportData.totals.push(totalRow);
    pdfData.reportData.reportInfo.push({
      name: snapshotData.name,
      commissionAmount: `Total producer commission: ${Formatter.currency(commissionAmount)}`,
      createdAt: Formatter.date(snapshotData.created_at),
      logoImg: logoImg,
      accountName: snapshotData.account.name,
    });
    const pdfResult = await CommissionPayoutTemplate(pdfData);

    if (userInfo && pdfResult) {
      // Upload the PDF to Firebase Storage
      const filePath = `downloads/${accountId}/${userInfo.user_contact[0].str_id}/${encodeURIComponent(snapshotData.name)}`;
      const file = storage.file(filePath);
      const stream = pdfResult.pipe(
        file.createWriteStream({
          metadata: {
            contentType: 'application/pdf',
          },
        })
      );

      await new Promise((resolve, reject) =>
        stream.on('finish', resolve).on('error', reject)
      );
    }
  }
}

type UserContact = {
  str_id: string;
};

type UserInfo = {
  user_contact: UserContact[];
};
export interface IPdfService {
  generateAndStorageCommissionPayoutPdf(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    snapshotData: any,
    accountId: string,
    userInfo: UserInfo
  ): Promise<void>;
}
