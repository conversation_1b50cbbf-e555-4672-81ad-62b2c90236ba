import axios from 'axios';
import { XML<PERSON>uilder, XMLParser } from 'fast-xml-parser';
import { inject, injectable } from 'inversify';
import pRetry from 'p-retry';

import { AppLoggerService } from '@/services/logger/appLogger';
import {
  AgentSchema,
  CommHierarchySchema,
  ContactSchema,
  ContactVendorSchema,
  PolicySchema,
  ProductSchema,
  VendorSchema,
} from '@/services/smart-office/interface';
import { Config } from '@/lib/decorators';
import { ConfigService } from '@/services/config';

@injectable()
export class SmartOfficeService {
  @Config('SMART_OFFICE_ENDPOINT')
  private endpoint;
  private siteName = '';
  private username = '';
  private apiKey = '';
  private apiSecret = '';
  private customEndpoint = '';

  private builder = new XMLBuilder({
    ignoreAttributes: false,
    attributeNamePrefix: '@',
    format: true,
  });

  private parser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: '@',
  });

  private logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: 'SmartOfficeService' },
  });

  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  @inject(ConfigService) private configService: ConfigService;

  loadConfig(config: {
    username: string;
    siteName: string;
    apiKey: string;
    apiSecret: string;
    endpoint?: string;
  }) {
    this.username = config.username;
    this.siteName = config.siteName;
    this.apiKey = config.apiKey;
    this.apiSecret = config.apiSecret;
    this.customEndpoint = config.endpoint;
  }

  get client() {
    return axios.create({
      baseURL: this.customEndpoint || this.endpoint,
      headers: {
        Sitename: this.siteName,
        username: this.username,
        'api-key': this.apiKey,
        'api-secret': this.apiSecret,
      },
    });
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getBaseRequest(operation: Record<string, any>) {
    return {
      request: {
        '@version': '1.0',
        header: {
          office: null,
          user: null,
          password: null,
          keepsession: 'true',
        },
        ...operation,
      },
    };
  }

  async get(params: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema: Record<string, any>;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    condition?: Record<string, any>;
  }) {
    const { schema, condition } = params;
    const request = this.getBaseRequest({
      get: {
        ...schema,
        ...(condition && { condition }),
      },
    });
    const Entity = Object.keys(schema)[0];
    this.logger.debug('request', request);
    const xml = this.builder.build(request);
    this.logger.debug('xml', { xml });
    const res = await this.client.post('/send', xml);
    const data = this.parser.parse(res.data);
    this.logger.debug('data', data);
    const {
      response: {
        get: { [Entity]: item },
      },
    } = data;
    return item;
  }
  // New method to handle common request logic
  private async makeRequest(params: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema: Record<string, any>;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    condition?: Record<string, any>;
    pageSize?: number;
    searchId?: string;
  }) {
    const { schema, condition, pageSize = 1000, searchId } = params;
    const request = this.getBaseRequest({
      search: {
        '@pagesize': pageSize,
        '@total': `'true'`,
        object: schema,
        ...(condition && { condition }),
        ...(searchId && { '@searchid': searchId }),
      },
    });
    const Entity = Object.keys(schema)[0];
    this.logger.debug('request', request);
    const xml = this.builder.build(request);
    this.logger.debug('xml', { xml });
    const res = await pRetry(() => this.client.post('/send', xml), {
      retries: 3,
    });
    const data = this.parser.parse(res.data);
    const {
      response: {
        search: {
          '@total': total,
          '@searchid': _searchId,
          '@more': more,
          '@pagesize': _pageSize,
          '@page': _page,
          [Entity]: items,
        },
      },
    } = data;
    return {
      data: Array.isArray(items) ? items : [items],
      total: +total,
      searchId: _searchId,
      more: more === 'true',
      pageSize: +_pageSize,
      page: +_page,
    };
  }

  async getProducts(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        Product: schema || ProductSchema,
      },
      ...rest,
    });
  }

  async queryEntityIds(
    entity: string,
    params: {
      searchId?: string;
      pageSize?: number;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      condition?: Record<string, any>;
    }
  ) {
    return this.makeRequest({
      schema: {
        [entity]: null,
      },
      ...params,
    });
  }

  async getContacts(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        Contact: schema || ContactSchema,
      },
      ...rest,
    });
  }

  async getCarriers(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        ContactVendor: schema || ContactVendorSchema,
      },
      ...rest,
    });
  }
  async getVendors(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        Vendor: schema || VendorSchema,
      },
      ...rest,
    });
  }

  async getAgents(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        Agent: schema || AgentSchema,
      },
      ...rest,
    });
  }

  async getAgentHierarchy(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        CommHierarchy: schema || CommHierarchySchema,
      },
      ...rest,
    });
  }

  async getProductExts(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        ProductExt: schema,
      },
      ...rest,
    });
  }
  async getPolicies(params: {
    searchId?: string;
    pageSize?: number;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    schema?: Record<string, any>;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    condition?: Record<string, any> | Record<string, any>[];
  }) {
    const { schema, ...rest } = params;
    return this.makeRequest({
      schema: {
        Policy: schema || PolicySchema,
      },
      ...rest,
    });
  }
}
