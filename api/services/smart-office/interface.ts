export enum Operation {
  EQUAL = 'eq',
  GREATER_THAN = 'gt',
  GREATER_THAN_OR_EQUAL = 'ge',
  LESS_THAN = 'lt',
  LESS_THAN_OR_EQUAL = 'le',
  NOT_EQUAL = 'ne',
  BETWEEN = 'between',
  LIKE = 'like',
  CONTAINS = 'contains',
  STARTS_WITH = 'starts',
  ENDS_WITH = 'ends',
  IN = 'in',
  IS_NULL = 'isnull',
  NOT_BETWEEN = 'not-between',
  NOT_IN = 'not-in',
  NOT_NULL = 'not-null',
  NOT_LIKE = 'not-like',
  NOT_CONTAINS = 'not-contains',
  NOT_STARTS_WITH = 'not-starts',
  NOT_ENDS_WITH = 'not-ends',
  ANY = 'any',
}
export const AgentTypeMap = new Map<number, string>([
  [0, ''],
  [1, 'Agent'],
  [2, 'Broker'],
  [3, 'GeneralAgent'],
  [4, 'Manager'],
  [5, 'Sales Staff'],
  [6, 'Staff'],
  [7, 'Merged Contact'],
  [10001, 'Home Office'],
  [10002, 'Branch Office'],
  [10003, 'Regional Office'],
  [10004, 'General Agency'],
  [10005, 'BGA'],
  [10006, 'Broker Dealer'],
  [10007, 'Firm'],
]);

export enum Role {
  SALES_REP = 6003,
  CASE_MANAGER = 6005,
  PRIMARY_AGENT = 10001,
  WRITING = 10002,
  ADDITIONAL_WRITING_ADVISOR = 10005,
}

export const ProductSubTypeMap = new Map<number, string>([
  [0, ''],
  [1, '10YRT'],
  [2, '20YRT'],
  [3, '15YRT'],
  [4, '30YRT'],
  [5, 'ART'],
  [6, '01YRT'],
  [7, '05YRT'],
  [8, '12YRT'],
  [9, '13YRT'],
  [10, '14YRT'],
  [11, '16YRT'],
  [12, '17YRT'],
  [13, '18YRT'],
  [14, '19YRT'],
  [15, '25YRT'],
  [16, 'ENDOWMENT'],
]);

export const AgentStatusMap = new Map<number, string>([
  [0, ''],
  [1, 'Active'],
  [2, 'Inactive'],
  [3, 'Recruit'],
  [4, 'Candidate'],
  [5, 'Terminated'],
  [6, 'Prospect'],
  [7, 'Deceased'],
  [8, 'Retired'],
]);
export const StateCodeMap = new Map<number, string>([
  [0, ''],
  [1, 'All'],
  [2, 'AK'],
  [3, 'AL'],
  [4, 'AR'],
  [5, 'AZ'],
  [6, 'CA'],
  [7, 'CO'],
  [8, 'CT'],
  [9, 'DC'],
  [10, 'DE'],
  [11, 'FL'],
  [12, 'GA'],
  [13, 'GU'],
  [14, 'HI'],
  [16, 'IA'],
  [17, 'ID'],
  [18, 'IL'],
  [19, 'IN'],
  [20, 'KS'],
  [21, 'KY'],
  [22, 'LA'],
  [23, 'MA'],
  [24, 'MD'],
  [25, 'ME'],
  [26, 'MI'],
  [27, 'MN'],
  [28, 'MO'],
  [29, 'MS'],
  [30, 'MT'],
  [31, 'NC'],
  [32, 'ND'],
  [33, 'NE'],
  [34, 'NH'],
  [35, 'NJ'],
  [36, 'NM'],
  [37, 'NV'],
  [38, 'NY'],
  [39, 'OH'],
  [40, 'OK'],
  [41, 'OR'],
  [42, 'PA'],
  [43, 'PR'],
  [44, 'RI'],
  [45, 'SC'],
  [46, 'SD'],
  [47, 'TN'],
  [48, 'TX'],
  [49, 'UT'],
  [50, 'VA'],
  [51, 'VI'],
  [52, 'VT'],
  [53, 'WA'],
  [54, 'WI'],
  [55, 'WV'],
  [56, 'WY'],
]);

export interface NewBusiness {
  CommPrem?: number;
  CommModal?: number;
  CommAnnPrem?: number;
  Team?: number;
  TeamOfficeId?: number;
  TeamText?: string;
}

export interface Policy {
  '@id'?: string;
  UniqueID?: string;
  CarrierName?: string;
  PolicyNumber?: string;
  Carrier?: ContactVendor;
  Age?: number;
  AnnualPlcyFee?: number;
  AnnualPremium?: number;
  InsuredName?: string;
  OwnerName?: string;
  PaymentMethod?: number;
  PlanType?: string;
  PolicyStatus?: number;
  PolicyStatusText?: string;
  PolicyType?: string;
  Premium?: number;
  PremiumMode?: number;
  PrimaryAgentName?: string;
  Contact?: Contact;
  PrimaryAdvisor?: Contact;
  TargetAmt?: number;
  Product?: Product;
  ProductID?: string;
  WriteState: number;
  ProductIDVal?: number;
  HoldingNotes?: string;
  IssuedDate?: string;
  MktManager?: string;
  PendingCase?: NewBusiness;
  InterestParties?: { InterestParty: InterestParty[] };
  PrimaryInterestParty?: { InterestParty: InterestParty };
  PrimaryAdvisorParty?: { InterestParty: InterestParty };
}

export interface InterestParty {
  PartyRoleText?: string;
  PartyRole?: number;
  InterestPercent?: number;
  Name?: string;
  MktManager?: string;
  ContactID?: number;
  Contact?: {
    Agent?: Agent;
    NewBusinesses?: {
      NewBusiness?: NewBusiness;
    };
  };
  PolicyID?: number;
}
export interface Person {
  Age?: number;
  Dob?: string;
  Gender?: number;
  GenderText?: string;
  JobTitle?: string;
}

export interface Phone {
  PhoneType: number;
  PhoneTypeText: string;
  Preferred: number;
  Primary: number;
  PureNumber: number;
  Number: number;
  CountryCode: number;
  AreaCode: number;
}
export interface Address {
  AddressType: number;
  AddressTypeText: string;
  City: string;
  Country: string;
  Postal: number;
  State: string;
  Primary: number;
}
export enum AgentType {
  EMPTY = 0,
  AGENT = 1,
  BROKER = 2,
  GENERAL_AGENT = 3,
  MANAGER = 4,
  SALES_STAFF = 5,
  STAFF = 6,
  MERGED_CONTACT = 7,
  HOME_OFFICE = 10001,
  BRANCH_OFFICE = 10002,
  REGIONAL_OFFICE = 10003,
  GENERAL_AGENCY = 10004,
  BGA = 10005,
  BROKER_DEALER = 10006,
  FIRM = 10007,
}

export const AgentTypeLookupMap = new Map<number, string>([
  [0, ''],
  [1, 'Agent'],
  [2, 'Broker'],
  [3, 'General agent'],
  [4, 'Manager'],
  [5, 'Sales staff'],
  [6, 'Staff'],
  [7, 'Merged contact'],
  [10001, 'Home office'],
  [10002, 'Branch office'],
  [10003, 'Regional office'],
  [10004, 'General agency'],
  [10005, 'BGA'],
  [10006, 'Broker dealer'],
  [10007, 'Firm'],
]);

export interface Agent {
  AgencyID?: number;
  AgencyName?: string;
  AgencyRecID?: number;
  CaseManagerID?: number;
  Code?: string;
  ServiceLevel?: number;
  Status?: number;
  Type?: number;
  BrokerDealer?: string;
}

export interface Contact {
  '@id'?: string;
  Name: string;
  ClientType: number;
  ClientTypeText: string;
  Category: string;
  ContactType: number;
  ContactTypeText: string;
  FirstName: string;
  EmployerName: string;
  LastName: string;
  MiddleName: string;
  Remark?: string;
  Type: string;
  Person?: Person;
  ModifiedOn?: string;
  Agent?: Agent;
  Addresses?: { Address: Address[] };
  Phones?: { Phone: Phone[] };
  BusinessPhone?: Phone;
  PreferredAddress?: Address;
  PreferredEmailAddress?: { WebAddress: WebAddress };
  Vendor?: Vendor;
  NewBusinesses?: {
    NewBusiness?: NewBusiness;
  };
}

export interface ContactVendor {
  Name: string;
  ContactType: number;
  Vendor: Vendor;
  '@id'?: string;
}

export interface ProductExt {
  Subtype: number;
}

export interface Vendor {
  '@id'?: string;
  CarrierID: number;
  Contact: Contact;
}
export interface Product {
  '@id'?: string;
  Name: string;
  ProductType: number;
  ProductTypeText: string;
  VendorContactID: string; // This is the CarrierID
  FullContactID: Contact;
  ProductExts?: ProductExt;
}

export interface WebAddress {
  Address: string;
  Preferred: number;
  Remark: string;
  WebAddressType: number;
  WebAddressTypeText: string;
}
export const WebAddressSchema = {
  Address: null,
  Preferred: null,
  Remark: null,
  WebAddressType: null,
  WebAddressTypeText: null,
};
export const AgentSchema = {
  AgencyID: null,
  AgencyName: null,
  AgencyRecID: null,
  Code: null,
  ECPUniqueID: null,
  ServiceLevel: null,
  Status: null,
  Type: null,
};
export const AddressSchema = {
  AddressType: null,
  AddressTypeText: null,
  City: null,
  Country: null,
  Postal: null,
  State: null,
  Primary: null,
};
export const PersonSchema = {
  Age: null,
  // Date of Birth
  Dob: null,
  Gender: null,
  GenderText: null,
  JobTitle: null,
};
export const PhoneSchema = {
  PhoneType: null,
  PhoneTypeText: null,
  Preferred: null,
  Primary: null,
  PureNumber: null,
  Remark: null,
  Number: null,
  Extension: null,
  CountryCode: null,
  AreaCode: null,
};

export const ContactIdSchema = {
  '@id': null,
};
export const ContactSchema = {
  Name: null,
  ClientType: null,
  ClientTypeText: null,
  Category: null,
  ContactFullName: null,
  ContactType: null,
  ContactTypeText: null,
  ContactSubType: null,
  FirstName: null,
  LastName: null,
  EmployerName: null,
  MiddleName: null,
  Person: PersonSchema,
  Type: null,
  Agent: AgentSchema,
  PrimaryAgent: AgentSchema,
  ModifiedOn: null,
  Remark: null,
  Title: null,
  Addresses: { Address: AddressSchema },
  BusinessPhone: PhoneSchema,
  Phones: { Phone: PhoneSchema },
  PreferredEmailAddress: { WebAddress: WebAddressSchema },
  Vendor: {
    CarrierID: null,
    Status: null,
    ContactID: null,
    ContactVendor: {
      Name: null,
      ContactType: null,
    },
  },
};
export const ProductSchema = {
  Name: null,
  ProductType: null,
  ContactID: null,
  ProductTypeText: null,
  FullContactID: {
    Vendor: null,
  },
  ProductExts: {
    ProductExt: {
      Subtype: null,
    },
  },
  // VendorContactID: null,
};
export const VendorSchema = {
  CarrierID: null,
  Contact: ContactSchema,
};
export const ContactVendorSchema = {
  Name: null,
  ContactType: null,
  Vendor: VendorSchema,
  // Products: { Product: ProductSchema },
};
export const CommHierarchySchema = {
  AgentID: null,
  HierarchyID: null,
  HierarchyLevel: null,
  CarrierContract: {
    EffDate: null,
    Vendor: VendorSchema,
    ExpDate: null,
    CarrierID: null,
  },
};

export const RecordNotesSchema = {
  Keywords: null,
  Note: null,
  Contact: ContactSchema,
};
export const RiderSchema = {
  Effective: null,
  Expiration: null,
  Mode: null,
  Period: null,
  Premium: null,
  ProductName: null,
  RiderName: null,
  Status: null,
  Contact: ContactSchema,
  PolicyID: null,
  Product: ProductSchema,
};

export const SmartPadSchema = {
  Note: null,
};
export const PolicySchema = {
  UniqueID: null,
  CarrierName: null,
  PolicyNumber: null,
  Carrier: { Name: null, ContactType: null },
  // Contact: ContactSchema, use get operation to get related contact in subsequent request
  Contact: null,
  Age: null,
  AnnualPlcyFee: null,
  AnnualPremium: null,
  BeneficaryName: null,
  Description: null,
  Duration: null,
  InsuredName: null,
  OwnerName: null,
  PaymentMethod: null,
  PlanType: null,
  PolicyDate: null,
  RenewalDate: null,
  PolicyStatus: null,
  PolicyStatusText: null,
  ModifiedDate: null,
  PolicyType: null,
  Premium: null,
  PrimaryAgentName: null,
  PremiumMode: null,
  // PrimaryAdvisor: ContactSchema, use get operation to get related primary advisor in subsequent request
  PrimaryAdvisor: null,
  Product: ProductSchema,
  ProductID: null,
  ProductIDVal: null,
  // Riders: { Rider: RiderSchema },
  PolicyNotes: { SmartPad: SmartPadSchema },
  // HoldingNotes: RecordNotesSchema,
};

export const PaymentMethodMap = new Map<number, string>([
  [1, 'Direct_Bill'],
  [2, 'Bank_Draft'],
  [3, 'List_Bill'],
  [4, 'Government_Allotment'],
  [5, 'Credit_Card'],
  [6, 'EFT'],
  [7, 'PAC'],
  [8, 'Check'],
  [9, 'Regular_Billing'],
  [10, 'Irregular_Billing'],
  [11, 'Paid_in_Advance'],
  [12, 'Payroll_Deduction'],
  [13, 'Collection_Institution'],
  [14, 'Suspended_Billing'],
  [15, 'Premium_Deposit_Fund'],
  [16, 'Special_Accounts'],
  [17, 'Combined_Billing'],
  [18, 'Other'],
  [19, 'Automatic_Premium_Loan'],
  [20, 'POP'],
  [21, 'NETPOP'],
  [22, 'SSN_Deduction'],
  [23, 'Single_Pay'],
  [24, 'Cash'],
  [25, 'COD'],
  [26, 'CPF_OA'],
  [27, 'CPF_SA'],
  [28, 'SRS'],
  [29, 'Dividends_on_Deposit'],
]);

export const PremModeMap = new Map<number, string>([
  [0, ''],
  [1, 'Annual'],
  [2, 'Monthly'],
  [3, 'SemiMonthly'],
  [4, 'MonthlyPAC'],
  [5, 'Quarterly'],
  [6, 'SemiAnnually'],
  [7, 'BiWeekly'],
  [9, 'SinglePay'],
  [10, 'Weekly'],
  [11, 'FourWeekly'],
  [12, 'EightPay'],
  [13, 'TenPay'],
  [14, 'Daily'],
  [15, 'Monthly_for_9_months'],
  [16, 'Every_4_months'],
  [17, 'Every_5_months'],
  [18, 'Every_7_months'],
  [19, 'Every_8_months'],
  [20, 'Every_9_months'],
  [21, 'Every_10_months'],
  [22, 'Every_11_months'],
  [23, 'Every_three_weeks'],
  [24, 'Every_2_months'],
  [25, 'Monthly_for_5_months'],
  [26, 'Monthly_for_7_months'],
  [27, 'Monthly_for_11_months'],
  [28, 'ThreePay'],
  [29, 'FourPay'],
]);
