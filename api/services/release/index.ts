// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { readFileSync } from 'fs';
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { resolve } from 'path';
import { injectable } from 'inversify';

@injectable()
export class ReleaseService {
  async getChangelogs() {
    const projectDirs = ['common', 'api', 'web', 'release'];
    return projectDirs
      .map((projectDir) => [projectDir, this.readChangelog(projectDir)])
      .reduce((acc, [projectDir, changelog]) => {
        acc[projectDir] = changelog;
        return acc;
      }, {});
  }

  readChangelog(projectDir: string) {
    try {
      const changelog = readFileSync(
        resolve(process.cwd(), `../${projectDir}/CHANGELOG.release.md`),
        'utf8'
      );
      return changelog;
    } catch {
      return '';
    }
  }
}
