import { injectable } from 'inversify';
import { z } from 'zod';

const documentId = z.number();
const accountId = z.string();
const uid = z.string();

export const ProcessDocumentSchema = z.object({
  documentId,
  accountId,
  userId: uid,
  update: z.boolean().optional(),
  extractionId: z.string().optional(),
});

export const DocumentSchema = z.object({
  id: z.number(),
  account_id: z.string(),
  file_path: z.string().optional(),
  override_file_path: z.string().optional(),
  filename: z.string().optional(),
  override_filename: z.string().optional(),
});

export type ProcessDocumentType = z.infer<typeof ProcessDocumentSchema>;
export type DocumentType = z.infer<typeof DocumentSchema>;

@injectable()
export class ExtractTableValidator {
  validateProcessParams(params: ProcessDocumentType) {
    return ProcessDocumentSchema.parse(params);
  }
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  validateDocument(document: any) {
    return DocumentSchema.parse(document);
  }
}
