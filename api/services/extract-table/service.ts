import { inject, injectable } from 'inversify';
import { captureException } from '@sentry/nextjs';
import { nanoid } from 'nanoid';
import { sha256 } from 'crypto-hash';
import { ExtractTableStatuses } from 'common/globalTypes';
import * as Sentry from '@sentry/nextjs';

import { ConfigService } from '@/services/config';
import { Config } from '@/lib/decorators';
import { EmailerService } from '@/services/emailer';
import { prismaClient } from '@/lib/prisma';
import { ExtractTableValidator, type ProcessDocumentType } from './validator';
import { DocumentFileService } from '@/services/documents/fileService';

interface IExtractDataResponse {
  JobId: string;
  JobStatus: string;
  Pages: number;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Tables: any[];
  DownloadUrl?: string;
  ProTip?: string;
  message?: string;
}
interface CreditsResponse {
  usage: {
    credits: number;
    used: number;
    queued: number;
  };
}
@injectable()
export class ExtractTableService {
  @inject(ConfigService) configService: ConfigService;
  @inject(EmailerService) private emailerService: EmailerService;
  @inject(DocumentFileService) private documentFileService: DocumentFileService;

  @Config('EXTRACT_TABLE_KEY')
  private apiKey: string;

  @Config('EXTRACT_TABLE_TRIGGER')
  private extractTableTrigger: string;

  @Config('EXTRACT_TABLE_GET_RESULT')
  private extractTableGetResult: string;

  @Config('GHOST_SCRIPT_OPTIMIZER')
  private ghostScriptOptimizer: string;

  private validator: ExtractTableValidator;

  private static readonly POLLING_DELAY_MS = 5000;

  private static readonly HttpStatus = {
    OK: 200,
    BAD_REQUEST: 400,
    PAYMENT_REQUIRED: 402,
  };

  constructor(@inject(ExtractTableValidator) validator: ExtractTableValidator) {
    this.validator = validator;
  }

  async processDocument(params: ProcessDocumentType) {
    try {
      const validatedData = this.validator.validateProcessParams(params);

      const document = await prismaClient.documents.findFirst({
        where: {
          id: validatedData.documentId,
          account_id: validatedData.accountId,
        },
      });

      if (!document) {
        throw new Error('Document not found');
      }

      const fileBuffer =
        await this.documentFileService.getFileFromStorage(document);
      const filename = document.override_filename || document.filename;
      const _filename = filename.split('/').pop().replace(/^"|"$/g, '');

      let form = new FormData();
      const blob = new Blob([fileBuffer], { type: 'application/pdf' });
      form.append('input', blob, _filename);

      try {
        const creditsResponse = await fetch(
          process.env.EXTRACT_TABLE_VALIDATOR,
          {
            headers: {
              'x-api-key': process.env.EXTRACT_TABLE_KEY,
            },
          }
        );

        const creditsData = (await creditsResponse.json()) as CreditsResponse;
        const totalCredits = creditsData?.usage.credits;
        const usedCredits = creditsData?.usage.used;
        const remainingCredits = totalCredits - usedCredits;
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(`Current extractTable credits are ${remainingCredits}`);

        if (remainingCredits < 250) {
          await this.emailerService.sendEmail(
            '<EMAIL>',
            `ExtractTable credits running low (${remainingCredits} remaining)`,
            `ExtractTable is running low on credits. ${remainingCredits} credits remain.<br/><br/>Purchase more credits soon<br/>https://www.extracttable.com/v2/index.html#pricing<br/><br/>And transfer credits to existing key<br/>https://extracttable.com/transfer-api-credits.html<br/>`
          );
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.warn(
            `ExtractTable credits running low: ${remainingCredits} remaining`
          );
        }
      } catch (creditsError) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(
          `Error checking ExtractTable credits: ${creditsError.message}`
        );
        Sentry.captureException(creditsError);
      }

      let triggerData = await this.triggerExtractTable(form);
      let optimizedFile = null;

      if (triggerData.status >= ExtractTableService.HttpStatus.BAD_REQUEST) {
        form = new FormData();
        const fileBlob = new Blob([fileBuffer], { type: 'application/pdf' });
        form.append('file', fileBlob, _filename);

        optimizedFile = await this.optimizeFile(form);
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.warn(
          `ExtractTable failed with file ${_filename}. Retrying with optimized file.`
        );

        if (optimizedFile.status === ExtractTableService.HttpStatus.OK) {
          const fileData = await optimizedFile.arrayBuffer();
          const optimizedFilename = _filename.replace(
            /(\.[^.]+)$/,
            '-optimized$1'
          );

          form = new FormData();
          const optimizedBlob = new Blob([Buffer.from(fileData)], {
            type: 'application/pdf',
          });
          form.append('input', optimizedBlob, optimizedFilename);

          triggerData = await this.triggerExtractTable(form);

          await this.saveOptimizedDocument(
            validatedData.accountId,
            validatedData.userId,
            Buffer.from(fileData),
            document.id,
            optimizedFilename
          );
        }
      }

      if (triggerData.status >= ExtractTableService.HttpStatus.BAD_REQUEST) {
        let error = `Error processing document (${triggerData.status}:${triggerData.statusText})`;
        if (
          optimizedFile &&
          optimizedFile.status >= ExtractTableService.HttpStatus.BAD_REQUEST
        ) {
          error += `\nError optimizing document (${optimizedFile.status}:${optimizedFile.statusText})`;
        }

        if (
          triggerData.status === ExtractTableService.HttpStatus.PAYMENT_REQUIRED
        ) {
          await this.emailerService.sendEmail(
            '<EMAIL>',
            'ExtractTable out of credits - document processing blocked',
            'ExtractTable is out of credits. This blocks document processing.<br/><br/>Purchase more credits<br/>https://www.extracttable.com/v2/index.html#pricing<br/><br/>And transfer credits to existing key<br/>https://extracttable.com/transfer-api-credits.html<br/>'
          );
        }

        throw new Error(error);
      }

      const extractRes = (await triggerData.json()) as IExtractDataResponse;
      const jobId = extractRes.JobId;

      if (!jobId || extractRes.JobStatus === 'Failed') {
        throw new Error('ExtractTable job failed or no job ID returned');
      }

      const result = await this.pollingExtractData(jobId);

      return result;
    } catch (error) {
      captureException(error);
      throw error;
    }
  }

  private async pollingExtractData(
    jobId: string,
    times = 50
  ): Promise<IExtractDataResponse> {
    try {
      let data: IExtractDataResponse | null = null;
      for (let i = 0; i < times; i++) {
        const result = await this.getExtractData(jobId);
        data = (await result.json()) as IExtractDataResponse;
        const jobStatus = data?.JobStatus.toLocaleLowerCase();

        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.log(
          `Polling job ${jobId}, attempt ${i + 1}/${times}, status: ${data.JobStatus}`
        );

        if (jobStatus === ExtractTableStatuses.SUCCESS) {
          // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          console.log(`Job ${jobId} completed successfully`);
          if (data.DownloadUrl) {
            const fileData = await this.downloadFileByUrl(data.DownloadUrl);
            return fileData;
          }
          return data;
        }

        if (jobStatus === ExtractTableStatuses.FAILED) {
          throw new Error(
            `ExtractTable job ${jobId} failed: ${data.message || 'No error message'}`
          );
        }

        const delay = ExtractTableService.POLLING_DELAY_MS;
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      if (data && data?.JobStatus === ExtractTableStatuses.PROCESSING) {
        throw new Error(
          `ExtractTable job ${jobId} still processing after ${times} attempts, please check status later`
        );
      } else {
        throw new Error(
          `Failed to retrieve extraction data after ${times} attempts. Last status: ${data?.JobStatus || 'unknown'}`
        );
      }
    } catch (error) {
      captureException(error);
      throw new Error(`Polling extract data failed: ${error.message}`);
    }
  }

  private async triggerExtractTable(form: FormData) {
    try {
      return await fetch(this.extractTableTrigger, {
        method: 'POST',
        body: form,
        headers: {
          'x-api-key': this.apiKey,
        },
      });
    } catch (error) {
      captureException(error);
      throw new Error(`Failed to trigger ExtractTable: ${error.message}`);
    }
  }

  private async downloadFileByUrl(url: string) {
    try {
      const fileResponse = await fetch(url);
      return (await fileResponse.json()) as IExtractDataResponse;
    } catch (error) {
      captureException(error);
      throw new Error(`Failed to download result file: ${error.message}`);
    }
  }

  private async optimizeFile(form: FormData) {
    try {
      return await fetch(this.ghostScriptOptimizer, {
        method: 'POST',
        body: form,
        headers: {
          token: process.env.PDF_OPTIMIZER_TOKEN,
        },
      });
    } catch (error) {
      captureException(error);
      throw new Error(`Failed to optimize file: ${error.message}`);
    }
  }

  private async saveOptimizedDocument(
    accountId: string,
    userId: string,
    fileBuffer: Buffer,
    documentId: number,
    optimizedFilename: string
  ) {
    try {
      const fileHash = await sha256(fileBuffer);
      const filePath = `uploads/${accountId}/${nanoid()}-${optimizedFilename}`;

      await prismaClient.documents.update({
        where: { id: documentId, account_id: accountId },
        data: {
          optimized_filename: optimizedFilename,
          optimized_file_path: filePath,
          optimized_file_hash: fileHash,
          updated_at: new Date(),
          updated_by: userId,
        },
      });

      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.log(
        `Optimized file (${optimizedFilename}) saved successfully as ${filePath}`
      );
    } catch (err) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        'Error updating optimized document in the database:',
        err.message
      );
      captureException(err);
    }
  }

  async getExtractData(jobId: string) {
    try {
      return await fetch(`${this.extractTableGetResult}/?JobId=${jobId}`, {
        method: 'GET',
        headers: {
          'x-api-key': this.apiKey,
        },
      });
    } catch (error) {
      captureException(error);
      throw new Error(`Failed to get extract data: ${error.message}`);
    }
  }
}
