import * as Sentry from '@sentry/nextjs';
import { ProcessorSelectorStatuses } from 'common/globalTypes';
import { injectable } from 'inversify';
import axios from 'axios';

import { AppLoggerService } from '@/services/logger/appLogger';
import prisma from '@/lib/prisma';

enum DataType {
  STRING = 'string',
  FUNCTION = 'function',
}

@injectable()
export class ClassificationService {
  private readonly logger: AppLoggerService = new AppLoggerService({
    defaultMeta: {
      service: ClassificationService.name,
    },
  });

  async classifyDocument(file, accountId: string) {
    try {
      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let base64Content;

      switch (true) {
        case file.content && Buffer.isBuffer(file.content):
          base64Content = file.content.toString('base64');
          break;
        case file.content && typeof file.content === DataType.STRING:
          base64Content = file.content;
          break;
        case file.data && typeof file.data === DataType.STRING:
          base64Content = file.data.replace(/-/g, '+').replace(/_/g, '/');
          break;
        case file.arrayBuffer &&
          typeof file.arrayBuffer === DataType.FUNCTION: {
          const arrayBuffer = await file.arrayBuffer();
          const buffer = Buffer.from(arrayBuffer);
          base64Content = buffer.toString('base64');
          break;
        }
        default:
          throw new Error(`Unable to extract file content from ${file.name}`);
      }

      const response = await axios({
        method: 'POST',
        url: process.env.DOCUMENT_CLASSIFICATION_URL,
        headers: {
          'content-type': 'application/json',
          authorization: process.env.DOCUMENT_CLASSIFICATION_TOKEN || '',
        },
        data: {
          file: base64Content,
          file_type: file.name.split('.').pop().toLowerCase(),
          file_name: file.name,
          top_k: 1,
        },
      });

      const classificationResult = response.data;

      let typeResult = null;
      if (
        classificationResult?.type_predictions?.[0] &&
        classificationResult?.type_predictions[0].confidence >= 0.8
      ) {
        typeResult = classificationResult?.type_predictions[0].type;
      }

      if (
        !classificationResult?.canonical_predictions?.[0]?.canonical_id ||
        classificationResult?.canonical_predictions[0].confidence < 0.7
      ) {
        this.logger.info('No matching company found or low confidence', {
          filename: file.name,
        });
        return {
          status: ProcessorSelectorStatuses.SUCCESS,
          result: null,
          typeResult,
          classificationResult,
        };
      }

      const { canonical_id } = classificationResult.canonical_predictions[0];
      const companyResult = await prisma.companies.findFirst({
        where: {
          account_id: accountId,
          canonical_id: parseInt(canonical_id),
          state: 'active',
        },
        select: {
          str_id: true,
          company_name: true,
        },
      });

      this.logger.info('Document classification completed', {
        filename: file.name,
        result: companyResult,
        typeResult: typeResult,
      });

      return {
        status: ProcessorSelectorStatuses.SUCCESS,
        result: companyResult || null,
        typeResult: typeResult,
        classificationResult,
      };
    } catch (error) {
      this.logger.error(`Classification failed for ${file.name}:`, error);
      Sentry.captureException(error);
      return {
        status: ProcessorSelectorStatuses.ERROR,
        result: null,
        typeResult: null,
      };
    }
  }
}
