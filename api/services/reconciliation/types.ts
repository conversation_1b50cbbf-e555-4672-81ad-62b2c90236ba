export interface ReconciliationData {
  str_id: string;
  carrier_name: string;
  writing_carrier_name: string;
  commissionable_premium_amount: number | null;
  premium_amount: number | null;
  effective_date: Date | null;
  product_type: string | null;
  product_name: string | null;
  issue_age: number | null;
  report_id: string;
  cancellation_date: Date | null;
  reinstatement_date: Date | null;
  commission_amount_monthly: CommissionAmountMonthly | null;
  log: string[] | null;
}

export type CommissionAmountMonthly = Record<
  string,
  { commissionAmountMonthly: number }
>;
export function isCommissionAmountMonthly(
  value: unknown
): value is CommissionAmountMonthly {
  // TODO: Deep-validation with zod or similar
  return typeof value === 'object';
}

export type CommissionExpectedMonthly = Record<string, number | null>;
export type CommissionBalanceMonthly = Record<string, number | null>;

export function isLog(value: unknown): value is string[] {
  return (
    value === null ||
    (Array.isArray(value) &&
      value.every((element) => typeof element === 'string'))
  );
}
