import { inject, injectable } from 'inversify';
import type { Prisma, PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

import { prismaClient } from '@/lib/prisma';
import type IReconciliationDataRepo from '@/persistence/reconciliation-data/IReconciliationDataRepo';
import type ReconciliationDataRecord from '@/persistence/reconciliation-data/ReconciliationDataRecord';
import type { UpdateReconciliationDataSpec } from '@/persistence/reconciliation-data/IReconciliationDataRepo';
import { REPOSITORY_TYPES } from '@/constants';
import { DataStates } from '@/types';
import {
  type CommissionBalanceMonthly,
  type CommissionExpectedMonthly,
  isCommissionAmountMonthly,
  isLog,
  type ReconciliationData,
} from './types';
import { generateDateClauses } from '@/lib/helpers/generateDateClauses';

export interface UpdateReceivableScheduleInput {
  str_id: string;
  balance?: number;
  commissions_expected?: number;
  commission_expected_monthly?: CommissionExpectedMonthly;
  commission_balance_monthly?: CommissionBalanceMonthly;
  log?: string[];
}

export interface FindReconciliationDataByAccountInput {
  account_id: string;
}

export interface FindReconciliationDataByAccountOutput {
  reconciliation_data: ReconciliationData[];
}

@injectable()
export class ReconciliationService implements IReconciliationService {
  @inject(REPOSITORY_TYPES.ReconciliationDataRepository)
  private readonly dataRepo: IReconciliationDataRepo;

  private mapReconciliationDataRecord(
    record: ReconciliationDataRecord
  ): ReconciliationData {
    const {
      str_id,
      carrier_name,
      writing_carrier_name,
      commissionable_premium_amount,
      premium_amount,
      effective_date,
      product_type,
      product_name,
      issue_age,
      report_id,
      cancellation_date,
      reinstatement_date,
      commission_amount_monthly,
      log,
    } = record;

    if (!isCommissionAmountMonthly(commission_amount_monthly)) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(
        `Unexpected format for commission_amount_monthly.`,
        commission_amount_monthly
      );
      throw new Error(`Unexpected value for commission_amount_monthly.`);
    }

    if (!isLog(log)) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error(`Unexpected value for log.`, log);
      throw new Error(`Unexpected value for log.`);
    }

    const result: ReconciliationData = {
      str_id,
      carrier_name,
      writing_carrier_name,
      commissionable_premium_amount: commissionable_premium_amount?.toNumber(),
      premium_amount: premium_amount?.toNumber(),
      effective_date,
      product_type,
      product_name,
      issue_age,
      report_id,
      cancellation_date,
      reinstatement_date,
      commission_amount_monthly,
      log,
    };
    return result;
  }

  async updateReceivableSchedule(
    input: UpdateReceivableScheduleInput
  ): Promise<void> {
    const {
      str_id,
      log,
      commission_balance_monthly,
      commission_expected_monthly,
      commissions_expected,
      balance,
    } = input;

    const updateSpec: UpdateReconciliationDataSpec = {};

    if (log !== undefined) {
      updateSpec.log = log;
    }
    if (commission_balance_monthly !== undefined) {
      updateSpec.commission_balance_monthly = commission_balance_monthly;
    }
    if (commission_expected_monthly !== undefined) {
      updateSpec.commission_expected_monthly = commission_expected_monthly;
    }
    if (commissions_expected !== undefined) {
      updateSpec.commissions_expected = new Decimal(commissions_expected);
    }
    if (balance !== undefined) {
      updateSpec.balance = new Decimal(balance);
    }

    await this.dataRepo.update(str_id, updateSpec);
  }

  async findReconciliationDataByAccount(
    input: FindReconciliationDataByAccountInput
  ): Promise<FindReconciliationDataByAccountOutput> {
    const { account_id } = input;

    const str_ids = await this.dataRepo.findByAccount(account_id);
    if (str_ids.length === 0) {
      return { reconciliation_data: [] };
    }

    const records = await this.dataRepo.getByStrId(str_ids);

    const reconciliation_data = records.map((record) =>
      this.mapReconciliationDataRecord(record)
    );

    return { reconciliation_data };
  }

  async getReconciliationsData(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    policyStatusWhere: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    productTypeWhere: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    agents: any,
    includeBlankDate: boolean = false
  ) {
    const andClauses: Prisma.reconciliation_dataWhereInput[] = [
      generateDateClauses(
        'payment_date_last',
        startDate,
        endDate,
        includeBlankDate
      ),
    ];
    const agentStrIds = agents?.map((agent) => agent.str_id);
    const data = await prismaClient.reconciliation_data.findMany({
      where: {
        ...whereBase,
        AND: [
          ...andClauses,
          { OR: policyStatusWhere },
          { OR: productTypeWhere },
          {
            report_data: {
              contacts: agentStrIds ? { hasSome: agentStrIds } : undefined,
            },
          },
        ],
      },
      orderBy: [{ effective_date: 'asc' }],
    });

    return data;
  }

  async create(
    data: Prisma.reconciliationsCreateInput,
    prisma: Prisma.TransactionClient | PrismaClient = prismaClient
  ) {
    return await prisma.reconciliations.create({ data });
  }

  async increamentalUpdate(data: Prisma.reconciliation_dataCreateInput) {
    data = {
      ...data,
      statements: (data.statement_ids as number[])?.length
        ? { connect: (data.statement_ids as number[]).map((id) => ({ id })) }
        : undefined,
    };
    const result = await prismaClient.reconciliation_data.findFirst({
      where: {
        report_id: data.report_id,
        normalized_id: data.normalized_id,
        state: DataStates.ACTIVE,
      },
      include: { statements: { select: { id: true } } },
    });
    if (result) {
      // Clear previous relations
      if (data.statement_ids) {
        await Promise.all(
          result.statements
            .filter((r) => (data.statement_ids as number[]).includes(r.id))
            .map((s) =>
              prismaClient.statement_data.update({
                where: { id: s.id },
                data: { reconciliations: { set: [] } },
              })
            )
        );
      }
      return await prismaClient.reconciliation_data.update({
        where: { id: result.id },
        data: data,
      });
    } else {
      return await prismaClient.reconciliation_data.create({ data });
    }
  }
}

export interface IReconciliationService {
  getReconciliationsData(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    policyStatusWhere: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    productTypeWhere: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    agentList: any
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
}
