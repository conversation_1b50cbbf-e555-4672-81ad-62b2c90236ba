import axios, { type AxiosInstance } from 'axios';
import { injectable } from 'inversify';

import dayjs from '@/lib/dayjs';
import { AppLoggerService } from '@/services/logger/appLogger';
import type {
  CarrierEffectiveDateListResponse,
  CarrierListResponse,
  CarrierResponse,
  AgentHierarchyResponse,
  AgentCommissionLevelResponse,
} from '@/services/myadvisorgrids/interface';

@injectable()
export class MyAdvisorGridsService {
  private readonly logger: AppLoggerService = new AppLoggerService({
    defaultMeta: {
      service: 'my-advisor-grids',
    },
  });

  private readonly endpoint: string = 'https://api.myadvisorgrids.com/api';

  private apiKey: string;

  private _client: AxiosInstance;

  get client() {
    if (this._client) {
      return this._client;
    }
    this._client = axios.create({
      baseURL: this.endpoint,
      headers: {
        'Content-Type': 'application/json',
        origin: 'https://commission-grid.stoplight.io',
      },
    });
    this._client.interceptors.request.use((config) => {
      config.headers.set('Authorization', `Bearer ${this.apiKey}`);
      return config;
    });
    return this._client;
  }

  loadConfig(config: { apiKey: string }) {
    this.apiKey = config.apiKey;
  }

  async getCarrierDataList() {
    try {
      const response = await this.client.get<CarrierListResponse>(
        '/carrier/shared/get-carrier-data-list'
      );
      return response.data?.data;
    } catch (error) {
      this.logger.error('Failed to fetch carrier data list', { error });
      throw error;
    }
  }

  async getCarrierData(carrierId: number, carrier_effective_date_id?: number) {
    try {
      const response = await this.client.get<CarrierResponse>(
        '/carrier/shared/get-carrier-data',
        {
          params: {
            carrier_id: carrierId,
            carrier_effective_date_id,
          },
        }
      );
      return response.data?.data?.[0];
    } catch (error) {
      this.logger.error('Failed to fetch carrier data', { error });
      throw error;
    }
  }

  async getCarrierEffectiveDateList(carrierId: number) {
    try {
      const response = await this.client.get<CarrierEffectiveDateListResponse>(
        '/carrier/shared/get-effective-date-list',
        {
          params: {
            carrier_id: carrierId,
          },
        }
      );
      return response.data?.data;
    } catch (error) {
      this.logger.error('Failed to fetch carrier effective date list', {
        error,
      });
      throw error;
    }
  }

  async getAgentHierarchy(
    start_updated_date: string,
    end_updated_date: string
  ) {
    try {
      const startDate = dayjs(start_updated_date);
      let endDate = dayjs(end_updated_date);

      if (startDate.isSame(endDate)) {
        endDate = endDate.add(1, 'day');
      }

      const response = await this.client.get<AgentHierarchyResponse>(
        '/fintary/get-application-hierarchy',
        {
          params: {
            start_updated_date: startDate.format('YYYY-MM-DD'),
            end_updated_date: endDate.format('YYYY-MM-DD'),
          },
        }
      );
      return response.data?.data;
    } catch (error) {
      this.logger.error('Failed to fetch agent hierarchy data', { error });
      throw error;
    }
  }

  async getAgentCommissionLevel(contactId: string) {
    try {
      const response = await this.client.get<AgentCommissionLevelResponse>(
        '/fintary/get-agent-commission-level',
        {
          params: {
            contact_id: contactId,
          },
        }
      );
      return response.data?.data;
    } catch (error) {
      this.logger.error('Failed to fetch agent commission level data', {
        error,
      });
      throw error;
    }
  }
}
