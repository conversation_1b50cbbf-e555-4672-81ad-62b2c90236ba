export interface CarrierDetail {
  carrier_detail_id: number;
  carrier_detail_field_id: number;
  carrier_detail_field_value: string;
  carrier_detail_field_name: string;
  carrier_detail_flag_internal_info: boolean;
  carrier_detail_flag_show_field: boolean;
  carrier_detail_field_sequence: number;
}

export interface CarrierLevel {
  carrier_level_id: number;
  carrier_level_name: string;
  carrier_ba_level_flag: boolean;
  carrier_level_sequence: number;
  carrier_level_color_id: number;
  carrier_level_color_hex_code: string;
  carrier_level_color_name: string;
}

export interface CarrierCommissionData {
  carrier_commission_value_id: number;
  carrier_level_id: number;
  carrier_level_name: string;
  carrier_ba_level_flag: boolean;
  carrier_level_sequence: number;
  commission_value: string;
}

export interface CarrierGroupData {
  carrier_group_data_id: number;
  carrier_types: string;
  carrier_state: string;
  carrier_product_name: string;
  carrier_sub_name: string;
  carrier_ba_total_comp: string;
  carrier_group_data_sequence: number;
  carrier_commission_data: CarrierCommissionData[];
}

export interface CarrierLevelData {
  carrier_group_id: number;
  carrier_group_data: CarrierGroupData[];
}

export interface CarrierResource {
  carrier_resource_id: number;
  carrier_resource_created_at_timestamp: string;
  carrier_resource_file_name: string;
  carrier_resource_file_url: string;
  carrier_resource_file_size: string;
  carrier_resource_file_type: string;
  carrier_resource_database_file_name: string;
}

export interface CarrierType {
  carrier_id: number;
  carrier_type_id: number;
  carrier_type_name: string;
  carrier_type_color: string;
  updated_at_timestamp: string;
  carrier_ai_mapping_data: {
    carrier_ai_id: string;
    carrier_mapping_timestamp: string;
  };
}

export interface CarrierList {
  carrier_name: string;
  carrier_types: CarrierType[];
}

export interface CarrierProductCommissionData {
  carrier_product_commission_id: number;
  carrier_level_id: string;
  carrier_level_name: string;
  carrier_level_sequence: string;
  carrier_product_commission_value: string;
}

export interface CarrierProductCriteria {
  carrier_product_criteria_id: number;
  criteria_id: string;
  criteria_name: string;
  carrier_product_criteria_data: CarrierProductCriteriaData[];
}
export interface CarrierProductData {
  carrier_product_data_id: string;
  carrier_product_type: string;
  carrier_product_state: string;
  carrier_product_name: string;
  carrier_product_sub_name: string;
  carrier_product_note: string;
  carrier_product_ba_total_comp: string;
  carrier_product_sequence: string;
  carrier_product_commission_data: CarrierProductCommissionData[];
  carrier_product_criteria: CarrierProductCriteria[];
  carrier_product_ai_mapping_data: {
    product_ai_id: string;
    carrier_product_ai_mapping_timestamp: string;
  };
}
export interface CarrierProduct {
  carrier_product_id: string;
  carrier_product_data: CarrierProductData[];
}

export interface CarrierProductCriteriaData {
  carrier_product_criteria_data_id: number;
  criteria_field_id: string;
  criteria_field_name: string;
  criteria_field_option_id: string | null;
  criteria_field_option_value: string | null;
  criteria_field_option_description: string | null;
  criteria_field_value: string;
}

export interface Carrier {
  carrier_id: number;
  carrier_version_id: number;
  carrier_version_timestamp: string;
  carrier_version_latest: boolean;
  carrier_effective_date_data: {
    carrier_effective_date_id: number | string;
    carrier_effective_date_from: string;
  };
  carrier_name: string;
  carrier_type_id: number;
  carrier_type_name: string;
  carrier_type_color: string;
  carrier_ai_mapping_data: {
    carrier_ai_id: string;
    carrier_mapping_timestamp: string;
  };
  carrier_level: CarrierLevel[];
  carrier_product: CarrierProduct[];
  carrier_types_list: CarrierType[];
}

export class CarrierEffectiveDate {
  carrier_effective_date_id: number;
  carrier_effective_date: string;
}
export class Response<T> {
  status: boolean;
  data: T;
}
export class CarrierEffectiveDateList {
  carrier_id: string;
  carrier_effective_dates: CarrierEffectiveDate[];
}

export class CarrierEffectiveDateListResponse extends Response<
  CarrierEffectiveDateList[]
> {}
export class CarrierResponse extends Response<Carrier[]> {}
export class CarrierListResponse extends Response<CarrierList[]> {}

export interface TemplateLevelData {
  template_carrier_assignment_level_id: number;
  carrier_level_name: string;
  carrier_ba_level_flag: boolean;
  carrier_direct_level_flag: boolean;
  template_level_sequence: number;
}

export interface TemplateCarrierData {
  template_carrier_assignment_id: number;
  carrier_id: number;
  carrier_name: string;
  carrier_level_data: TemplateLevelData[];
}

export interface TemplateAssignedAgent {
  username: string;
  agent_id: number;
  address: string | null;
  state: string | null;
  country: string | null;
  zip_code: string | null;
  agency_id: number;
  agency_name: string;
  master_user_id: number;
}
export interface TemplateList {
  template_id: number;
  template_name: string;
  template_created_by_user_id: number;
  template_created_by_username: string;
  template_created_at_timestamp: string;
  carrier_type_id: number;
  carrier_type_name: string;
  carrier_type_color: string;
  template_carrier_count: number;
}

export interface Template {
  template_id: number;
  template_name: string;
  carrier_type_id: number;
  carrier_type_name: string;
  carrier_type_color: string;
  template_data: TemplateCarrierData[];
  template_assign_to_agent_list: TemplateAssignedAgent[];
  template_assign_to_agent_count: number;
  template_assign_to_agency_count: number;
}

export class TemplateListResponse extends Response<TemplateList[]> {}
export class TemplateResponse extends Response<Template[]> {}

export interface PrivacyPolicyConsent {
  privacy_policy_activity_message: string;
  privacy_policy_activity_timestamp: string;
  privacy_policy_activity: string;
}

export interface AgentUserData {
  created_by_user_id: number;
  created_by_username: string;
  created_by_user_email_address: string;
  user_id: number;
  primary_data_flag: boolean;
  username: string;
  email_address: string;
  address: string | null;
  state: string | null;
  country: string | null;
  zip_code: string | null;
  sso_email: string | null;
  user_status_id: number;
  user_status_name: string;
  user_role_id: number;
  user_role_name: string;
  user_migrated: boolean;
  user_privacy_policy_accepted_flag: boolean;
  user_privacy_policy_consent: PrivacyPolicyConsent[];
}

export interface AgentCommissionTemplateLevel {
  template_carrier_assignment_level_id: number;
  carrier_level_name: string;
  carrier_ba_level_flag: boolean;
  carrier_direct_level_flag: boolean;
  template_level_sequence: number;
}

export interface AgentCommissionTemplateData {
  template_carrier_assignment_id: number;
  carrier_id: number;
  carrier_name: string;
  carrier_level_data: AgentCommissionTemplateLevel[];
}

export interface AgentCommissionData {
  agent_commission_data_id: number;
  carrier_type_id: number;
  carrier_type_name: string;
  carrier_type_color: string;
  template_assign_flag: boolean;
  template_id: number;
  template_name: string;
  template_data: AgentCommissionTemplateData[];
}

export interface AgentList {
  agent_id: number;
  agent_created_by_user_id: number;
  agent_created_by_username: string;
  agent_created_at_timestamp: string;
  agent_user_data: (Pick<
    AgentUserData,
    | 'user_id'
    | 'username'
    | 'email_address'
    | 'primary_data_flag'
    | 'user_privacy_policy_consent'
  > & {
    user_assigned_agent_by_user_id: number;
    user_assigned_agent_by_username: string;
  })[];
  agent_commission_data: AgentCommissionData[];
}

export interface Agent {
  agent_id: number;
  agent_created_by_user_id: number;
  agent_created_by_username: string;
  agent_created_at_timestamp: string;
  agent_status_id: number;
  agent_status_name: string;
  agent_user_data: AgentUserData[];
  agent_commission_data: AgentCommissionData[];
}

export class AgentResponse extends Response<Agent[]> {}
export class AgentListResponse extends Response<AgentList[]> {}

export interface AgentHierarchyData {
  application_id: string;
  policy_number: string;
  agent_contact_id: number;
  agent_name: string;
  code: string;
  description: string;
  hierarchy_parent_contact_id: number | null;
  hierarchy_parent_name: string | null;
  top_line_hierarchy: string;
  top_line_hierarchy_contact_id: number | null;
  crm_owner: string;
  life_manager: string;
  annuity_manager: string;
  submit_date: string;
  last_updated_on: string;
}

export class AgentHierarchyResponse extends Response<AgentHierarchyData[]> {}

export interface AgentCommissionLevelAppointment {
  ai: {
    carrier_id: string;
    carrier_name: string;
    carrier_level: string;
  };
  mag: {
    carrier_id: string;
    carrier_name: string;
    carrier_level: {
      carrier_level_id: string;
      carrier_level_name: string;
    };
  };
  loa_badge: boolean;
  id: number;
  status?: string;
}

export interface AgentCommissionLevelData {
  contact_id: string;
  contact_name: string;
  contact_email: string;
  appointments: AgentCommissionLevelAppointment[];
}

export class AgentCommissionLevelResponse extends Response<
  AgentCommissionLevelData[]
> {}
