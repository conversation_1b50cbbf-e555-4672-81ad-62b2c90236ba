import { injectable } from 'inversify';
import type { GetSignedUrlConfig } from '@google-cloud/storage';

import type { ExtNextApiRequest } from '@/types';
import { prisma } from '@/lib/prisma';
import { storage } from '@/lib/firebase-admin';
import { getMimeType } from '@/lib/helpers';

export type StorageSignedUrlParams = {
  endpoint: 'documents' | 'accounts';
  endpoint_str_id?: string;
  file_preview_type: 'override' | 'original' | 'logo';
  expires?: number;
  action?: 'read' | 'write' | 'delete' | 'resumable';
};

@injectable()
export class FirebaseStorageSignedUrlService {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async getEntity(req: ExtNextApiRequest): Promise<any> {
    const { endpoint, endpoint_str_id } = req.body as StorageSignedUrlParams;

    const isAdmin = await prisma.admin.findUnique({
      where: { uid: req.ouid ?? req.uid },
    });

    const whereClause = {
      str_id: endpoint_str_id,
      account_id: req.account_id,
      state: 'active',
    };

    if (isAdmin) {
      whereClause.account_id = undefined;
    }

    const entity = await prisma[endpoint].findUnique({
      where: whereClause,
    });

    return entity;
  }

  getEntityFilePath(params: StorageSignedUrlParams, entity): string {
    const { endpoint, file_preview_type } = params;
    let filePath = '';
    if (endpoint === 'documents') {
      filePath =
        file_preview_type === 'override'
          ? entity?.override_file_path
          : entity?.file_path;
    } else if (endpoint === 'accounts') {
      filePath = entity?.logo_url;
    }
    return filePath;
  }

  async getStorageFile(filePath: string): Promise<File> {
    const fileRef = storage.file(filePath);
    const [buffer] = await fileRef.download();
    // Convert to File object
    const blob = new Blob([buffer]);
    const file = new File([blob], filePath.split('/').pop());
    return file;
  }

  async getSignedUrl(
    params: Partial<StorageSignedUrlParams>,
    filePath: string,
    req: ExtNextApiRequest,
    autoGenerateAction = false
  ): Promise<{ error?: string; url: string; exists?: boolean }> {
    try {
      const { expires, action } = params;
      let urlAction = action;

      const fileRef = storage.file(filePath);
      const [exists] = await fileRef.exists();

      if (autoGenerateAction) {
        if (!exists) {
          urlAction = 'write';
        } else {
          urlAction = 'read';
        }
      }

      if (urlAction === 'read') {
        if (!exists) {
          return {
            url: '',
          };
        }
      }

      const mimeType = getMimeType(filePath);
      const expiresTime = expires || 1000 * 60 * 15;
      const options: GetSignedUrlConfig = {
        action: urlAction,
        expires: Date.now() + expiresTime,
        contentType: mimeType,
      };

      const [url] = await fileRef.getSignedUrl(options);
      return {
        url,
        exists,
      };
    } catch (error) {
      req.logger.error(`Error getting signed URL: ${error}`);
      return {
        error: error,
        url: '',
      };
    }
  }
}
