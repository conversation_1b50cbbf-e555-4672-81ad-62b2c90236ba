// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { IncomingMessage } from 'http';
import type { NextApiRequest } from 'next';

import { container } from '@/ioc';
import { MethodNotAllowedException } from '@/lib/exceptionHandler';
import { PermissionService } from '@/services/permission';
import type { ExtAccountInfo, ExtNextApiRequest } from '@/types';

type DecoratorFunction = (req: NextApiRequest) => Promise<void>;

function createPermissionDecorator(fn: DecoratorFunction) {
  return (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    _target: any,
    _propertyKey: string,
    descriptor: PropertyDescriptor
  ) => {
    const originalMethod = descriptor.value;

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    descriptor.value = async function (...args: any[]) {
      const req = args.find((r) => r instanceof IncomingMessage);
      if (!req) {
        throw new Error('Request is not injected');
      }
      await fn(req as NextApiRequest);
      return await originalMethod.apply(this, args);
    };

    return descriptor;
  };
}

export function Guard(action: string | string[], resource: string | string[]) {
  return createPermissionDecorator(
    async (req: NextApiRequest & ExtNextApiRequest) => {
      const permissionService =
        container.get<PermissionService>(PermissionService);

      const ability = await permissionService.getAbilities({
        account_id: req.account_id,
        role_id: req.role_id,
        ouid: req.ouid,
        uid: req.uid,
      } as ExtAccountInfo);

      const actions = Array.isArray(action) ? action : [action];
      const resources = Array.isArray(resource) ? resource : [resource];

      const hasPermission = actions.some((act) =>
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        resources.some((res) => ability.can(act as any, res as any))
      );

      if (!hasPermission) {
        throw new MethodNotAllowedException(
          `No permission, need the following permissions: action: ${actions.join(',')}, resource: ${resources.join(',')}`
        );
      }
    }
  );
}
