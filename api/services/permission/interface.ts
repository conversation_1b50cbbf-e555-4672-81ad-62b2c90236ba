export enum EntityType {
  ACCOUNT = 'Account',
  CALCULATION_METHOD = 'CalculationMethod',
  COMMISSIONS = 'Commissions',
  POLICIES = 'Policies',
  RECONCILIATION = 'Reconciliation',
  AGENTS = 'Agents',
  AGENTS_GROUPS = 'AgentsGroups',
  AGENTS_PRODUCTION = 'AgentsProduction',
  CARRIERS_SCHEDULES = 'CarriersSchedules',
  INCENTIVES_SCHEDULES = 'IncentivesSchedules',
  COMP_GRIDS_SCHEDULES = 'CompGridsSchedules',
  REPORTS = 'Reports',
  COMPANIES = 'Companies',
  PRODUCTS = 'Products',
  OPTIONS = 'Options',
  DOCUMENTS = 'Documents',
  VIEWS = 'Views',
  SETTINGS_DATA_PROCESSING = 'SettingsDataProcessing',
  COMPENSATION_PROFILES = 'CompensationProfiles',
  COMPENSATION_PROFILE_SETS = 'CompensationProfileSets',
  CUSTOMERS = 'Customers',
  INSIGHTS = 'Insights',
  /* ... */
}
export enum CommonAction {
  EXPORT = 'export',
  UPLOAD = 'upload',
  RUN = 'run',
}
export enum APIAction {
  POST = 'post',
  GET = 'get',
  PUT = 'put',
  DELETE = 'delete',
}

export enum CrudAction {
  READ = 'read',
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
}

// Entity-specific actions exported as distinct enums
export enum ReconcilerAction {
  RUN = 'run',
}

// Union type of all supported actions
export type Actions =
  | 'manage'
  | CommonAction
  | CrudAction
  | ReconcilerAction
  | APIAction;
export type Subjects = EntityType | 'all';
