import type { MongoAbility } from '@casl/ability';
import { createMongoAbility } from '@casl/ability';
import { inject, injectable } from 'inversify';

import type { ExtAccountInfo } from '@/types';
import { AccountService } from '@/services/account';
import {
  type Actions,
  CrudAction,
  EntityType,
  type Subjects,
} from '@/services/permission/interface';
import { MethodNotAllowedException } from '@/lib/exceptionHandler';

const pageViewToSubjectMap = new Map<string, EntityType>([
  ['reconciliation', EntityType.RECONCILIATION],
  ['commissions', EntityType.COMMISSIONS],
  ['policies', EntityType.POLICIES],
  ['agents', EntityType.AGENTS],
  ['agents_groups', EntityType.AGENTS_GROUPS],
  ['agents_production', EntityType.AGENTS_PRODUCTION],
  ['carriers_schedules', EntityType.CARRIERS_SCHEDULES],
  ['incentives_schedules', EntityType.INCENTIVES_SCHEDULES],
  ['comp_grids_schedules', EntityType.COMP_GRIDS_SCHEDULES],
  ['reports', EntityType.REPORTS],
  ['companies', EntityType.COMPANIES],
  ['products', EntityType.PRODUCTS],
  ['options', EntityType.OPTIONS],
  ['documents', EntityType.DOCUMENTS],
  ['add_documents', EntityType.DOCUMENTS],
  ['views', EntityType.VIEWS],
  ['settings_data_processing', EntityType.SETTINGS_DATA_PROCESSING],
  ['compensation_profiles', EntityType.COMPENSATION_PROFILES],
  ['compensation_profile_sets', EntityType.COMPENSATION_PROFILE_SETS],
  ['customers', EntityType.CUSTOMERS],
  ['insights', EntityType.INSIGHTS],
]);

@injectable()
export class PermissionService {
  @inject(AccountService) accountService: AccountService;

  private abilities: MongoAbility<[Actions, Subjects]>;

  async getAbilities(
    accountInfo: ExtAccountInfo
  ): Promise<MongoAbility<[Actions, Subjects]>> {
    // Fetch rules from the database as JSON

    const settings = await this.accountService.getSettingsForRoles(
      accountInfo.account_id
    );
    const roleSettings = settings.filter(
      (setting) => setting.role === accountInfo.role_id && setting.show_page
    );
    const rules = roleSettings.map((setting) => {
      return {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        action: setting.read_only ? [CrudAction.READ] : (['manage'] as any),
        subject: [pageViewToSubjectMap.get(setting.key) || setting.key],
      };
    });

    // Create and return the MongoAbility instance
    const abilities = createMongoAbility<[Actions, Subjects]>(rules);
    this.abilities = abilities;
    return abilities;
  }

  can(action: Actions, subject: Subjects): boolean {
    if (!this.abilities) {
      throw new Error('Abilities not initialized');
    }
    return this.abilities.can(action, subject);
  }
  canWithError(action: Actions, subject: Subjects) {
    if (!this.abilities.can(action, subject)) {
      throw new MethodNotAllowedException(
        `No permission, need the following permissions: action: ${action}, subject: ${subject}`
      );
    }
  }
}
