import type XLSX from 'xlsx';
import {
  CURRENCY_FORMAT_COLUMNS,
  EXCEL_COLUMN_NAMES,
} from 'common/constants/excel-export';
import { formatCurrency } from 'common/helpers/formatCurrency';

export const EXCEL_CURRENCY_FORMAT = '$#,##0.00';
export const formatCellCurrency = ({
  worksheet,
  totalRows,
  startRow = 1,
  headers,
}: {
  worksheet: XLSX.WorkSheet;
  totalRows: number;
  startRow?: number;
  headers: string[];
}) => {
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  CURRENCY_FORMAT_COLUMNS.forEach((name) => {
    const index = headers.findIndex((header) => header === name);
    const dollarRegex = /\$|,/g;

    const col = EXCEL_COLUMN_NAMES[index];
    if (col) {
      for (let i = 0; i <= totalRows; i++) {
        const cell = worksheet[`${col}${startRow + i}`];
        if (cell) {
          const v = cell.v;
          // Some data are formatted as currency strings which is hard to refactor
          // So we need to convert them to numbers and apply the currency format
          if (typeof v === 'string' && v.includes('$')) {
            // Update cell to number
            try {
              cell.v = parseFloat(v.replace(dollarRegex, ''));
              cell.t = 'n'; // Check xlsx documentation for cell types
            } catch (e) {
              // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              console.error(`Error parsing currency value: ${v}`, e);
            }
          }
          cell.z = EXCEL_CURRENCY_FORMAT;
        }
      }
    }
  });
};

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const formatPdfCurrency = (data: any[]) => {
  const formattedData = data.map((row) => {
    for (const key in row) {
      // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (CURRENCY_FORMAT_COLUMNS.includes(key) && !isNaN(+row[key])) {
        row[key] = formatCurrency(row[key]);
      }
    }
    return { ...row };
  });
  return formattedData;
};
