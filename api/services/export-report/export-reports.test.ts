import { describe, it, expect, vi, beforeEach } from 'vitest';

import { generateReport, ExportReportType } from './export-reports';

vi.mock('@sentry/nextjs', () => ({
  captureException: vi.fn(),
}));

const mockFindFirst = vi.fn();
vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    processors: {
      findFirst: mockFindFirst,
    },
  },
}));

describe('Export policies and commissions reports', () => {
  beforeEach(() => {
    mockFindFirst.mockReset();
  });

  const baseData = [
    {
      'Agent payout rate override': '',
      'Linked account transaction details': [],
      'Agent comp calc': '',
      'Agent comp log': '',
      'Agent comp calc status': '',
      'Product sub type': '',
      'Premium type': 'policy',
      Reconciler: '',
      'Policy number': 'TEST123456',
      'Group name': 'Test Group',
      'Aggregation Id': 'AGG-001',
      'Premium amount': '$100.00',
      'Payment date': '01/01/2025',
      'Paying entity': 'Test Carrier',
      'Commission amount': '$10.00',
      'Member count': '5',
      'Agent payout rate': '5%',
      Tags: ['tag1', 'tag2'],
      'Linked policy': 'LINKED123',
      'Payout status*': 'Pending',
      'Agent commissions log': '',
      'Agent commission payout rate': '',
      'Carrier/MGA': 'Test MGA',
      'Statement date': '01/31/2025',
      State: 'NY',
      'Agent name': 'Test Agent',
      'Commission amount paid': '$5.00',
      'Compensation type': 'Base',
      'Processing date': '01/15/2025',
      'Product type': 'Life',
      'Effective date': '01/01/2024',
      'Payout status': 'Active',
      'Internal Id': 'INT-001',
      Agents: 'Agent1',
      'Product option name': 'Option A',
      'Payment mode': 'Monthly',
      Document: 'testdoc.pdf',
      'Payment status': 'Paid',
      'Commission basis': 'Annual',
      'Commission rate': '2',
      'Product name': 'Test Product',
      Fees: '$0.00',
      'Transaction type': 'New',
      'Carrier rate': '2%',
      Status: 'Active',
      'Commissionable premium amount': '$100.00',
      'Agent Id': 'AGENT001',
      'Group number': 'GRP-001',
      'Statement number': 'STMT-001',
      'Standardized customer name': 'Test Customer',
      AgentCommissionsV2: '',
      'Split percentage': '100.000%',
      'Issue age': '35',
      'Period date': '01/01/2025',
      'Agent commissions': '$10.00',
      'Customer paid premium amount': '$100.00',
      Notes: 'Test note',
      'Account type': 'test',
      'Customer name': 'Test Customer',
      'Processing status': 'Processed',
    },
  ];

  const baseOptions = {
    reportType: ExportReportType.POLICY as ExportReportType,
    role_id: '123',
    account_id: 'acc',
    uid: 'uid',
  };

  // These are broken but should ideally be fixed soon by iteration 3 of the report processor feature
  // it('Given report_processor is provided and found, when generating report, then processor function is applied', async () => {
  //   mockFindFirst.mockResolvedValue({
  //     processor: '(data) => data.map(item => ({...item, processed: true}))',
  //   });

  //   const result = await generateReport(baseData, {
  //     ...baseOptions,
  //     report_processor: 'proc1',
  //   });
  //   expect(result.every((item) => item.processed)).toBe(true);
  // });

  // it('Given report_processor is provided and not found, when generating report, then error is thrown', async () => {
  //   const mockFindFirst = vi.fn();
  //   vi.mock('@/lib/prisma', () => ({
  //     prismaClient: {
  //       processors: {
  //         findFirst: mockFindFirst,
  //       },
  //     },
  //   }));
  //   mockFindFirst.mockResolvedValueOnce(null);
  //   await expect(
  //     generateReport(baseData, {
  //       ...baseOptions,
  //       report_processor: 'notfound',
  //     })
  //   ).rejects.toThrow(
  //     'Report processor notfound not found. Could not export data.'
  //   );
  // });

  it('Given report_processor throws error, when generating report, then error is captured and thrown', async () => {
    const mockFindFirst = vi.fn();
    vi.mock('@/lib/prisma', () => ({
      prismaClient: {
        processors: {
          findFirst: vi.fn(),
        },
      },
    }));
    mockFindFirst.mockResolvedValueOnce({
      processor: '(() => { throw new Error("fail"); })',
    });
    await expect(
      generateReport(baseData, {
        ...baseOptions,
        reportProcessor: 'badproc',
      })
    ).rejects.toThrow('Error processing data using report processor badproc');
    const Sentry = await import('@sentry/nextjs');
    expect(Sentry.captureException).toHaveBeenCalled();
  });
});
