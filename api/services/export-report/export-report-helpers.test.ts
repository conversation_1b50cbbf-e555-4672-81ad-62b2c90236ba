import { describe, it, expect, vi, beforeEach } from 'vitest';
import * as XLSX from 'xlsx';

import {
  setColumnWidths,
  createSummarySheet,
  processReportData,
  createReportWorksheet,
} from './export-report-helpers';
import { savedReportsGroupsTemplates } from '@/types';

// Mock dependencies
vi.mock('xlsx', () => ({
  utils: {
    book_new: vi.fn().mockReturnValue({}),
    json_to_sheet: vi.fn().mockReturnValue({}),
    book_append_sheet: vi.fn(),
    sheet_add_aoa: vi.fn(),
    sheet_add_json: vi.fn(),
  },
  write: vi.fn().mockReturnValue(Buffer.from('mock-xlsx-data')),
}));

vi.mock('@/pages/api/export/exportFields', () => ({
  exportTotalRows: vi.fn().mockResolvedValue({
    'Commission amount': 100,
    'Premium amount': 1000,
  }),
}));

vi.mock('@/pages/api/export/base', () => ({
  getExportData: vi.fn(async ({ data }) =>
    data.map((d) => ({ ...d, formatted: true }))
  ),
}));

vi.mock('@/lib/helpers/processContacts', () => ({
  processContacts: vi.fn().mockResolvedValue(undefined),
}));

vi.mock('@/lib/helpers', () => ({
  consolidateGroupedRecords: vi.fn(() => [{ id: 1, consolidated: true }]),
  arrayToObjByKey: vi.fn().mockReturnValue({ 'policy-1': [{ id: 1 }] }),
}));

vi.mock('@/lib/Formatter', () => ({
  __esModule: true,
  default: {
    date: vi.fn().mockReturnValue('06/10/2025'),
  },
}));

vi.mock('./format-currency', () => ({
  EXCEL_CURRENCY_FORMAT: '$#,##0.00',
  formatCellCurrency: vi.fn(),
}));

vi.mock('./utils', () => ({
  isReportInSelectedData: vi.fn().mockReturnValue(true),
  truncateName: vi.fn((name) => name.substring(0, 31)),
}));

describe('Export Report Helpers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('setColumnWidths', () => {
    it('Should return early for empty data', () => {
      const worksheet = {};
      setColumnWidths(worksheet, []);
      expect(worksheet['!cols']).toBeUndefined();
    });

    it('Should calculate column widths based on content', () => {
      const worksheet = {};
      const data = [
        { short: 'abc', long: 'this is a much longer column value' },
        { short: 'def', long: 'another value' },
      ];

      setColumnWidths(worksheet, data);

      expect(worksheet['!cols']).toBeDefined();
      // The "long" column Should have a wider width
      expect(worksheet['!cols'][1].wch).toBeGreaterThan(
        worksheet['!cols'][0].wch
      );
    });

    it('Should cap column width at maximum value of 50', () => {
      const worksheet = {};
      const data = [
        { column: 'a'.repeat(100) }, // Very long value that Should be capped
      ];

      setColumnWidths(worksheet, data);

      expect(worksheet['!cols'][0].wch).toBe(50);
    });
  });

  describe('createSummarySheet', () => {
    it('Should return undefined for non-commission payout templates', () => {
      const result = createSummarySheet({
        groupData: { template: 'OTHER_TEMPLATE' },
        summaryResults: [],
      });

      expect(result).toBeUndefined();
    });

    it('Should create summary sheet for commission payout template', () => {
      const groupData = {
        template: savedReportsGroupsTemplates.COMMISSION_PAYOUT,
      };

      const summaryResults = [{ column1: 'value1', column2: 100 }];

      const result = createSummarySheet({ groupData, summaryResults });

      expect(XLSX.utils.json_to_sheet).toHaveBeenCalledWith(summaryResults);
      expect(XLSX.utils.book_append_sheet).toHaveBeenCalled();
      expect(XLSX.write).toHaveBeenCalled();
      expect(result).toBeDefined();
    });
  });

  describe('processReportData', () => {
    const defaultParams = {
      report: {
        name: 'Test Report',
        snapshot_data: {
          data: {
            data: [{ id: 1, policy_id: 'policy-1' }],
            totals: {
              agent_commissions: { agent1: 500 },
            },
          },
        },
      },
      exportOptions: { selected_data: true },
      groupData: { template: 'STANDARD_TEMPLATE' },
      table: 'policy',
      roleForExport: 'admin',
      req: { account_id: 'acc123', uid: 'user123', query: {} },
    };

    it('Should return null when report is not in selected data', async () => {
      const { isReportInSelectedData } = await import('./utils');
      vi.mocked(isReportInSelectedData).mockReturnValueOnce(false);

      const result = await processReportData(defaultParams);

      expect(result).toBeNull();
    });

    it('Should apply amount due threshold filter for commission payout', async () => {
      const params = {
        ...defaultParams,
        groupData: {
          template: savedReportsGroupsTemplates.COMMISSION_PAYOUT,
        },
        exportOptions: {
          selected_data: true,
          amount_due_threshold: '600', // Higher than the commission amount
        },
      };

      const result = await processReportData(params);

      expect(result).toBeNull();
    });

    it('Should process policy number grouping if specified', async () => {
      const { arrayToObjByKey, consolidateGroupedRecords } = await import(
        '@/lib/helpers'
      );

      const params = {
        ...defaultParams,
        exportOptions: { selected_data: true, grouping: 'policyNumber' },
      };

      await processReportData(params);

      expect(arrayToObjByKey).toHaveBeenCalled();
      expect(consolidateGroupedRecords).toHaveBeenCalled();
    });

    it('Should return processed data with formatted totals', async () => {
      const { getExportData } = await import('@/pages/api/export/base');
      const { exportTotalRows } = await import(
        '@/pages/api/export/exportFields'
      );

      const result = await processReportData(defaultParams);

      expect(result).toHaveProperty('formatData');
      expect(result).toHaveProperty('filteredTotalRowFormatted');
      expect(result).toHaveProperty('commissionAmount');
      expect(getExportData).toHaveBeenCalled();
      expect(exportTotalRows).toHaveBeenCalled();
    });
  });

  describe('createReportWorksheet', () => {
    const mockWorksheet = {};
    const defaultParams = {
      report: { name: 'Test Report' },
      formatData: [
        {
          'Policy number': 'POL-123',
          'Premium amount': 1000,
          'Commission amount': 100,
        },
      ],
      filteredTotalRowFormatted: {
        'Premium amount': 1000,
        'Commission amount': 100,
      },
      commissionAmount: 500,
      groupData: { created_at: '2025-06-10' },
    };

    beforeEach(() => {
      vi.mocked(XLSX.utils.json_to_sheet).mockReturnValue(mockWorksheet);
    });

    it('Should create worksheet with header data', () => {
      createReportWorksheet(defaultParams);

      // Should add report name and dates at the top
      expect(XLSX.utils.sheet_add_aoa).toHaveBeenCalledWith(
        expect.any(Object), // The worksheet
        expect.arrayContaining([
          expect.arrayContaining([defaultParams.report.name]),
        ]),
        expect.any(Object) // The options object
      );
    });

    it('Should add both top and bottom totals rows', async () => {
      const { formatCellCurrency } = await import('./format-currency');

      createReportWorksheet(defaultParams);

      // Should format currency in three places: top totals, data rows, and bottom totals
      expect(formatCellCurrency).toHaveBeenCalledTimes(3);

      // Check for the totals label in the last add_aoa call (bottom totals)
      const calls = vi.mocked(XLSX.utils.sheet_add_aoa).mock.calls;

      // Ensure there are calls before accessing them
      expect(calls.length).toBeGreaterThan(0);

      // Find the call that contains the bottom totals row
      // The last call should have an array with "Totals" as the first item
      const bottomTotalsCall = calls.find(
        (call) =>
          call[1] && Array.isArray(call[1][0]) && call[1][0][0] === 'Totals'
      );

      expect(bottomTotalsCall).toBeDefined();
      expect(bottomTotalsCall[1][0][0]).toBe('Totals');
    });
  });
});
