import type { ExportOptions } from 'common/exports';

import type {
  ExtNextApiRequest,
  ExtNextApiResponse,
  savedReportsGroupsTemplates,
} from '@/types';

export type ExportParams = {
  exportOptions: ExportOptions;
  res: ExtNextApiResponse;
  req: ExtNextApiRequest;
  groupData: {
    template: savedReportsGroupsTemplates;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    saved_reports: any[];
    created_at: string;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    account: any;
  };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  summaryResults: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  table: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  roleForExport: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  compReportSettings?: any;
};
