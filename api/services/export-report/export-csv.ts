import * as Sentry from '@sentry/nextjs';
import * as fastcsv from 'fast-csv';
import { CompReportViewTypes } from 'common/globalTypes';

import { savedReportsGroupsTemplates } from '@/types';
import { exportTotalRows } from '@/pages/api/export/exportFields';
import { consolidateGroupedRecords, arrayToObjByKey } from '@/lib/helpers';
import {
  createZipStream,
  isReportInSelectedData,
  truncateName,
} from '@/services/export-report/utils';
import { formatPdfCurrency } from '@/services/export-report/format-currency';
import type { ExportParams } from './types';
import { getExportData } from '@/pages/api/export/base';
import { processContacts } from '@/lib/helpers/processContacts';

export const exportCsv = async (params: ExportParams) => {
  const {
    groupData,
    res,
    req,
    summaryResults,
    exportOptions,
    table,
    roleForExport,
  } = params;

  const zip = createZipStream({ res });

  // Add summary file only for commission payout
  if (groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT) {
    const csvSummaryStream = fastcsv.write(formatPdfCurrency(summaryResults), {
      headers: true,
    });
    zip.append(csvSummaryStream, { name: `Summary.csv` });
  }

  for (const report of groupData.saved_reports) {
    // Check for create reports of selected data
    if (!isReportInSelectedData(report, exportOptions.selected_data)) {
      continue;
    }

    // Check grouping options
    if (exportOptions.grouping === 'policyNumber') {
      const groupedRecords = arrayToObjByKey(
        report.snapshot_data.data.data,
        'policy_id',
        true
      );
      const consolidatedData = consolidateGroupedRecords(groupedRecords);

      report.snapshot_data.data.data = consolidatedData;
    }

    await processContacts(report.snapshot_data.data.data);

    let formatData = await getExportData({
      data: report.snapshot_data.data.data,
      table,
      roleId: roleForExport,
      accountId: req.account_id,
      templateType: groupData.template,
      uid: req.uid,
      contactStrId:
        req.query.view === CompReportViewTypes.PRODUCER_VIEW
          ? report.snapshot_data.data.contactStrId
          : undefined,
    });

    const totalRow = await exportTotalRows(formatData);
    formatData.push(totalRow);

    formatData = formatPdfCurrency(formatData);
    // This is a transform stream that turns formatData into a CSV
    const csvStream = fastcsv.write(formatData, { headers: true });

    const truncatedName = truncateName(report.name, groupData.template);

    // This adds the CSV stream to the zip file as a file
    zip.append(csvStream, { name: `${truncatedName}.csv` });
  }
  try {
    // This finalizes the archive and sends it to the client
    await zip.finalize();
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
