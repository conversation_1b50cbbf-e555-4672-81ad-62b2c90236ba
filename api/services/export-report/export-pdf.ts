import * as Sentry from '@sentry/nextjs';
import { formatCurrency } from 'common/helpers/formatCurrency';
import { CompReportViewTypes } from 'common/globalTypes';

import { savedReportsGroupsTemplates } from '@/types';
import { exportTotalRows } from '@/pages/api/export/exportFields';
import Formatter from '@/lib/Formatter';
import CommissionPayoutTemplate from '@/lib/templates/CommissionPayoutTemplate';
import {
  consolidateGroupedRecords,
  getStorageSignedUrl,
  arrayToObjByKey,
} from '@/lib/helpers';
import { isReportInSelectedData } from '@/services/export-report/utils';
import { formatPdfCurrency } from '@/services/export-report/format-currency';
import type { ExportParams } from './types';
import { getExportData } from '@/pages/api/export/base';
import { processContacts } from '@/lib/helpers/processContacts';

export const exportPdf = async (params: ExportParams) => {
  const {
    groupData,
    res,
    req,
    summaryResults,
    exportOptions,
    table,
    roleForExport,
    compReportSettings,
  } = params;

  try {
    let logoImg = '';
    if (groupData.account.white_label_mode) {
      logoImg = await getStorageSignedUrl(groupData.account.logo_url);
    }

    const pdfData = {
      reportData: {
        data: [],
        totals: [],
        reportInfo: [],
      },
    };
    let formatData = [];
    let totalReportRow = {};
    let totalReportInfo = {};
    // Add summary page only for commission payout
    if (groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT) {
      // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      pdfData['summary'] = formatPdfCurrency(summaryResults);
    }

    for (const report of groupData.saved_reports) {
      // Check for create reports of selected data
      if (!isReportInSelectedData(report, exportOptions.selected_data)) {
        continue;
      }

      const commissionAmount = +Object.values(
        report.snapshot_data?.data?.totals?.agent_commissions
      )?.[0];
      // Apply amount due threshold filter for commission payout
      if (
        groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT &&
        exportOptions &&
        exportOptions.amount_due_threshold &&
        +exportOptions.amount_due_threshold > 0
      ) {
        if (commissionAmount < +exportOptions.amount_due_threshold) {
          continue;
        }
      }

      // Check grouping options
      if (exportOptions.grouping === 'policyNumber') {
        const groupedRecords = arrayToObjByKey(
          report.snapshot_data.data.data,
          'policy_id',
          true
        );
        const consolidatedData = consolidateGroupedRecords(groupedRecords);

        report.snapshot_data.data.data = consolidatedData;
      }

      await processContacts(report.snapshot_data.data.data);

      formatData = await getExportData({
        data: report.snapshot_data.data.data,
        table,
        roleId: roleForExport,
        accountId: req.account_id,
        templateType: groupData.template,
        uid: req.uid,
        contactStrId:
          req.query.view === CompReportViewTypes.PRODUCER_VIEW
            ? report.snapshot_data.data.contactStrId
            : undefined,
      });

      const totalRow = await exportTotalRows(formatData);

      formatData = formatPdfCurrency(formatData);
      totalReportRow = totalRow;
      totalReportInfo = {
        name: report.name,
        commissionAmount: `Total producer commission: ${formatCurrency(commissionAmount)}`,
        createdAt: Formatter.date(groupData.created_at),
        logoImg: logoImg,
        accountName: groupData.account.name,
        customTermsEnabled: compReportSettings?.enable_custom_terms,
        customTermsText: compReportSettings?.custom_terms_text,
      };

      pdfData.reportData.data.push(formatData);
      pdfData.reportData.totals.push(totalReportRow);
      pdfData.reportData.reportInfo.push(totalReportInfo);
    }
    // Create pdf using a template
    const pdfResult = await CommissionPayoutTemplate(pdfData);
    // Setting up the response headers
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Access-Control-Expose-Headers', 'Content-Disposition');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="Saved-Report-Group-Export.pdf"`
    );

    const stream = pdfResult.pipe(res);

    await new Promise((resolve, reject) =>
      stream.on('finish', resolve).on('error', reject)
    );
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
