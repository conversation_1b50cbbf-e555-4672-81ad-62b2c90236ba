import * as Sentry from '@sentry/nextjs';

import { prismaClient } from '@/lib/prisma';

export enum ExportReportType {
  POLICY = 'policy',
  COMMISSIONS = 'commissions',
}

interface ExportOptions {
  reportProcessor?: string;
  producerView?: boolean;
  isExportRelationship?: boolean;
  roleId?: string;
  accountId?: string;
  uid?: string;
  reportType: ExportReportType;
}

export const generateReport = async (
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data: any[],
  { reportProcessor }: ExportOptions
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
): Promise<any[]> => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let filterList: any[] = [...data];

  if (reportProcessor) {
    try {
      const processorRecord = await prismaClient.processors.findFirst({
        accountInject: false,
        where: {
          str_id: reportProcessor,
        },
      });

      if (!processorRecord) {
        throw Error(
          `Report processor ${reportProcessor} not found. Could not export data.`
        );
      }

      // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      // biome-ignore lint/security/noGlobalEval: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const processorFn = eval('(' + processorRecord.processor + ')');
      filterList = processorFn(filterList);
    } catch (error) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error processing data with report processor:', error);
      Sentry.captureException(error);
      throw Error(
        `Error processing data using report processor ${reportProcessor}`
      );
    }
  }
  return filterList;
};
