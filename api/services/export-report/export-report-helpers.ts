import * as XLSX from 'xlsx';
import { CompReportViewTypes } from 'common/globalTypes';

import { savedReportsGroupsTemplates } from '@/types';
import { exportTotalRows } from '@/pages/api/export/exportFields';
import Formatter from '@/lib/Formatter';
import { consolidateGroupedRecords, arrayToObjByKey } from '@/lib/helpers';
import {
  EXCEL_CURRENCY_FORMAT,
  formatCellCurrency,
} from '@/services/export-report/format-currency';
import { isReportInSelectedData } from './utils';
import { getExportData } from '@/pages/api/export/base';
import { processContacts } from '@/lib/helpers/processContacts';

/**
 * Sets the column widths in a worksheet based on the content
 */

// biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
export const setColumnWidths = (worksheet: XLSX.WorkSheet, data: any[]) => {
  if (!data || data.length === 0) return;

  const columnWidths = {};

  const headers = Object.keys(data[0]);
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  headers.forEach((header) => {
    columnWidths[header] = Math.max(header.length, 10) + 2;
  });

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  data.forEach((row) => {
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    Object.keys(row).forEach((key) => {
      const value =
        row[key] !== null && row[key] !== undefined ? String(row[key]) : '';
      columnWidths[key] = Math.max(columnWidths[key] || 0, value.length + 2);

      // Cap width at a reasonable maximum
      if (columnWidths[key] > 50) {
        columnWidths[key] = 50;
      }
    });
  });

  worksheet['!cols'] = headers.map((header) => ({ wch: columnWidths[header] }));
};

/**
 * Creates a summary sheet for commission payout reports
 */
export const createSummarySheet = ({ groupData, summaryResults }) => {
  // Add summary file only for commission payout
  const summaryWorkbook = XLSX.utils.book_new();
  if (groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT) {
    const worksheet = XLSX.utils.json_to_sheet(summaryResults);
    const headers = Object.keys(summaryResults[0]);
    formatCellCurrency({
      worksheet,
      totalRows: summaryResults.length,
      headers,
      startRow: 1,
    });

    setColumnWidths(worksheet, summaryResults);

    XLSX.utils.book_append_sheet(summaryWorkbook, worksheet, 'Summary');

    const wbout = XLSX.write(summaryWorkbook, {
      bookType: 'xlsx',
      type: 'buffer',
    });

    return wbout;
  }
};

/**
 * Process report data according to export options
 */
export const processReportData = async ({
  report,
  exportOptions,
  groupData,
  table,
  roleForExport,
  req,
}) => {
  if (!isReportInSelectedData(report, exportOptions.selected_data)) {
    return null;
  }

  // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let commissionAmount;
  if (
    report.snapshot_data?.data?.totals?.agent_commissions &&
    typeof report.snapshot_data.data.totals.agent_commissions === 'object'
  ) {
    const rawValue = Object.values(
      report.snapshot_data.data.totals.agent_commissions
    )?.[0] as string;
    commissionAmount = parseFloat(rawValue);
    // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (isNaN(commissionAmount)) {
      commissionAmount = 0;
    }
  }

  if (
    groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT &&
    exportOptions &&
    exportOptions.amount_due_threshold &&
    +exportOptions.amount_due_threshold > 0
  ) {
    if (commissionAmount < +exportOptions.amount_due_threshold) {
      return null;
    }
  }

  if (exportOptions.grouping === 'policyNumber') {
    const groupedRecords = arrayToObjByKey(
      report.snapshot_data.data.data,
      'policy_id',
      true
    );
    const consolidatedData = consolidateGroupedRecords(groupedRecords);

    report.snapshot_data.data.data = consolidatedData;
  }

  await processContacts(report.snapshot_data.data.data);

  const formatData = await getExportData({
    data: report.snapshot_data.data.data,
    table,
    roleId: roleForExport,
    accountId: req.account_id,
    templateType: groupData.template,
    uid: req.uid,
    contactStrId:
      req.query.view === CompReportViewTypes.PRODUCER_VIEW
        ? report.snapshot_data.data.contactStrId
        : undefined,
  });

  const totalRow = await exportTotalRows(formatData);
  const totalRowFormatted = {};
  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  Object.keys(formatData[0]).forEach((key) => {
    totalRowFormatted[key] = totalRow[key] || '';
  });

  const filteredTotalRowFormatted = Object.fromEntries(
    Object.entries(totalRowFormatted).filter(([_, value]) => value !== '')
  );

  return {
    formatData,
    filteredTotalRowFormatted,
    commissionAmount,
  };
};

/**
 * Creates a worksheet with the report data
 */
export const createReportWorksheet = ({
  report,
  formatData,
  filteredTotalRowFormatted,
  commissionAmount,
  groupData,
}) => {
  const worksheet = XLSX.utils.json_to_sheet([]);

  const data = [
    [`${report.name}`],
    [`Created at: ${Formatter.date(groupData.created_at)}`],
    [],
  ];

  if (commissionAmount) {
    data.splice(1, 0, [`Total producer commission:`, commissionAmount]);
  }

  XLSX.utils.sheet_add_aoa(worksheet, data, {
    origin: 'A1',
  });

  // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  const commissionAmountCell = worksheet['B2'];
  if (commissionAmountCell) {
    commissionAmountCell.z = EXCEL_CURRENCY_FORMAT;
  }

  // Empty spacing row
  XLSX.utils.sheet_add_aoa(worksheet, [[]], {
    origin: 'A4',
  });

  // Add the totals row before the report data
  XLSX.utils.sheet_add_aoa(worksheet, [['Totals']], {
    origin: 'A5',
  });
  XLSX.utils.sheet_add_json(worksheet, [filteredTotalRowFormatted], {
    origin: -1,
    skipHeader: false,
  });

  formatCellCurrency({
    worksheet,
    totalRows: 1,
    startRow: 6,
    headers: Object.keys(filteredTotalRowFormatted),
  });

  // Empty spacing row
  XLSX.utils.sheet_add_aoa(worksheet, [['']], {
    origin: 'A8',
  });

  // Add the rest of the formatData to the worksheet
  XLSX.utils.sheet_add_json(worksheet, formatData, {
    origin: -1,
    skipHeader: false,
  });

  /**
   * https://docs.sheetjs.com/docs/api/utilities/array/
   * SheetJS uses Object.keys for header order
   */
  const headers = Object.keys(formatData[0]);
  formatCellCurrency({
    worksheet,
    totalRows: formatData.length,
    startRow: 10,
    headers,
  });

  // Create a properly aligned totals row with values under their respective columns
  const bottomTotalsStartRow = 10 + formatData.length + 1;

  XLSX.utils.sheet_add_aoa(worksheet, [['']], {
    origin: -1,
  });

  // Find the first header that has a value
  const firstHeaderWithValue = headers.find(
    (header) =>
      filteredTotalRowFormatted[header] !== undefined &&
      filteredTotalRowFormatted[header] !== null &&
      filteredTotalRowFormatted[header] !== ''
  );

  const totalsArray = [];

  if (firstHeaderWithValue) {
    const firstValueIndex = headers.indexOf(firstHeaderWithValue);

    if (firstValueIndex === 0) {
      // When the first column has a value, combine "Totals:" with that value in the same cell
      totalsArray.push(
        `Totals: ${filteredTotalRowFormatted[firstHeaderWithValue]}`
      );

      for (let i = 1; i < headers.length; i++) {
        totalsArray.push(filteredTotalRowFormatted[headers[i]] || '');
      }
    } else {
      // Add empty strings for all columns before the one that should contain 'Totals'
      for (let i = 0; i < firstValueIndex - 1; i++) {
        totalsArray.push('');
      }

      totalsArray.push('Totals');

      for (let i = firstValueIndex; i < headers.length; i++) {
        totalsArray.push(filteredTotalRowFormatted[headers[i]] || '');
      }
    }
  }

  XLSX.utils.sheet_add_aoa(worksheet, [totalsArray], {
    origin: -1,
  });

  formatCellCurrency({
    worksheet,
    totalRows: 1,
    startRow: bottomTotalsStartRow,
    headers,
  });

  setColumnWidths(worksheet, [...formatData, filteredTotalRowFormatted]);

  return worksheet;
};
