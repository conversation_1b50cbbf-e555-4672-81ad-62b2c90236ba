import * as Sentry from '@sentry/nextjs';
import * as XLSX from 'xlsx';
import type { ExportOptions } from 'common/exports';

import {
  type ExtNextApiRequest,
  type ExtNextApiResponse,
  savedReportsGroupsTemplates,
} from '@/types';
import { formatCellCurrency } from '@/services/export-report/format-currency';
import { truncateName } from './utils';
import {
  createReportWorksheet,
  processReportData,
  setColumnWidths,
} from './export-report-helpers';

export const exportXlsx = async ({
  exportOptions,
  groupData,
  res,
  summaryResults,
  table,
  req,
  roleForExport,
}: {
  exportOptions: ExportOptions;
  res: ExtNextApiResponse;
  req: ExtNextApiRequest;
  groupData: {
    template: savedReportsGroupsTemplates;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    saved_reports: any[];
    created_at: string;
  };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  summaryResults: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  table: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  roleForExport: any;
}) => {
  try {
    const workbook = XLSX.utils.book_new();

    // Add summary sheet only for commission payout
    if (groupData.template === savedReportsGroupsTemplates.COMMISSION_PAYOUT) {
      const worksheet = XLSX.utils.json_to_sheet(summaryResults);
      const headers = Object.keys(summaryResults[0]);
      formatCellCurrency({
        worksheet,
        totalRows: summaryResults.length,
        headers,
        startRow: 1,
      });

      // Set column widths based on content
      setColumnWidths(worksheet, summaryResults);

      XLSX.utils.book_append_sheet(workbook, worksheet, 'Summary');
    }

    for (const report of groupData.saved_reports) {
      const processedData = await processReportData({
        report,
        exportOptions,
        groupData,
        table,
        roleForExport,
        req,
      });

      if (!processedData) continue;

      const { formatData, filteredTotalRowFormatted, commissionAmount } =
        processedData;

      const worksheet = createReportWorksheet({
        report,
        formatData,
        filteredTotalRowFormatted,
        commissionAmount,
        groupData,
      });

      const maxSheetNameLength = 31;
      const truncatedName = truncateName(
        report.name,
        groupData.template,
        maxSheetNameLength
      );

      // Check if the worksheet name already exists. Having the same name will cause an error
      let sheetName = truncatedName;
      let counter = 1;
      while (workbook.SheetNames.includes(sheetName)) {
        const counterString = `(${counter})`;
        const maxLengthTruncatedName =
          maxSheetNameLength - counterString.length - 1;
        sheetName = `${truncatedName.substring(0, maxLengthTruncatedName)} ${counterString}`;
        counter++;
      }

      XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    }

    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'buffer',
    });
    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    );
    res.send(Buffer.from(wbout));
  } catch (error) {
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.error(`Error: ${error.message}`);
    Sentry.captureException(error);
    res.status(500).json({ error: error.message });
  }
};
