import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  AGENT_COMMISSION_THIS_PERIOD_COLUMN,
  TOTAL_COMMISSION_COLUMN,
} from 'common/constants/excel-export';

import { formatCellCurrency, formatPdfCurrency } from './format-currency';

vi.mock('@/constants/excel-export', () => ({
  CURRENCY_FORMAT_COLUMNS: [
    AGENT_COMMISSION_THIS_PERIOD_COLUMN,
    TOTAL_COMMISSION_COLUMN,
  ],
  EXCEL_COLUMN_NAMES: ['A', 'B', 'C', 'D'],
}));

const mockFormatCurrency = vi.hoisted(() => {
  return { formatCurrency: vi.fn((v) => `$${Number(v).toFixed(2)}`) };
});

vi.mock('common/helpers/formatCurrency', () => ({
  formatCurrency: mockFormatCurrency.formatCurrency,
}));

describe('formatCellCurrency', () => {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  let worksheet: Record<string, any>;
  let headers: string[];

  beforeEach(() => {
    worksheet = {
      A1: { v: 100 },
      A2: { v: '$200.123' },
      B1: { v: 300 },
      B2: { v: '40$0' },
      C1: { v: 500 },
      D1: { v: 600 },
    };
    headers = [
      AGENT_COMMISSION_THIS_PERIOD_COLUMN,
      TOTAL_COMMISSION_COLUMN,
      'Other',
      'Extra',
    ];
  });

  it('Given currency columns, should apply currency format to specified columns', () => {
    formatCellCurrency({
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      worksheet: worksheet as any,
      totalRows: 1,
      startRow: 1,
      headers,
    });

    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['A1'].z).toBe('$#,##0.00');
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['A2'].v).toBe(200.123);
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['A2'].z).toBe('$#,##0.00');
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['B1'].z).toBe('$#,##0.00');
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['B2'].z).toBe('$#,##0.00');
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['B2'].v).toBe(400);
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['C1'].z).toBeUndefined();
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['D1'].z).toBeUndefined();
  });

  it('Given missing currency columns in headers, should not throw', () => {
    headers = ['Other', 'Extra'];
    expect(() =>
      formatCellCurrency({
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        worksheet: worksheet as any,
        totalRows: 1,
        startRow: 1,
        headers,
      })
    ).not.toThrow();
  });

  it('Given empty worksheet, should handle gracefully', () => {
    worksheet = {};
    formatCellCurrency({
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      worksheet: worksheet as any,
      totalRows: 2,
      startRow: 1,
      headers,
    });
    expect(Object.keys(worksheet).length).toBe(0);
  });

  it('Given no startRow, should use default startRow', () => {
    formatCellCurrency({
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      worksheet: worksheet as any,
      totalRows: 1,
      headers,
    });
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['A1'].z).toBe('$#,##0.00');
    // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    expect(worksheet['B1'].z).toBe('$#,##0.00');
  });
});

describe('formatPdfCurrency', () => {
  beforeEach(() => {
    mockFormatCurrency.formatCurrency.mockClear();
  });

  it('Given currency columns, should format currency columns using formatCurrency', () => {
    const data = [
      {
        [AGENT_COMMISSION_THIS_PERIOD_COLUMN]: 1234.5,
        [TOTAL_COMMISSION_COLUMN]: 200,
        Other: 10,
      },
      {
        [AGENT_COMMISSION_THIS_PERIOD_COLUMN]: 0,
        [TOTAL_COMMISSION_COLUMN]: 999.99,
        Other: 20,
      },
    ];
    const result = formatPdfCurrency(data);

    expect(result[0][AGENT_COMMISSION_THIS_PERIOD_COLUMN]).toBe('$1234.50');
    expect(result[0][TOTAL_COMMISSION_COLUMN]).toBe('$200.00');
    expect(result[0].Other).toBe(10);
    expect(result[1][AGENT_COMMISSION_THIS_PERIOD_COLUMN]).toBe('$0.00');
    expect(result[1][TOTAL_COMMISSION_COLUMN]).toBe('$999.99');
    expect(result[1].Other).toBe(20);
    expect(mockFormatCurrency.formatCurrency).toHaveBeenCalledTimes(4);
  });

  it('Given non-currency columns, should not format non-currency columns', () => {
    const data = [{ Other: 123, Extra: 456 }];
    const result = formatPdfCurrency(data);
    expect(result[0].Other).toBe(123);
    expect(result[0].Extra).toBe(456);
    expect(mockFormatCurrency.formatCurrency).not.toHaveBeenCalled();
  });

  it('Given non-numeric values, should skip formatting if value is not a number', () => {
    const data = [
      {
        [AGENT_COMMISSION_THIS_PERIOD_COLUMN]: 'abc',
        [TOTAL_COMMISSION_COLUMN]: null,
        Other: 10,
      },
      {
        [AGENT_COMMISSION_THIS_PERIOD_COLUMN]: undefined,
        [TOTAL_COMMISSION_COLUMN]: NaN,
        Other: 20,
      },
    ];
    const result = formatPdfCurrency(data);
    expect(result[0][AGENT_COMMISSION_THIS_PERIOD_COLUMN]).toBe('abc');
    expect(result[0][TOTAL_COMMISSION_COLUMN]).toBe('$0.00');
    expect(result[1][AGENT_COMMISSION_THIS_PERIOD_COLUMN]).toBeUndefined();
    expect(Number.isNaN(result[1][TOTAL_COMMISSION_COLUMN])).toBe(true);
    expect(mockFormatCurrency.formatCurrency).toHaveBeenCalled();
  });

  it('Given input data, should return a new array and not mutate original data', () => {
    const data = [{ Amount: 100, Total: 200 }];
    const result = formatPdfCurrency(data);
    expect(result).not.toBe(data);
    expect(result[0]).not.toBe(data[0]);
  });

  it('Given empty data array, should handle empty data array', () => {
    const result = formatPdfCurrency([]);
    expect(result).toEqual([]);
    expect(mockFormatCurrency.formatCurrency).not.toHaveBeenCalled();
  });
});
