import archiver from 'archiver';

import { savedReportsGroupsTemplates } from '@/types';
import type { ExportParams } from './types';

export const truncateName = (
  reportName: string,
  template: string,
  maxLength: number = 30
) => {
  let truncatedName = '';

  if (template === savedReportsGroupsTemplates.COMMISSION_PAYOUT) {
    const regexFilter =
      /.*\d{1,2}\/\d{1,2}\/\d{4} - \d{1,2}\/\d{1,2}\/\d{4} - /g;
    const fallbackRegex = /.* - (.+)/;
    const specialChars = /[\\/:*?"<>|]/g;

    if (regexFilter.test(reportName)) {
      truncatedName = reportName.replace(regexFilter, '');
    } else if (fallbackRegex.test(reportName)) {
      truncatedName = reportName.replace(fallbackRegex, '$1');
    } else {
      truncatedName = reportName;
    }

    truncatedName = truncatedName
      .substring(0, maxLength)
      .replace(specialChars, '');
  } else {
    const specialChars = /[\\/:*?"<>|]/g;
    const datePattern =
      /\b(\d{4}[/-]\d{1,2}[/-]\d{1,2})|(\d{1,2}[/-]\d{1,2}[/-]\d{4})\b/g;
    const spaceAndHyphen = /[ -]/g;
    truncatedName = reportName
      .replace(datePattern, '')
      .substring(0, maxLength)
      .replace(specialChars, '')
      .replace(spaceAndHyphen, '');
  }
  return truncatedName;
};

export const isReportInSelectedData = (report, selected_data) => {
  if (selected_data.length > 0) {
    const reportExists = selected_data.some((data) => data.id === report.id);
    if (!reportExists) {
      return false;
    }
  }
  return true;
};

export const createZipStream = ({ res }: Pick<ExportParams, 'res'>) => {
  // If individual reports are selected, we will create a zip file with each report as a separate file
  res.setHeader('Content-Type', 'application/zip');
  const archive = archiver('zip', {
    zlib: { level: 1 }, // Sets the compression level. 1 for fastest.
  });
  archive.pipe(res);
  return archive;
};
