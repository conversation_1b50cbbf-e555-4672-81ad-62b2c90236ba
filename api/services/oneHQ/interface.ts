export enum PolicyQueryFilterFields {
  ADVISOR_SOURCE_ID = 'advisorSourceId',
  ANNUAL_PREMIUM = 'annualPremium',
  ANNUALIZED_PREMIUM = 'annualizedPremium',
  APPROVED_DATE = 'approvedDate',
  BENEFIT_AMOUNT = 'benefitAmount',
  BENEFIT_BOOKLET_DATE = 'benefitBookletDate',
  BENEFIT_MODE_ID = 'benefitModeId',
  BENEFIT_PERIOD = 'benefitPeriod',
  BENEFIT_STATEMENT_DATE = 'benefitStatementDate',
  CARRIER_ID = 'carrierId',
  CLIENT_SOURCE_ID = 'clientSourceId',
  CLOSED_DATE = 'closedDate',
  COMMISSIONABLE_PREMIUM = 'commissionablePremium',
  CONVERSION_DATE = 'conversionDate',
  COUNTY_ID = 'countyId',
  CREATED_AT = 'createdAt',
  CREATED_BY_ID = 'createdById',
  DEDUCTIONS = 'deductions',
  DELIVERY_DATE = 'deliveryDate',
  DELIVERY_DUE_DATE = 'deliveryDueDate',
  DELIVERY_NOTE = 'deliveryNote',
  DELIVERY_ORDERED_DATE = 'deliveryOrderedDate',
  DELIVERY_TYPE_ID = 'deliveryTypeId',
  DISCOUNT_ID = 'discountId',
  EFFECTIVE_DATE = 'effectiveDate',
  ELIMINATION_PERIOD = 'eliminationPeriod',
  ENROLLMENT_TYPE_ID = 'enrollmentTypeId',
  EXCLUSIONS = 'exclusions',
  EXPRESS_STEP = 'expressStep',
  FACE_AMOUNT = 'faceAmount',
  FIRM_ID = 'firmId',
  FORMER_POLICY_NUMBER = 'formerPolicyNumber',
  FUTURE_PURCHASE_OPTION_DATE = 'futurePurchaseOptionDate',
  GROUP_ID = 'groupId',
  GROUP_NAME_DN = 'groupNameDN',
  GROUP_NUMBER = 'groupNumber',
  ID = 'id',
  IMPORT_KEY = 'importKey',
  IS_EXCEPTION = 'isException',
  IS_FEDERAL_EMPLOYEES = 'isFederalEmployees',
  IS_POLICY_FEE_COMMISSIONABLE = 'isPolicyFeeCommissionable',
  IS_REPLACEMENT = 'isReplacement',
  IS_SAVE_AGE = 'isSaveAge',
  ISSUED_DATE = 'issuedDate',
  ISSUED_STATE_ID = 'issuedStateId',
  LAST_EFFECTIVE_DATE = 'lastEffectiveDate',
  LAST_SIGNED_DATE = 'lastSignedDate',
  LAST_SUBMITTED_DATE = 'lastSubmittedDate',
  LAST_TERMINATION_DATE = 'lastTerminationDate',
  LAST_VIEWED_AT = 'lastViewedAt',
  MODAL_FACTOR_PERCENT = 'modalFactorPercent',
  MODAL_PREMIUM = 'modalPremium',
  NAME = 'name',
  NOTE = 'note',
  OPPORTUNITY_ID = 'opportunityId',
  ORDER_DETAIL_ID = 'orderDetailId',
  ORGANIZATION_ID = 'organizationId',
  ORIGINAL_EFFECTIVE_DATE = 'originalEffectiveDate',
  ORIGINAL_SIGNED_DATE = 'originalSignedDate',
  ORIGINAL_SUBMITTED_DATE = 'originalSubmittedDate',
  ORIGINAL_TERMINATION_DATE = 'originalTerminationDate',
  PACKAGE_ID = 'packageId',
  PAID_DATE = 'paidDate',
  PAYMENT_MODE_ID = 'paymentModeId',
  PAYMENT_OPTION_ID = 'paymentOptionId',
  PLACED_DATE = 'placedDate',
  POLICY_FEE = 'policyFee',
  POLICY_NUMBER = 'policyNumber',
  POLICY_SOURCE_ID = 'policySourceId',
  POLICY_STATUS_ID = 'policyStatusId',
  POLICY_SUBMISSION_TYPE_ID = 'policySubmissionTypeId',
  PREMIUM_NOTE = 'premiumNote',
  PRIMARY_CLIENT_NAME_DN = 'primaryClientNameDn',
  PROCESSED_DATE = 'processedDate',
  PRODUCT_DURATION_ID = 'productDurationId',
  PRODUCT_ID = 'productId',
  PRODUCT_NAME_DN = 'productNameDN',
  PRODUCT_TYPE_ID = 'productTypeId',
  PRODUCT_UNDERWRITING_TYPE_ID = 'productUnderwritingTypeId',
  RE_OPENED_DATE = 'reOpenedDate',
  RECEIVED_DATE = 'receivedDate',
  RENEWAL_DATE = 'renewalDate',
  REPLACEMENT_AMOUNT = 'replacementAmount',
  REPLACEMENT_CARRIER_ID = 'replacementCarrierId',
  REPLACEMENT_PRODUCT_ID = 'replacementProductId',
  REPLACEMENT_REASON = 'replacementReason',
  RESIDENT_STATE_ID = 'residentStateId',
  REVIEW_DATE = 'reviewDate',
  SENT_DATE = 'sentDate',
  SHIPMENT_METHOD_ID = 'shipmentMethodId',
  SIGNED_DATE = 'signedDate',
  STATE_ID = 'stateId',
  SUBMITTED_DATE = 'submittedDate',
  TAX_STATUS_ID = 'taxStatusId',
  TERMINATION_DATE = 'terminationDate',
  TRAIL_OPTION_ID = 'trailOptionId',
  TYPE = 'type',
  UPDATED_AT = 'updatedAt',
  UPDATED_BY_ID = 'updatedById',
  WAITING_PERIOD_DAYS = 'waitingPeriodDays',
  WAITING_PERIOD_TYPE_ID = 'waitingPeriodTypeId',
  ZIPCODE = 'zipcode',
}

export enum FilterOperation {
  EQUAL = 'EQUAL',
  NOT_EQUAL = 'NOT_EQUAL',
  LIKE = 'LIKE',
  NOT_LIKE = 'NOT_LIKE',
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  WITH = 'WITH',
}

export interface PolicyQueryFilterInput {
  field: PolicyQueryFilterFields;
  operation: FilterOperation;
  value: string;
}
export enum PaymentMode {
  MODE_10_PAY = 'Mode10Pay',
  MODE_SEMI_MONTHLY = 'ModeSemiMonthly',
  MODE_DAILY = 'ModeDaily',
  MODE_MONTHLY = 'ModeMonthly',
  MODE_QUARTERLY = 'ModeQuarterly',
  MODE_SEMI_ANNUAL = 'ModeSemiAnnual',
  MODE_ANNUAL = 'ModeAnnual',
  MODE_SINGLE = 'ModeSingle',
  MODE_BI_WEEKLY = 'ModeBiWeekly',
  MODE_WEEKLY = 'ModeWeekly',
  MODE_5_PAY = 'Mode5Pay',
  MODE_20_PAY = 'Mode20Pay',
}

export interface Policy {
  id: string;
  name: string;
  policyNumber: string;
  policyStatus: string;
  createdAt: string;
  effectiveDate: string;
  paymentMode: PaymentMode;
  primaryClientNameDn: string;
  sentDate: string | null;
  commissionablePremium: number;
  writingAdvisors: WritingAdvisor[];
  product: Product;
  productTypeId: number;
  productType: ProductType;
  carrier: Carrier;
  firm: Firm;
  state: State;
  createdBy: CreatedBy;
  policyHolders: PolicyHodler[];
  salesManagers: SalesManager[];
}

interface SalesManager {
  id: string;
  startDate: string;
  endDate: string;
  user: User;
}

interface User {
  id: string;
  username: string;
  name: string;
  startDate: string;
  endDate: string;
  addresses: Address[];
  note: string;
  demographic: Demographic;
  phones: Phone[];
  emails: Email[];
  contracts: Contract[];
}

interface PolicyHodler {
  issueAge: number;
}

interface WritingAdvisor {
  id: string;
  commissionPercent: number;
  startDate: string;
  endDate: string | null;
  advisor: Advisor;
}

export interface Advisor {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  addresses: Address[];
  advisorStatus: string;
  birthDate: Date;
  note: string;
  demographic: Demographic;
  phones: Phone[];
  emails: Email[];
  affiliations: Affiliation[];
  primaryAddressCityDn: string;
  primaryAddressStateDn: string;
  primaryEmailDn: string;
  primaryFirmNameDn: string;
  primaryPhoneNumberDn: string;
  contracts: Contract[];
}

interface Affiliation {
  id: string;
  firm: Firm;
  isPrimary: boolean;
}

interface Contract {
  advisorId: string;
  carrierId: string;
  levels: Level[];
}

export interface ContractLevel {
  id: string;
  contract: Contract;
}

interface Level {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
}

interface Phone {
  displayPhone: string;
  description: string;
  number: string;
}
interface Email {
  id: string;
  address: string;
}
interface Address {
  id: string;
  city: string;
  county: County;
  state: State;
  zipcode: string;
}

interface County {
  id: string;
  name: string;
}

interface State {
  id: string;
  country: Country;
  name: string;
}

interface Country {
  id: string;
  name: string;
}
interface Demographic {
  advisor: Advisor;
  advisorId: string;
  birthDate: Date;
  codeName: string;
  contactId: string;
  deathDate: Date;
  firstName: string;
  gender: string;
  genderId: string;
  id: string;
  importKey: string;
  informalName: string;
  lastName: string;
  maritalStatusId: string;
  middleName: string;
  nickname: string;
  organizationId: number;
  prefixId: string;
  retirementDate: Date;
  ssn: string;
  suffixId: string;
  title: string;
}

export interface Product {
  id: string;
  name: string;
  productType: ProductType;
  productTypeId: number;
  carrierId: string;
  carrierNameDn: string;
  productCategory: ProductCategory;
}
interface ProductCategory {
  id: string;
  name: string;
}

interface ProductType {
  id: string;
  name: string;
}

export interface Carrier {
  id: string;
  name: string;
  codeName: string;
  naicCode: string;
  products: Product[];
}

interface Firm {
  id: string;
  name: string;
}

interface CreatedBy {
  id: string;
  name: string;
}

export interface ReportDetail {
  id: string;
  exportOnPublish: boolean;
  expressStep: string;
  fileContentType: string;
  fileFileName: string;
  fileFileSize: number;
  firmId: string;
  reportId: string;
  report: Report;
  reportCategoryId: string;
  results: ReportResults;
}

interface ReportResults {
  data: PolicyReportData[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  summary: any;
}
interface Report {
  id: string;
  name: string;
}

export interface PolicyReportData {
  policy_number: string;
  carrier_id: string;
  carrier_name: string;
  advisor_s_affiliation: string;
  customer_name: string;
  effective_date: string;
  status: string;
  product_type: string;
  product_id: string;
  product_name: string;
  issue_age: number | null;
  state: string;
  payment_mode_optional: string | null;
  submitted_target_premium: string | null;
  agent_name: string;
  split_percentage: string;
  onehq_id: string;
  first_agent_id: string;
  first_agent_commission_percent: string;
  second_agent_id: string | null;
  second_agent_commission_percent: string | null;
  third_agent_id: string | null;
  third_agent_commission_percent: string | null;
  type_of_business: string;
  product_durations: string | null;
  product_durations_start_date: string | null;
  product_durations_end_date: string | null;
  first_sales_representative: string;
  sales_managers_active_first_id: string;
  second_sales_representative: string | null;
  sales_managers_active_second_id?: string;
  total_count: number;
  primary_advisor_first_affiliation?: string;
  primary_advisor_second_affiliation?: string;
}

export enum SortOrder {
  ASC = 'ASC',
  DESC = 'DESC',
}

export enum SortBy {
  CREATED_AT = 'CreatedAt',
  UPDATED_AT = 'UpdatedAt',
}
