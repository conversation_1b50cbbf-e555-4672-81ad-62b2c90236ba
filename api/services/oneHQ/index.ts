import axios, { type AxiosInstance } from 'axios';
import { inject, injectable } from 'inversify';
import pRetry from 'p-retry';

import { Config } from '@/lib/decorators';
import { ConfigService } from '@/services/config';
import {
  type Advisor,
  type Carrier,
  type ContractLevel,
  type Policy,
  type PolicyQueryFilterInput,
  type ReportDetail,
  SortBy,
  SortOrder,
} from '@/services/oneHQ/interface';

const defaultParams = {
  sortBy: SortBy.CREATED_AT,
  sortOrder: SortOrder.DESC,
  limit: 200,
};
@injectable()
export class OneHQService {
  @Config('ONE_HQ_ENDPOINT')
  endpoint: string;

  apiKey: string;

  _client: AxiosInstance;

  @inject(ConfigService) configService: ConfigService;

  loadConfig(config: { apiKey: string }) {
    this.apiKey = config?.apiKey;
  }

  get client(): AxiosInstance {
    if (this._client) {
      return this._client;
    }
    this._client = axios.create({
      baseURL: this.endpoint,
      headers: {
        authorization: `Bearer ${this.apiKey}`,
      },
    });
    return this._client;
  }

  async request<T>(params: {
    query: string;
    variables?: Record<string, unknown>;
  }) {
    return await pRetry(
      async () => {
        const { data } = await this.client.post<T>('/', {
          variables: {},
          ...params,
        });
        return data;
      },
      {
        retries: 3,
        minTimeout: 100,
      }
    );
  }

  async getCarriersWithProducts(
    params: {
      after?: string;
      limit?: number;
      sortBy?: SortBy;
      sortOrder?: SortOrder;
    } = {}
  ) {
    const query = `query GetCarriersWithProducts($after: String, $limit: Int, $sortBy: SortBy, $sortOrder: SortOrder) {
      carriers(after: $after, limit: $limit, sortBy: $sortBy, sortOrder: $sortOrder) {
       totalCount
        pageInfo {
          endCursor
          hasNextPage
        }
        nodes {
          id
          name
          codeName
          naicCode
          startDate
          products {
            id
            name
            productTypeId
            carrierId
          }
        }
      }
    }`;
    const { data } = await this.request<{
      data: {
        carriers: {
          nodes: Carrier[];
          totalCount: number;
          pageInfo: { endCursor: string; hasNextPage: boolean };
        };
      };
    }>({
      query,
      variables: params ? { ...defaultParams, ...params } : defaultParams,
    });
    return data.carriers;
  }

  async getLevels(_params?: {
    after?: string;
    limit?: number;
    sortBy?: SortBy;
    sortOrder?: SortOrder;
  }) {
    const query = `query GetLevels($after: String, $limit: Int, $sortBy: SortBy, $sortOrder: SortOrder) {
      levels(after: $after, limit: $limit, sortBy: $sortBy, sortOrder: $sortOrder) {
        nodes {
          id
          name
          levelType
          startDate
          carrier {
            id
            name
          }
        }
      }
    }`;
    return await this.request({ query });
  }

  async getContractLevels(params?: {
    after?: string;
    limit?: number;
    sortBy?: SortBy;
    sortOrder?: SortOrder;
  }) {
    const query = `query GetContractLevels($after: String, $limit: Int, $sortBy: SortBy, $sortOrder: SortOrder) {
      contractLevels(after: $after, limit: $limit, sortBy: $sortBy, sortOrder: $sortOrder) {
        totalCount
        pageInfo {
          endCursor
          hasNextPage
        }
        nodes {
          id
          name
          startDate
          endDate
          contract {
            advisorId
            carrierId
            levels {
              startDate
              endDate
              name
            }
          }
        }
      }
    }`;
    const { data } = await this.request<{
      data: {
        contractLevels: {
          nodes: ContractLevel[];
          totalCount: number;
          pageInfo: { endCursor: string; hasNextPage: boolean };
        };
      };
    }>({
      query,
      variables: params ? { ...defaultParams, ...params } : defaultParams,
    });
    return data.contractLevels;
  }
  async getAdvisors(params?: {
    after?: string;
    limit?: number;
    sortBy?: SortBy;
    sortOrder?: SortOrder;
  }) {
    const query = `
      query GetAdvisors($filters: [AdvisorQueryFilterInput!], $after: String, $limit: Int, $sortBy: SortBy, $sortOrder: SortOrder) {
        advisors(filters: $filters, after: $after, limit: $limit, sortBy: $sortBy, sortOrder: $sortOrder) {
         totalCount
            pageInfo {
                endCursor
                hasNextPage
          }
          nodes {
            id
            name
            startDate
            advisorStatus
            note
            primaryAddressCityDn
            primaryAddressStateDn
            primaryEmailDn
            primaryFirmNameDn
            primaryPhoneNumberDn
            demographic {
              id
              firstName
              lastName
              birthDate
              gender
              middleName
              ssn
            }
            contracts {
             advisorId
            }
            addresses {
              id
              city
              state {
                name
                country {
                  name
                }
              }
              zipcode
            }
            phones {
             displayPhone
             description
             number
            }
            emails {
             address
            }
          }
        }
      }
    `;

    const { data } = await this.request<{
      data: {
        advisors: {
          nodes: Advisor[];
          totalCount: number;
          pageInfo: { endCursor: string; hasNextPage: boolean };
        };
      };
    }>({
      query,
      variables: params ? { ...defaultParams, ...params } : defaultParams,
    });
    return data?.advisors;
  }

  async getReport(params: { id: string; page?: number }) {
    const query = `
    query GetReport($id: ID!, $filters: [ReportFilter!], $page: Int) {
      report(id: $id, filters: $filters, page: $page) {
        id
        exportOnPublish
        expressStep
        fileContentType
        fileFileName
        fileFileSize
        firmId
        reportId
        report {
          id
          name
        }
        reportCategoryId
        results {
          data
          summary
        }
      }
    }
    `;

    const { data } = await this.request<{
      data: {
        report: ReportDetail;
      };
    }>({ query, variables: params });
    return data.report;
  }
  async getPolicies(params: {
    filter?: PolicyQueryFilterInput[];
    after?: string;
    limit?: number;
    sortBy?: SortBy;
    sortOrder?: SortOrder;
  }) {
    const query = `query GetPolicies($filters: [PolicyQueryFilterInput!], $after: String, $limit: Int, $sortBy: SortBy, $sortOrder: SortOrder) {
      policies(filters: $filters, after: $after, limit: $limit, sortBy: $sortBy, sortOrder: $sortOrder) {
       totalCount
        pageInfo {
          endCursor
          hasNextPage
        }
        nodes {
          id
          name
          policyNumber
          policyStatus
          createdAt
          sentDate
          effectiveDate
          commissionablePremium
          primaryClientNameDn
          policyHolders {
            issueAge
          }
          state  {
            name
          }
          salesManagers {
            id 
            startDate
            endDate
            user {
              id
              name
              username
              note
              demographic {
                id
                firstName
                lastName
                birthDate
                gender
                middleName
                ssn
              }
              addresses {
                id
                city
                state {
                  name
                  country {
                    name
                  }
                }
                zipcode
              }
              phones {
                displayPhone
                description
                number
              }
              emails {
                address
              }
            }
          }
          writingAdvisors {
            id
            commissionPercent
            startDate
            endDate
            advisor {
              id
              name
              affiliations {
                firm {
                  name
                }
                isPrimary
              }
            }
          }
          paymentMode
          product {
            id
            name
            productTypeId
            productCategory {
              name
            }
          }
          productType {
            id
            name
          }
          carrier {
            id
            name
            naicCode
            codeName
          }
          firm {
            id
            name
          }
          createdBy {
            id
            name
          }
        }
      }
    }`;
    const { data } = await this.request<{
      data: {
        policies: {
          nodes: Policy[];
          pageInfo: { endCursor: string; hasNextPage: boolean };
          totalCount: number;
        };
      };
    }>({
      query,
      variables: params ? { ...defaultParams, ...params } : defaultParams,
    });
    return data.policies;
  }
}
