import type {
  ReportDataBulkEditDtoType,
  ReportDataCreateDtoType,
} from 'common/dto/report_data/dto';
import { getReportFieldConfig } from 'common/field-config/report';
import { inject, injectable } from 'inversify';
import { nanoid } from 'nanoid';
import * as Sentry from '@sentry/nextjs';
import { dayjs } from 'common/helpers/datetime';
import currency from 'currency.js';
import {
  history_state,
  history_status,
  history_type,
  PrismaClient,
} from '@prisma/client';
import { TransactionType } from 'common/globalTypes';

import type { ReportDataModel } from './types';
import { HistoryService } from '../history';
import type { ExtAccountInfo } from '@/types';
import { limitConcurrency } from '@/lib/helpers';
import { container } from '@/ioc';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import {
  getTransactionsDetailsUpdates,
  type TransactionsDetailsUpdates,
} from '@/services/report_data/helpers/transaction-details.helper';
import { AppLoggerService } from '../logger/appLogger';
import { Decimal } from '@prisma/client/runtime/library';

@injectable()
export class ReportDataService {
  private logger: AppLoggerService = new AppLoggerService();

  constructor(
    @inject(HistoryService) private historyService: HistoryService,
    @inject(PrismaClient) private prisma: PrismaClient
  ) {}

  private async getMapStrIdsToIds(strIds: string[]) {
    const data = await this.prisma.report_data.findMany({
      where: {
        str_id: { in: strIds },
      },
      select: { id: true, str_id: true },
    });
    const map = new Map<string, number>();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((item) => {
      map.set(item.str_id, item.id);
    });
    return map;
  }

  async bulkUpdateFromCsv({
    data,
    uid,
    account_id,
  }: {
    data: ReportDataBulkEditDtoType;
    uid: string;
    account_id: string;
  }) {
    const failedPolicyIds: string[] = [];
    const nonExistDocumentStrIds = await this.checkNonExistDocumentStrIds([
      ...new Set(data.map((row) => row.document_id)),
    ]);

    if (nonExistDocumentStrIds.length > 0) {
      throw new Error(
        `Some document ids do not exist: ${nonExistDocumentStrIds.join(', ')}`
      );
    }

    const strIds = new Set<string>();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((row) => {
      const regexId = /^\d+$/;
      if (row.id && !regexId.test(String(row.id))) {
        strIds.add(String(row.id));
      }
    });

    if (strIds.size) {
      const mapStrIdsToIds = await this.getMapStrIdsToIds(Array.from(strIds));
      if (strIds.size !== mapStrIdsToIds.size) {
        throw new Error(`Some report ids do not exist`);
      }
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      data.forEach((row) => {
        if (mapStrIdsToIds.has(String(row.id))) {
          row.id = mapStrIdsToIds.get(String(row.id));
        }
      });
    }

    const result = await limitConcurrency(
      async (row: ReportDataModel) =>
        this.updateReportData({
          body: row,
          account_id: account_id,
          uid: uid,
        }),
      data,
      20,
      {
        onFail: ({ data }) => {
          failedPolicyIds.push(data.policy_id);
        },
      }
    );

    if (failedPolicyIds.length > 0) {
      throw new Error(
        `Failed to update some report data for policies: ${failedPolicyIds.join(', ')}`
      );
    }

    return { total: result.length };
  }

  private async checkNonExistDocumentStrIds(strIds: string[]) {
    strIds = strIds.filter(Boolean);
    const existDocuments = await this.prisma.documents.findMany({
      where: {
        str_id: { in: strIds },
      },
      select: { str_id: true },
    });
    // Filter out non existing document IDs
    const nonExistDocumentStrIds = strIds.filter(
      (strId) => !existDocuments.some((doc) => doc.str_id === strId)
    );
    return nonExistDocumentStrIds;
  }

  async bulkAddFromCsv({
    data,
    account_id,
    uid,
  }: {
    data: ReportDataCreateDtoType[];
    uid: string;
    account_id: string;
  }) {
    const failedPolicyIds: string[] = [];
    const nonExistDocumentStrIds = await this.checkNonExistDocumentStrIds([
      ...new Set(data.map((row) => row.document_id)),
    ]);

    if (nonExistDocumentStrIds.length > 0) {
      throw new Error(
        `Some document ids do not exist: ${nonExistDocumentStrIds.join(', ')}`
      );
    }

    const result = await limitConcurrency(
      async (row: ReportDataModel) =>
        this.addReportData({
          body: row,
          account_id: account_id,
          uid: uid,
        }),
      data,
      20,
      {
        onFail: ({ data }) => {
          failedPolicyIds.push(data.policy_id);
        },
      }
    );

    if (failedPolicyIds.length > 0) {
      throw new Error(
        `Failed to add some report data for policies: ${failedPolicyIds.join(', ')}`
      );
    }

    return { total: result.length };
  }

  getTableFields = (post: Partial<ReportDataModel>) => {
    const config = getReportFieldConfig({});
    const fields: (keyof ReportDataModel)[] = Object.keys(config.fields)
      .filter((key) => config.fields[key].allowedInBody)
      .map((key) => key as keyof ReportDataModel);
    return fields.reduce((acc, key) => {
      acc[key] = this.parseFieldValue(key, post[key]);
      return acc;
    }, {});
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  parseFieldValue = <K extends keyof ReportDataModel>(key: K, value: any) => {
    // TODO: Make this more structured by data types.
    if (key.endsWith('_date')) {
      if (value) {
        return dayjs(value);
      }
    } else if (key.endsWith('_amount') || key === 'commissions_expected') {
      return value === null || value === undefined || value === ''
        ? value
        : currency(value).value;
    } else {
      return value;
    }

    return undefined;
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  asyncReportHistoryData = (newRows: any[]) => {
    const historyData = newRows.map((item) => ({
      json_data: JSON.stringify(item),
      uid: item.uid,
      table_type: history_type.report,
      state: history_state.new,
      status: history_status.active,
      statement_data_id: null, // ??
      report_data_id: item.id,
      account_id: item.account_id,
    }));
    this.historyService.postHistoryData(historyData).catch((err) => {
      Sentry.captureException(err);
    });
  };

  async addReportData({
    body,
    account_id,
    uid,
  }: {
    body: ReportDataModel | ReportDataModel[];
    account_id: string;
    uid: string;
  }) {
    const list = Array.isArray(body) ? body : [body];
    try {
      const importId = nanoid();
      const newData = list.map((item) => {
        const post = this.getTableFields(item);
        return {
          ...post,
          account_id,
          uid,
          str_id: nanoid(),
          import_id: importId,
          document_id: item.document_id ?? null,
        };
      });
      const data = await this.prisma.report_data.createMany({
        data: newData,
      });
      // CreateMany doesn't return the created data, so using import_id to re-query it
      // https://github.com/prisma/prisma/issues/8131#issuecomment-**********
      const newCreated = await this.prisma.report_data.findMany({
        where: {
          import_id: importId,
        },
      });

      this.asyncReportHistoryData(newCreated);
      if (
        newData.length !== newCreated.length ||
        newData.length !== data.count
      ) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(
          `Warning: Potential issue in creating new data. Expected new: ${newData.length}, Created new: ${data.count}, Queried new: ${newCreated.length}`
        );
      }
      return { total: newData.length };
    } catch (error) {
      Sentry.captureException(error);
      throw { error: error.message || error.toString() };
    }
  }

  async updateReportData({
    body,
    account_id,
    uid,
  }: {
    body: ReportDataModel;
    account_id: string;
    uid: string;
  }) {
    const post: Partial<ReportDataModel> = this.getTableFields(body);
    try {
      // TODO: Do we want to align splits with agents on policy or can they diverge?
      // Allow divergence for now since contacts_split may be synced, and we use
      // contacts_commission_split for override
      // If agent is not saved with the policy, remove them from split config.
      // (post as any).contacts_split = Object.fromEntries(
      //   Object.entries((post as any).contacts_split ?? {}).filter(
      //     ([agentId, v]) => ((post as any).contacts ?? []).includes(agentId)
      //   )
      // );
      // (post as any).contacts_commission_split = Object.fromEntries(
      //   Object.entries((post as any).contacts_commission_split ?? {}).filter(
      //     ([agentId, v]) => ((post as any).contacts ?? []).includes(agentId)
      //   )
      // );
      const split_percentage =
        Number.isNaN(
          +((body.split_percentage as unknown as string) ?? '').replace('%', '')
        ) || (body.split_percentage as unknown as string) === ''
          ? null
          : +((body.split_percentage as unknown as string) ?? '').replace(
              '%',
              ''
            );
      const syncFieldService = container.get(SyncFieldService);
      syncFieldService.canUpdateIfChanged({
        newData: post,
        tableName: 'report_data',
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        id: Number((post as any).id),
        account: { account_id } as ExtAccountInfo,
        config: body.config || {},
      });

      const transactionDetails = getTransactionsDetailsUpdates(body);
      const transactionsDetailsUpdates =
        await this.getTransactionDetailsOperations({
          transactionDetails,
          updatedBy: uid,
        });

      const {
        id: reportId,
        document_id: _documentId,
        customer_id: _customerId,
        commission_profile_id: _commissionProfileId,
        receivable_schedule: _receivableSchedule,
        ...updateData
      } = post;

      const [data] = await this.prisma.$transaction([
        this.prisma.report_data.update({
          where: { id: Number(reportId), account_id },
          data: {
            ...updateData,
            split_percentage,
            account_id,
            uid,
            updated_at: new Date(),
            updated_by: uid,
          },
        }),
        ...transactionsDetailsUpdates,
      ]);

      this.asyncReportHistoryData([data]);

      return { data };
    } catch (error) {
      Sentry.captureException(error);
      throw { error: error.message || error.toString() };
    }
  }

  async deleteReportData({
    ids,
    account_id,
  }: {
    ids: number[];
    account_id: string;
  }) {
    try {
      if (!Array.isArray(ids) || ids.length === 0)
        throw new Error('Missing ids');
      // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      if (ids.some((id) => isNaN(id) || !Number.isInteger(id)))
        throw new Error('Invalid ids');
      await this.prisma.report_data.updateMany({
        where: {
          AND: [{ id: { in: ids } }, { account_id: String(account_id) }],
        },
        data: {
          state: 'deleted',
        },
      });
      return true;
    } catch (error) {
      Sentry.captureException(error);
      throw { error: error.message };
    }
  }

  public getTransactionDetailsOperations = async (input: {
    transactionDetails: TransactionsDetailsUpdates;
    updatedBy: string;
  }) => {
    const operations = [];

    const { agencyPolicyUpdates } = input.transactionDetails;

    for (const detail of agencyPolicyUpdates.updated) {
      operations.push(
        this.prisma.accounting_transaction_details.update({
          where: { str_id: detail.transactionDetailStrId },
          data: {
            rate: new Decimal(detail.rate),
            updated_by: input.updatedBy,
          },
        })
      );
    }

    if (agencyPolicyUpdates.added.length > 0) {
      operations.push(
        this.prisma.accounting_transaction_details.createMany({
          data: agencyPolicyUpdates.added.map((detail) => ({
            rate: new Decimal(detail.rate),
            party: detail.party,
            type: TransactionType.RECEIVABLE,
            created_by: input.updatedBy,
          })),
        })
      );
    }

    if (agencyPolicyUpdates.deleted.length > 0) {
      operations.push(
        this.prisma.accounting_transaction_details.deleteMany({
          where: {
            str_id: {
              in: agencyPolicyUpdates.deleted.map(
                (item) => item.transactionDetailStrId
              ),
            },
          },
        })
      );
    }

    const { agentUpdates, reportId } = input.transactionDetails;

    const buildTransactionsData = async (
      transactions:
        | TransactionsDetailsUpdates['agentUpdates']['added']
        | TransactionsDetailsUpdates['agentUpdates']['updated']
        | TransactionsDetailsUpdates['agentUpdates']['deleted']
    ) => {
      const contacts = await this.prisma.contacts.findMany({
        where: { str_id: { in: transactions.map((t) => t.contactStrId) } },
        select: { id: true, str_id: true },
      });

      return transactions
        .map((update) => {
          const contact = contacts.find(
            (c) => c.str_id === update.contactStrId
          );

          if (!contact) {
            this.logger.debug(
              `No contact found with str_id: ${update.contactStrId} for report_id: ${reportId}`
            );
          }

          return {
            contact_id: contact?.id,
            party: update.party,
            report_id: reportId,
            updated_by: input.updatedBy,
            rate: update.rate && new Decimal(update.rate),
            type: TransactionType.RECEIVABLE,
          };
        })
        .filter(Boolean);
    };

    if (agentUpdates.added.length > 0) {
      const data = await buildTransactionsData(agentUpdates.added);

      operations.push(
        this.prisma.accounting_transaction_details.createMany({ data })
      );
    }

    if (agentUpdates.updated.length > 0) {
      const data = await buildTransactionsData(agentUpdates.updated);

      for (const update of data) {
        operations.push(
          this.prisma.accounting_transaction_details.updateMany({
            where: {
              contact_id: update.contact_id,
              report_id: update.report_id,
            },
            data: {
              rate: update.rate,
              updated_by: update.updated_by,
              party: update.party,
            },
          })
        );
      }
    }

    if (agentUpdates.deleted.length > 0) {
      const data = await buildTransactionsData(agentUpdates.deleted);

      for (const deleted of data) {
        operations.push(
          this.prisma.accounting_transaction_details.deleteMany({
            where: {
              contact_id: deleted.contact_id,
              report_id: deleted.report_id,
            },
          })
        );
      }
    }

    return operations;
  };
}
