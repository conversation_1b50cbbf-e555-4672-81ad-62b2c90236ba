import isNil from 'lodash-es/isNil';
import type { ReceivablesAgentIdToRateMap } from 'common/types/receivableRates';
import { TransactionParty } from 'common/globalTypes';

import type { AccountingTransactionDetails } from '@/pages/api/statement_data/types';
import type { ReportDataModelUpdates } from '@/services/report_data/types';
import { isValidString } from 'common/helpers';

const getAgentRates = (receivable: ReceivablesAgentIdToRateMap) => {
  return receivable
    ? Object.entries(receivable)
        .map(([key, value]) => {
          const nonAgentIdKeyInMap = ['transactionDetailStrId', 'total'];

          if (nonAgentIdKeyInMap.includes(key)) {
            return;
          }

          return { agentStrId: key, rate: value };
        })
        .filter(Boolean)
    : [];
};

type TransactionDetailsAgentUpdates = {
  added: Array<{
    reportId: number;
    agentStrId: string;
    rate: string | number;
    party: TransactionParty.AGENT | TransactionParty.AGENCY;
  }>;
  updated: Array<{
    agentStrId: string;
    transactionDetailsId: string;
    rate: string | number;
    party: TransactionParty.AGENT | TransactionParty.AGENCY;
  }>;
  deleted: Array<{ transactionDetailsStrId: string; contactStrId: string }>;
};

const getTransactionDetailsAgentsUpdates = (input: {
  reportId: number;
  receivable: ReceivablesAgentIdToRateMap;
  party: TransactionParty.AGENT | TransactionParty.AGENCY;
  accountingTransactionDetails: AccountingTransactionDetails[];
}): TransactionDetailsAgentUpdates => {
  const { reportId, receivable, accountingTransactionDetails } = input;

  const agentRates = getAgentRates(receivable);

  const added = [];
  const updated = [];
  const deleted = [];

  for (const agent of agentRates) {
    const foundTransactionLinkedToAgent = accountingTransactionDetails.find(
      (detail) =>
        detail.contact?.str_id === agent.agentStrId ||
        detail.logs?.contactStrId === agent.agentStrId
    );

    if (!foundTransactionLinkedToAgent) {
      added.push({
        reportId,
        agentStrId: agent.agentStrId,
        rate: agent.rate,
        party: input.party,
      });

      continue;
    }

    const hasUpdates =
      foundTransactionLinkedToAgent.rate?.toString() !== agent.rate.toString();

    if (hasUpdates) {
      updated.push({
        agentStrId: agent.agentStrId,
        transactionDetailsId: foundTransactionLinkedToAgent.str_id,
        rate: agent.rate,
        party: input.party,
      });
    }
  }

  const accountingTransactionDetailsSameParty =
    accountingTransactionDetails.filter(
      (detail) => detail.party === input.party
    );

  // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  accountingTransactionDetailsSameParty.forEach((detail) => {
    const agentId = detail.contact?.str_id || detail.logs?.contactStrId;
    const hasBeenDeleted =
      agentId && !agentRates.some((agent) => agent.agentStrId === agentId);

    if (hasBeenDeleted) {
      deleted.push({
        transactionDetailsStrId: detail.str_id,
        contactStrId: agentId,
      });
    }
  });

  return { added, updated, deleted };
};

const getAgencyPoliceRatesResult = (
  receivableValueAgencyRate: Partial<ReportDataModelUpdates>['receivable_value_agency_rate']
) => {
  const strId = receivableValueAgencyRate?.str_id;
  const valueRaw = receivableValueAgencyRate?.valueRaw;

  if (!isValidString(strId) || isNil(valueRaw)) {
    return undefined;
  }

  const rateValueParsed = Number(valueRaw);

  if (!Number.isFinite(rateValueParsed)) {
    return {
      transactionDetailStrId: strId,
      rate: 0,
    };
  }

  return {
    transactionDetailStrId: strId,
    rate: rateValueParsed,
  };
};

export type TransactionsDetailsUpdates = {
  reportId: number;

  agencyPolicyUpdates: {
    added: Array<{
      rate: string | number;
      party: TransactionParty.POLICY;
    }>;

    updated: Array<{
      transactionDetailStrId: string;
      rate: string | number;
    }>;

    deleted: Array<{ transactionDetailStrId: string }>;
  };

  agentUpdates: {
    added: Array<{
      contactStrId: string;
      rate: string | number;
      party: TransactionParty.AGENT | TransactionParty.AGENCY;
    }>;

    updated: Array<{
      contactStrId: string;
      party: TransactionParty.AGENT | TransactionParty.AGENCY;
      rate: string | number;
    }>;

    deleted: Array<{ contactStrId: string }>;
  };
};

const getTransactionsDetailsUpdates = (
  post: Partial<ReportDataModelUpdates>
): TransactionsDetailsUpdates => {
  const {
    receivable_value_agency_rate: receivableValueAgencyRate,
    receivable_value_agent_rate_map: receivableValueAgentRateMap,
    receivable_value_override_rate_map: receivableValueOverrideRateMap,
    accounting_transaction_details: accountingTransactionDetails = [],
  } = post;

  const agencyPoliceRates = getAgencyPoliceRatesResult(
    receivableValueAgencyRate
  );

  const agentUpdates = getTransactionDetailsAgentsUpdates({
    reportId: post.id,
    receivable: receivableValueAgentRateMap,
    party: TransactionParty.AGENT,
    accountingTransactionDetails,
  });

  const agencyUpdates = getTransactionDetailsAgentsUpdates({
    reportId: post.id,
    receivable: receivableValueOverrideRateMap,
    party: TransactionParty.AGENCY,
    accountingTransactionDetails,
  });

  return parseTransactionsDetailsUpdates({
    reportId: post.id,
    agencyPoliceRates,
    agentUpdates,
    agencyUpdates,
  });
};

const parseTransactionsDetailsUpdates = (input: {
  reportId: number;
  agencyPoliceRates?: {
    transactionDetailStrId: string;
    rate: string | number;
  };
  agentUpdates: TransactionDetailsAgentUpdates;
  agencyUpdates: TransactionDetailsAgentUpdates;
}) => {
  const agencyPolicyUpdates: TransactionsDetailsUpdates['agencyPolicyUpdates'] =
    {
      added: [],
      updated: [
        ...(input.agencyPoliceRates
          ? [
              {
                rate: input.agencyPoliceRates.rate,
                transactionDetailStrId:
                  input.agencyPoliceRates.transactionDetailStrId,
              },
            ]
          : []),
      ],
      deleted: [],
    };

  const parseUpdate = (
    item:
      | TransactionDetailsAgentUpdates['added'][number]
      | TransactionDetailsAgentUpdates['updated'][number]
  ) => {
    return {
      contactStrId: item.agentStrId,
      rate: item.rate,
      party: item.party,
    };
  };

  const agentUpdates: TransactionsDetailsUpdates['agentUpdates'] = {
    added: [
      ...input.agentUpdates.added.map(parseUpdate),
      ...input.agencyUpdates.added.map(parseUpdate),
    ],
    updated: [
      ...input.agentUpdates.updated.map(parseUpdate),
      ...input.agencyUpdates.updated.map(parseUpdate),
    ],
    deleted: [
      ...input.agentUpdates.deleted.map((item) => ({
        contactStrId: item.contactStrId,
      })),
      ...input.agencyUpdates.deleted.map((item) => ({
        contactStrId: item.contactStrId,
      })),
    ],
  };

  return {
    agencyPolicyUpdates,
    agentUpdates,
    reportId: input.reportId,
  };
};

export { getTransactionsDetailsUpdates };
