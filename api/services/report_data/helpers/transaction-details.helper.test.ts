import { describe, it, expect } from 'vitest';
import { TransactionParty } from 'common/globalTypes';
import { faker } from '@faker-js/faker';

import type { ReportDataModelUpdates } from '@/services/report_data/types';
import { getTransactionsDetailsUpdates } from './transaction-details.helper';

describe('getTransactionsDetailsUpdates', () => {
  const defaultPost: Partial<ReportDataModelUpdates> = {
    id: 1,
    receivable_value_agency_rate: {
      str_id: 'agency-str-id',
      valueRaw: 25,
    },
    receivable_value_agent_rate_map: {
      agent1: 10,
      agent2: 20,
      transactionDetailStrId: 'id',
    },
    receivable_value_override_rate_map: {
      agency1: 15,
      transactionDetailStrId: 'id',
    },
    accounting_transaction_details: [
      {
        str_id: 'detail1',
        contact: { str_id: 'agent1', first_name: '<PERSON>', last_name: '<PERSON><PERSON>' },
        rate: 10,
        party: TransactionParty.AGENT,
      },
      {
        str_id: 'detail2',
        contact: { str_id: 'agent2', first_name: 'Bra', last_name: 'Bro' },

        rate: 15,
        party: TransactionParty.AGENT,
      },
      {
        str_id: 'detail3',
        contact: { str_id: 'agency1', first_name: 'Bra', last_name: 'Bro' },
        rate: 10,
        party: TransactionParty.AGENCY,
      },
    ],
  };

  it('Given valid agency and agent rates, should return correct updates and agencyPoliceRates', () => {
    const result = getTransactionsDetailsUpdates(defaultPost);

    expect(result.agencyPolicyUpdates.updated).toEqual([
      {
        transactionDetailStrId: 'agency-str-id',
        rate: 25,
      },
    ]);

    expect(result.agentUpdates).toEqual({
      added: [],
      deleted: [],
      updated: [
        {
          contactStrId: 'agent2',
          rate: 20,
          party: TransactionParty.AGENT,
        },
        {
          contactStrId: 'agency1',
          rate: 15,
          party: TransactionParty.AGENCY,
        },
      ],
    });
  });

  describe('agencyPoliceRates', () => {
    const validRawValue = faker.number.float({ min: 0, max: 100 });
    const validStrId = faker.string.uuid();

    it.each([
      { str_id: validStrId, valueRaw: '' },
      { str_id: validStrId, valueRaw: ' ' },
    ])(
      'Given empty valueRaw($valueRaw) for agencyPoliceRates, should return default rate 0',
      (receivable_value_agency_rate) => {
        const result = getTransactionsDetailsUpdates({
          ...defaultPost,
          receivable_value_agency_rate,
        });

        expect(result.agencyPolicyUpdates.updated).toEqual([
          {
            transactionDetailStrId: receivable_value_agency_rate.str_id,
            rate: 0,
          },
        ]);
      }
    );

    describe('when return undefined', () => {
      it.each([
        { str_id: '', valueRaw: validRawValue },
        { str_id: ' ', valueRaw: validRawValue },
        { str_id: undefined, valueRaw: validRawValue },
        { str_id: null, valueRaw: validRawValue },
      ])(
        'Given invalid str_id($str_id) for agencyPoliceRates, should return update empty',
        (receivable_value_agency_rate) => {
          const result = getTransactionsDetailsUpdates({
            ...defaultPost,
            receivable_value_agency_rate,
          });

          expect(result.agencyPolicyUpdates.updated).toEqual([]);
        }
      );

      it.each([
        { str_id: validStrId, valueRaw: undefined },
        { str_id: validStrId, valueRaw: null },
      ])(
        'Given invalid valueRaw($valueRaw) for agencyPoliceRates, should return correct updates for agencyPoliceRates',
        (receivable_value_agency_rate) => {
          const result = getTransactionsDetailsUpdates({
            ...defaultPost,
            receivable_value_agency_rate,
          });

          expect(result.agencyPolicyUpdates.updated).toEqual([]);
        }
      );
    });
  });

  it('Given missing accounting_transaction_details, should add all agent and agency rates', () => {
    const post = {
      id: 2,
      receivable_value_agency_rate: {
        str_id: 'agency-str-id-2',
        valueRaw: 30,
      },
      receivable_value_agent_rate_map: {
        agentA: 12,
      },
      receivable_value_override_rate_map: {
        agencyB: 22,
      },
      accounting_transaction_details: [],
    } as unknown as Partial<ReportDataModelUpdates>;

    const result = getTransactionsDetailsUpdates(post);

    expect(result.agencyPolicyUpdates.updated).toEqual([
      {
        transactionDetailStrId: 'agency-str-id-2',
        rate: 30,
      },
    ]);

    expect(result.agentUpdates).toEqual({
      added: [
        {
          contactStrId: 'agentA',
          rate: 12,
          party: TransactionParty.AGENT,
        },
        {
          contactStrId: 'agencyB',
          rate: 22,
          party: TransactionParty.AGENCY,
        },
      ],
      updated: [],
      deleted: [],
    });
  });

  it('Given agent rate matches transaction detail, should not update', () => {
    const post = {
      id: 3,
      receivable_value_agency_rate: {
        str_id: 'agency-str-id-3',
        valueRaw: 40,
      },
      receivable_value_agent_rate_map: {
        agentX: 50,
      },
      receivable_value_override_rate_map: {},
      accounting_transaction_details: [
        {
          str_id: 'detailX',
          contact: { str_id: 'agentX' },
          rate: 50,
          party: TransactionParty.AGENT,
        },
      ],
    } as unknown as Partial<ReportDataModelUpdates>;

    const result = getTransactionsDetailsUpdates(post);

    expect(result.reportId).toEqual(post.id);
    expect(result.agentUpdates).toEqual({
      added: [],
      updated: [],
      deleted: [],
    });
    expect(result.agencyPolicyUpdates.updated).toEqual([
      {
        transactionDetailStrId: 'agency-str-id-3',
        rate: 40,
      },
    ]);
  });

  it('Given agent rate differs from transaction detail, should update', () => {
    const post = {
      id: 4,
      receivable_value_agency_rate: {
        str_id: 'agency-str-id-4',
        valueRaw: 60,
      },
      receivable_value_agent_rate_map: {
        agentY: 70,
      },
      receivable_value_override_rate_map: {},
      accounting_transaction_details: [
        {
          str_id: 'detailY',
          contact: { str_id: 'agentY' },
          rate: 65,
          party: TransactionParty.AGENT,
        },
      ],
    } as unknown as Partial<ReportDataModelUpdates>;

    const result = getTransactionsDetailsUpdates(post);

    expect(result.reportId).toEqual(post.id);
    expect(result.agentUpdates).toEqual({
      added: [],
      deleted: [],
      updated: [
        {
          contactStrId: 'agentY',
          rate: 70,
          party: TransactionParty.AGENT,
        },
      ],
    });
    expect(result.agencyPolicyUpdates.updated).toEqual([
      {
        transactionDetailStrId: 'agency-str-id-4',
        rate: 60,
      },
    ]);
  });

  it('Given non-agent/agency keys in receivable maps, should ignore them', () => {
    const post = {
      id: 5,
      receivable_value_agency_rate: {
        str_id: 'agency-str-id-5',
        valueRaw: 80,
      },
      receivable_value_agent_rate_map: {
        agentZ: 90,
        transactionDetailStrId: 'should-ignore',
        total: 999,
      },
      receivable_value_override_rate_map: {
        agencyC: 100,
        transactionDetailStrId: 'should-ignore',
        total: 888,
      },
      accounting_transaction_details: [],
    } as unknown as Partial<ReportDataModelUpdates>;

    const result = getTransactionsDetailsUpdates(post);

    expect(result.reportId).toEqual(post.id);
    expect(result.agentUpdates.added).toEqual([
      {
        contactStrId: 'agentZ',
        rate: 90,
        party: TransactionParty.AGENT,
      },
      {
        contactStrId: 'agencyC',
        rate: 100,
        party: TransactionParty.AGENCY,
      },
    ]);
  });
});
