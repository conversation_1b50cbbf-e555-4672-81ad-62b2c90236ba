// biome-ignore lint/style/useImportType: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { ReceivablesAgentIdToRateMap } from 'common/types/receivableRates';

// biome-ignore lint/style/useImportType: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import { AccountingTransactionDetails } from '@/pages/api/statement_data/types';

export interface ReportDataModel {
  id: number;
  str_id: string | null;
  import_id: string | null;
  state: string | null;
  processing_status: string | null;
  created_at: Date | null;
  created_by: string | null;
  updated_at: Date | null;
  updated_by: string | null;
  account_type: string | null;
  agent_name: string | null;
  aggregation_id: string | null;
  aggregation_primary: boolean | null;
  cancellation_date: Date | null;
  commissionable_premium_amount: number | null;
  excess_amount: number | null;
  commissions_expected: number | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  contacts: any[];
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  contacts_split: any;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  contacts_commission_split: any;
  customer_first_name: string | null;
  customer_last_name: string | null;
  customer_name: string | null;
  group_id: string | null;
  internal_id: string | null;
  dba: string | null;
  effective_date: Date | null;
  issue_age: number | null;
  notes: string | null;
  policy_id: string | null;
  policy_status: string | null;
  policy_term_months: number | null;
  policy_date: Date | null;
  premium_amount: number | null;
  product_type: string | null;
  product_sub_type: string | null;
  product_name: string | null;
  product_option_name: string | null;
  reinstatement_date: Date | null;
  signed_date: Date | null;
  transaction_type: string | null;
  type: string | null;
  uid: string | null;
  document_id: string | null;
  writing_carrier_name: string | null;
  split_percentage: number | null;
  group_name: string | null;
  payment_mode: string | null;
  geo_state: string | null;
  first_payment_date: Date | null;
  first_processed_date: Date | null;
  receivable_schedule: unknown | null;
  commission_profile_id: string | null;
  config?: { overrideFields: string[] };
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  agent_payout_rate_override?: any | null;
  tags: string[] | null;
  customer_id?: number;
}

export interface ReportDataModelUpdates extends ReportDataModel {
  receivable_value_agency_rate: { str_id: string; valueRaw: number | string };
  receivable_value_agent_rate_map: ReceivablesAgentIdToRateMap;
  receivable_value_override_rate_map: ReceivablesAgentIdToRateMap;
  accounting_transaction_details: AccountingTransactionDetails[];
}
