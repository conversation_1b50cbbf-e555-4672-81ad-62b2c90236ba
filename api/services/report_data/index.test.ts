import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import * as Sentry from '@sentry/nextjs';
import { TransactionParty } from 'common/globalTypes';

import { ReportDataService } from './index';
import { HistoryService } from '../history';
import { SyncFieldService } from '@/pages/api/data_processing/sync/syncFieldService';
import type { TransactionsDetailsUpdates } from './helpers/transaction-details.helper';
import type { PrismaClient } from '@prisma/client/extension';
import type { ReportDataModel } from './types';
import { Decimal } from '@prisma/client/runtime/library';

vi.mock('@/ioc', () => ({
  container: {
    get: vi.fn((service) => {
      if (service === SyncFieldService) {
        return {
          canUpdateIfChanged: vi.fn(),
        };
        // biome-ignore lint/correctness/noUnreachable: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        return {};
      }
    }),
  },
}));

// Mocks
vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    report_data: {
      findMany: vi.fn(),
      createMany: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
    },
    contacts: {
      findMany: vi.fn(),
    },
    accounting_transaction_details: {
      create: vi.fn(),
      update: vi.fn(),
      deleteMany: vi.fn(),
    },
    documents: {
      findMany: vi.fn(),
    },
    $transaction: vi.fn((queries) => Promise.all(queries)),
  },
}));
vi.mock('@sentry/nextjs', () => ({
  captureException: vi.fn(),
}));
vi.mock('nanoid', () => ({
  nanoid: vi.fn(() => 'mocked-nanoid'),
}));
vi.mock('currency.js', () => ({
  __esModule: true,
  default: vi.fn((val) => ({ value: Number(val) })),
}));
vi.mock('common/helpers/datetime', () => ({
  dayjs: vi.fn((val) => `dayjs(${val})`),
}));
vi.mock('@/pages/api/data_processing/sync/syncFieldService', () => ({
  SyncFieldService: class {
    canUpdateIfChanged = vi.fn();
  },
}));
vi.mock('../history', () => ({
  HistoryService: class {
    postHistoryData = vi.fn(() => Promise.resolve('history-ok'));
  },
}));

describe('ReportDataService', () => {
  let service: ReportDataService;

  const mockHistoryService = new HistoryService();
  const mockPrismaClient = {
    report_data: {
      findMany: vi.fn(),
      createMany: vi.fn(),
      update: vi.fn(),
      updateMany: vi.fn(),
    },
    contacts: {
      findMany: vi.fn(),
    },
    accounting_transaction_details: {
      create: vi.fn(),
      update: vi.fn(),
      deleteMany: vi.fn(),
      findMany: vi.fn(),
      createMany: vi.fn(),
      updateMany: vi.fn(),
    },
    documents: {
      findMany: vi.fn(),
    },
    $transaction: vi.fn((queries) => Promise.all(queries)),
  };

  beforeEach(() => {
    service = new ReportDataService(
      mockHistoryService,
      mockPrismaClient as PrismaClient
    );
    vi.clearAllMocks();
  });
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('Given str_ids, should return correct id map', async () => {
    mockPrismaClient.report_data.findMany.mockResolvedValue([
      { id: 1, str_id: 'a' },
      { id: 2, str_id: 'b' },
    ]);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const map = await (service as any).getMapStrIdsToIds(['a', 'b']);
    expect(map.get('a')).toBe(1);
    expect(map.get('b')).toBe(2);
  });

  it('Given some missing document str_ids, should return missing ids', async () => {
    mockPrismaClient.documents.findMany.mockResolvedValue([
      { str_id: 'exists' },
    ]);
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const result = await (service as any).checkNonExistDocumentStrIds([
      'exists',
      'missing',
    ]);
    expect(result).toEqual(['missing']);
  });

  it('Given a post object, should parse fields correctly', () => {
    const post = {
      id: 1,
      premium_amount: '100.5',
      effective_date: '2023-01-01',
      commissions_expected: '200',
    } as unknown as Partial<ReportDataModel>;

    const result = service.getTableFields(post) as ReportDataModel;

    expect(result.id).equal(1);
    expect(result.premium_amount).equal(100.5);
    expect(result.effective_date).equal('dayjs(2023-01-01)');
    expect(result.commissions_expected).equal(200);
  });

  it('Given various field types, should parse field values correctly', () => {
    expect(service.parseFieldValue('effective_date', '2023-01-01')).toBe(
      'dayjs(2023-01-01)'
    );
    expect(service.parseFieldValue('premium_amount', '123.45')).toBe(123.45);
    expect(service.parseFieldValue('commissions_expected', '99')).toBe(99);
    expect(service.parseFieldValue('id', 5)).toBe(5);
    expect(service.parseFieldValue('effective_date', null)).toBe(undefined);
  });

  it('Given valid data, should add report data and return total', async () => {
    mockPrismaClient.report_data.createMany.mockResolvedValue({
      count: 1,
    });

    mockPrismaClient.report_data.findMany.mockResolvedValue([{ id: 1 }]);
    const spy = vi.spyOn(service, 'asyncReportHistoryData');
    const result = await service.addReportData({
      body: { document_id: 'doc' } as unknown as ReportDataModel,
      account_id: 'a',
      uid: 'u',
    });
    expect(result).toEqual({ total: 1 });
    expect(spy).toHaveBeenCalled();
  });

  it('Given addReportData throws, should capture exception and return error', async () => {
    mockPrismaClient.report_data.createMany.mockRejectedValue(
      new Error('fail')
    );
    await expect(
      service.addReportData({
        body: { document_id: 'doc' } as unknown as ReportDataModel,
        account_id: 'a',
        uid: 'u',
      })
    ).rejects.toEqual({ error: 'fail' });
    expect(Sentry.captureException).toHaveBeenCalled();
  });

  it('Given valid update, should update report data and call asyncReportHistoryData', async () => {
    mockPrismaClient.report_data.update.mockResolvedValue({ id: 1 });

    const spy = vi.spyOn(service, 'asyncReportHistoryData');
    const result = await service.updateReportData({
      body: { id: 1, split_percentage: '10%' } as unknown as ReportDataModel,
      account_id: 'a',
      uid: 'u',
    });
    expect(result).toHaveProperty('data');
    expect(spy).toHaveBeenCalled();
  });

  it('Given updateReportData throws, should capture exception and return error', async () => {
    mockPrismaClient.report_data.update.mockRejectedValue(new Error('fail'));
    await expect(
      service.updateReportData({
        body: { id: 1 } as unknown as ReportDataModel,
        account_id: 'a',
        uid: 'u',
      })
    ).rejects.toEqual({ error: 'fail' });
    expect(Sentry.captureException).toHaveBeenCalled();
  });

  it('Given invalid ids, should throw appropriate errors', async () => {
    await expect(
      service.deleteReportData({ ids: [], account_id: 'a' })
    ).rejects.toEqual({ error: 'Missing ids' });
    await expect(
      service.deleteReportData({ ids: null, account_id: 'a' })
    ).rejects.toEqual({ error: 'Missing ids' });
    await expect(
      service.deleteReportData({ ids: [1, NaN], account_id: 'a' })
    ).rejects.toEqual({ error: 'Invalid ids' });
  });

  it('Given valid ids, should delete report data and return true', async () => {
    mockPrismaClient.report_data.updateMany.mockResolvedValue({});
    const result = await service.deleteReportData({
      ids: [1, 2],
      account_id: 'a',
    });
    expect(result).toBe(true);
  });

  it('Given deleteReportData throws, should capture exception and return error', async () => {
    mockPrismaClient.report_data.updateMany.mockRejectedValue(
      new Error('fail')
    );
    await expect(
      service.deleteReportData({ ids: [1], account_id: 'a' })
    ).rejects.toEqual({ error: 'fail' });
    expect(Sentry.captureException).toHaveBeenCalled();
  });

  it('Given newRows, should call historyService.postHistoryData', async () => {
    const spy = vi.spyOn(mockHistoryService, 'postHistoryData');
    await service.asyncReportHistoryData([
      { id: 1, uid: 'u', account_id: 'a' },
    ]);
    expect(spy).toHaveBeenCalled();
  });

  describe('getTransactionDetailsOperations', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    const buildInput = (
      transactionDetails?: Partial<TransactionsDetailsUpdates>
    ): {
      transactionDetails: TransactionsDetailsUpdates;
      updatedBy: string;
    } => {
      return {
        transactionDetails: {
          reportId: 1,
          agentUpdates: { updated: [], added: [], deleted: [] },
          agencyPolicyUpdates: { updated: [], added: [], deleted: [] },
          ...(transactionDetails || {}),
        },
        updatedBy: 'user1',
      };
    };

    it('Given no transaction details, should return an empty array', async () => {
      const input = buildInput();
      const operations = await service.getTransactionDetailsOperations(input);
      expect(operations).toEqual([]);
    });

    it('Given agencyPoliceRates, should generate an update operation', async () => {
      const input = buildInput({
        agencyPolicyUpdates: {
          added: [],
          deleted: [],
          updated: [
            {
              transactionDetailStrId: 'transaction-1',
              rate: 50,
            },
          ],
        },
      });

      await service.getTransactionDetailsOperations(input);

      expect(
        mockPrismaClient.accounting_transaction_details.update
      ).toHaveBeenCalledWith({
        where: { str_id: 'transaction-1' },
        data: {
          rate: new Decimal(50),
          updated_by: 'user1',
        },
      });
    });

    it('Given updated details, should generate update operations', async () => {
      const input = buildInput({
        agentUpdates: {
          updated: [
            {
              contactStrId: 'agent-update-1',
              rate: 10,
              party: TransactionParty.AGENT,
            },
            {
              contactStrId: 'agency-update-1',
              rate: 20,
              party: TransactionParty.AGENCY,
            },
          ],
          added: [],
          deleted: [],
        },
      });

      mockPrismaClient.contacts.findMany.mockResolvedValue([
        {
          id: 101,
          str_id: 'agent-update-1',
        },
        {
          id: 102,
          str_id: 'agency-update-1',
        },
      ]);

      await service.getTransactionDetailsOperations(input);

      expect(
        mockPrismaClient.accounting_transaction_details.updateMany
      ).toHaveBeenCalledTimes(2);
      expect(
        mockPrismaClient.accounting_transaction_details.updateMany
      ).nthCalledWith(1, {
        where: { contact_id: 101, report_id: 1 },
        data: {
          rate: new Decimal(10),
          party: TransactionParty.AGENT,
          updated_by: 'user1',
        },
      });
      expect(
        mockPrismaClient.accounting_transaction_details.updateMany
      ).nthCalledWith(
        2,
        expect.objectContaining({
          where: { contact_id: 102, report_id: 1 },
          data: {
            rate: new Decimal(20),
            party: TransactionParty.AGENCY,
            updated_by: 'user1',
          },
        })
      );
    });

    it('Given deleted details, should generate a deleteMany operation', async () => {
      const input = buildInput({
        agentUpdates: {
          updated: [],
          added: [],
          deleted: [
            { contactStrId: 'agent-delete-1' },
            { contactStrId: 'agency-delete-1' },
          ],
        },
      });

      mockPrismaClient.contacts.findMany.mockResolvedValue([
        {
          id: 101,
          str_id: 'agent-delete-1',
        },
        {
          id: 102,
          str_id: 'agency-delete-1',
        },
      ]);

      await service.getTransactionDetailsOperations(input);

      expect(
        mockPrismaClient.accounting_transaction_details.deleteMany
      ).toHaveBeenNthCalledWith(1, {
        where: { contact_id: 101, report_id: 1 },
      });
      expect(
        mockPrismaClient.accounting_transaction_details.deleteMany
      ).toHaveBeenNthCalledWith(2, {
        where: { contact_id: 102, report_id: 1 },
      });
    });

    it('Given added details, should find contacts and generate create operations', async () => {
      const input = buildInput({
        agentUpdates: {
          updated: [],
          added: [
            {
              contactStrId: 'agent-add-1',
              rate: 15,
              party: TransactionParty.AGENT,
            },
            {
              contactStrId: 'agency-add-1',
              rate: 25,
              party: TransactionParty.AGENCY,
            },
          ],
          deleted: [],
        },
      });

      vi.mocked(mockPrismaClient.contacts.findMany).mockResolvedValue([
        { id: 101, str_id: 'agent-add-1' },
        { id: 102, str_id: 'agency-add-1' },
      ]);

      await service.getTransactionDetailsOperations(input);

      expect(mockPrismaClient.contacts.findMany).toHaveBeenCalledWith({
        where: {
          str_id: {
            in: ['agent-add-1', 'agency-add-1'],
          },
        },
        select: {
          id: true,
          str_id: true,
        },
      });

      expect(
        mockPrismaClient.accounting_transaction_details.createMany
      ).toHaveBeenCalledWith({
        data: [
          {
            contact_id: 101,
            party: 'agent',
            rate: new Decimal(15),
            report_id: 1,
            type: 'receivable',
            updated_by: 'user1',
          },
          {
            contact_id: 102,
            party: 'agency',
            rate: new Decimal(25),
            report_id: 1,
            type: 'receivable',
            updated_by: 'user1',
          },
        ],
      });
    });

    it('Given a mix of operations, should generate all corresponding prisma calls', async () => {
      const input = buildInput({
        agentUpdates: {
          updated: [
            {
              contactStrId: 'agent-update-1',
              rate: 10,
              party: TransactionParty.AGENT,
            },
          ],
          added: [
            {
              contactStrId: 'agent-add-1',
              rate: 15,
              party: TransactionParty.AGENT,
            },
          ],
          deleted: [{ contactStrId: 'agent-delete-1' }],
        },
        agencyPolicyUpdates: {
          updated: [
            {
              transactionDetailStrId: 'agency-rate-1',
              rate: 50,
            },
          ],
          added: [],
          deleted: [],
        },
      });

      mockPrismaClient.contacts.findMany.mockResolvedValue([
        { id: 1, str_id: 'agent-add-1' },
        { id: 2, str_id: 'agent-update-1' },
        { id: 3, str_id: 'agent-delete-1' },
      ]);

      await service.getTransactionDetailsOperations(input);

      expect(
        mockPrismaClient.accounting_transaction_details.updateMany
      ).nthCalledWith(1, {
        where: { contact_id: 2, report_id: 1 },
        data: {
          party: TransactionParty.AGENT,
          rate: new Decimal(10),
          updated_by: 'user1',
        },
      });

      expect(
        mockPrismaClient.accounting_transaction_details.deleteMany
      ).toHaveBeenCalledWith({
        where: { contact_id: 3, report_id: 1 },
      });

      expect(
        mockPrismaClient.accounting_transaction_details.createMany
      ).toHaveBeenCalledWith({
        data: [
          {
            contact_id: 1,
            party: 'agent',
            rate: new Decimal(15),
            report_id: 1,
            type: 'receivable',
            updated_by: 'user1',
          },
        ],
      });
    });
  });
});
