import { describe, it, expect } from 'vitest';
import { BigNumber } from 'bignumber.js';

import { ReceivableScheduleService } from './index';
import { container } from '@/ioc';

describe('ReceivableScheduleService', () => {
  const receivableScheduleService = container.get<ReceivableScheduleService>(
    ReceivableScheduleService
  );

  describe('getActualRateSplit', () => {
    it('should return the agent rate split if specific rate is set', () => {
      const result = receivableScheduleService.getActualRateSplit({
        contactStrId: '1',
        contacts: ['1', '2'],
        contactSplits: {
          '1': BigNumber(50),
          '2': BigNumber(50),
        },
      });
      expect(result.toNumber()).toBe(BigNumber(0.5).toNumber());
    });
    it('should return 1 if no specific rate is set and there is only one contact', () => {
      const result = receivableScheduleService.getActualRateSplit({
        contactStrId: '1',
        contacts: ['1'],
        contactSplits: {},
      });
      expect(result.toNumber()).toBe(BigNumber(1).toNumber());
    });
    it('should return evely splited remaining rates if no specific rate is set', () => {
      const result = receivableScheduleService.getActualRateSplit({
        contactStrId: '3',
        contacts: ['1', '2', '3'],
        contactSplits: { '1': BigNumber(0.5) },
      });
      expect(result.toNumber()).toBe(BigNumber(0.25).toNumber());
    });
  });
});
