import type {
  $Enums,
  companies,
  data_imports,
  data_processing,
  documents,
  extractions,
  report_data,
  statement_data,
  users,
} from '@prisma/client';

export type GroupCountInfo = {
  [key: string]: number;
};

export type CustomStatementData = {
  groupedCountInfo: GroupCountInfo;
  groupedCommissionInfo: GroupCountInfo;
  total_count: number;
  total_commission: number;
};

export type Document = documents & {
  calc_imported_total?: number | string;
  calc_records?: number | string;
  calc_commission_total?: number | string;
  tipType?: 'success' | 'info' | 'warning';
  calc_company_name?: string;
  imports: data_imports[];
  companies: companies;
  extractions: extractions[];
  created_by_user: users;
  statement_data: Partial<statement_data>[] | CustomStatementData;
  report_data?: report_data[];
  updated_by_user: users;

  processing_log?: data_processing[];
  imports_log?: data_imports[];

  deleteRecords?: boolean;
  type?: string;
  processing: boolean;
  profile_str_id?: string;
  processing_task_id?: string;
  processing_notes: string | null;
};

export type GetDocumentsQuery = {
  id?: string;
  file_type?: $Enums.file_type;
  profile?: string;
  page?: number;
  limit?: number;
  is_dynamic_select?: boolean;
  type?: string;

  statement_month_end?: string | null;
  statement_month_start?: string | null;
  statement_month_empty?: string;

  upload_date_start?: string | null;
  upload_date_end?: string | null;
  processing_date_start?: string | null;
  processing_date_end?: string | null;
  processing_date_empty?: string;
  deposit_date_start?: string | null;
  deposit_date_end?: string | null;
  deposit_date_empty?: string;

  orderBy?: string;
  sort?: string;
  companies?: string[] | string;
  sync_status?: string;
  str_id?: string[] | string;
  status?: string | string[];
  q?: string;
  filter?: string;
};
