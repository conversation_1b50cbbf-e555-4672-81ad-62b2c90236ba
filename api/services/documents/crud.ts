import { injectable } from 'inversify';
import { isValidDateRange } from 'common/helpers/datetime';
import { BadRequestException } from 'next-api-decorators';
import { getOrderBy } from 'common/helpers';
import * as Sentry from '@sentry/node';
import type { documents, Prisma } from '@prisma/client';
import { dayjs } from 'common/helpers/datetime';
import { DocumentFilters } from 'common/globalTypes';

import { calculateSkipAndTake } from '@/prisma';
import { updateStartDateAndEndDate } from '@/lib/helpers/updateStartDateAndEndDate';
import { DataStates } from '@/types';
import { prismaClient } from '@/lib/prisma';
import { documentSubTableFields } from './constants';
import { formatDocumentsData } from './helpers/formatDocumentsData';
import type { Document, GetDocumentsQuery } from './types';

@injectable()
export class DocumentsCrudService {
  SelectFields = {
    id: true,
    str_id: true,
    created_at: true,
    created_by: true,
    bank_total_amount: true,
    check_date: true,
    company_str_id: true,
    deposit_date: true,
    file_hash: true,
    file_path: true,
    file_type: true,
    filename: true,
    import_id: true,
    imported_at: true,
    mapping: true,
    method: true,
    notes: true,
    override_file_path: true,
    override_filename: true,
    override_mapping: true,
    processor: true,
    profile_str_id: true,
    prompt: true,
    status: true,
    sync_id: true,
    sync_worker: true,
    tag: true,
    type: true,
    statement_amount: true,
    statement_month: true,
    payment_method: true,
    validations: true,
    upload_source: true,
    process_method: true,
    processing_task_id: true,
    ...documentSubTableFields,
  };
  SelectFieldsForDynamicSelect = {
    id: true,
    str_id: true,
    created_at: true,
    filename: true,
    override_filename: true,
    file_path: true,
    sync_id: true,
    sync_worker: true,
    company_str_id: true,
  };

  getDateWhere = ({
    startDate,
    endDate,
    includeNull = false,
    where,
    key,
  }: {
    startDate?: Date | null;
    endDate?: Date | null;
    includeNull?: boolean;
    where: Prisma.documentsWhereInput;
    key: keyof Prisma.documentsWhereInput;
  }) => {
    if (!startDate && !endDate) return;

    const dateCondition: Prisma.DateTimeFilter = {};
    if (startDate) dateCondition.gte = startDate;
    if (endDate) dateCondition.lt = endDate;

    const condition = { [key]: dateCondition };

    if (includeNull) {
      where.OR.push(condition, { [key]: { equals: null } });
    } else {
      (where.AND as Prisma.documentsWhereInput[]).push(condition);
    }
  };

  validateAndGetResultDateRanges = ({
    upload_date_end,
    upload_date_start,
    processing_date_end,
    processing_date_start,
    processing_date_empty,
    deposit_date_end,
    deposit_date_start,
    deposit_date_empty,
    statement_month_end,
    statement_month_start,
    statement_month_empty,
  }: GetDocumentsQuery) => {
    const dataRangeList: Array<{
      start: string;
      end: string;
      key: keyof Prisma.documentsWhereInput;
      includeNull: boolean;
    }> = [
      {
        start: upload_date_start,
        end: upload_date_end,
        key: 'created_at',
        includeNull: false,
      },
      {
        start: processing_date_start,
        end: processing_date_end,
        key: 'imported_at',
        includeNull: processing_date_empty === 'true',
      },
      {
        start: deposit_date_start,
        end: deposit_date_end,
        key: 'deposit_date',
        includeNull: deposit_date_empty === 'true',
      },
      {
        start: statement_month_start,
        end: statement_month_end,
        key: 'statement_month',
        includeNull: statement_month_empty === 'true',
      },
    ];

    const dateRanges = new Map<
      keyof Prisma.documentsWhereInput,
      { startDate?: Date; endDate?: Date; includeNull: boolean }
    >();

    for (const { start, end, key, includeNull } of dataRangeList) {
      const dates = updateStartDateAndEndDate(start, end);
      if (!isValidDateRange({ start: dates.startDate, end: dates.endDate })) {
        throw new BadRequestException('Invalid date range');
      }
      dateRanges.set(key, {
        startDate: dates.startDate,
        endDate: dates.endDate,
        includeNull,
      });
    }

    return dateRanges;
  };

  getDocuments = async ({
    query,
    account_id,
    fintaryAdmin = false,
  }: {
    query: GetDocumentsQuery;
    account_id: string;
    fintaryAdmin?: boolean;
  }) => {
    const {
      id,
      file_type,
      profile,
      page,
      limit,
      is_dynamic_select = false,
      type,
      processing_date_empty,
      orderBy = 'created_at',
      sort = 'desc',
      companies,
      sync_status,
      str_id,
      status,
      q: searchQuery,
      filter,
    } = query;

    const companyStrIds = (
      Array.isArray(companies) ? companies : [companies]
    ).filter((c) => c);

    const { take, skip } = is_dynamic_select
      ? { take: 1000, skip: 0 }
      : calculateSkipAndTake({ page, limit });

    const dateRanges = this.validateAndGetResultDateRanges(query);

    const orderByData = getOrderBy(orderBy, sort, [
      { query_field: 'companies', db_field: 'company_name' },
      { query_field: 'statement_data', db_field: 'commission_amount' },
      { query_field: 'imports', db_field: 'summed_total_amount' },
    ]);

    const parseValidations = (
      validations: string | object | null | undefined
    ): object | null => {
      if (!validations) return null;

      return typeof validations === 'string'
        ? JSON.parse(validations)
        : validations;
    };

    const isUnverified = (doc) => {
      if (!doc.validations) return false;
      const validationsObj = parseValidations(doc.validations);
      if (!validationsObj) return false;
      return Object.values(validationsObj).some((value) => value === true);
    };

    const where: Prisma.documentsWhereInput = {
      account_id,
      state: DataStates.ACTIVE,
      status: undefined,
      str_id: undefined,
      file_type: undefined,
      type: undefined,
      profile_str_id: undefined,
      sync_id: undefined,
      OR: [],
      AND: [],
    };

    dateRanges.forEach((value, key) => {
      // Convert startDate to the first day of the month
      // and endDate to the last day of the month
      if (key === 'statement_month') {
        if (value?.startDate) {
          value.startDate.setDate(1);
        }
        if (value?.endDate) {
          value.endDate = dayjs(value.endDate).endOf('month').toDate();
        }
      }

      this.getDateWhere({
        startDate: value.startDate,
        endDate: value.endDate,
        includeNull: value.includeNull,
        where,
        key,
      });
    });

    if (processing_date_empty === 'true') {
      where.OR.push({
        imported_at: null,
      });
    }

    if (companies !== undefined) {
      where.company_str_id = { in: companyStrIds };
    }

    if (status) {
      where.status = Array.isArray(status) ? { in: status } : status;
    }

    if (id) {
      where.str_id = {
        in: id.split(','),
      };
    }
    if (file_type) {
      where.file_type = file_type;
    }
    if (profile) {
      where.profile_str_id = profile;
    }
    if (sync_status) {
      where.sync_id = sync_status === 'not_synced' ? null : { not: null };
    }
    if (type) {
      where.type = type;
    }

    if (Array.isArray(str_id)) {
      where.str_id = {
        in: str_id,
      };
    }

    if (filter === DocumentFilters.MISSING_COMPANIES) {
      (where.AND as Prisma.documentsWhereInput[]).push({
        OR: [{ company_str_id: null }, { company_str_id: '' }],
      });
    }

    if (filter === DocumentFilters.MISSING_TYPES) {
      (where.AND as Prisma.documentsWhereInput[]).push({
        OR: [{ type: null }, { type: '' }],
      });
    }

    if (filter === DocumentFilters.MISSING_STATEMENT_AMOUNT) {
      (where.AND as Prisma.documentsWhereInput[]).push({
        statement_amount: null,
      });
    }
    const _query = searchQuery as string;
    // OR search for filename, file_path, and company_name
    if (_query) {
      where.OR.push(
        {
          filename: {
            contains: _query,
            mode: 'insensitive',
          },
        },
        {
          file_path: {
            contains: _query,
            mode: 'insensitive',
          },
        },
        {
          companies: {
            company_name: {
              contains: _query,
              mode: 'insensitive',
            },
          },
        }
      );
    }

    const [data, count]: [Partial<documents>[], number] = await Promise.all([
      prismaClient.documents.findMany({
        where,
        skip,
        take,
        orderBy: {
          ...orderByData,
        },
        select: is_dynamic_select
          ? this.SelectFieldsForDynamicSelect
          : this.SelectFields,
      }),
      prismaClient.documents.count({
        where,
      }),
    ]);

    const getfilterCounts = async () => {
      const baseWhere = {
        account_id,
        state: DataStates.ACTIVE,
      };

      const [
        missingCompaniesCount,
        missingTypesCount,
        missingStatementAmountCount,
        allDocumentsForValidation,
      ] = await Promise.all([
        prismaClient.documents.count({
          where: {
            ...baseWhere,
            OR: [{ company_str_id: null }, { company_str_id: '' }],
          },
        }),
        prismaClient.documents.count({
          where: {
            ...baseWhere,
            OR: [{ type: null }, { type: '' }],
          },
        }),
        prismaClient.documents.count({
          where: {
            ...baseWhere,
            statement_amount: null,
          },
        }),
        prismaClient.documents.findMany({
          where: baseWhere,
          select: {
            validations: true,
          },
        }),
      ]);

      const unverifiedCount =
        allDocumentsForValidation.filter(isUnverified).length;

      return {
        missing_companies: missingCompaniesCount,
        missing_types: missingTypesCount,
        missing_statement_amount: missingStatementAmountCount,
        unverified: unverifiedCount,
      };
    };

    let filterCounts = {};
    if (!is_dynamic_select) {
      filterCounts = await getfilterCounts();
    }

    let dataWithProcessingNotes: Partial<Document>[] = data;

    if (!is_dynamic_select) {
      const taskIds = data
        .filter((doc) => doc.processing_task_id)
        .map((doc) => doc.processing_task_id);

      let processingMap = new Map<string, { notes?: string }>();

      if (taskIds.length > 0) {
        try {
          const processingTasks = await prismaClient.data_processing.findMany({
            where: {
              str_id: { in: taskIds },
              state: DataStates.ACTIVE,
            },
            select: {
              str_id: true,
              notes: true,
            },
          });

          processingMap = new Map(
            processingTasks.map((task) => [task.str_id, task])
          );
        } catch (error) {
          Sentry.captureException(error, {
            extra: { message: 'Failed to fetch processing notes' },
          });
        }
      }

      dataWithProcessingNotes = data.map((doc) => ({
        ...doc,
        processing_notes: doc.processing_task_id
          ? processingMap.get(doc.processing_task_id)?.notes
          : null,
      }));
    }

    let finalData = dataWithProcessingNotes;
    let finalCount = count;

    if (filter === DocumentFilters.UNVERIFIED_DOCUMENTS) {
      finalData = dataWithProcessingNotes.filter(isUnverified);
      finalCount = finalData.length;
    }

    const formatData = formatDocumentsData(finalData, fintaryAdmin);
    return {
      data: formatData,
      count: finalCount,
      filterCounts,
    };
  };
}
