import type { data_update_config, Prisma } from '@prisma/client';
import { injectable } from 'inversify';
import { AccessTypes, type SortOrder } from 'common/globalTypes';
import type { Pagination } from 'common/types/pagination';

import prisma from '@/lib/prisma';
import { DataStates } from '@/types';
import { calculateSkipAndTake } from '@/prisma';

@injectable()
export class DataUpdateConfigService implements IDataUpdateConfigService {
  async getByAccountId(input: GetByAccountIdInput): Promise<{
    data: data_update_config[];
    count: number;
  }> {
    const baseClause: Prisma.data_update_configWhereInput = input.isGlobal
      ? { access: AccessTypes.GLOBAL, state: DataStates.ACTIVE }
      : {
          account_id: input.accountId,
          state: DataStates.ACTIVE,
          access: AccessTypes.ACCOUNT,
        };

    const searchClause: Prisma.data_update_configWhereInput = {
      OR: [],
    };

    if (input.textSearch) {
      const SEARCHABLE_FIELDS = [
        'group',
        'name',
        'data_entity',
        'access',
        'notes',
      ];

      for (const key of SEARCHABLE_FIELDS) {
        searchClause.OR.push({
          [key]: {
            contains: input.textSearch,
            mode: 'insensitive',
          },
        });
      }
    }

    const pagination = calculateSkipAndTake(input.pagination);
    const orderBy = { [input.orderBy ?? 'created_at']: input.sort };

    const whereClause =
      searchClause.OR.length > 0
        ? { AND: [baseClause, searchClause] }
        : baseClause;

    const findQueryArgs = {
      where: whereClause,
      accountInject: !input.isGlobal,
    };

    const [data, count] = await prisma.$transaction([
      prisma.data_update_config.findMany({
        ...findQueryArgs,
        skip: pagination.skip,
        take: pagination.take,
        include: {
          data_update_actions: {
            where: {
              state: DataStates.ACTIVE,
            },
          },
          data_update_criteria: {
            where: {
              state: DataStates.ACTIVE,
            },
          },
        },
        orderBy,
      }),

      prisma.data_update_config.count(findQueryArgs),
    ]);

    return { data, count };
  }

  async createOne(
    flagConfigMode: boolean,
    group: string,
    data_entity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_criteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_actions: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string,
    access?: string
  ): Promise<data_update_config> {
    const groupValue = !group && flagConfigMode ? 'Flags group' : group;
    const createdData = await prisma.data_update_config.create({
      data: {
        flag_config_mode: flagConfigMode,
        created_by: uid,
        created_proxied_by: ouid,
        account_id: access === AccessTypes.GLOBAL ? null : accountId,
        access: access || AccessTypes.ACCOUNT,
        group: groupValue,
        data_entity,
        data_update_criteria: {
          connect: data_update_criteria.map((criteria) => ({
            id: criteria.id,
          })),
        },
        data_update_actions: {
          connect: data_update_actions.map((action) => ({ id: action.id })),
        },
        notes,
        name,
      },
    });
    return createdData;
  }

  async updateOne(
    flagConfigMode: boolean,
    str_id: string,
    group: string,
    data_entity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_criteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_actions: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_config> {
    const groupValue = flagConfigMode ? 'Flags group' : group;
    // First, disconnect existing criteria and actions
    await prisma.data_update_config.update({
      where: {
        str_id,
        account_id: accountId,
      },
      data: {
        data_update_criteria: {
          set: [],
        },
        data_update_actions: {
          set: [],
        },
      },
    });

    const updatedData = await prisma.data_update_config.update({
      where: {
        str_id,
        account_id: accountId,
      },
      data: {
        updated_by: uid,
        updated_proxied_by: ouid,
        updated_at: new Date(),
        group: groupValue,
        flag_config_mode: flagConfigMode,
        data_entity,
        data_update_criteria: {
          connect: data_update_criteria.map((criteria) => ({
            id: criteria.id,
          })),
        },
        data_update_actions: {
          connect: data_update_actions.map((action) => ({ id: action.id })),
        },
        notes,
        name,
      },
    });

    return updatedData;
  }

  async deleteOne(
    id: number,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_config> {
    const deletedData = await prisma.data_update_config.update({
      where: {
        id,
        account_id: accountId,
      },
      data: {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });

    return deletedData;
  }

  async getGroupsByAccountId(accountId: string): Promise<string[]> {
    const data = await prisma.data_update_config.groupBy({
      where: {
        account_id: accountId,
      },
      by: ['group'],
    });
    const uniqueGroups = data
      .map((item) => item.group)
      .filter(
        (group, index, self) => group !== null && self.indexOf(group) === index
      );

    return uniqueGroups;
  }

  async getGroupsGlobal(): Promise<string[]> {
    const data = await prisma.data_update_config.groupBy({
      accountInject: false,
      by: ['group'],
    });
    const uniqueGroups = data
      .map((item) => item.group)
      .filter(
        (group, index, self) => group !== null && self.indexOf(group) === index
      );

    return uniqueGroups;
  }
}

type GetByAccountIdInput = {
  accountId: string;
  isGlobal?: boolean;
  textSearch?: string;
  orderBy?: string;
  sort?: SortOrder;
  pagination?: Pagination;
};

export interface IDataUpdateConfigService {
  getByAccountId(input: GetByAccountIdInput): Promise<{
    data: data_update_config[];
    count: number;
  }>;
  createOne(
    flagConfigMode: boolean,
    group: string,
    data_entity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_criteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_actions: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string,
    access?: string
  ): Promise<data_update_config>;
  updateOne(
    flagConfigMode: boolean,
    str_id: string,
    group: string,
    data_entity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_criteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_actions: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_config>;
  deleteOne(
    id: number,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_config>;
  getGroupsByAccountId(accountId: string): Promise<string[]>;
  getGroupsGlobal(): Promise<string[]>;
}
