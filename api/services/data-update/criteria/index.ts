import { injectable } from 'inversify';
import { AccessTypes, type SortOrder } from 'common/globalTypes';
import type { Pagination } from 'common/types/pagination';
import type { data_update_criteria, Prisma } from '@prisma/client';

import { calculateSkipAndTake } from '@/prisma';
import prisma from '@/lib/prisma';
import { DataStates } from '@/types';

@injectable()
export class DataUpdateCriteriaService implements IDataUpdateCriteriaService {
  async getByAccountId(input: GetByAccountIdInput): Promise<{
    data: data_update_criteria[];
    count: number;
  }> {
    const baseClause: Prisma.data_update_criteriaWhereInput = input.isGlobal
      ? { access: AccessTypes.GLOBAL, state: DataStates.ACTIVE }
      : {
          account_id: input.accountId,
          state: DataStates.ACTIVE,
          access: AccessTypes.ACCOUNT,
        };

    const searchClause: Prisma.data_update_criteriaWhereInput = {
      OR: [],
    };

    if (input.textSearch) {
      const SEARCHABLE_FIELDS = [
        'name',
        'data_entity',
        'access',
        'notes',
        'custom_data_update_criteria',
        'custom_data_update_criteria_params',
      ];

      for (const key of SEARCHABLE_FIELDS) {
        searchClause.OR.push({
          [key]: {
            contains: input.textSearch,
            mode: 'insensitive',
          },
        });
      }
    }

    const pagination = calculateSkipAndTake(input.pagination);
    const orderBy = { [input.orderBy ?? 'created_at']: input.sort };

    const whereClause =
      searchClause.OR.length > 0
        ? { AND: [baseClause, searchClause] }
        : baseClause;

    const findQueryArgs = {
      where: whereClause,
      accountInject: !input.isGlobal,
    };

    const [data, count] = await prisma.$transaction([
      prisma.data_update_criteria.findMany({
        ...findQueryArgs,
        skip: pagination.skip,
        take: pagination.take,
        select: {
          id: true,
          str_id: true,
          access: true,
          name: true,
          notes: true,
          data_entity: true,
          data_update_criteria: true,
          custom_data_update_criteria: true,
          custom_data_update_criteria_params: true,
          custom_data_update_criteria_mode: true,
        },
        orderBy,
      }),

      prisma.data_update_criteria.count(findQueryArgs),
    ]);

    return { data, count };
  }

  async createDataUpdateCriteria(
    customMode: boolean,
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteriaParams: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string,
    access?: string
  ): Promise<data_update_criteria> {
    const data = prisma.data_update_criteria.create({
      data: {
        custom_data_update_criteria_mode: customMode,
        data_entity: dataEntity,
        data_update_criteria: dataUpdateCriteria,
        custom_data_update_criteria: customDataUpdateCriteria,
        custom_data_update_criteria_params: customDataUpdateCriteriaParams,
        notes,
        name,
        account_id: access === AccessTypes.GLOBAL ? null : accountId,
        access: access || AccessTypes.ACCOUNT,
        created_by: uid,
        created_proxied_by: ouid,
      },
    });
    return data;
  }

  async updateDataUpdateCriteria(
    customDataUpdateCriteriaMode: boolean,
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteriaParams: any,
    notes: string,
    name: string,
    dataUpdateCriteriaId: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_criteria> {
    const data = prisma.data_update_criteria.update({
      where: {
        account_id: accountId,
        str_id: dataUpdateCriteriaId,
      },
      data: {
        custom_data_update_criteria_mode: customDataUpdateCriteriaMode,
        data_entity: dataEntity,
        data_update_criteria: customDataUpdateCriteriaMode
          ? null
          : dataUpdateCriteria,
        custom_data_update_criteria: customDataUpdateCriteriaMode
          ? customDataUpdateCriteria
          : '',
        custom_data_update_criteria_params: customDataUpdateCriteriaParams,
        notes,
        name,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });
    return data;
  }

  validateDataUpdateCriteria(
    custom_data_update_criteria_mode: boolean,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_criteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    custom_data_update_criteria: any
  ): void {
    if (custom_data_update_criteria_mode === true) {
      if (!custom_data_update_criteria) {
        throw new Error(
          'custom_data_update_criteria must be provided when custom_data_update_criteria_mode is true'
        );
      }
    } else {
      if (!data_update_criteria) {
        throw new Error(
          'data_update_criteria must be provided when custom_data_update_criteria_mode is false'
        );
      }
    }
  }

  async deleteOne(
    id: number,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_criteria> {
    const deletedData = await prisma.data_update_criteria.update({
      where: {
        id,
        account_id: accountId,
      },
      data: {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });

    return deletedData;
  }
}

type GetByAccountIdInput = {
  accountId: string;
  isGlobal?: boolean;
  textSearch?: string;
  orderBy?: string;
  sort?: SortOrder;
  pagination?: Pagination;
};

export interface IDataUpdateCriteriaService {
  validateDataUpdateCriteria(
    custom_data_update_criteria_mode: boolean,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data_update_criteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    custom_data_update_criteria: any
  ): void;
  getByAccountId(input: GetByAccountIdInput): Promise<{
    data: data_update_criteria[];
    count: number;
  }>;
  createDataUpdateCriteria(
    customDataUpdateCriteriaMode: boolean,
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteriaParams: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string,
    access?: string
  ): Promise<data_update_criteria>;
  updateDataUpdateCriteria(
    customDataUpdateCriteriaMode: boolean,
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteria: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    customDataUpdateCriteriaParams: any,
    notes: string,
    name: string,
    dataUpdateCriteriaId: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_criteria>;
  deleteOne(
    id: number,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_criteria>;
}
