import { injectable } from 'inversify';
import { AccessTypes, type SortOrder } from 'common/globalTypes';
import type { data_update_actions, Prisma } from '@prisma/client';
import type { Pagination } from 'common/types/pagination';

import prisma from '@/lib/prisma';
import { DataStates } from '@/types';
import { calculateSkipAndTake } from '@/prisma';

@injectable()
export class DataUpdateActionsService implements IDataUpdateActionsService {
  async getByAccountId(input: GetByAccountIdInput): Promise<{
    data: data_update_actions[];
    count: number;
  }> {
    const baseClause: Prisma.data_update_actionsWhereInput = input.isGlobal
      ? { access: AccessTypes.GLOBAL, state: DataStates.ACTIVE }
      : {
          account_id: input.accountId,
          state: DataStates.ACTIVE,
          access: AccessTypes.ACCOUNT,
        };

    const searchClause: Prisma.data_update_actionsWhereInput = {
      OR: [],
    };

    if (input.textSearch) {
      const SEARCHABLE_FIELDS = [
        'name',
        'data_entity',
        'access',
        'notes',
        'data_update_actions_params',
      ];

      for (const key of SEARCHABLE_FIELDS) {
        searchClause.OR.push({
          [key]: {
            contains: input.textSearch,
            mode: 'insensitive',
          },
        });
      }
    }

    const pagination = calculateSkipAndTake(input.pagination);
    const orderBy = { [input.orderBy ?? 'created_at']: input.sort };

    const whereClause =
      searchClause.OR.length > 0
        ? { AND: [baseClause, searchClause] }
        : baseClause;

    const findQueryArgs = {
      where: whereClause,
      accountInject: !input.isGlobal,
    };

    const [data, count] = await prisma.$transaction([
      prisma.data_update_actions.findMany({
        ...findQueryArgs,
        skip: pagination.skip,
        take: pagination.take,
        select: {
          id: true,
          str_id: true,
          access: true,
          name: true,
          notes: true,
          data_entity: true,
          data_update_actions: true,
          data_update_actions_params: true,
        },
        orderBy,
      }),

      prisma.data_update_actions.count(findQueryArgs),
    ]);

    return { data, count };
  }

  async createDataUpdateAction(
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateAction: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateActionParams: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string,
    access?: string
  ): Promise<data_update_actions> {
    const data = prisma.data_update_actions.create({
      data: {
        data_update_actions_params: dataUpdateActionParams,
        data_entity: dataEntity,
        data_update_actions: dataUpdateAction,
        notes,
        name,
        account_id: access === AccessTypes.GLOBAL ? null : accountId,
        access: access || AccessTypes.ACCOUNT,
        created_by: uid,
        created_proxied_by: ouid,
      },
    });
    return data;
  }

  async updateDataUpdateAction(
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateAction: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateActionParams: any,
    notes: string,
    name: string,
    dataUpdateCriteriaId: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_actions> {
    const data = prisma.data_update_actions.update({
      where: {
        str_id: dataUpdateCriteriaId,
        account_id: accountId,
      },
      data: {
        data_update_actions_params: dataUpdateActionParams,
        data_entity: dataEntity,
        data_update_actions: dataUpdateAction,
        notes,
        name,
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });
    return data;
  }

  async deleteOne(
    id: number,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_actions> {
    const deletedData = await prisma.data_update_actions.update({
      where: {
        id,
        account_id: accountId,
      },
      data: {
        state: DataStates.DELETED,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });

    return deletedData;
  }
}

type GetByAccountIdInput = {
  accountId: string;
  isGlobal?: boolean;
  textSearch?: string;
  orderBy?: string;
  sort?: SortOrder;
  pagination?: Pagination;
};

export interface IDataUpdateActionsService {
  getByAccountId(input: GetByAccountIdInput): Promise<{
    data: data_update_actions[];
    count: number;
  }>;
  createDataUpdateAction(
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateAction: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateActionParams: any,
    notes: string,
    name: string,
    accountId: string,
    uid: string,
    ouid: string,
    access?: string
  ): Promise<data_update_actions>;
  updateDataUpdateAction(
    dataEntity: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateAction: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    dataUpdateActionParams: any,
    notes: string,
    name: string,
    dataUpdateCriteriaId: string,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_actions>;
  deleteOne(
    id: number,
    accountId: string,
    uid: string,
    ouid: string
  ): Promise<data_update_actions>;
}
