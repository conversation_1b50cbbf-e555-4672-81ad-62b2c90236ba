import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DataStates } from 'common/types/common';

import prisma from '@/lib/prisma';
import { DataUpdateActionsService } from '.';
import { AccessTypes } from 'common/globalTypes';

vi.mock('@/lib/prisma', () => {
  const findMany = vi.fn();
  const count = vi.fn();
  const $transaction = vi.fn((queries) => Promise.all(queries));

  return {
    default: {
      data_update_actions: {
        findMany,
        count,
      },
      $transaction,
    },
  };
});

describe('DataUpdateActionsService', () => {
  let dataUpdateActionsService: DataUpdateActionsService;

  const mockCount = 2;
  const mockDataItems = [
    {
      id: 1,
      str_id: 'action-1',
      access: 'GLOBAL',
      name: 'Action 1',
      notes: 'Notes 1',
      data_entity: 'Entity 1',
      data_update_actions: {},
      data_update_actions_params: {},
    },
    {
      id: 2,
      str_id: 'action-2',
      access: 'GLOBAL',
      name: 'Action 2',
      notes: 'Notes 2',
      data_entity: 'Entity 2',
      data_update_actions: {},
      data_update_actions_params: {},
    },
  ];

  beforeEach(() => {
    dataUpdateActionsService = new DataUpdateActionsService();
    vi.resetAllMocks();

    vi.mocked(prisma.data_update_actions.findMany).mockResolvedValue(
      mockDataItems
    );
    vi.mocked(prisma.data_update_actions.count).mockResolvedValue(mockCount);

    prisma.data_update_actions.findMany.mockResolvedValue(mockDataItems);
    prisma.data_update_actions.count.mockResolvedValue(2);
  });

  describe('getByAccountId', () => {
    const defaultInput = {
      accountId: 'account-1',
      pagination: { page: 1, limit: 10 },
    };

    it('given valid input, should return data and count', async () => {
      const result =
        await dataUpdateActionsService.getByAccountId(defaultInput);

      expect(result.data).toHaveLength(2);
      expect(result.count).toEqual(2);
    });

    it('given isGlobal input as true, find data by access global and accountInject false', async () => {
      await dataUpdateActionsService.getByAccountId({
        ...defaultInput,
        isGlobal: true,
      });

      expect(prisma.data_update_actions.findMany).toBeCalledWith(
        expect.objectContaining({
          accountInject: false,
          where: expect.objectContaining({
            access: 'global',
            state: DataStates.ACTIVE,
          }),
        })
      );
    });

    it('given isGlobal input as false, find data by account id and accountInject true', async () => {
      await dataUpdateActionsService.getByAccountId({
        ...defaultInput,
        isGlobal: false,
      });

      expect(prisma.data_update_actions.findMany).toBeCalledWith(
        expect.objectContaining({
          accountInject: true,
          where: expect.objectContaining({
            account_id: 'account-1',
            state: 'active',
          }),
        })
      );
    });

    it('given textSearch, should apply search conditions', async () => {
      await dataUpdateActionsService.getByAccountId({
        ...defaultInput,
        textSearch: 'Action 1',
      });

      expect(prisma.data_update_actions.findMany).toHaveBeenCalled();
      expect(prisma.data_update_actions.findMany).toBeCalledWith(
        expect.objectContaining({
          where: {
            AND: [
              {
                account_id: 'account-1',
                state: 'active',
                access: AccessTypes.ACCOUNT,
              },
              {
                OR: [
                  {
                    name: {
                      contains: 'Action 1',
                      mode: 'insensitive',
                    },
                  },
                  {
                    data_entity: {
                      contains: 'Action 1',
                      mode: 'insensitive',
                    },
                  },
                  {
                    access: {
                      contains: 'Action 1',
                      mode: 'insensitive',
                    },
                  },
                  {
                    notes: {
                      contains: 'Action 1',
                      mode: 'insensitive',
                    },
                  },
                  {
                    data_update_actions_params: {
                      contains: 'Action 1',
                      mode: 'insensitive',
                    },
                  },
                ],
              },
            ],
          },
        })
      );
    });

    it('given pagination, should apply skip and take', async () => {
      await dataUpdateActionsService.getByAccountId({
        ...defaultInput,
        pagination: { page: 2, limit: 10 },
      });

      expect(prisma.data_update_actions.findMany).toBeCalledWith(
        expect.objectContaining({
          skip: 20,
          take: 10,
        })
      );
    });

    it('given no data, should return empty array and zero count', async () => {
      prisma.data_update_actions.findMany.mockResolvedValue([]);
      prisma.data_update_actions.count.mockResolvedValue(0);

      const input = {
        accountId: 'account-1',
        pagination: { page: 1, limit: 10 },
      };
      const result = await dataUpdateActionsService.getByAccountId(input);

      expect(result.data).toHaveLength(0);
      expect(result.count).toEqual(0);
    });
  });
});
