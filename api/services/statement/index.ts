import { Prisma } from '@prisma/client';
import { injectable } from 'inversify';
import type { AgentCommissionsStatuses } from 'common/globalTypes';

import { prismaClient, prisma } from '@/lib/prisma';
import type { ReconciliationDataStatus } from '@/services/reconciliation/interface';
import { AccountConfigService } from '@/services/account/config';
import { generateDateClauses } from '@/lib/helpers/generateDateClauses';

@injectable()
export class StatementService implements IStatementService {
  async getStatementData(
    statementWhere: Prisma.statement_dataWhereInput,
    include?: Prisma.statement_dataInclude,
    select?: Prisma.statement_dataSelect
  ) {
    try {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const queryOptions: any = {
        where: statementWhere,
        orderBy: [{ processing_date: 'asc' }],
      };

      if (include) {
        queryOptions.include = include;
      } else if (select) {
        queryOptions.select = select;
      }

      const statementData =
        await prismaClient.statement_data.findMany(queryOptions);

      return statementData;
    } catch (error) {
      throw new Error(error);
    }
  }
  async getStatementDataGroupByPolicyAgentName(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    try {
      const andClauses: Prisma.statement_dataWhereInput = generateDateClauses(
        'processing_date',
        startDate,
        endDate,
        includeBlankDate
      );
      const statementData = await prisma.statement_data.groupBy({
        by: ['agent_name'],
        where: {
          ...whereBase,
          AND: [{ agent_name: { not: null } }, { agent_name: { not: '' } }],
          ...andClauses,
        },
        select: { agent_name: true },
      });

      return statementData;
    } catch (error) {
      throw new Error(error);
    }
  }
  async getStatementDataGroupByCompensationType(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    try {
      const dateClauses: Prisma.statement_dataWhereInput = generateDateClauses(
        'processing_date',
        startDate,
        endDate,
        includeBlankDate
      );
      const statementData = await prisma.statement_data.groupBy({
        by: ['compensation_type'],
        where: {
          ...whereBase,
          AND: [
            { compensation_type: { not: null } },
            { compensation_type: { not: '' } },
          ],
          ...dateClauses,
        },
        select: { compensation_type: true },
      });
      return statementData;
    } catch (error) {
      throw new Error(error);
    }
  }
  async getStatementDataGroupByProductType(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    try {
      const dateClauses: Prisma.statement_dataWhereInput = generateDateClauses(
        'processing_date',
        startDate,
        endDate,
        includeBlankDate
      );
      const statementData = await prisma.statement_data.groupBy({
        by: ['product_type'],
        where: {
          ...whereBase,
          ...dateClauses,
        },
      });
      return statementData;
    } catch (error) {
      throw new Error(error);
    }
  }
  async getAgentCommissions(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date,
    includeBlankDate: boolean = false
  ) {
    try {
      const andClauses: Prisma.statement_dataWhereInput[] = [
        generateDateClauses(
          'processing_date',
          startDate,
          endDate,
          includeBlankDate
        ),
      ];

      const agentCommissions = await prismaClient.statement_data.groupBy({
        by: ['agent_name'],
        _sum: { commission_amount: true },
        where: {
          ...whereBase,
          AND: andClauses,
          commission_amount: { gt: 0 },
        },
      });
      return agentCommissions;
    } catch (error) {
      throw new Error(error);
    }
  }

  async updateReconciliationStatus(data: {
    statement_ids: number[];
    report_id: number;
    status: ReconciliationDataStatus;
  }) {
    const { statement_ids, report_id, status } = data;
    if (statement_ids.length === 0 || !status || !report_id) {
      return;
    }

    await prismaClient.statement_data.updateMany({
      where: {
        id: { in: statement_ids },
        report_data_id: report_id,
      },
      data: {
        reconciliation_status: status,
      },
    });
  }

  async updateStatementPayoutStatusByIds(
    statementIds: number[],
    newPayoutStatus: AgentCommissionsStatuses,
    accountId: string,
    contactStrId: string | null = null,
    uid: string,
    ouid: string
  ) {
    if (statementIds.length === 0 || !newPayoutStatus || !accountId) {
      return;
    }

    const accountConfigService = new AccountConfigService();
    const agentPayoutConfig = (
      (await accountConfigService.getConfigByType(
        accountId,
        'per_agent_payout_status'
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      )) as any
    )?.per_agent_payout_status;

    if (agentPayoutConfig && contactStrId) {
      const statements = await prismaClient.statement_data.findMany({
        where: {
          account_id: accountId,
          id: { in: statementIds },
        },
        select: {
          id: true,
          agent_commissions_status2: true,
        },
      });

      for (const statement of statements) {
        let updatedStatus2 = {};

        if (
          typeof statement.agent_commissions_status2 === 'object' &&
          statement.agent_commissions_status2 !== null
        ) {
          updatedStatus2 = {
            ...statement.agent_commissions_status2,
            [contactStrId]: newPayoutStatus,
          };
        } else {
          updatedStatus2 = {
            [contactStrId]: newPayoutStatus,
          };
        }

        const allSameStatus = Object.values(updatedStatus2).every(
          (status) => status === newPayoutStatus
        );

        await prismaClient.statement_data.update({
          where: { id: statement.id },
          data: {
            agent_commissions_status2: updatedStatus2,
            agent_commissions_status: allSameStatus
              ? newPayoutStatus
              : Prisma.DbNull,
            updated_at: new Date(),
            updated_by: uid,
            updated_proxied_by: ouid,
          },
        });
      }
    }

    await prismaClient.statement_data.updateMany({
      where: {
        account_id: accountId,
        id: { in: statementIds },
      },
      data: {
        agent_commissions_status: newPayoutStatus,
        updated_at: new Date(),
        updated_by: uid,
        updated_proxied_by: ouid,
      },
    });
  }
}

export interface IStatementService {
  getStatementData(
    statementWhere: Prisma.statement_dataWhereInput,
    include?: Prisma.statement_dataInclude,
    select?: Prisma.statement_dataSelect
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getStatementDataGroupByPolicyAgentName(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getStatementDataGroupByCompensationType(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getStatementDataGroupByProductType(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getAgentCommissions(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    whereBase: any,
    startDate: Date,
    endDate: Date
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  updateStatementPayoutStatusByIds(
    statementIds: number[],
    newPayoutStatus: AgentCommissionsStatuses,
    accountId: string,
    contactStrId: string | null,
    uid: string,
    ouid: string
  ): Promise<void>;
}
