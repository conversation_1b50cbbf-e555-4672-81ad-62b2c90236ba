import { describe, expect, it } from 'vitest';
import { faker } from '@faker-js/faker';
import { Prisma } from '@prisma/client';

import {
  type ApplyStatementFilterInput,
  StatementFilterService,
} from '@/services/statement/filter';
import { DEFAULT_FILTER } from 'common/constants';

describe('StatementFilterService', () => {
  const service = new StatementFilterService();

  describe('applyStatementFilter', () => {
    const defaultInput = {
      query: {},
      where: { AND: [] },
      account: {
        account_id: faker.string.uuid(),
        uid: faker.string.uuid(),
        ouid: faker.string.uuid(),
        role_id: faker.string.uuid(),
      },
      contactAndChildrenStrIds: [],
      queryParams: [],
      userData: { user_contact: [] },
      isProducer: false,
    } as ApplyStatementFilterInput;

    it('should return the result correctly for the default input', async () => {
      const result = await service.applyStatementFilter({
        ...defaultInput,
        query: {},
      } as ApplyStatementFilterInput);

      expect(result).toEqual({
        additionalFilterFields: [],
        filterList: [
          'carrier_name',
          'status',
          'product_type',
          'product_name',
          'agent_name',
          'compensation_type',
          'payment_status',
          'writing_carrier_name',
          'document_id',
          'account_type',
          'transaction_type',
          'group_name',
          'virtual_type',
          'contacts',
          'agent_commissions_status',
          'agent_commissions_status2',
          'reconciliation_status',
          'tags',
          'flags',
        ],
        where: {
          AND: [
            {
              OR: [
                {
                  AND: [
                    {
                      payment_date: undefined,
                    },
                    {
                      payment_date: undefined,
                    },
                    {
                      processing_date: undefined,
                    },
                    {
                      processing_date: undefined,
                    },
                    {
                      invoice_date: undefined,
                    },
                    {
                      invoice_date: undefined,
                    },
                    {
                      effective_date: undefined,
                    },
                    {
                      effective_date: undefined,
                    },
                  ],
                },
                {
                  OR: [
                    {
                      payment_date: undefined,
                    },
                    {
                      processing_date: undefined,
                    },
                    {
                      invoice_date: undefined,
                    },
                    {
                      effective_date: undefined,
                    },
                  ],
                },
              ],
            },
          ],
        },
      });
    });

    describe('query filters', () => {
      it('should return where field correctly when query contains hide_payout_calc_commissions', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            hide_payout_calc_commissions: true,
          },
        } as ApplyStatementFilterInput);

        expect(result).not.toBeUndefined();
        expect(result.where).toEqual({
          AND: expect.arrayContaining([
            {
              OR: [
                {
                  agent_commissions: {
                    equals: {},
                  },
                },
              ],
            },
          ]),
        });
      });
    });

    describe('tags filtering', () => {
      it('Given blank tag filter, should be handled in query', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            tags: DEFAULT_FILTER.BLANK_OPTION,
          },
        });

        expect(result.additionalFilterFields).toContain('tags');
        expect(result.filterList).not.toContain('tags');
        expect(result.where.AND).toEqual(
          expect.arrayContaining([
            {
              OR: [
                {
                  tags: {
                    isEmpty: true,
                  },
                },
              ],
            },
          ])
        );
      });

      it('Given single tag filter, should be handled in query', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            tags: 'important',
          },
        });

        expect(result.additionalFilterFields).toContain('tags');
        expect(result.filterList).not.toContain('tags');
        expect(result.where.AND).toEqual(
          expect.arrayContaining([
            {
              OR: [
                {
                  tags: {
                    has: 'important',
                  },
                },
              ],
            },
          ])
        );
      });

      it('Given multiple tags filter, should be handled in query', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            tags: ['important', 'urgent'],
          },
        });

        expect(result.additionalFilterFields).toContain('tags');
        expect(result.filterList).not.toContain('tags');
        expect(result.where.AND).toEqual(
          expect.arrayContaining([
            {
              OR: [
                {
                  tags: {
                    has: 'important',
                  },
                },
                {
                  tags: {
                    has: 'urgent',
                  },
                },
              ],
            },
          ])
        );
      });
    });

    describe('flags filtering', () => {
      it('Given blank flag filter, should be handled in query', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            flags: [DEFAULT_FILTER.BLANK_VALUE],
          },
        });

        expect(result.additionalFilterFields).toContain('flags');
        expect(result.filterList).not.toContain('flags');
        expect(result.where.AND).toEqual(
          expect.arrayContaining([
            {
              OR: [
                {
                  OR: [
                    {
                      flags: {
                        equals: Prisma.AnyNull,
                      },
                    },
                    {
                      flags: {
                        equals: {},
                      },
                    },
                  ],
                },
              ],
            },
          ])
        );
      });

      it('Given single flag filter, should be handled in query', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            flags: ['priority:high'],
          },
        });

        expect(result.additionalFilterFields).toContain('flags');
        expect(result.filterList).not.toContain('flags');
        expect(result.where.AND).toEqual(
          expect.arrayContaining([
            {
              OR: [
                {
                  flags: {
                    path: ['priority'],
                    equals: 'high',
                  },
                },
              ],
            },
          ])
        );
      });

      it('Given multiple flags filter, should be handled in query', async () => {
        const result = await service.applyStatementFilter({
          ...defaultInput,
          query: {
            flags: ['priority:high', 'status:active'],
          },
        });

        expect(result.additionalFilterFields).toContain('flags');
        expect(result.filterList).not.toContain('flags');
        expect(result.where.AND).toEqual(
          expect.arrayContaining([
            {
              OR: [
                {
                  flags: {
                    path: ['priority'],
                    equals: 'high',
                  },
                },
                {
                  flags: {
                    path: ['status'],
                    equals: 'active',
                  },
                },
              ],
            },
          ])
        );
      });
    });
  });
});
