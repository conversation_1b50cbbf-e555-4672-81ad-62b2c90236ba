import { inject, injectable } from 'inversify';
import currency from 'currency.js';

import type { StatementDataModel } from '../types';
import dayjs from '@/lib/dayjs';
import {
  updateAccountingTransactionDetailsForCommissions,
  createAccountingTransactionDetailsForNewCommissions,
  createSoftDeleteOperationsForRemovedCommissions,
} from '@/pages/api/statement_data/helpers/commissionsHelper';
import { ContactService } from '@/services/contact';
import { DataStates } from '@/types';

@injectable()
export class StatementHelper {
  @inject(ContactService) contactService: ContactService;

  areCommissionFieldsUpdated = (updatedFields: string[]): boolean => {
    const commissionFields = [
      'premium_amount',
      'split_percentage',
      'commission_amount',
      'premium_type',
      'agent_commissions',
    ];
    return updatedFields.some((field) => commissionFields.includes(field));
  };

  getTableFields = (post: StatementDataModel) => {
    // Pick up all the fields from the post
    const {
      account_type,
      advanced_commission_amount,
      agent_commissions_status,
      comp_calc_status,
      agent_commissions_status2,
      agent_commissions,
      comp_calc,
      comp_calc_log,
      agent_id,
      agent_name,
      agent_payout_rate_override,
      bill_mode,
      carrier_name,
      carrier_rate,
      commission_amount,
      commission_basis,
      commission_paid_amount,
      commission_rate,
      commissionable_premium_amount,
      compensation_type,
      contacts,
      customer_name,
      document_id,
      effective_date,
      fees,
      geo_state,
      group_id,
      group_name,
      id,
      import_id,
      internal_id,
      invoice_date,
      issue_age,
      member_count,
      new_carrier_rate,
      new_commission_rate,
      notes,
      payment_date,
      payment_mode,
      payment_status,
      period_date,
      policy_id,
      premium_amount,
      premium_type,
      processing_date,
      processing_status,
      product_name,
      product_option_name,
      product_sub_type,
      product_type,
      report_data_id,
      split_percentage,
      standardized_customer_name,
      state,
      statement_number,
      status,
      str_id,
      tags,
      transaction_type,
      type,
      writing_carrier_name,
      agent_payout_rate,
      agent_commission_payout_rate,
    } = post;

    const tableFields = {
      account_type,
      advanced_commission_amount,
      agent_commissions_status,
      comp_calc_status,
      agent_commissions_status2,
      agent_commissions,
      comp_calc,
      comp_calc_log,
      agent_id,
      agent_name,
      agent_payout_rate_override,
      bill_mode,
      carrier_name,
      carrier_rate,
      commission_amount,
      commission_basis,
      commission_paid_amount,
      commission_rate,
      commissionable_premium_amount,
      compensation_type,
      contacts,
      customer_name,
      document_id,
      effective_date,
      fees,
      geo_state,
      group_id,
      group_name,
      id,
      import_id,
      internal_id,
      invoice_date,
      issue_age,
      member_count,
      new_carrier_rate,
      new_commission_rate,
      notes,
      payment_date,
      payment_mode,
      payment_status,
      period_date,
      policy_id,
      premium_amount,
      premium_type,
      processing_date,
      processing_status,
      product_name,
      product_option_name,
      product_sub_type,
      product_type,
      report_data_id,
      split_percentage,
      standardized_customer_name,
      state,
      statement_number,
      status,
      str_id,
      tags,
      transaction_type,
      type,
      writing_carrier_name,
      agent_payout_rate,
      agent_commission_payout_rate,
    };
    // Pick up valid fields
    // TODO: Make this more structured by data types.
    // Apply normalizers here or in client?
    // Check inputs or valid/normalized
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const validFields: Record<string, any> = Object.keys(tableFields).reduce(
      (acc, key) => {
        if (![undefined, null].includes(tableFields[key])) {
          if (key.endsWith('_date')) {
            acc[key] = dayjs(tableFields[key]);
          } else if (key.endsWith('_amount') || key === 'fees') {
            const val = tableFields[key];
            acc[key] =
              val === null || val === undefined || val === ''
                ? null
                : currency(tableFields[key]).value;
          } else {
            acc[key] = tableFields[key];
          }
        }
        return acc;
      },
      {}
    );
    return validFields;
  };

  /**
   * Creates accounting transaction updates for statement data
   *
   * @param oldData - The existing statement data with accounting transaction details
   * @param compCalcStatus - The comp calc status object
   * @param compCalc - The comp calc amounts object
   * @param agentCommissionsEdited - Whether agent commissions were edited
   * @param agentCommissions - The new agent commissions object
   * @param accountId - The account ID
   * @returns Promise resolving to accounting transaction operations (create, update, soft delete)
   */
  createAccountingTransactionUpdates = async (
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    oldData: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    compCalcStatus: Record<number, any>,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    compCalc: Record<number, any>,
    agentCommissionsEdited: boolean,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    agentCommissions: Record<string, any> | null,
    accountId: string
  ) => {
    const oldAccountingTransactionDetails =
      oldData.accounting_transaction_details ?? [];

    if (!agentCommissionsEdited || !agentCommissions) {
      // If agent commissions weren't edited, just update existing records
      return {
        update: oldAccountingTransactionDetails.map((detail) => ({
          where: { id: detail.id },
          data: {
            status: compCalcStatus[detail.contact_id],
            amount: compCalc[detail.contact_id],
          },
        })),
        create: [],
      };
    }

    const contactService = this.contactService;

    const updateOperations =
      await updateAccountingTransactionDetailsForCommissions(
        contactService,
        agentCommissions,
        oldAccountingTransactionDetails,
        compCalcStatus,
        compCalc,
        accountId
      );

    const existingContactIds = new Set<number>(
      oldAccountingTransactionDetails
        .filter((detail) => detail.state === DataStates.ACTIVE)
        .map((detail) => detail.contact_id)
    );

    const createOperations =
      await createAccountingTransactionDetailsForNewCommissions(
        contactService,
        agentCommissions,
        existingContactIds,
        compCalcStatus,
        compCalc,
        accountId
      );

    const softDeleteOperations =
      await createSoftDeleteOperationsForRemovedCommissions(
        contactService,
        agentCommissions,
        oldAccountingTransactionDetails,
        accountId
      );

    const allUpdateOperations = [...updateOperations, ...softDeleteOperations];

    return {
      update: allUpdateOperations,
      create: createOperations,
    };
  };
}
