import { describe, it, expect, vi, beforeEach } from 'vitest';
import { TransactionType } from 'common/globalTypes';

import { DataStates } from '@/types';
import { StatementHelper } from './index';

describe('StatementHelper', () => {
  const utils = new StatementHelper();

  describe('areCommissionFieldsUpdated', () => {
    it('Given any commission field is updated, should return true', () => {
      expect(utils.areCommissionFieldsUpdated(['premium_amount'])).toBe(true);
      expect(utils.areCommissionFieldsUpdated(['split_percentage'])).toBe(true);
      expect(utils.areCommissionFieldsUpdated(['commission_amount'])).toBe(
        true
      );
      expect(utils.areCommissionFieldsUpdated(['premium_type'])).toBe(true);
      expect(utils.areCommissionFieldsUpdated(['agent_commissions'])).toBe(
        true
      );
    });

    it('Given no commission field is updated, should return false', () => {
      expect(utils.areCommissionFieldsUpdated(['random_field'])).toBe(false);
      expect(utils.areCommissionFieldsUpdated([])).toBe(false);
    });

    it('Given both commission and non-commission fields are updated, should return true', () => {
      expect(
        utils.areCommissionFieldsUpdated(['random_field', 'commission_amount'])
      ).toBe(true);
    });
  });

  describe('getTableFields', () => {
    it('Given valid fields and amounts/dates, should return only valid fields and normalize amounts and dates', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const post: any = {
        account_type: 'type1',
        premium_amount: '123.45',
        commission_amount: 50,
        effective_date: '2024-06-01',
        fees: '',
        notes: null,
        agent_id: 42,
        payment_date: undefined,
        random_field: 'should be ignored',
      };
      const result = utils.getTableFields(post);

      expect(result.account_type).toBe('type1');
      expect(result.premium_amount).toBe(123.45);
      expect(result.commission_amount).toBe(50);
      expect(result.effective_date.isValid()).toBe(true); // Dayjs object
      expect(result.fees).toBe(null);
      expect(result.notes).toBeUndefined();
      expect(result.agent_id).toBe(42);
      expect(result.payment_date).toBeUndefined();
      expect(result.random_field).toBeUndefined();
    });

    it('Given null and undefined values, should handle them correctly', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const post: any = {
        premium_amount: null,
        commission_amount: undefined,
        fees: undefined,
        agent_id: 1,
      };
      const result = utils.getTableFields(post);

      expect(result.premium_amount).toBeUndefined();
      expect(result.commission_amount).toBeUndefined();
      expect(result.fees).toBeUndefined();
      expect(result.agent_id).toBe(1);
    });

    it('Given multiple date and amount fields, should parse them', () => {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const post: any = {
        effective_date: '2024-01-01',
        invoice_date: '2024-02-01',
        premium_amount: '1000',
        commission_paid_amount: '200.50',
      };
      const result = utils.getTableFields(post);

      expect(result.effective_date.isValid()).toBe(true);
      expect(result.invoice_date.isValid()).toBe(true);
      expect(result.premium_amount).toBe(1000);
      expect(result.commission_paid_amount).toBe(200.5);
    });
  });

  describe('createAccountingTransactionUpdates', () => {
    const mockContactService = {
      getContactsByStrIdList: vi.fn(),
    };

    const statementHelper = new StatementHelper();

    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (statementHelper as any).contactService = mockContactService;

    const mockOldData = {
      accounting_transaction_details: [
        {
          id: 1,
          contact_id: 101,
          amount: 100,
          status: 'draft',
          state: DataStates.ACTIVE,
        },
        {
          id: 2,
          contact_id: 102,
          amount: 200,
          status: 'draft',
          state: DataStates.ACTIVE,
        },
      ],
    };

    const mockCompCalcStatus = {
      101: 'approved',
      102: 'pending',
      103: 'draft',
    };

    const mockCompCalc = {
      101: 150,
      102: 250,
      103: 350,
    };

    const mockContacts = [
      { id: 101, str_id: 'agent1' },
      { id: 102, str_id: 'agent2' },
      { id: 103, str_id: 'agent3' },
    ];

    beforeEach(() => {
      vi.clearAllMocks();
      mockContactService.getContactsByStrIdList.mockResolvedValue(mockContacts);
    });

    it('Given agent commissions not edited, should only update existing records', async () => {
      const result = await statementHelper.createAccountingTransactionUpdates(
        mockOldData,
        mockCompCalcStatus,
        mockCompCalc,
        false,
        null,
        'account123'
      );

      expect(result).toEqual({
        update: [
          {
            where: { id: 1 },
            data: {
              status: 'approved',
              amount: 150,
            },
          },
          {
            where: { id: 2 },
            data: {
              status: 'pending',
              amount: 250,
            },
          },
        ],
        create: [],
      });

      expect(mockContactService.getContactsByStrIdList).not.toHaveBeenCalled();
    });

    it('Given agent commissions edited with new and removed contacts, should handle all operations', async () => {
      const agentCommissions = {
        agent2: 300, // Existing contact, updated amount
        agent3: 400, // New contact, should be created
        total: 700,
      };

      const result = await statementHelper.createAccountingTransactionUpdates(
        mockOldData,
        mockCompCalcStatus,
        mockCompCalc,
        true,
        agentCommissions,
        'account123'
      );

      expect(mockContactService.getContactsByStrIdList).toHaveBeenCalledWith(
        ['agent2', 'agent3'],
        'account123'
      );

      expect(result.update).toHaveLength(3); // 2 regular updates + 1 soft delete
      expect(result.create).toHaveLength(1); // 1 new contact

      expect(result.create[0]).toEqual({
        contact_id: 103,
        status: 'draft',
        amount: 400,
        state: DataStates.ACTIVE,
        type: TransactionType.PAYABLE,
        account_id: 'account123',
        created_at: expect.any(Date),
        updated_at: expect.any(Date),
      });

      const softDeleteOperation = result.update.find(
        (op) => op.data.state === DataStates.DELETED
      );
      expect(softDeleteOperation).toEqual({
        where: { id: 1 },
        data: {
          state: DataStates.DELETED,
          updated_at: expect.any(Date),
        },
      });
    });

    it('Given agent commissions with null values, should fallback to comp calc', async () => {
      const agentCommissions = {
        agent1: null,
        agent2: 300,
        total: 300,
      };

      const result = await statementHelper.createAccountingTransactionUpdates(
        mockOldData,
        mockCompCalcStatus,
        mockCompCalc,
        true,
        agentCommissions,
        'account123'
      );

      expect(result.update).toContainEqual({
        where: { id: 1 },
        data: {
          status: 'approved',
          amount: 150,
        },
      });
    });

    it('Given empty old accounting transaction details, should only create new records', async () => {
      const oldDataEmpty = {
        accounting_transaction_details: [],
      };

      const agentCommissions = {
        agent3: 400,
        total: 400,
      };

      const result = await statementHelper.createAccountingTransactionUpdates(
        oldDataEmpty,
        mockCompCalcStatus,
        mockCompCalc,
        true,
        agentCommissions,
        'account123'
      );

      expect(result.update).toHaveLength(0);
      expect(result.create).toHaveLength(1);
      expect(result.create[0].contact_id).toBe(103);
    });

    it('Given agent commissions is null, should only update existing records', async () => {
      const result = await statementHelper.createAccountingTransactionUpdates(
        mockOldData,
        mockCompCalcStatus,
        mockCompCalc,
        true,
        null,
        'account123'
      );

      expect(result).toEqual({
        update: [
          {
            where: { id: 1 },
            data: {
              status: 'approved',
              amount: 150,
            },
          },
          {
            where: { id: 2 },
            data: {
              status: 'pending',
              amount: 250,
            },
          },
        ],
        create: [],
      });
    });

    it('Given old data with already deleted records, should ignore deleted records', async () => {
      const oldDataWithDeleted = {
        accounting_transaction_details: [
          {
            id: 1,
            contact_id: 101,
            amount: 100,
            status: 'draft',
            state: DataStates.ACTIVE,
          },
          {
            id: 2,
            contact_id: 102,
            amount: 200,
            status: 'draft',
            state: DataStates.DELETED,
          },
        ],
      };

      const agentCommissions = {
        agent3: 400,
        total: 400,
      };

      const result = await statementHelper.createAccountingTransactionUpdates(
        oldDataWithDeleted,
        mockCompCalcStatus,
        mockCompCalc,
        true,
        agentCommissions,
        'account123'
      );

      const softDeleteOperations = result.update.filter(
        (op) => op.data.state === DataStates.DELETED
      );
      expect(softDeleteOperations).toHaveLength(1);
      expect(softDeleteOperations[0].where.id).toBe(1);

      expect(result.create).toHaveLength(1);
      expect(result.create[0].contact_id).toBe(103);
    });
  });
});
