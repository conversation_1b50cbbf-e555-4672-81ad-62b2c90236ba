import { injectable } from 'inversify';
import { Prisma } from '@prisma/client';
import type { JsonValue } from '@prisma/client/runtime/library';

import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';

interface StatementDataOptions {
  compensation_type: string[];
  agent_commissions_status: JsonValue[];
  agent_commissions_status2: JsonValue[];
  product_type: string[];
}

@injectable()
export class StatementDataOptionsService
  implements IStatementDataOptionsService
{
  async getStatementDataOptions(
    accountId: string
  ): Promise<StatementDataOptions> {
    // TODO: We should have defined enums for compensation_type and agent_commissions_status
    const where = {
      account_id: accountId,
      state: DataStates.ACTIVE,
      OR: [
        { compensation_type: { not: null } },
        { agent_commissions_status: { not: Prisma.DbNull } },
        { agent_commissions_status2: { not: Prisma.DbNull } },
        { product_type: { not: null } },
      ],
    };

    const data = await prismaClient.statement_data.groupBy({
      where,
      by: [
        'compensation_type',
        'agent_commissions_status',
        'agent_commissions_status2',
        'product_type',
      ],
    });

    const compensationTypes = new Set<string>(
      data.map((item) => item.compensation_type).filter(Boolean)
    );

    const agentCommissionsStatuses = new Set<JsonValue>(
      data.map((item) => item.agent_commissions_status).filter(Boolean)
    );

    const agentCommissionsStatuses2 = new Set<JsonValue>(
      data.map((item) => item.agent_commissions_status2).filter(Boolean)
    );

    const productTypes = new Set<string>(
      data.map((item) => item.product_type).filter(Boolean)
    );

    const options: StatementDataOptions = {
      compensation_type: Array.from(compensationTypes),
      agent_commissions_status: Array.from(agentCommissionsStatuses),
      agent_commissions_status2: Array.from(agentCommissionsStatuses2),
      product_type: Array.from(productTypes),
    };

    return options;
  }
}

export interface IStatementDataOptionsService {
  getStatementDataOptions(accountId: string): Promise<StatementDataOptions>;
}
