import { inject, injectable } from 'inversify';
import * as Sen<PERSON> from '@sentry/nextjs';
import { history_state, history_status, history_type } from '@prisma/client';
import { nanoid } from 'nanoid';
import {
  generateAgentPayoutRate,
  getUpdatedFields,
  isNill,
} from 'common/helpers';
import { AgentCommissionsStatuses } from 'common/globalTypes';
import { isEqual } from 'lodash-es';
import BigNumber from 'bignumber.js';
import type { AccountDTO } from 'common/validators/account';

import { prismaClient } from '@/lib/prisma';
import dayjs from '@/lib/dayjs';
import { DataStates } from '@/types';
import { AccountConfigService } from '../account/config';
import { SavedReportsService } from '../saved-reports';
import type { StatementDataModel } from './types';
import { HistoryService } from '../history';
import { limitConcurrency } from '@/lib/helpers';
import { StatementHelper } from './helpers';
import { BadRequestException } from 'next-api-decorators';

@injectable()
export class StatementDataService {
  private statementHelper: StatementHelper;
  private savedReportsService: SavedReportsService;
  private accountConfigService: AccountConfigService;
  private historyService: HistoryService;

  constructor(
    @inject(StatementHelper) statementHelper: StatementHelper,
    @inject(SavedReportsService) savedReportsService: SavedReportsService,
    @inject(AccountConfigService) accountConfigService: AccountConfigService,
    @inject(HistoryService) historyService: HistoryService
  ) {
    this.statementHelper = statementHelper;
    this.savedReportsService = savedReportsService;
    this.accountConfigService = accountConfigService;
    this.historyService = historyService;
  }

  // Async statement_data to history table
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private asyncStatementHistoryData = (newRows: any[]) => {
    const historyData = newRows.map((item) => ({
      json_data: JSON.stringify(item),
      uid: item.uid,
      account_id: item.account_id,
      table_type: history_type.statement,
      state: history_state.new,
      status: history_status.active,
      statement_data_id: item.id,
      report_data_id: null,
    }));
    this.historyService.postHistoryData(historyData).catch((err) => {
      Sentry.captureException(err);
    });
  };

  async bulkAddStatementData({
    body,
    uid,
    account_id,
    ouid,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  }: { body: any[] } & AccountDTO) {
    const results = await limitConcurrency(
      async (data) => {
        await this.addStatementData({ body: data, uid, account_id, ouid });
      },
      body,
      20
    );

    return { total: results.length };
  }

  private async fetchIdsByStrIds({
    strIds,
    account_id,
  }: {
    strIds: string[];
    account_id: string;
  }) {
    const result = await prismaClient.statement_data.findMany({
      where: {
        str_id: { in: strIds },
        account_id,
        state: {
          not: DataStates.DELETED,
        },
      },
      select: { id: true, str_id: true },
    });

    return new Map(result.map((item) => [item.str_id, item.id]));
  }

  private async convertStrIdsToIds({
    body,
    account_id,
  }: {
    body: (Omit<StatementDataModel, 'id'> & { id: string })[];
  } & {
    account_id: string;
  }) {
    const strIds = new Set<string>();
    let strIdsMap: Map<string, number> = new Map();
    const updateBody: StatementDataModel[] = body.map((item) => {
      const numberId = parseInt(item.id, 10);
      const isStringId = numberId.toString() !== item.id;
      if (isStringId) {
        strIds.add(item.id);
        return {
          ...item,
          str_id: item.id,
          id: undefined,
        };
      }

      return {
        ...item,
        id: numberId,
      };
    });

    if (strIds.size) {
      strIdsMap = await this.fetchIdsByStrIds({
        strIds: [...strIds],
        account_id,
      });
    }

    if (strIds.size > 0 && strIdsMap.size !== strIds.size) {
      throw new BadRequestException(
        `Some ids were not found: ${[...strIds]
          .filter((id) => !strIdsMap.has(id))
          .join(', ')}`
      );
    }

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    updateBody.forEach((item) => {
      if (strIdsMap.has(item.str_id)) {
        // biome-ignore lint/style/noNonNullAssertion: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        item.id = strIdsMap.get(item.str_id)!; // Use the mapped ID
      }
    });

    return updateBody;
  }

  async batchUpdate({
    body,
    uid,
    account_id,
    ouid,
  }: {
    body: (Omit<StatementDataModel, 'id'> & { id: string })[];
  } & AccountDTO) {
    const updateBody = await this.convertStrIdsToIds({
      body,
      account_id,
    });

    const results = await limitConcurrency(
      async (item: StatementDataModel) => {
        await this.updateStatementData({
          body: item,
          uid,
          account_id,
          ouid,
        });
      },
      updateBody,
      20
    );

    return { total: results.length };
  }

  /**
   * Add statement_data
   */
  addStatementData = async ({
    body,
    uid,
    account_id,
    ouid,
  }: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    body: any;
  } & AccountDTO) => {
    const list = Array.isArray(body) ? body : [body];
    try {
      const jsonFields = [
        'agent_commissions',
        'agent_payout_rate',
        'agent_commission_payout_rate',
      ];

      const promises = list.map((item) => {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const post = this.statementHelper.getTableFields(item) as any;

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        jsonFields.forEach((field) => {
          if (post[field] && typeof post[field] === 'string') {
            post[field] = JSON.parse(post[field]);
          }
        });

        const new_commission_rate = post.commission_rate
          ? !Number.isNaN(+post.commission_rate.replace('%', ''))
            ? +post.commission_rate.replace('%', '') / 100
            : undefined
          : undefined;

        const split_percentage =
          Number.isNaN(
            +((post.split_percentage as unknown as string) ?? '').replace(
              '%',
              ''
            )
          ) || isNill(post.split_percentage)
            ? null
            : +((post.split_percentage as unknown as string) ?? '').replace(
                '%',
                ''
              );

        const comissionFieldsUpdated =
          this.statementHelper.areCommissionFieldsUpdated(Object.keys(post));

        if (comissionFieldsUpdated) {
          const commissionData = {
            premium_amount: post.premium_amount,
            split_percentage: post.split_percentage,
            commission_amount: post.commission_amount,
            premium_type: post.premium_type,
          };

          const { agentPayoutRate, agentCommissionPayoutRate } =
            generateAgentPayoutRate(commissionData, post.agent_commissions);

          post.agent_payout_rate = agentPayoutRate;
          post.agent_commission_payout_rate = agentCommissionPayoutRate;
        }

        return prismaClient.statement_data.create({
          data: {
            ...post,
            is_virtual: false,
            virtual_type: null,
            parent_id: null,
            master_id: null,
            account_id: account_id,
            uid: uid,
            str_id: nanoid(),
            document_id: item.document_id ? `${item.document_id}` : null, // Str_id
            new_commission_rate,
            commission_rate_percent: post.commission_rate_percent
              ? BigNumber(post.commission_rate_percent).toNumber()
              : BigNumber(new_commission_rate).times(100).toNumber(),
            split_percentage,
            created_by: uid,
            created_proxied_by: ouid,
            ...(post.processing_date
              ? {}
              : { processing_date: dayjs.utc().startOf('day') }),
          },
        });
      });
      const data = await prismaClient.$transaction(promises);

      this.asyncStatementHistoryData(data);

      return { data: data.length };
    } catch (error) {
      Sentry.captureException(error);
      throw { error: error.message || 'Error adding statement data' };
    }
  };

  /**
   * Update statement_data
   */
  updateStatementData = async ({
    body,
    account_id,
    uid,
    ouid,
  }: {
    body: StatementDataModel;
    account_id: string;
    uid: string;
    ouid: string;
  }) => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const post = this.statementHelper.getTableFields(body) as any;
    // When editing a comp report, we send the comp_report_id in the body. Then we can update the report data
    if (body.comp_report_id) {
      const { id, ...rest } = post;
      const dataToUpdateReport = [
        {
          id: id.toString(),
          agent_commission_payout_rate: body.agent_commission_payout_rate,
          agent_payout_rate: body.agent_payout_rate,
          ...rest,
        },
      ];

      this.savedReportsService.patchReportData(
        null,
        body.comp_report_id,
        account_id,
        dataToUpdateReport,
        uid,
        ouid
      );

      // We just update the report_data, so we don't need to update the actual statement_data
      return { data: [] };
    }
    const agentPayoutConfig = (
      (await this.accountConfigService.getConfigByType(
        account_id,
        'per_agent_payout_status'
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      )) as any
    )?.per_agent_payout_status;

    const new_commission_rate = post.commission_rate
      ? !Number.isNaN(+post.commission_rate.replace('%', ''))
        ? +post.commission_rate.replace('%', '') / 100
        : undefined
      : undefined;

    try {
      if (!body.id) throw new Error('Missing id');
      // Hack normalization of split_percentage. FE should normalize this before sending it and we should re-normalize everything on the BE.
      // Right now, we're still passing a string to the BE.
      // TODO: Normalize on FE and BE to the proper type.
      const rawPercentage = body.split_percentage as unknown as string;
      const parsedPercentage =
        rawPercentage === null ||
        rawPercentage === '' ||
        rawPercentage === undefined
          ? null
          : Number(rawPercentage.replace('%', ''));
      // biome-ignore lint/suspicious/noGlobalIsNan: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const split_percentage = isNaN(parsedPercentage)
        ? null
        : parsedPercentage;

      const oldData = await prismaClient.statement_data.findUnique({
        where: {
          id: Number(post.id),
          account_id: String(account_id),
        },
        include: {
          accounting_transaction_details: {
            where: {
              state: DataStates.ACTIVE,
            },
          },
        },
      });

      if (
        [
          AgentCommissionsStatuses.APPROVED,
          AgentCommissionsStatuses.PAID,
          AgentCommissionsStatuses.REVIEWED,
        ].includes(
          oldData.agent_commissions_status as AgentCommissionsStatuses
        ) ||
        (agentPayoutConfig &&
          Object.values(oldData.agent_commissions_status2 ?? {}).some(
            (status) =>
              [
                AgentCommissionsStatuses.APPROVED,
                AgentCommissionsStatuses.PAID,
                AgentCommissionsStatuses.REVIEWED,
              ].includes(status as AgentCommissionsStatuses)
          )) ||
        (oldData.accounting_transaction_details ?? []).some((details) =>
          [
            AgentCommissionsStatuses.APPROVED,
            AgentCommissionsStatuses.PAID,
            AgentCommissionsStatuses.REVIEWED,
          ].includes(details.status as AgentCommissionsStatuses)
        )
      ) {
        const updatedFields = getUpdatedFields(oldData, post);

        const editableFields = [
          'agent_commissions_status2',
          'comp_calc_status',
          'notes',
        ];
        if (
          updatedFields.every((field) => editableFields.includes(field)) &&
          updatedFields.length <= 2
        ) {
          const data = await prismaClient.statement_data.update({
            where: {
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              id: Number((post as any).id),
              account_id: String(account_id),
            },
            data: {
              agent_commissions_status2: post.agent_commissions_status2,
              accounting_transaction_details: {
                update: (oldData.accounting_transaction_details ?? []).map(
                  (detail) => ({
                    where: { id: detail.id },
                    data: {
                      status: post.comp_calc_status[detail.contact_id],
                    },
                  })
                ),
              },
              notes: post.notes,
              updated_at: new Date(),
              updated_by: uid,
              updated_proxied_by: ouid,
            },
          });
          this.asyncStatementHistoryData([data]);
          return { data };
        }
        throw new Error(
          'Commission record cannot be edited (Payout status already "Reviewed", "Approved", or "Paid")'
        );
      }

      const oldAgentCommissions = Object.fromEntries(
        Object.entries(oldData.agent_commissions ?? {}).map(([k, v]) => [
          k,
          (+v).toFixed(2),
        ])
      );

      // Include existing zero commission value in the new agent_commissions object
      if (!body.incl_zero_commissions) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.entries(oldAgentCommissions).forEach(([k, v]) => {
          if (v === '0.00' && !(k in post.agent_commissions)) {
            post.agent_commissions[k] = v;
          }
        });
      }

      const newAgentCommissions = Object.fromEntries(
        Object.entries(post.agent_commissions ?? {}).map(([k, v]) => [
          k,
          (+v).toFixed(2),
        ])
      );

      const agentCommissionsEdited =
        !isEqual(oldAgentCommissions, newAgentCommissions) &&
        !(
          isNill(oldData.agent_commissions) && isNill(post.agent_commissions)
        ) &&
        oldData.agent_commissions_status !== AgentCommissionsStatuses.OFFSET &&
        oldData.agent_commissions_status !==
          AgentCommissionsStatuses.NO_PAYMENT;

      const agentCommissionsEditedData = Object.keys(newAgentCommissions)
        .filter(
          (contactId) =>
            oldAgentCommissions[contactId] !== newAgentCommissions[contactId]
        )
        .reduce((acc, contactId) => {
          const currentStatus = post.agent_commissions_status2?.[contactId];
          if (
            contactId !== 'total' &&
            currentStatus !== AgentCommissionsStatuses.OFFSET &&
            currentStatus !== AgentCommissionsStatuses.NO_PAYMENT
          )
            acc[contactId] = AgentCommissionsStatuses.MANUAL;
          return acc;
        }, {});

      const compCalc = post.comp_calc ?? {};
      const compCalcStatus = post.comp_calc_status ?? {};

      delete post.comp_calc;
      delete post.comp_calc_status;

      // Check if any of the commission fields was edited and re calculate agent payout rates
      const updatedFields = getUpdatedFields(oldData, post);
      const comissionFieldsUpdated =
        this.statementHelper.areCommissionFieldsUpdated(updatedFields);

      if (comissionFieldsUpdated || agentCommissionsEdited) {
        const commissionData = {
          premium_amount: post.premium_amount,
          split_percentage: post.split_percentage,
          commission_amount: post.commission_amount,
          premium_type: post.premium_type,
        };

        const { agentPayoutRate, agentCommissionPayoutRate } =
          generateAgentPayoutRate(commissionData, post.agent_commissions);

        post.agent_payout_rate = agentPayoutRate;
        post.agent_commission_payout_rate = agentCommissionPayoutRate;
      }

      const accountingTransactionUpdates =
        await this.statementHelper.createAccountingTransactionUpdates(
          oldData,
          compCalcStatus,
          compCalc,
          agentCommissionsEdited,
          post.agent_commissions,
          account_id
        );

      const data = await prismaClient.statement_data.update({
        where: {
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          id: Number((post as any).id),
          account_id: String(account_id),
        },
        data: {
          ...post,
          agent_commissions_status:
            post.agent_commissions_status === AgentCommissionsStatuses.REVIEWED
              ? post.agent_commissions_status
              : agentCommissionsEdited && post.agent_commissions
                ? AgentCommissionsStatuses.MANUAL
                : post.agent_commissions_status,
          agent_commissions_status2: {
            ...post.agent_commissions_status2,
            ...agentCommissionsEditedData,
          },
          split_percentage,
          new_commission_rate,
          commission_rate_percent: post.commission_rate_percent
            ? BigNumber(post.commission_rate_percent).toNumber()
            : BigNumber(new_commission_rate).times(100).toNumber(),
          account_id,
          uid,
          updated_at: new Date(),
          updated_by: uid,
          updated_proxied_by: ouid,
          accounting_transaction_details: {
            ...(accountingTransactionUpdates.update.length > 0 && {
              update: accountingTransactionUpdates.update,
            }),
            ...(accountingTransactionUpdates.create.length > 0 && {
              create: accountingTransactionUpdates.create,
            }),
          },
        },
      });

      this.asyncStatementHistoryData([data]);
      return { data };
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error(`Error updating statement data: ${error.message}`);
      }
      Sentry.captureException(error);
      throw {
        error: error.message || 'Error updating statement data',
        status: 400,
      };
    }
  };
}
