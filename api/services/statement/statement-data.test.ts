import { describe, it, expect, beforeEach, vi, type Mock } from 'vitest';
import { AgentCommissionsStatuses } from 'common/globalTypes';

import { StatementDataService } from './statement-data';
import type { SavedReportsService } from '../saved-reports';
import type { AccountConfigService } from '../account/config';
import type { HistoryService } from '../history';
import { prismaClient } from '@/lib/prisma';
import type { StatementHelper } from './helpers';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    statement_data: {
      create: vi.fn(),
      update: vi.fn(),
      findUnique: vi.fn(),
      findMany: vi.fn(),
    },
    $transaction: vi.fn(),
  },
}));
vi.mock('@sentry/nextjs', () => ({
  captureException: vi.fn(),
}));
vi.mock('nanoid', () => ({
  nanoid: () => 'mocked-nanoid',
}));
vi.mock('common/helpers', async () => {
  const actual = await vi.importActual('common/helpers');
  return {
    ...actual,
    generateAgentPayoutRate: vi.fn(() => ({
      agentPayoutRate: {},
      agentCommissionPayoutRate: {},
    })),
    getUpdatedFields: vi.fn(() => []),
    isNill: vi.fn((v) => v == null),
  };
});
vi.mock('lodash-es', () => ({
  isEqual: vi.fn((a, b) => JSON.stringify(a) === JSON.stringify(b)),
}));
vi.mock('@/lib/dayjs', () => {
  const actual = vi.importActual ? vi.importActual('@/lib/dayjs') : {};
  const mockDayjs = () => ({
    startOf: () => ({
      toDate: () => new Date('2023-01-01T00:00:00Z'),
      valueOf: () => new Date('2023-01-01T00:00:00Z').valueOf(),
    }),
    toDate: () => new Date('2023-01-01T00:00:00Z'),
    valueOf: () => new Date('2023-01-01T00:00:00Z').valueOf(),
  });
  mockDayjs.utc = mockDayjs;
  return {
    ...actual,
    default: mockDayjs,
    utc: mockDayjs,
  };
});

describe('StatementDataService', () => {
  let service: StatementDataService;
  let statementHelper: StatementHelper;
  let savedReportsService: SavedReportsService;
  let accountConfigService: AccountConfigService;
  let historyService: HistoryService;

  beforeEach(() => {
    statementHelper = {
      getTableFields: vi.fn((x) => x),
      areCommissionFieldsUpdated: vi.fn(() => false),
      createAccountingTransactionUpdates: vi.fn(() =>
        Promise.resolve({
          update: [],
          create: [],
        })
      ),
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    savedReportsService = { patchReportData: vi.fn() } as any;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    accountConfigService = { getConfigByType: vi.fn() } as any;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    historyService = { postHistoryData: vi.fn(() => Promise.resolve()) } as any;
    service = new StatementDataService(
      statementHelper,
      savedReportsService,
      accountConfigService,
      historyService
    );
    vi.clearAllMocks();
  });

  it('Given valid data, should create statement_data ', async () => {
    (prismaClient.statement_data.create as Mock).mockResolvedValueOnce({
      id: 1,
      account_id: 'acc',
      uid: 'uid',
    });
    (prismaClient.$transaction as Mock).mockResolvedValue([
      { id: 1, account_id: 'acc', uid: 'uid' },
    ]);

    const result = await service.addStatementData({
      body: { some: 'data', commission_rate: '10%' },
      uid: 'uid',
      account_id: 'acc',
      ouid: 'ouid',
    });

    expect(prismaClient.statement_data.create).toHaveBeenCalled();
    expect(prismaClient.$transaction).toHaveBeenCalled();
    expect(result).toEqual({ data: 1 });
  });

  it('Given an error occurs, should handle errors and capture with Sentry', async () => {
    (prismaClient.statement_data.create as Mock).mockImplementation(() => {
      throw new Error('fail');
    });
    (prismaClient.$transaction as Mock).mockImplementation(() => {
      throw new Error('fail');
    });
    const Sentry = await import('@sentry/nextjs');

    await expect(
      service.addStatementData({
        body: { some: 'data', commission_rate: '10%' },
        uid: 'uid',
        account_id: 'acc',
        ouid: 'ouid',
      })
    ).rejects.toMatchObject({ error: 'fail' });

    expect(Sentry.captureException).toHaveBeenCalled();
  });

  it('Given a batch update, should call updateStatementData for each item', async () => {
    const spy = vi
      .spyOn(service, 'updateStatementData')
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      .mockResolvedValue({ data: {} as any });
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    vi.spyOn(service as any, 'convertStrIdsToIds').mockResolvedValue([
      { id: 1, some: 'data' },
      { id: 2, some: 'data' },
    ]);
    const result = await service.batchUpdate({
      body: [
        { id: '1', some: 'data' },
        { id: '2', some: 'data' },
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ] as any,
      uid: 'uid',
      account_id: 'acc',
      ouid: 'ouid',
    });
    expect(spy).toHaveBeenCalledTimes(2);
    expect(result).toEqual({ total: 2 });
  });

  it('Given string ids, should convert string ids to numeric ids', async () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    vi.spyOn(service as any, 'fetchIdsByStrIds').mockResolvedValue(
      new Map([['abc', 123]])
    );
    const body = [
      { id: 'abc', str_id: 'abc', some: 'data' },
      { id: '2', str_id: '2', some: 'data' },
    ];
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    const result = await (service as any).convertStrIdsToIds({
      body,
      account_id: 'acc',
    });
    expect(result[0].id).toBe(123);
    expect(result[1].id).toBe(2);
  });

  it('Given valid update data, should update statement_data', async () => {
    (prismaClient.statement_data.findUnique as Mock).mockResolvedValue({
      id: 1,
      account_id: 'acc',
      agent_commissions_status: AgentCommissionsStatuses.DRAFT,
      agent_commissions: { a: 1 },
      accounting_transaction_details: [],
    });
    (prismaClient.statement_data.update as Mock).mockResolvedValue({
      id: 1,
      account_id: 'acc',
    });

    const result = await service.updateStatementData({
      body: {
        id: 1,
        agent_commissions: { a: 1 },
        split_percentage: '10',
        comp_calc: {},
        comp_calc_status: {},
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any,
      account_id: 'acc',
      uid: 'uid',
      ouid: 'ouid',
    });

    expect(prismaClient.statement_data.update).toHaveBeenCalled();
    expect(result).toHaveProperty('data');
  });

  it('Given commission status is reviewed and fields are not editable, should throw error', async () => {
    (prismaClient.statement_data.findUnique as Mock).mockResolvedValue({
      id: 1,
      account_id: 'acc',
      agent_commissions_status: AgentCommissionsStatuses.REVIEWED,
      agent_commissions: { a: 1 },
      agent_commissions_status2: {},
      accounting_transaction_details: [],
    });
    (accountConfigService.getConfigByType as Mock).mockResolvedValue({
      per_agent_payout_status: true,
    });
    const body = {
      id: 1,
      agent_commissions: { a: 2 },
      split_percentage: '10',
      comp_calc: {},
      comp_calc_status: {},
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;

    // Only editable fields updated
    const getUpdatedFields = (await import('common/helpers'))
      .getUpdatedFields as Mock;
    getUpdatedFields.mockReturnValue(['agent_commissions']);

    await expect(
      service.updateStatementData({
        body,
        account_id: 'acc',
        uid: 'uid',
        ouid: 'ouid',
      })
    ).rejects.toMatchObject({
      error: expect.stringContaining('Commission record cannot be edited'),
      status: 400,
    });
  });

  it('Given editable fields and allowed update, should update only editable fields', async () => {
    (prismaClient.statement_data.findUnique as Mock).mockResolvedValue({
      id: 1,
      account_id: 'acc',
      agent_commissions_status: AgentCommissionsStatuses.REVIEWED,
      agent_commissions: { a: 1 },
      agent_commissions_status2: {},
      accounting_transaction_details: [],
    });
    (accountConfigService.getConfigByType as Mock).mockResolvedValue({
      per_agent_payout_status: true,
    });
    (prismaClient.statement_data.update as Mock).mockResolvedValue({
      id: 1,
      account_id: 'acc',
    });
    // Only editable fields updated
    const getUpdatedFields = (await import('common/helpers'))
      .getUpdatedFields as Mock;
    getUpdatedFields.mockReturnValue(['notes']);

    const body = {
      id: 1,
      agent_commissions: { a: 1 },
      split_percentage: '10',
      notes: 'test',
      comp_calc: {},
      comp_calc_status: {},
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;

    const result = await service.updateStatementData({
      body,
      account_id: 'acc',
      uid: 'uid',
      ouid: 'ouid',
    });

    expect(prismaClient.statement_data.update).toHaveBeenCalled();
    expect(result).toHaveProperty('data');
  });

  it('Given comp_report_id is present, should call patchReportData', async () => {
    const patchSpy = vi.spyOn(savedReportsService, 'patchReportData');
    const body = {
      id: 1,
      comp_report_id: 'report',
      agent_commission_payout_rate: {},
      agent_payout_rate: {},
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any;
    const result = await service.updateStatementData({
      body,
      account_id: 'acc',
      uid: 'uid',
      ouid: 'ouid',
    });
    expect(patchSpy).toHaveBeenCalled();
    expect(result).toEqual({ data: [] });
  });

  it('Given bulk add data, should call addStatementData for each item', async () => {
    const spy = vi
      .spyOn(service, 'addStatementData')
      .mockResolvedValue({ data: 1 });
    const result = await service.bulkAddStatementData({
      body: [{ a: 1 }, { a: 2 }],
      uid: 'uid',
      account_id: 'acc',
      ouid: 'ouid',
    });
    expect(spy).toHaveBeenCalledTimes(2);
    expect(result).toEqual({ total: 2 });
  });

  it('Should throw error if updateStatementData is called with missing id', async () => {
    await expect(
      service.updateStatementData({
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        body: { agent_commissions: { a: 1 } } as any,
        account_id: 'acc',
        uid: 'uid',
        ouid: 'ouid',
      })
    ).rejects.toMatchObject({
      error: expect.stringContaining('Missing id'),
      status: 400,
    });
  });

  it('Should throw error if convertStrIdsToIds cannot find all string ids', async () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    vi.spyOn(service as any, 'fetchIdsByStrIds').mockResolvedValue(
      new Map<string, number>([['abc', 123]])
    );
    const body = [
      { id: 'abc', str_id: 'abc', some: 'data' },
      { id: 'abcd', str_id: 'abc', some: 'data' },
    ];
    await expect(
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      (service as any).convertStrIdsToIds({ body, account_id: 'acc123123' })
    ).rejects.toThrow(/Some ids were not found/);
  });
});
