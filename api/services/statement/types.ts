export interface StatementDataModel {
  id: number;
  str_id: string | null;
  uid: string | null;
  account_id: string | null;
  state: string | null;
  processing_status: string | null;
  created_at: Date | null;
  created_by: string | null;
  updated_at: Date | null;
  updated_by: string | null;

  account_type: string | null;
  advanced_commission_amount: number | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  agent_commission_payout_rate?: any | null;
  agent_commissions_status: string | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  agent_commissions_status2: any | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  agent_commissions: any | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  comp_calc: any | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  comp_calc_status: any | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  comp_calc_log: any | null;
  agent_id: string | null;
  agent_name: string | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  agent_payout_rate?: any | null;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  agent_payout_rate_override?: any | null;
  bill_mode: string | null;
  carrier_name: string | null;
  carrier_rate: string | null;
  commission_amount: number | null;
  commission_basis: string | null;
  commission_paid_amount: number | null;
  commission_rate: string | null;
  commissionable_premium_amount: number | null;
  comp_report_id?: string | null;
  compensation_type: string | null;
  contacts: string[] | null;
  customer_name: string | null;
  document_id: string | null;
  effective_date: Date | null;
  expected_result: string | null;
  fees: number | null;
  geo_state: string | null;
  group_id: string | null;
  group_name: string | null;
  import_id: string | null;
  incl_zero_commissions?: boolean;
  internal_id: string | null;
  invoice_date: Date | null;
  issue_age: number | null;
  member_count: number | null;
  new_carrier_rate: string | null;
  new_commission_rate: string | null;
  notes: string | null;
  payment_date: Date | null;
  payment_mode: string | null;
  payment_status: string | null;
  period_date: Date | null;
  policy_id: string | null;
  premium_amount: number | null;
  premium_type: 'policy' | 'split' | null;
  processing_date: Date | null;
  product_name: string | null;
  product_option_name: string | null;
  product_sub_type: string | null;
  product_type: string | null;
  report_data_id: number | null;
  split_percentage: number | null;
  standardized_customer_name: string | null;
  statement_number: string | null;
  status: string | null;
  tags: string[] | null;
  transaction_type: string | null;
  type: string | null;
  writing_carrier_name: string | null;
}
