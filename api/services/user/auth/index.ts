import { injectable } from 'inversify';
import type { auth as firebaseAuth } from 'firebase-admin';

import { auth } from '@/lib/firebase-admin';
import { userAuthActions } from '@/types';

@injectable()
export class UserAuthService implements IUserAuthService {
  async updateUserAuthByUid(
    userUid: string,
    action: userAuthActions,
    newUserPassword?: string
  ): Promise<firebaseAuth.UserRecord | string> {
    let updatedUser: firebaseAuth.UserRecord | string;

    switch (action) {
      case userAuthActions.VERIFY_EMAIL:
        updatedUser = await auth.updateUser(userUid, {
          emailVerified: true,
        });
        break;
      case userAuthActions.RESET_PASSWORD:
        if (!newUserPassword) {
          return 'New password is required for resetting password';
        }
        updatedUser = await auth.updateUser(userUid, {
          password: newUserPassword,
        });
        break;
      default:
        return 'Invalid action';
    }
    return updatedUser;
  }
}

export interface IUserAuthService {
  updateUserAuthByUid(
    userUid: string,
    action: string,
    newUserPassword?: string
  ): Promise<firebaseAuth.UserRecord | string>;
}
