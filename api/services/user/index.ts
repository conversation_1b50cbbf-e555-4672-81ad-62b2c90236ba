import { injectable } from 'inversify';
import type { account_user_roles, users, Prisma } from '@prisma/client';

import { prismaClient } from '@/lib/prisma';
import { Roles } from '@/types';
import { UserStates } from 'common/constants/user-states.enum';

@injectable()
export class UserService implements IUserService {
  async acceptTos({ uid }: { uid: string }): Promise<boolean> {
    const user = await prismaClient.users.findUnique({
      where: { uid: uid },
    });

    if (!user) {
      throw new Error('Not found');
    }

    await prismaClient.users.update({
      where: {
        uid: uid,
      },
      data: {
        tos_accepted_at: new Date(),
      },
    });

    return true;
  }

  async updateUser(
    whereObj: Prisma.usersWhereUniqueInput,
    dataObj: Prisma.usersUpdateInput,
    selectObj?: Partial<Record<keyof users, boolean>>
  ) {
    const result = await prismaClient.users.update({
      where: whereObj,
      data: dataObj,
      select: selectObj,
    });

    return result ?? null;
  }

  async getUniqueUser(
    whereObj: Prisma.usersWhereUniqueInput,
    selectObj?: Partial<Record<keyof users, boolean>>
  ) {
    const result = await prismaClient.users.findUnique({
      where: whereObj,
      select: selectObj,
    });

    return result ?? null;
  }

  async getUserByUid(uid: string) {
    const userData = await prismaClient.users.findUnique({
      where: {
        uid: String(uid),
        state: 'active',
      },
      select: {
        user_contact: {
          where: { state: 'active' },
          select: { id: true, str_id: true },
        },
        account_user_roles: true,
      } as const,
    });

    return userData;
  }

  async getUserByStrId(strId: string) {
    const userData = await prismaClient.users.findUnique({
      where: {
        str_id: String(strId),
        state: 'active',
      },
      select: {
        email: true,
        first_name: true,
        last_name: true,
        user_contact: {
          where: {
            state: 'active',
          },
          select: {
            str_id: true,
          },
        },
      },
    });

    return userData;
  }

  async getUserRoleByUidAndAccountId(
    uid: string,
    accountId: string
  ): Promise<account_user_roles | null> {
    const userRole = await prismaClient.account_user_roles.findFirst({
      where: {
        account_id: accountId,
        user: {
          uid: uid,
        },
        state: { in: ['active', 'invited'] },
      },
    });

    return userRole;
  }

  async getAccountAdminByEmail(email: string, accountId: string) {
    const user = await prismaClient.users.findFirst({
      where: {
        email: email,
        state: 'active',
        account_user_roles: {
          some: {
            account_id: accountId,
            role_id: Roles.ACCOUNT_ADMIN,
          },
        },
      },
    });

    return user;
  }

  isAdminOrDataSpecialist(roles: account_user_roles[]) {
    const acceptedRoles = [Roles.ACCOUNT_ADMIN, Roles.DATA_SPECIALIST];

    return roles.some((role) => acceptedRoles.includes(role.role_id));
  }

  async getUserForLoginByEmail(email: string) {
    return await prismaClient.users.findUnique({
      where: { email: email, state: { not: UserStates.DELETED } },
      select: {
        email: true,
        state: true,
        str_id: true,
        sub_type: true,
        tos_accepted_at: true,
        type: true,
        uid: true,
        account_user_roles: {
          where: {
            account: { state: { not: UserStates.DELETED } },
            state: { not: UserStates.DELETED },
          },
          select: {
            account_id: true,
            role_id: true,
            state: true,
            account: {
              select: {
                accounting_transactions_enabled: true,
                comp_grids_enabled: true,
                logo_url: true,
                mode: true,
                name: true,
                short_name: true,
                str_id: true,
                uid: true,
                white_label_mode: true,
                account_role_settings: {
                  where: { state: { not: UserStates.DELETED } },
                  select: {
                    default_page: true,
                    role_id: true,
                  },
                },
              },
            },
          },
        },
      },
    });
  }
}

export interface IUserService {
  acceptTos({ uid }: { uid: string }): Promise<boolean>;
  updateUser(
    where: Prisma.usersWhereUniqueInput,
    data: Prisma.usersUpdateInput,
    select?: Partial<Record<keyof users, boolean>>
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  getUniqueUser(
    where: Prisma.usersWhereUniqueInput,
    select?: Partial<Record<keyof users, boolean>>
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<any>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getUserByUid(uid: string): Promise<any>;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  getUserByStrId(strId: string): Promise<any>;
  getAccountAdminByEmail(
    email: string,
    accountId: string
  ): Promise<users | null>;
  getUserRoleByUidAndAccountId(
    uid: string,
    accountId: string
  ): Promise<account_user_roles | null>;
}
