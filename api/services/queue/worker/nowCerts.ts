import { inject, injectable } from 'inversify';
import { nanoid } from 'nanoid';
import type { Entity, SyncParamsDTO } from 'common/dto/data_processing/sync';
import { WorkerNames } from 'common/constants';

import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { NowCertsService } from '@/services/nowCerts';
import type {
  AgentList,
  CarrierDetailList,
  PolicyDetailList,
} from '@/services/nowCerts/interface';
import {
  type AgentDatum,
  BaseWorker,
  type CompanyDatum,
  type DBData,
  type IDataSyncWorker,
  type PolicyDatum,
} from '@/services/queue/worker/base';
import {
  DataProcessingStatuses,
  DataProcessingTypes,
  DataStates,
  type ExtAccountInfo,
} from '@/types';
import { Cache } from '@/lib/decorators';
import { getValidDate, runInBatch } from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import { CloudTaskService } from '@/services/cloud-task';
import { DataProcessService } from '@/services/data_processing';
import checkFeature from '@/lib/statsig';
import { Queue, type QueueTask } from '@/services/queue/types';
import dayjs from '@/lib/dayjs';
import { ReportService } from '@/services/report';
import { ContactService } from '@/services/contact';
import { AppLoggerService } from '@/services/logger/appLogger';

@injectable()
export class NowCertsWorker
  extends BaseWorker
  implements IDataSyncWorker<SyncParamsDTO>
{
  name = WorkerNames.NowCertsWorker;
  task: QueueTask<SyncParamsDTO>;

  @inject(NowCertsService) service: NowCertsService;
  @inject(CloudTaskService) cloudTaskService: CloudTaskService;
  @inject(DataProcessService) dataProcessingService: DataProcessService;
  @inject(ReportService) reportService: ReportService;
  @inject(ContactService) contactService: ContactService;
  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: WorkerNames.NowCertsWorker },
  });

  constructor() {
    super();
    this.registerDataSource({
      policies: this.fetchPolicies.bind(this),
      agents: this.fetchAgents.bind(this),
      carriersAndProducts: this.fetchCarriers.bind(this),
    });
  }

  async setup(account: ExtAccountInfo) {
    const config = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        username: string;
        password: string;
      }>
    >({ account_id: account.account_id, worker: this.name });

    this.service.loadConfig(config.value.credentials);
  }

  async fetchAgents() {
    const count = await this.service.getAgentList({ $top: 1 });
    const data = await this.service.getAgentList({
      $top: count['@odata.count'],
    });
    return data.value;
  }

  @Cache('AllCarriers')
  async fetchCarriers() {
    const count = await this.service.getCarrierDetailList({ $top: 1 });
    const data = await this.service.getCarrierDetailList({
      $top: count['@odata.count'],
    });
    return data.value;
  }

  @Cache('AllPolicies')
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async fetchPolicies(dbData: any, account: ExtAccountInfo) {
    const params = this.task.payload;
    const enable = await checkFeature('enabelnowcertsfilter', '');
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log('EnableNowCertsFilter:', enable);
    const lastSync = enable
      ? await this.dataProcessingService.getLastTask({
          account_id: account.account_id,
          type: DataProcessingTypes.data_sync,
          status: DataProcessingStatuses.COMPLETED,
          worker: this.name,
          entity: 'policies',
        })
      : null;
    let filter =
      enable && lastSync
        ? `changeDate gt ${dayjs(lastSync.created_at).startOf('day').format('YYYY-MM-DDTHH:mm:ss')}Z`
        : 'createDate gt 2023-01-01T00:00:00Z';
    if (this.task.payload?.policyStates?.length) {
      filter = `${filter} and status in (${this.task.payload.policyStates.map((r) => `'${r}'`).join(',')})`;
    }
    const dateRanges = [];
    if (params.startDate) {
      dateRanges.push(
        `changeDate gt ${dayjs(params.startDate).startOf('day').format('YYYY-MM-DDTHH:mm:ss')}Z`
      );
    }
    if (params.endDate) {
      dateRanges.push(
        `changeDate lt ${dayjs(params.endDate).endOf('day').format('YYYY-MM-DDTHH:mm:ss')}Z`
      );
    }
    if (dateRanges.length) {
      filter = dateRanges.join(' and ');
    }
    const count = await this.service.getPolicyDetailList({
      $top: 1,
      $filter: filter,
    });

    if (count['@odata.count'] === 0) {
      return [];
    }
    let hasNext = true;
    let result = [];
    const pageSize = 10000;
    let skip = 0;
    let loops = 0;
    const maxLoops = 50;

    while (hasNext) {
      if (loops++ >= maxLoops) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.warn('maxLoops reached, breaking');
        break;
      }
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.info('fetching page: ', skip / pageSize);
      const data = await this.service.getPolicyDetailList({
        $filter: filter,
        $top: pageSize,
        $skip: skip,
      });
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.info(`fetched ${data.value.length} records`);
      skip += pageSize;
      if (data.value?.length < pageSize) {
        hasNext = false;
      }
      result = result.concat(data.value);
    }

    return result;
  }

  async getPolicyBySyncId(sync_id) {
    const data = await this.service.getPolicyDetailList({
      $filter: `databaseId eq ${sync_id}`,
    });
    return data.value[0];
  }

  determineProductType(productName: string) {
    if (
      productName?.includes('Commercial') ||
      ['Business Owners', "Worker's Compensation"].includes(productName)
    ) {
      return 'Commercial';
    }
    if (
      productName?.includes('Personal') ||
      ['Homeowners', 'Dwelling Fire'].includes(productName)
    ) {
      return 'Personal';
    }
    return null;
  }

  determineTransactionType(
    oldestEffectiveDate: Date | string,
    currentEffectiveDate: Date | string,
    isSame: boolean
  ) {
    if (!oldestEffectiveDate || !currentEffectiveDate) {
      return '';
    }
    if (
      !dayjs.utc(oldestEffectiveDate).isValid() ||
      !dayjs.utc(currentEffectiveDate).isValid()
    ) {
      return '';
    }
    const effectiveDateDayjs = dayjs.utc(oldestEffectiveDate).startOf('day');
    const currentEffectiveDateDayjs = dayjs
      .utc(currentEffectiveDate)
      .startOf('day');
    const firstYear = effectiveDateDayjs.add(1, 'year');

    if (isSame || currentEffectiveDateDayjs.isBefore(firstYear)) {
      return 'First 12 Months';
    }

    return 'After First Year';
  }

  async getTransactionType(params: {
    databaseId: string;
    insuredDatabaseId: string;
  }) {
    const { databaseId } = params;
    const target = await prismaClient.report_data.findFirst({
      where: {
        sync_id: databaseId,
        state: {
          in: [DataStates.ACTIVE, DataStates.GROUPED, DataStates.DUPLICATE],
        },
      },
      accountInject: false,
    });
    const policies = await this.service.getPolicyDetailList({
      $filter: `insuredDatabaseId eq ${params.insuredDatabaseId}`,
      $orderby: 'effectiveDate asc',
    });
    const oldest = policies.value[0];
    return this.determineTransactionType(
      oldest?.effectiveDate,
      target?.effective_date,
      databaseId === oldest?.databaseId
    );
  }

  async updateExtraInfo(data: PolicyDetailList, account: ExtAccountInfo) {
    const { databaseId, insuredDatabaseId } = data;
    let agents = await this.service.getPolicyAgents([databaseId]);
    const details = await this.service.getInsuredDetailList({
      $filter: `id eq ${insuredDatabaseId}`,
    });
    const client = details.value[0];
    if (!client && !agents?.length) {
      return;
    }

    const productName = data.lineOfBusinesses
      ? data.lineOfBusinesses[0]?.lineOfBusinessName
      : undefined;

    const transaction_type = await this.getTransactionType({
      databaseId: data.databaseId,
      insuredDatabaseId: data.insuredDatabaseId,
    });

    // Override agents if Dan Sitz
    agents = agents.map((agent) => {
      if (agent.firstName === 'Dan' && agent.lastName === 'Sitz') {
        return {
          ...agent,
          firstName: 'Mark',
          lastName: 'Rodgers',
          databaseId: undefined,
        };
      }
      return agent;
    });
    const item = {
      transaction_type,
      agent_name: agents?.length
        ? agents
            .map(
              // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              (agent) => agent.firstName?.trim() + ' ' + agent.lastName?.trim()
            )
            .join(';')
        : undefined,
      group_id: client?.insuredId ?? undefined,
      notes: this.determineProductType(productName),
    };
    const record = await prismaClient.report_data.findFirst({
      where: {
        sync_id: databaseId,
        account_id: account.account_id,
        state: {
          in: [DataStates.ACTIVE, DataStates.GROUPED, DataStates.DUPLICATE],
        },
      },
      accountInject: false,
    });
    this.overrideValues({
      datum: item,
      originDatum: record,
      table: 'report_data',
    });
    // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    console.log(`updating ${insuredDatabaseId}, ${JSON.stringify(item)}`);
    await prismaClient.report_data.updateMany({
      where: { sync_id: databaseId, account_id: account.account_id },
      data: item,
    });
    await this.linkingPolicyAgents({
      syncId: databaseId,
      agents,
      opts: { contactStatus: 'active' },
      account_id: account.account_id,
    });
  }
  async afterSyncHook(context: {
    entity: Entity;
    data: unknown;
    dbData: DBData;
    account: ExtAccountInfo;
  }) {
    const { entity, data, account } = context;
    if (entity === 'policies') {
      await runInBatch({
        items: data as PolicyDetailList[],
        onBatch: async (items) => {
          return await Promise.all(
            items.map(
              (item) =>
                this.cloudTaskService
                  .createTask({
                    account: undefined,
                    task_id: nanoid(),
                    type: DataProcessingTypes.data_sync,
                    queue: Queue.NOWCERTS_SYNC,
                    payload: { item, account },
                  })
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  .catch((err) => console.log(err)) // Prevent batch from stopping
            )
          );
        },
        batchSize: 50,
      });
    }
  }

  policyTransform(data: PolicyDetailList): PolicyDatum {
    const productName = data.lineOfBusinesses
      ? data.lineOfBusinesses[0]?.lineOfBusinessName
      : undefined;

    return {
      policy_id: data.number,
      sync_id: data.databaseId,
      writing_carrier_name: data.carrierName,
      payment_mode: data.billingType,
      effective_date: getValidDate(data.effectiveDate),
      policy_status: data.status,
      policy_term_months: data.policyTerm,
      product_name: productName,
      notes: this.determineProductType(productName),
      commissionable_premium_amount: data.totalPremium,
      customer_name: data.insuredCommercialName,

      // The following fields are not available in the data, will be populated via task worker service
      agent_name: undefined,
      transaction_type: undefined,
      group_id: undefined,
      contacts: undefined,
    };
  }

  agentTransform(data: AgentList): AgentDatum {
    return {
      sync_id: data.id,
      city: data.city,
      country: data.county,
      email: data.email,
      first_name: data.firstName,
      last_name: data.lastName,
      geo_state: data.state,
      zip: data.zipCode,
      phone: data.phone,
      phone_type: undefined,
      phone2: data.cellPhone,
      phone2_type: undefined,
      status: data.active ? 'active' : 'inactive',
      birthday: undefined,
      title: undefined,
      middle_name: undefined,
      name: undefined,
      nickname: undefined,
      level: undefined,
      gender: undefined,
      company_name: undefined,
      agent_code: undefined,
      notes: undefined,
    };
  }

  companyTransform(data: CarrierDetailList): CompanyDatum {
    return {
      sync_id: data.id,
      company_name: data.name,
      type: ['Carrier'],
      company_id: data.insuredId,
    };
  }
}
