import { describe, it, expect } from 'vitest';

import { AgencyIntegratorWorker } from '@/services/queue/worker/agencyIntegrator';
import { AwsS3Worker } from '@/services/queue/worker/awss3';
import type { DBData } from '@/services/queue/worker/base';
import { BenefitPointWorker } from '@/services/queue/worker/benefitPoint';
import { MyAdvisorGridsWorker } from '@/services/queue/worker/myadvisorgrids';
import { NowCertsWorker } from '@/services/queue/worker/nowCerts';
import { OneHQWorker } from '@/services/queue/worker/oneHQ';
import { SmartOfficeWorker } from '@/services/queue/worker/smartOffice';
import { TransGlobalWorker } from '@/services/queue/worker/transGlobal';

describe('Workers', () => {
  describe('All workers should transform the data correctly when no data is provided', () => {
    const workers = [
      new AgencyIntegratorWorker(),
      new AwsS3Worker(),
      new BenefitPointWorker(),
      new MyAdvisorGridsWorker(),
      new NowCertsWorker(),
      new OneHQWorker(),
      new SmartOfficeWorker(),
      new TransGlobalWorker(),
    ];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    workers.forEach((worker) => {
      it(`${worker.name} worker`, async () => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.values(worker.entityConfig).forEach((entity) => {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          entity.mappers.forEach((mapper) => {
            if (mapper.transformer) {
              expect(mapper.transformer({}, {} as DBData)).not.toBeNull();
            }
          });
        });
      });
    });
  });
});
