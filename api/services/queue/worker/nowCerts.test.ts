import { describe, expect, it } from 'vitest';

import { container } from '@/ioc';
import { NowCertsWorker } from '@/services/queue/worker/nowCerts';

process.env.PROJECT_ID = 'test-project-id';
process.env.CLOUD_TASKS_URL = 'http://localhost';
process.env.CLOUD_TASKS_JSON = '{}';

describe('NowCerts Worker', () => {
  describe('determineTransactionType', () => {
    const service = container.get<NowCertsWorker>(NowCertsWorker);
    it('should return an empty string if oldestEffectiveDate or currentEffectiveDate is null', () => {
      expect(service.determineTransactionType(null, null, false)).toBe('');
    });
    it('should return an empty string if oldestEffectiveDate or currentEffectiveDate is invalid', () => {
      expect(
        service.determineTransactionType('fakedate', 'faktdate', false)
      ).toBe('');
    });
    it('should return "First 12 Months" if currentEffectiveDate is before the first year', () => {
      expect(
        service.determineTransactionType(
          '2023-08-07T00:00:00-05:00',
          '2022-08-08T00:00:00-05:00',
          false
        )
      ).toBe('First 12 Months');

      expect(
        service.determineTransactionType(
          '2023-08-08T00:00:00-05:00',
          '2022-09-08T00:00:00-05:00',
          false
        )
      ).toBe('First 12 Months');
    });

    it('should return "After First Year" if currentEffectiveDate is after the first year', () => {
      expect(
        service.determineTransactionType(
          '2022-07-29T17:16:00-05:00',
          '2023-08-08T00:00:00-05:00',
          false
        )
      ).toBe('After First Year');
    });
    it('should return "After First Year" if currentEffectiveDate is same  the first year', () => {
      expect(
        service.determineTransactionType(
          '2022-07-29T17:16:00-05:00',
          '2023-07-29T17:16:00-05:00',
          false
        )
      ).toBe('After First Year');
    });
    it('should return "First 12 Months" if current policy is the same as the oldest policy', () => {
      expect(
        service.determineTransactionType(
          '2022-07-30T17:16:00-05:00',
          '2022-07-29T17:16:00-05:00',
          true
        )
      ).toBe('First 12 Months');
    });
  });
});
