import { inject, injectable } from 'inversify';
import { WorkerNames } from 'common/constants';
import type { DocumentProcessingDTO } from 'common/dto/document_processing/dto';
import {
  DocumentStatuses,
  ExtractTableStatuses,
  ImportStatuses,
  ImportMethod,
  ProcessMethod,
  ProcessorSelectorStatuses,
  DocumentPreviewTypes,
  DocumentImportMethod,
} from 'common/globalTypes';
import { document_extract_method } from '@prisma/client';
import { nanoid } from 'nanoid';
import * as Sentry from '@sentry/nextjs';
import { tool } from 'common/tools';
import { DocumentTypes } from 'common/constants/documents';
import { normalizeCurrency } from 'common/tools/normalizer';
import * as XLSX from 'xlsx';
import { BigNumber } from 'bignumber.js';
import { isNill } from 'common/helpers/isNill';

import { prismaClient } from '@/lib/prisma';
import prisma from '@/lib/prisma';
import { ProcessorsService } from '@/services/processors/service';
import { DocumentsService } from '@/services/documents';
import { DocumentFileService } from '@/services/documents/fileService';
import type { IDocumentProcessingWorker } from './base';
import type { QueueTask } from '@/services/queue/types';
import type { ExtAccountInfo } from '@/types';
import { DataProcessingStatuses, DataStates } from '@/types';
import { ExtractTableService } from '@/services/extract-table/service';
import { AdobePDFExtractService } from '@/services/adobe-pdf-extract';
import { GoogleDocumentAIService } from '@/services/google-document-ai';
import { NanonetsService } from '@/services/nanonets/service';
import { HtmlExtractService } from '@/services/htmlExtract/service';
import { AppLoggerService } from '@/services/logger/appLogger';
import { DataProcessService } from '@/services/data_processing';

interface ProcessingStats {
  startTime: number;
  errors: Array<{
    method?: string;
    processor?: string;
    extraction_method?: string;
    general?: string;
    selection?: string;
    autoImport?: string;
    mapping?: string;
    message?: string;
  }>;
  processorCount?: number;
  extractionMethods?: string[];
  validResultsCount?: number;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  bestResultMetrics?: any;
  totalProcessedRows?: number;
  mappingCount?: number;
}

interface ExtractionResult {
  method: string;
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  result: any;
}

interface Processor {
  str_id: string;
  name: string;
  method: string;
  processor: string;
  import_status: ImportStatuses;
}

interface ProcessingResult {
  result: {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data: any[];
    fields: string[];
  };
  import_status: ImportStatuses;
  processor_str_id?: string;
  mapping_str_id?: string;
  metrics?: {
    totalRows: number;
    errorCount: number;
    commissionTotal: string | number;
    status: 'success' | 'partial';
  };
}

interface ProcessingParams {
  documentId: number;
  accountId: string;
  userId: string;
}

interface MappingConfig {
  str_id: string;
  mapping: Record<string, { colIndex: number; colHeader: string } | number>;
}

@injectable()
export class DocumentProcessingWorker
  implements IDocumentProcessingWorker<DocumentProcessingDTO>
{
  name = WorkerNames.DocumentProcessingWorker;

  @inject(ProcessorsService) private processorsService: ProcessorsService;
  @inject(DocumentsService) private documentsService: DocumentsService;
  @inject(DocumentFileService) private documentFileService: DocumentFileService;
  @inject(ExtractTableService) private extractTableService: ExtractTableService;
  @inject(GoogleDocumentAIService)
  private googleDocumentAIService: GoogleDocumentAIService;
  @inject(AdobePDFExtractService)
  private adobePDFExtractService: AdobePDFExtractService;
  @inject(NanonetsService) private nanonetsService: NanonetsService;
  @inject(HtmlExtractService) private htmlExtractService: HtmlExtractService;
  @inject(DataProcessService) private dataProcessingService: DataProcessService;
  private logger: AppLoggerService = new AppLoggerService();

  // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async setup(_account: ExtAccountInfo): Promise<void> {}

  private async completeProcessing(
    task: QueueTask<DocumentProcessingDTO>,
    taskId: string,
    status: DataProcessingStatuses,
    stats: ProcessingStats,
    notes: string,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    output?: any,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    documentUpdateData?: any
  ): Promise<void> {
    const MAX_NOTES_LENGTH = 100;
    const displayNotes =
      notes.length > MAX_NOTES_LENGTH ? 'Internal issue' : notes;

    await this.dataProcessingService.updateTaskStatus({
      str_id: taskId,
      status: status,
      stats: stats,
      notes: displayNotes,
      output: output,
    });

    const documentData = {
      processing_task_id: taskId,
      updated_at: new Date(),
      updated_by: task.payload.uid,
      ...documentUpdateData,
    };

    await prismaClient.documents.update({
      where: {
        str_id: task.payload.document_str_id,
        account_id: task.payload.account_id,
      },
      data: documentData,
    });
  }

  private createExtractMethodHandlers(
    task: QueueTask<DocumentProcessingDTO>,
    stats: ProcessingStats
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Record<string, () => Promise<any>> {
    const { account_id, uid, document_id, file_path } = task.payload;

    return {
      [document_extract_method.documentAI]: async () => {
        try {
          const result =
            await this.googleDocumentAIService.processDocument(file_path);
          return result.data;
        } catch (error) {
          Sentry.captureException(error);
          stats.errors.push({
            method: document_extract_method.documentAI,
            message: error.message,
          });
        }
      },
      [document_extract_method.adobeExtract]: async () => {
        try {
          const result =
            await this.adobePDFExtractService.extractTableData(file_path);
          return result.data;
        } catch (error) {
          Sentry.captureException(error);
          stats.errors.push({
            method: document_extract_method.adobeExtract,
            message: error.message,
          });
        }
      },
      [document_extract_method.nanonets]: async () => {
        try {
          const processingParams: ProcessingParams = {
            documentId: document_id,
            accountId: account_id,
            userId: uid,
          };
          return await this.nanonetsService.processDocument(processingParams);
        } catch (error) {
          Sentry.captureException(error);
          stats.errors.push({
            method: document_extract_method.nanonets,
            message: error.message,
          });
        }
      },
      [document_extract_method.htmlExtract]: async () => {
        try {
          const processingParams: ProcessingParams = {
            documentId: document_id,
            accountId: account_id,
            userId: uid,
          };
          return await this.htmlExtractService.processDocument(
            processingParams
          );
        } catch (error) {
          Sentry.captureException(error);
          stats.errors.push({
            method: document_extract_method.htmlExtract,
            message: error.message,
          });
        }
      },
      [document_extract_method.extractTable]: async () => {
        try {
          const processingParams: ProcessingParams = {
            documentId: document_id,
            accountId: account_id,
            userId: uid,
          };
          return await this.extractTableService.processDocument(
            processingParams
          );
        } catch (error) {
          Sentry.captureException(error);
          stats.errors.push({
            method: document_extract_method.extractTable,
            message: error.message,
          });
        }
      },
    };
  }

  private async extractDocument(
    task: QueueTask<DocumentProcessingDTO>,
    processors: Processor[],
    stats: ProcessingStats
  ): Promise<ExtractionResult[]> {
    const { account_id, uid, document_id } = task.payload;

    const extractMethodHandlers = this.createExtractMethodHandlers(task, stats);
    const extractMethods = [...new Set(processors.map((p) => p.method))];
    stats.extractionMethods = extractMethods;
    const extractionResults: ExtractionResult[] = [];

    for (const method of extractMethods) {
      const handler = extractMethodHandlers[method];
      if (!handler) {
        const error = new Error(`No handler found for method: ${method}`);
        stats.errors.push({
          method,
          message: error.message,
        });
      } else {
        try {
          const result = await handler();
          const shouldCreateExtraction =
            result != null &&
            (typeof result === 'object'
              ? Object.keys(result).length > 0
              : true);

          if (shouldCreateExtraction) {
            await prisma.extractions.create({
              data: {
                document_id,
                account_id,
                uid,
                method: document_extract_method[method],
                output: JSON.stringify(result),
                result: ExtractTableStatuses.SUCCESS,
                result_id: result?.JobId || null,
                str_id: nanoid(),
              },
            });
            extractionResults.push({
              method: document_extract_method[method],
              result,
            });
          }
        } catch (error) {
          stats.errors.push({
            method,
            message: error.message,
          });
        }
      }
    }
    return extractionResults;
  }

  private async extractDefaultPdf(
    task: QueueTask<DocumentProcessingDTO>,
    stats: ProcessingStats
  ): Promise<ExtractionResult[]> {
    const { account_id, uid, document_id } = task.payload;

    try {
      const processingParams: ProcessingParams = {
        documentId: document_id,
        accountId: account_id,
        userId: uid,
      };
      const result =
        await this.extractTableService.processDocument(processingParams);

      const shouldCreateExtraction =
        result != null &&
        (typeof result === 'object' ? Object.keys(result).length > 0 : true);

      if (shouldCreateExtraction) {
        await prisma.extractions.create({
          data: {
            document_id,
            account_id,
            uid,
            method: document_extract_method.extractTable,
            output: JSON.stringify(result),
            result: ExtractTableStatuses.SUCCESS,
            result_id: result?.JobId || null,
            str_id: nanoid(),
          },
        });
      }

      stats.extractionMethods = [document_extract_method.extractTable];
      return [{ method: document_extract_method.extractTable, result }];
    } catch (error) {
      Sentry.captureException(error);
      stats.errors.push({
        method: document_extract_method.extractTable,
        message: error.message,
      });
      throw error;
    }
  }

  private async getSpreadsheet(
    task: QueueTask<DocumentProcessingDTO>,
    stats: ProcessingStats
  ): Promise<ExtractionResult[]> {
    const { account_id, document_str_id } = task.payload;

    try {
      const document = await prismaClient.documents.findUnique({
        where: {
          str_id: document_str_id,
          state: DataStates.ACTIVE,
          account_id,
        },
      });

      if (!document) {
        const error = new Error('Document not found');
        stats.errors.push({
          method: ProcessorSelectorStatuses.SPREADSHEET,
          message: error.message,
        });
        throw error;
      }

      const fileBuffer =
        await this.documentFileService.getFileFromStorage(document);

      const workbook = XLSX.read(fileBuffer, { type: 'buffer' });
      const sheetNames = workbook.SheetNames;

      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const sheetsData: Record<string, any[]> = {};

      for (const sheetName of sheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, {
          header: 1,
          raw: false,
          blankrows: false,
        });

        sheetsData[sheetName] = jsonData;
      }

      if (sheetNames.length > 1) {
        const combinedData = [];
        let isFirstSheet = true;

        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        Object.entries(sheetsData).forEach(([sheetName, sheetData]) => {
          try {
            if (sheetData && Array.isArray(sheetData) && sheetData.length > 0) {
              if (!isFirstSheet) {
                combinedData.push([]);
              }

              combinedData.push(['Sheet name:', sheetName]);

              // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              sheetData.forEach((row) => {
                if (row && Array.isArray(row)) {
                  const cleaned = [...row];
                  while (
                    cleaned.length > 0 &&
                    isNill(cleaned[cleaned.length - 1])
                  ) {
                    cleaned.pop();
                  }

                  if (cleaned.every((cell) => isNill(cell))) {
                    return;
                  }

                  if (cleaned.length > 0) {
                    combinedData.push(cleaned);
                  }
                }
              });

              isFirstSheet = false;
            }
          } catch (error) {
            this.logger.warn(
              `Failed to process sheet "${sheetName}" for combination:`,
              error
            );
          }
        });

        // biome-ignore lint/complexity/useLiteralKeys: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        sheetsData['All'] = combinedData;
      }

      stats.extractionMethods = [ProcessorSelectorStatuses.SPREADSHEET];
      return [
        { method: ProcessorSelectorStatuses.SPREADSHEET, result: sheetsData },
      ];
    } catch (error) {
      Sentry.captureException(error);
      stats.errors.push({
        method: ProcessorSelectorStatuses.SPREADSHEET,
        message: error.message,
      });
      throw error;
    }
  }

  private async getMappingsForCompany(
    task: QueueTask<DocumentProcessingDTO>
  ): Promise<MappingConfig[]> {
    const { companies } = task.payload;

    if (!companies?.str_id) {
      return [];
    }

    try {
      const companyProfiles =
        await prismaClient.companies_document_profiles.findMany({
          where: {
            company_str_id: companies.str_id,
            auto_mapping_id: {
              not: null,
            },
            state: DataStates.ACTIVE,
          },
          include: {
            auto_mapping: {
              where: {
                state: DataStates.ACTIVE,
              },
            },
          },
        });

      const mappings: MappingConfig[] = [];

      for (const profile of companyProfiles) {
        if (profile.auto_mapping?.mapping) {
          try {
            const mappingData =
              typeof profile.auto_mapping.mapping === 'string'
                ? JSON.parse(profile.auto_mapping.mapping)
                : profile.auto_mapping.mapping;

            mappings.push({
              str_id: profile.auto_mapping.str_id,
              mapping: mappingData,
            });
          } catch (parseError) {
            this.logger.error('Failed to parse mapping data', {
              mappingId: profile.auto_mapping.str_id,
              error: parseError,
            });
          }
        }
      }

      return mappings;
    } catch (error) {
      this.logger.error('Failed to get mappings for company', {
        companyId: companies.str_id,
        error: error,
      });
      return [];
    }
  }

  private async processWithMappings(
    task: QueueTask<DocumentProcessingDTO>,
    extractions: ExtractionResult[],
    stats: ProcessingStats
  ): Promise<ProcessingResult[]> {
    try {
      this.logger.info('Starting mapping processing for spreadsheet');

      const mappings = await this.getMappingsForCompany(task);
      stats.mappingCount = mappings.length;

      if (mappings.length === 0) {
        this.logger.info('No mappings found for company');
        return [];
      }

      this.logger.info(`Found ${mappings.length} mappings to process`);

      const spreadsheetExtraction = extractions.find(
        (extraction) =>
          extraction.method === ProcessorSelectorStatuses.SPREADSHEET
      );

      if (!spreadsheetExtraction) {
        this.logger.error(
          'No spreadsheet extraction found for mapping processing'
        );
        stats.errors.push({
          mapping: 'No spreadsheet extraction found',
        });
        return [];
      }

      const sheetsData = spreadsheetExtraction.result;
      const mappingResults: ProcessingResult[] = [];

      for (const mappingConfig of mappings) {
        this.logger.info(`Processing mapping ${mappingConfig.str_id}`);

        try {
          let foundValidSheet = false;
          let mappingResult: ProcessingResult | null = null;

          for (const sheetName in sheetsData) {
            try {
              const sheetData = sheetsData[sheetName];

              if (!Array.isArray(sheetData) || sheetData.length === 0) {
                continue;
              }

              const processedData = await this.processMappingForSheet(
                sheetData,
                mappingConfig.mapping,
                task.payload.type as DocumentTypes
              );

              if (processedData?.data?.length) {
                mappingResult = {
                  result: processedData,
                  import_status: ImportStatuses.AUTO_IMPORT,
                  mapping_str_id: mappingConfig.str_id,
                };
                foundValidSheet = true;
                this.logger.info(
                  `Mapping ${mappingConfig.str_id} succeeded on sheet ${sheetName}`
                );
                break;
              }
            } catch (sheetError) {
              this.logger.info(
                `Mapping ${mappingConfig.str_id} failed on sheet ${sheetName}:`,
                sheetError.message
              );
            }
          }

          if (foundValidSheet && mappingResult) {
            const errorCount = mappingResult.result.data.filter((row) =>
              Object.values(row).some((val) => val == null || val === '')
            ).length;

            let commissionTotal: string | number = 'Invalid';
            const commissionIndex = mappingResult.result.fields.findIndex(
              (field) => field.toLowerCase() === 'commission_amount'
            );

            if (
              commissionIndex !== -1 &&
              mappingResult.result.data.length > 0
            ) {
              const total = mappingResult.result.data.reduce((sum, row) => {
                const amount = normalizeCurrency(row[commissionIndex]);
                return amount ? sum + amount : sum;
              }, 0);

              commissionTotal = total === 0 ? 'Invalid' : total;
            }

            if (commissionTotal !== 'Invalid') {
              commissionTotal = Number(commissionTotal).toFixed(2);
            }

            mappingResult.metrics = {
              totalRows: mappingResult.result.data.length,
              errorCount,
              commissionTotal,
              status: errorCount > 0 ? 'partial' : 'success',
            };

            mappingResults.push(mappingResult);
          } else {
            this.logger.info(
              `No valid result found for mapping ${mappingConfig.str_id}`
            );
            stats.errors.push({
              mapping: `No valid result found for mapping ${mappingConfig.str_id}`,
            });
          }
        } catch (error) {
          this.logger.error(
            `Error processing mapping ${mappingConfig.str_id}:`,
            error.message
          );
          stats.errors.push({
            mapping: `Mapping ${mappingConfig.str_id} failed: ${error.message}`,
          });
        }
      }

      this.logger.info(
        `Mapping processing completed. Found ${mappingResults.length} valid results`
      );
      return mappingResults;
    } catch (error) {
      this.logger.error('General error in processWithMappings:', error.message);
      stats.errors.push({
        mapping: error.message,
      });
      return [];
    }
  }

  private async processMappingForSheet(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    sheetData: any[],
    mapping: Record<string, { colIndex: number; colHeader: string } | number>,
    documentType: DocumentTypes
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  ): Promise<{ data: any[]; fields: string[] }> {
    if (!Array.isArray(sheetData) || sheetData.length === 0) {
      throw new Error('Invalid sheet data');
    }

    const hasColHeaders = Object.values(mapping).some(
      (mappingInfo) => typeof mappingInfo === 'object' && mappingInfo?.colHeader
    );

    this.logger.info('Processing mapping for sheet', {
      sheetDataLength: sheetData.length,
      hasColHeaders,
      mappingFields: Object.keys(mapping),
    });

    const fields = Object.keys(mapping);

    const processedData = sheetData.map((row) => {
      if (!Array.isArray(row)) {
        return [];
      }

      return fields.map((fieldName) => {
        const mappingInfo = mapping[fieldName];
        let columnIndex: number;

        if (typeof mappingInfo === 'object' && mappingInfo !== null) {
          columnIndex = mappingInfo.colIndex;
        } else if (typeof mappingInfo === 'number') {
          columnIndex = mappingInfo;
        } else {
          return null;
        }

        if (columnIndex >= 0 && columnIndex < row.length) {
          return row[columnIndex];
        }

        return null;
      });
    });

    const filteredData = processedData.filter((row) =>
      row.some((cell) => cell != null && cell !== '')
    );

    let finalData = filteredData;

    if (hasColHeaders) {
      if (filteredData.length > 0) {
        finalData = filteredData.slice(1);
        this.logger.info(
          'Mapping has colHeader config, removed header from result'
        );
      }
    } else {
      if (filteredData.length > 0) {
        const firstRow = filteredData[0];
        const isHeaderRow = this.detectHeaderRow(firstRow);

        if (isHeaderRow) {
          finalData = filteredData.slice(1);
          this.logger.info('Detected and removed header from result');
        } else {
          this.logger.info('No header detected in result data');
        }
      }
    }

    if (finalData.length === 0) {
      throw new Error('No valid data found after processing mapping');
    }

    const rawResult = {
      data: finalData,
      fields: fields,
    };

    this.logger.info('Calling normalizeData', {
      dataLength: finalData.length,
      fieldsLength: fields.length,
    });

    const normalizedResult = await this.documentsService.normalizeData(
      rawResult,
      documentType
    );

    this.logger.info('normalizeData completed', {
      normalizedDataLength: normalizedResult?.data?.length,
    });

    return normalizedResult;
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private detectHeaderRow(row: any[]): boolean {
    if (!Array.isArray(row)) return false;

    const headerKeywords = [
      'policy',
      'product',
      'carrier',
      'effective',
      'premium',
      'commission',
    ];

    const hasHeaderKeywords = row.some((cell) => {
      if (typeof cell !== 'string') return false;
      const cellLower = cell.toLowerCase();
      return headerKeywords.some((keyword) => cellLower.includes(keyword));
    });

    return hasHeaderKeywords;
  }

  private async processWithProcessors(
    task: QueueTask<DocumentProcessingDTO>,
    processors: Processor[],
    extractions: ExtractionResult[],
    stats: ProcessingStats
  ): Promise<ProcessingResult[]> {
    try {
      const processingResults: ProcessingResult[] = [];

      for (const processor of processors) {
        const matchingExtraction = extractions.find(
          (extraction) => extraction.method === processor.method
        );

        if (!matchingExtraction) {
          this.logger.error(
            `No matching extraction found for processor ${processor.name} with method ${processor.method}`
          );
          stats.errors.push({
            processor: processor.name,
            message: `No matching extraction found for processor with method ${processor.method}`,
          });
          continue;
        }

        this.logger.info(
          `Processing extraction with processor ${processor.name}`
        );

        try {
          // biome-ignore lint/security/noGlobalEval: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          const processorFn = eval(`${processor.processor}`);
          const libs = {
            document: task.payload,
            tools: tool,
          };

          // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          let processingResult;
          if (
            matchingExtraction.method === ProcessorSelectorStatuses.SPREADSHEET
          ) {
            const sheetsData = matchingExtraction.result;
            let foundValidSheet = false;

            for (const sheetName in sheetsData) {
              try {
                const sheetData = sheetsData[sheetName];
                const sheetResult = processorFn(sheetData, libs);

                if (sheetResult?.data?.length) {
                  processingResult = sheetResult;
                  foundValidSheet = true;
                  break;
                }
              } catch (sheetError) {
                this.logger.info(
                  `Processing sheet ${sheetName} failed for processor ${processor.name}:`,
                  sheetError
                );
                stats.errors.push({
                  processor: processor.name,
                  message: `Sheet ${sheetName} processing failed: ${sheetError.message}`,
                });
              }
            }

            if (!foundValidSheet) {
              this.logger.info(
                'No valid result found in any sheet for processor'
              );
              stats.errors.push({
                processor: processor.name,
                message: 'No valid result found in any sheet',
              });
              continue;
            }
          } else {
            try {
              processingResult = processorFn(matchingExtraction.result, libs);
            } catch (processorError) {
              this.logger.info(
                `Processing extraction failed for processor ${processor.name}:`,
                processorError
              );
              stats.errors.push({
                processor: processor.name,
                message: `Processing failed: ${processorError.message}`,
              });
              continue;
            }
          }

          processingResults.push({
            result: await this.documentsService.normalizeData(
              processingResult,
              task.payload.type as DocumentTypes
            ),
            import_status: processor.import_status,
            processor_str_id: processor.str_id,
          });
        } catch (error) {
          this.logger.error(
            `Error processing extraction with processor ${processor.name}:`,
            error.message
          );
          stats.errors.push({
            processor: processor.name,
            extraction_method: matchingExtraction.method,
            message: error.message,
          });
        }
      }

      return processingResults;
    } catch (error) {
      this.logger.error(
        'General error in processWithProcessors:',
        error.message
      );
      stats.errors.push({
        general: error.message,
      });
      return [];
    }
  }

  private async selectBestProcessingResult(
    task: QueueTask<DocumentProcessingDTO>,
    validResults: ProcessingResult[],
    stats: ProcessingStats
  ): Promise<ProcessingResult | null> {
    try {
      const resultsWithMetrics = validResults.map((result) => {
        const errorCount = result.result.data.filter((row) =>
          Object.values(row).some((val) => val == null || val === '')
        ).length;

        let commissionTotal: string | number = 'Invalid';
        const commissionIndex = result.result.fields.findIndex(
          (field) => field.toLowerCase() === 'commission_amount'
        );

        if (commissionIndex !== -1 && result.result.data.length > 0) {
          const total = result.result.data.reduce((sum, row) => {
            const amount = normalizeCurrency(row[commissionIndex]);
            return amount ? sum + amount : sum;
          }, 0);

          commissionTotal = total === 0 ? 'Invalid' : total;
        }

        if (commissionTotal !== 'Invalid') {
          commissionTotal = Number(commissionTotal).toFixed(2);
        }

        return {
          ...result,
          metrics: {
            totalRows: result.result.data.length,
            errorCount,
            commissionTotal,
            status: errorCount > 0 ? 'partial' : 'success',
          },
        };
      });

      stats.validResultsCount = resultsWithMetrics.length;

      const sortedResults = resultsWithMetrics.sort((a, b) => {
        const statusPriority = {
          success: 2,
          partial: 1,
        };
        const statusDiff =
          statusPriority[b.metrics.status] - statusPriority[a.metrics.status];
        if (statusDiff !== 0) return statusDiff;

        const aHasValidCommission = a.metrics.commissionTotal !== 'Invalid';
        const bHasValidCommission = b.metrics.commissionTotal !== 'Invalid';

        if (aHasValidCommission !== bHasValidCommission) {
          return aHasValidCommission ? -1 : 1;
        }

        if (
          task.payload.statement_amount &&
          aHasValidCommission &&
          bHasValidCommission
        ) {
          const statementAmount = +task.payload.statement_amount;
          const aMatch = statementAmount === Number(a.metrics.commissionTotal);
          const bMatch = statementAmount === Number(b.metrics.commissionTotal);
          if (aMatch !== bMatch) return aMatch ? -1 : 1;
        }

        return (b.metrics.totalRows || 0) - (a.metrics.totalRows || 0);
      });

      if (sortedResults.length > 0) {
        stats.bestResultMetrics = sortedResults[0].metrics;
      }

      return sortedResults[0] as ProcessingResult;
    } catch (error) {
      Sentry.captureException(error);
      stats.errors.push({
        selection: error.message,
      });
      return null;
    }
  }

  private async processAutoImport(
    task: QueueTask<DocumentProcessingDTO>,
    bestResult: ProcessingResult,
    stats: ProcessingStats,
    taskId: string
  ): Promise<{
    stats: ProcessingStats;
    success: boolean;
    importId?: string;
    error?: string;
    duration: number;
  }> {
    try {
      this.logger.info('Starting auto import process');

      const importId = nanoid();

      const fields = bestResult.result.fields || [];
      const rows = bestResult.result.data || [];

      const normalizedData = rows.map((row) => {
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        const record: Record<string, any> = fields.reduce(
          (obj, field, index) => {
            obj[field] = row[index];
            return obj;
          },
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          {} as Record<string, any>
        );

        if (record.contacts && typeof record.contacts === 'string') {
          record.contacts = record.contacts.split(',');
        }
        if (record.commission_rate) {
          record.new_commission_rate =
            parseFloat(
              String(record.commission_rate).replace(/[^0-9.-]+/g, '')
            ) / 100 || 0;
          record.commission_rate_percent = record.commission_rate_percent
            ? BigNumber(record.commission_rate_percent).toNumber()
            : BigNumber(record.new_commission_rate).times(100).toNumber();
        }
        if (record.carrier_rate) {
          record.new_carrier_rate =
            parseFloat(String(record.carrier_rate).replace(/[^0-9.-]+/g, '')) /
              100 || 0;
        }
        // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        if (record.split_percentage && record.split_percentage.includes('%')) {
          record.split_percentage =
            parseFloat(
              String(record.split_percentage).replace(/[^0-9.-]+/g, '')
            ) / 100 || 0;
        }
        if (record.tags != null && !Array.isArray(record.tags)) {
          record.tags = [record.tags];
        }

        return {
          ...record,
          type: task.payload.type,
          document_id: task.payload.document_str_id,
          import_id: importId,
          account_id: task.payload.account_id,
          uid: task.payload.uid,
          str_id: nanoid(),
          created_by: task.payload.uid,
          created_at: new Date(),
        };
      });

      const insertResults: Array<{ count: number }> = [];
      if (task.payload.type === DocumentTypes.STATEMENT) {
        const chunkPromises = [];
        for (let i = 0; i < normalizedData.length; i += 1000) {
          const chunk = normalizedData.slice(i, i + 1000);
          chunkPromises.push(
            prismaClient.statement_data.createMany({ data: chunk })
          );
        }
        const results = await Promise.all(chunkPromises);
        insertResults.push(...results);
      } else if (task.payload.type === DocumentTypes.REPORT) {
        const chunkPromises = [];
        for (let i = 0; i < normalizedData.length; i += 1000) {
          const chunk = normalizedData.slice(i, i + 1000);
          chunkPromises.push(
            prismaClient.report_data.createMany({ data: chunk })
          );
        }
        const results = await Promise.all(chunkPromises);
        insertResults.push(...results);
      }

      const totalInserted = insertResults.reduce(
        (sum, result) => sum + result.count,
        0
      );

      stats.totalProcessedRows = totalInserted;

      await prismaClient.data_imports.create({
        data: {
          str_id: importId,
          account_id: task.payload.account_id,
          created_by: task.payload.uid,
          created_at: new Date(),
          process_duration: Date.now() - stats.startTime,
          summed_total_amount:
            bestResult.metrics?.commissionTotal !== 'Invalid'
              ? bestResult.metrics?.commissionTotal
              : null,
          count: totalInserted,
          type: task.payload.type as
            | DocumentTypes.REPORT
            | DocumentTypes.STATEMENT,
          status: totalInserted > 0 ? 'Success' : 'Failed',
          import_method: ImportMethod.AUTO,
          state: DataStates.ACTIVE,
          metadata: {
            status: totalInserted > 0 ? 'Success' : 'Failed',
            count: totalInserted,
          },
          document: {
            connect: { str_id: task.payload.document_str_id },
          },
          ...(task.payload.companies?.str_id && {
            company: {
              connect: { str_id: task.payload.companies.str_id },
            },
          }),
          ...(bestResult.processor_str_id && {
            processor: {
              connect: { str_id: bestResult.processor_str_id },
            },
          }),
          ...(bestResult.mapping_str_id && {
            mapping: {
              connect: { str_id: bestResult.mapping_str_id },
            },
          }),
        },
      });

      await this.completeProcessing(
        task,
        taskId,
        DataProcessingStatuses.COMPLETED,
        stats,
        `Successfully auto-imported ${totalInserted} records using ${bestResult.processor_str_id ? 'processor' : 'mapping'}`,
        undefined,
        {
          status: DocumentStatuses.PROCESSED,
          process_method: ProcessMethod.AUTO,
          processor: bestResult.processor_str_id,
          import_id: importId,
          imported_at: new Date().toISOString(),
          method: bestResult.processor_str_id
            ? DocumentImportMethod.PROCESSOR
            : DocumentImportMethod.MAPPING,
          validations: {},
        }
      );

      this.logger.info(`Successfully auto-imported ${totalInserted} records`);
      return {
        stats,
        success: true,
        importId,
        duration: Date.now() - stats.startTime,
      };
    } catch (error) {
      this.logger.error('Auto import failed:', error.message);
      Sentry.captureException(error);
      stats.errors.push({
        autoImport: error.message,
      });

      await this.completeProcessing(
        task,
        taskId,
        DataProcessingStatuses.ERROR,
        stats,
        `${error.message}`
      );

      return {
        stats,
        success: false,
        error: error.message,
        duration: Date.now() - stats.startTime,
      };
    }
  }

  private async processRequestReview(
    task: QueueTask<DocumentProcessingDTO>,
    validResults: ProcessingResult[],
    stats: ProcessingStats,
    taskId: string,
    reason?: string
  ): Promise<{ stats: ProcessingStats; success: boolean; duration: number }> {
    try {
      const reviewReason = reason || 'Processing requires manual review';
      await this.completeProcessing(
        task,
        taskId,
        DataProcessingStatuses.COMPLETED,
        stats,
        `${reviewReason}`,
        validResults.length > 0
          ? {
              validResults: validResults.map((r) => ({
                processorId: r.processor_str_id,
                mappingId: r.mapping_str_id,
                metrics: r.metrics,
              })),
            }
          : null,
        {
          status: DocumentStatuses.PENDING_REVIEW,
        }
      );

      this.logger.info('Document marked for manual review');
      return { stats, success: true, duration: Date.now() - stats.startTime };
    } catch (error) {
      await this.completeProcessing(
        task,
        taskId,
        DataProcessingStatuses.ERROR,
        stats,
        `${error.message}`
      );

      return {
        stats: {
          ...stats,
          errors: [...stats.errors, { message: error.message }],
        },
        success: false,
        duration: Date.now() - stats.startTime,
      };
    }
  }

  private shouldTryMappings(
    processors: Processor[],
    validResults: ProcessingResult[],
    fileType: string
  ): boolean {
    if (fileType !== ProcessorSelectorStatuses.SPREADSHEET) {
      this.logger.info('File type is not spreadsheet, skipping mappings');
      return false;
    }

    if (processors.length === 0) {
      this.logger.info('No processors found, will try mappings');
      return true;
    }

    const allProcessorsStatusNone = processors.every(
      (processor) =>
        !processor.import_status ||
        processor.import_status === ImportStatuses.NONE
    );

    if (allProcessorsStatusNone) {
      this.logger.info(
        'All processors have import status NONE, will try mappings'
      );
      return true;
    }

    if (validResults.length === 0) {
      this.logger.info('No valid processor results, will try mappings');
      return true;
    }

    this.logger.info('Processors produced valid results, skipping mappings');
    return false;
  }

  async process(
    task: QueueTask<DocumentProcessingDTO>
  ): Promise<{ stats: ProcessingStats; duration: number }> {
    const startTime = Date.now();
    const stats: ProcessingStats = { startTime, errors: [] };

    const existingTask = await this.dataProcessingService.findByTaskId(
      task.task_id
    );

    if (!existingTask) {
      this.logger.error(
        `No data processing record found for task ID: ${task.task_id}`
      );
      return { stats, duration: Date.now() - startTime };
    }

    await this.dataProcessingService.updateTaskStatus({
      str_id: task.task_id,
      status: DataProcessingStatuses.PROCESSING,
      params: task.payload?.file_name,
    });

    try {
      const { account_id, document_str_id, file_type, companies } =
        task.payload;

      let method: string;
      if (!file_type || file_type === ProcessorSelectorStatuses.SPREADSHEET) {
        method = ProcessorSelectorStatuses.SPREADSHEET;
      } else if (file_type === DocumentPreviewTypes.HTML) {
        method = document_extract_method.htmlExtract;
      } else {
        method = ProcessorSelectorStatuses.MULTI;
      }

      this.logger.info('Starting matching processors for document');
      const processors = await this.processorsService.getProcessorsByDocument({
        document_id: document_str_id,
        account_id: account_id,
        companies: companies,
        method: method,
      });

      stats.processorCount = processors.length;

      await prismaClient.documents.update({
        where: { str_id: document_str_id, state: DataStates.ACTIVE },
        data: {
          status: DocumentStatuses.PROCESSING,
          updated_at: new Date(),
          updated_by: task.account.uid,
        },
      });

      this.logger.info('Starting extracting document');
      let extractions: ExtractionResult[] | null;

      if (
        processors.length &&
        file_type !== ProcessorSelectorStatuses.SPREADSHEET
      ) {
        extractions = await this.extractDocument(
          task,
          processors.map((processor) => ({
            ...processor,
            import_status: processor.import_status as ImportStatuses,
          })),
          stats
        );
      } else if (file_type === 'pdf') {
        extractions = await this.extractDefaultPdf(task, stats);
      } else {
        extractions = null;
      }

      await this.dataProcessingService.updateTaskStatus({
        str_id: task.task_id,
        status:
          processors.length > 0
            ? DataProcessingStatuses.PROCESSING
            : DataProcessingStatuses.COMPLETED,
        stats: stats,
        output: extractions ? { extractionCount: extractions.length } : null,
        notes: extractions
          ? `Extracted document using ${extractions.length} method(s)`
          : 'No extraction methods available',
      });

      if (
        !task.payload.type ||
        ![DocumentTypes.STATEMENT, DocumentTypes.REPORT].includes(
          task.payload.type as DocumentTypes
        )
      ) {
        this.logger.info(
          'No document type or matched document type, skipping processing'
        );

        const errorMessage = 'Invalid or missing document type';
        stats.errors.push({ general: errorMessage });

        await this.completeProcessing(
          task,
          task.task_id,
          DataProcessingStatuses.ERROR,
          stats,
          errorMessage
        );

        return { stats, duration: Date.now() - startTime };
      }

      if (file_type === ProcessorSelectorStatuses.SPREADSHEET) {
        extractions = await this.getSpreadsheet(task, stats);
      }

      if (!extractions || extractions.length === 0) {
        this.logger.info('No extraction results, skipping processing');

        const errorMessage = 'No extraction results available';
        stats.errors.push({ general: errorMessage });

        await this.completeProcessing(
          task,
          task.task_id,
          DataProcessingStatuses.ERROR,
          stats,
          errorMessage
        );

        return { stats, duration: Date.now() - startTime };
      }

      let processingResults: ProcessingResult[] = [];

      if (processors.length > 0) {
        this.logger.info('Starting processing document with active processors');
        const filteredProcessors = processors.filter(
          (processor) =>
            processor.import_status &&
            processor.import_status !== ImportStatuses.NONE
        );

        if (filteredProcessors.length > 0) {
          processingResults = await this.processWithProcessors(
            task,
            filteredProcessors.map((processor) => ({
              ...processor,
              import_status: processor.import_status as ImportStatuses,
            })),
            extractions,
            stats
          );

          await this.dataProcessingService.updateTaskStatus({
            str_id: task.task_id,
            status: DataProcessingStatuses.PROCESSING,
            stats: stats,
            output: {
              processingResultsCount: processingResults.length,
              processorIds: filteredProcessors.map((p) => p.str_id),
            },
            notes: `Processed document with ${filteredProcessors.length} processor(s)`,
          });
        }
      }

      let validResults = processingResults.filter(
        (result) =>
          // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          result &&
          result.result &&
          result.result.data &&
          Array.isArray(result.result.data) &&
          result.result.data.length > 0
      );

      if (
        this.shouldTryMappings(
          processors.map((processor) => ({
            ...processor,
            import_status: processor.import_status as ImportStatuses,
          })),
          validResults,
          file_type
        )
      ) {
        this.logger.info('Attempting to process with mappings');

        const mappingResults = await this.processWithMappings(
          task,
          extractions,
          stats
        );

        if (mappingResults.length > 0) {
          this.logger.info(
            `Found ${mappingResults.length} valid mapping results`
          );
        } else {
          this.logger.info('No valid mapping results found');
          stats.errors.push({
            mapping: 'No valid mapping results found',
          });
        }

        await this.dataProcessingService.updateTaskStatus({
          str_id: task.task_id,
          status: DataProcessingStatuses.PROCESSING,
          stats: stats,
          output: {
            mappingResultsCount: mappingResults.length,
            mappingCount: stats.mappingCount,
          },
          notes: `Processed document with ${stats.mappingCount || 0} mapping(s)`,
        });

        validResults = [...validResults, ...mappingResults];
      }

      if (validResults.length === 0) {
        this.logger.info(
          'No valid processing results found from processors or mappings.'
        );

        const errorMessage = 'No valid processing results found';
        stats.errors.push({ general: errorMessage });

        await this.completeProcessing(
          task,
          task.task_id,
          DataProcessingStatuses.ERROR,
          stats,
          errorMessage
        );

        return { stats, duration: Date.now() - startTime };
      }

      const bestResult = await this.selectBestProcessingResult(
        task,
        validResults,
        stats
      );

      if (!bestResult) {
        this.logger.info(
          'Could not determine best processing result, sending to pending review'
        );
        return this.processRequestReview(
          task,
          validResults,
          stats,
          task.task_id,
          'Could not determine best processing result'
        );
      }

      const canAutoImport =
        bestResult.import_status === ImportStatuses.AUTO_IMPORT &&
        bestResult.metrics?.commissionTotal !== 'Invalid' &&
        (!task.payload.statement_amount ||
          // biome-ignore lint/suspicious/noDoubleEquals: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          +task.payload.statement_amount ==
            bestResult.metrics?.commissionTotal);

      if (canAutoImport) {
        const source = bestResult.processor_str_id ? 'processor' : 'mapping';
        this.logger.info(`All criteria met for auto import using ${source}`);
        return this.processAutoImport(task, bestResult, stats, task.task_id);
      } else {
        let reason = '';
        if (bestResult.import_status !== ImportStatuses.AUTO_IMPORT) {
          const source = bestResult.processor_str_id ? 'processor' : 'mapping';
          reason = `${source} not configured for auto import`;
          // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          this.logger.info(reason + ', sending to pending review');
        } else if (bestResult.metrics?.commissionTotal === 'Invalid') {
          reason = 'Commission total is invalid';
          // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          this.logger.info(reason + ', sending to pending review');
        } else {
          reason = 'Commission total does not match statement total';
          // biome-ignore lint/style/useTemplate: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          this.logger.info(reason + ', sending to pending review');
        }

        stats.errors.push({
          general: `${reason}, sending to pending review`,
        });

        return this.processRequestReview(
          task,
          validResults,
          stats,
          task.task_id,
          reason
        );
      }
    } catch (error) {
      Sentry.captureException(error);
      stats.errors.push({
        general: error.message || 'Unknown error',
      });

      await this.completeProcessing(
        task,
        task.task_id,
        DataProcessingStatuses.ERROR,
        stats,
        `${error.message}`
      );
    }

    const duration = Date.now() - startTime;
    return { stats, duration };
  }
}
