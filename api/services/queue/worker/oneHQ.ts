import { inject, injectable } from 'inversify';
import type { SyncParamsDTO } from 'common/dto/data_processing/sync';
import currency from 'currency.js';
import { WorkerNames } from 'common/constants';

import { OneHQService } from '@/services/oneHQ';
import {
  type AgentDatum,
  type AgentLevelDatum,
  BaseWorker,
  type CompanyDatum,
  type DBData,
  type IDataSyncWorker,
  type PolicyDatum,
  type ProductDatum,
} from '@/services/queue/worker/base';
import type {
  Advisor,
  Carrier,
  ContractLevel,
  Policy,
  PolicyReportData,
  Product,
} from '@/services/oneHQ/interface';
import { getValidDate } from '@/lib/helpers';
import type { ExtAccountInfo } from '@/types';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import dayjs from '@/lib/dayjs';
import { Cache } from '@/lib/decorators';
import { AppLoggerService } from '@/services/logger/appLogger';

@injectable()
export class OneHQWorker
  extends BaseWorker
  implements IDataSyncWorker<SyncParamsDTO>
{
  name = WorkerNames.OneHQWorker;

  @inject(OneHQService) service: OneHQService;
  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: WorkerNames.OneHQWorker },
  });

  constructor() {
    super();

    this.registerDataSource({
      policies: this.fetchReport.bind(this),
      carriersAndProducts: this.fetchCarriers.bind(this),
      agents: this.fetchAgents.bind(this),
      agentLevel: this.fetchContractLevels.bind(this),
    });

    this.registerTransformer({
      productTransform: {
        getData: this.getProductData.bind(this),
      },
      agentLevelTransform: {
        getData: this.getContactLevels.bind(this),
      },
    });
  }

  async setup(account: ExtAccountInfo): Promise<void> {
    const config = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        apiKey: string;
      }>
    >({ account_id: account.account_id, worker: this.name });

    this.service.loadConfig(config.value.credentials);
  }

  async pagination<T>(
    fetcher: (params: {
      after: string;
    }) => Promise<{ nodes: T[]; pageInfo: { endCursor: string } }>,
    title?: string
  ) {
    let results: T[] = [];
    let lastCuror = '';
    let page = 0;
    this.logger.profile(`${title} pagination`);
    do {
      const data = await fetcher({ after: lastCuror });
      results = [...results, ...(data?.nodes || [])];
      if (title) {
        this.logger.info(
          `${title}\tfetched ${results.length} records\tendCursor: ${data?.pageInfo?.endCursor}, page: ${page++}`
        );
      }
      lastCuror = data?.pageInfo?.endCursor;
    } while (lastCuror);
    this.logger.profile(`${title} pagination`);
    return results;
  }
  @Cache('policies')
  async fetchPolicies() {
    const policies: Policy[] = await this.pagination(
      ({ after }) => this.service.getPolicies({ after }),
      'policies'
    );
    return policies;
  }

  async fetchContractLevels() {
    const levels: ContractLevel[] = await this.pagination(
      ({ after }) => this.service.getContractLevels({ after }),
      'contractLevels'
    );
    return levels;
  }

  async fetchCarriers() {
    const carriers = await this.pagination(
      ({ after }) => this.service.getCarriersWithProducts({ after }),
      'carriersAndProducts'
    );
    return carriers;
  }

  @Cache('agents')
  async fetchAgents(): Promise<(Advisor & { type?: string })[]> {
    const agents: Advisor[] = await this.pagination(
      ({ after }) => this.service.getAdvisors({ after }),
      'agents'
    );
    const policies = await this.fetchPolicies();
    const uniqManagersIdSet = new Set();
    const managers: (Advisor & { type?: string })[] = policies
      .flatMap((policy) => policy.salesManagers)
      .map((r) => ({
        ...r.user,
        // Id: r.id, // Use salesManager id as sync_id
        advisorStatus: undefined,
        birthDate: undefined,
        affiliations: undefined,
        primaryAddressCityDn: undefined,
        primaryAddressStateDn: undefined,
        primaryEmailDn: undefined,
        primaryFirmNameDn: undefined,
        primaryPhoneNumberDn: undefined,
        type: 'Sales rep',
      }))
      .filter((r) => {
        if (uniqManagersIdSet.has(r.id)) {
          return false;
        }
        uniqManagersIdSet.add(r.id);
        return true;
      });

    return [...agents, ...managers];
  }

  getContactLevels(data: ContractLevel[]) {
    const levels: {
      carrierId: string;
      level: string;
      contractLevelId: string;
      startDate: Date;
      endDate: Date;
      advisorId: string;
    }[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((contractLevel) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      contractLevel.contract?.levels?.forEach((level) => {
        levels.push({
          carrierId: contractLevel?.contract?.carrierId,
          level: level.name,
          contractLevelId: contractLevel.id,
          startDate: getValidDate(level.startDate),
          endDate: getValidDate(level.endDate),
          advisorId: contractLevel.contract?.advisorId,
        });
      });
    });
    return levels;
  }

  async getPolicyAgentsMapping() {
    const policies = await this.fetchPolicies();
    const agentMapping = new Map<string, string>();
    const managerMapping = new Map<string, string>();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    policies.forEach((policy) => {
      const agents = policy.writingAdvisors?.map((r) => [r.id, r.advisor.id]);
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      agents.forEach(([id, advisorId]) => {
        agentMapping.set(id, advisorId);
      });
      const managers = policy.salesManagers?.map((r) => [r.id, r.user.id]);
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      managers.forEach(([id, managerId]) => {
        managerMapping.set(id, managerId);
      });
    });
    return { agentMapping, managerMapping };
  }

  async fetchReport() {
    let reports: PolicyReportData[] = [];
    let lastPage = 1;
    let hasNextPage = true;
    const pageSize = 250;
    do {
      const data = await this.service.getReport({
        id: '63916',
        page: lastPage,
      });
      reports = [...(reports ?? []), ...(data?.results?.data ?? [])];
      hasNextPage = data?.results?.data.length === pageSize;
      this.logger.info(
        `Total fetched ${reports.length} policies, page: ${lastPage}`
      );
      lastPage++;
    } while (hasNextPage);
    if (!reports?.length) {
      return [];
    }
    const mappings = await this.getPolicyAgentsMapping();
    return reports.map((r) => {
      const contacts: string[] = [];
      const agent = mappings.agentMapping.get(r.first_agent_id);
      if (agent) {
        contacts.push(agent);
        this.logger.info(`Mapping agent: ${r.first_agent_id} -> ${agent}`);
      }
      const secondAgent = mappings.agentMapping.get(r.second_agent_id);
      if (secondAgent) {
        contacts.push(secondAgent);
        this.logger.info(
          `Mapping second agent: ${r.second_agent_id} -> ${secondAgent}`
        );
      }
      const thirdAgent = mappings.agentMapping.get(r.third_agent_id);
      if (thirdAgent) {
        contacts.push(thirdAgent);
        this.logger.info(
          `Mapping third agent: ${r.third_agent_id} -> ${thirdAgent}`
        );
      }
      const firstManager = mappings.managerMapping.get(
        r.sales_managers_active_first_id
      );
      if (firstManager) {
        contacts.push(firstManager);
        this.logger.info(
          `Mapping first sales manager: ${r.sales_managers_active_first_id} -> ${firstManager}`
        );
      }
      const secondManager = mappings.managerMapping.get(
        r.sales_managers_active_second_id
      );
      if (secondManager) {
        contacts.push(secondManager);
        this.logger.info(
          `Mapping second sales manager: ${r.sales_managers_active_second_id} -> ${secondManager}`
        );
      }
      return {
        ...r,
        // Need to get the str_id from the contacts
        contacts: contacts?.length ? contacts : undefined,
      };
    });
  }

  getProductData(data: Carrier[]) {
    const products: Product[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((carrier) => {
      if (carrier?.products?.length) {
        products.push(...carrier.products);
      }
    });
    return products;
  }

  agentLevelTransform(
    data: Awaited<ReturnType<typeof this.getContactLevels>>[0],
    dbData?: DBData
  ): AgentLevelDatum {
    return {
      sync_id: data.contractLevelId,
      end_date: data.endDate,
      start_date: data.startDate,
      company_id: dbData.companies?.get(`${data.carrierId}`)?.id,
      level_label: data.level,
      loa: data.level === 'LOA',
      contact_id: data.advisorId,
    };
  }

  agentTransform(data: Advisor & { type?: string[] }): AgentDatum {
    const phones = data.phones?.map((phone) => phone.displayPhone) ?? [];
    const addresses = data.addresses ?? [];
    return {
      sync_id: data.id,
      name: data.name,
      first_name: data.demographic?.firstName,
      middle_name: data.demographic?.middleName,
      last_name: data.demographic?.lastName,
      gender: data.demographic?.gender,
      notes: data.note,
      type: data.type,
      email: data.primaryEmailDn,
      birthday: data.demographic?.birthDate
        ? dayjs.utc(data.demographic?.birthDate).toDate()
        : null,
      status: data.advisorStatus,
      phone: phones?.[0],
      phone2: phones?.[1],
      city: data.primaryAddressCityDn,
      country: addresses[0]?.state?.country?.name,
      geo_state: addresses[0]?.state?.name,
      zip: addresses[0]?.zipcode,
      company_name: undefined,
    };
  }

  companyTransform(data: Carrier): CompanyDatum {
    return {
      sync_id: data.id,
      company_name: data.name,
      company_id: data.id,
      type: ['Carrier'],
    };
  }

  productTransform(data: Product, dbData?: DBData): ProductDatum {
    return {
      sync_id: data.id,
      company_id: dbData.companies?.get(`${data.carrierId}`)?.id,
      product_type: `${data.productType}`,
      product_name: data.name,
    };
  }

  getProductSubType(productName: string) {
    const rules = [
      { regexp: /\bMYGA|MYG\b/gi, output: 'MYGA' },
      { regexp: /\bSPIA\b/gi, output: 'SPIA' },
      { regexp: /\bFIA\b/gi, output: 'FIA' },
    ];
    const target = rules.find((rule) => rule.regexp.test(productName));
    return target ? target.output : '';
  }

  getAgeOfBusiness(productName: string) {
    const regex = /\b\d+\b/g;
    const matches = (productName || '').match(regex);
    const year = matches?.find((m) => Number.isInteger(Number(m)));
    const isAbnormalYear = (year) => {
      const allDigits = /^\d+$/;
      return allDigits.test(productName) || Number(year) > 100;
    };
    return year && !isAbnormalYear(year) ? year : undefined;
  }

  policyTransform(
    data: PolicyReportData & { contacts: string[] },
    dbData?: DBData
  ): PolicyDatum {
    return {
      sync_id: data.onehq_id,
      policy_id: data.policy_number,
      company_id: dbData.companies?.get(`${data.carrier_id}`)?.id,
      writing_carrier_name: data.carrier_name,
      product_name: data.product_name,
      product_type: data.product_type,
      product_sub_type: data.type_of_business,
      group_name: [
        data.primary_advisor_first_affiliation,
        data.primary_advisor_second_affiliation,
      ]
        ?.filter((r) => r)
        .join(','),
      effective_date: getValidDate(data.effective_date),
      issue_age: data.issue_age,
      geo_state: data.state,
      notes: this.getAgeOfBusiness(data.product_name),
      company_product_id: dbData.company_products?.get(`${data.product_id}`)
        ?.id,
      policy_status: data.status,
      customer_name: data.customer_name,
      payment_mode: data.payment_mode_optional,
      agent_name: data.agent_name,
      contacts: data.contacts
        ?.map((r) => dbData.contacts.get(r)?.str_id)
        .filter((r) => r),
      // Format to float "$7,680.00"
      commissionable_premium_amount: data.submitted_target_premium
        ? currency(data.submitted_target_premium).value
        : undefined,
    };
  }

  oldpolicyTransform(data: Policy, dbData?: DBData): PolicyDatum {
    // Get the first available policy holder
    const [holder] = data.policyHolders ?? [];
    let groupName = '';
    data.writingAdvisors?.find((r) => {
      return r.advisor?.affiliations?.find((a) => {
        if (a.isPrimary) {
          groupName = a.firm?.name;
          return true;
        }
      });
    });
    const advisors = data.writingAdvisors
      ?.map((r) => dbData.contacts.get(r.advisor.id)?.str_id)
      .filter((r) => r);
    const managers = data.salesManagers
      ?.map((r) => dbData.contacts.get(r.user.id)?.str_id)
      .filter((r) => r);
    return {
      sync_id: data.id,
      policy_id: data.policyNumber,
      company_id: dbData.companies?.get(`${data.carrier.id}`)?.id,
      writing_carrier_name: data.carrier?.name,
      product_name: data.product?.name,
      product_type: `${data.productType?.name}`,
      product_sub_type:
        data.product?.productCategory?.name ||
        this.getProductSubType(data.product?.name),
      group_name: groupName,
      effective_date: getValidDate(data.effectiveDate),
      issue_age: holder?.issueAge,
      geo_state: data.state?.name,
      notes: this.getAgeOfBusiness(data.product?.name),
      company_product_id: dbData.company_products?.get(`${data.product.id}`)
        ?.id,
      policy_status: data.policyStatus,
      customer_name: data.primaryClientNameDn,
      payment_mode: data.paymentMode,
      agent_name: data.writingAdvisors?.map((r) => r.advisor?.name).join(','),
      contacts: [...(advisors ?? []), ...(managers ?? [])],
      commissionable_premium_amount: dbData?.report_data?.get(`${data.id}`)
        ?.commissionable_premium_amount
        ? undefined
        : data.commissionablePremium,
    };
  }
}
