import { mockDeep, mockReset } from 'vitest-mock-extended';
import { vi, describe, it, expect, beforeEach } from 'vitest';

import { DataStates } from '@/types';
import { BaseWorker } from '@/services/queue/worker/base';
import { prismaClient } from '@/lib/prisma';

// Mock prisma client
vi.mock('@/lib/prisma', () => ({
  prismaClient: mockDeep(),
}));

describe('BaseWorker', () => {
  class TestWorker extends BaseWorker {
    name = 'TestWorker';
  }

  let worker: TestWorker;
  const mockTable = 'test_table';
  const mockSyncId = 'test-sync-id';
  const mockAccountId = 'test-account-id';

  beforeEach(() => {
    mockReset(prismaClient);
    worker = new TestWorker();
  });

  describe('ensureUniqueSyncId', () => {
    it('should reactivate a single deleted item', async () => {
      // Mock data: single deleted item
      const mockItems = [
        {
          id: 1,
          sync_id: mockSyncId,
          state: DataStates.DELETED,
        },
      ];

      prismaClient[mockTable].findMany.mockResolvedValue(mockItems);
      prismaClient[mockTable].update.mockResolvedValue({
        ...mockItems[0],
        state: DataStates.ACTIVE,
      });

      await worker.ensureUniqueSyncId(mockTable, mockSyncId, mockAccountId);

      expect(prismaClient[mockTable].findMany).toHaveBeenCalledWith({
        where: {
          sync_id: mockSyncId,
          account_id: mockAccountId,
          state: {
            in: [
              DataStates.ACTIVE,
              DataStates.GROUPED,
              DataStates.DELETED,
              DataStates.DUPLICATE,
            ],
          },
        },
      });

      expect(prismaClient[mockTable].update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { state: DataStates.ACTIVE },
      });
    });

    it('should nullify sync_id of deleted item when multiple items exist', async () => {
      // Mock data: multiple items including one deleted
      const mockItems = [
        {
          id: 1,
          sync_id: mockSyncId,
          state: DataStates.ACTIVE,
        },
        {
          id: 2,
          sync_id: mockSyncId,
          state: DataStates.DELETED,
        },
      ];

      prismaClient[mockTable].findMany.mockResolvedValue(mockItems);
      prismaClient[mockTable].update.mockResolvedValue({
        ...mockItems[1],
        sync_id: null,
      });

      await worker.ensureUniqueSyncId(mockTable, mockSyncId, mockAccountId);

      expect(prismaClient[mockTable].findMany).toHaveBeenCalledWith({
        where: {
          sync_id: mockSyncId,
          account_id: mockAccountId,
          state: {
            in: [
              DataStates.ACTIVE,
              DataStates.GROUPED,
              DataStates.DELETED,
              DataStates.DUPLICATE,
            ],
          },
        },
      });

      expect(prismaClient[mockTable].update).toHaveBeenCalledWith({
        where: { id: 2 },
        data: { sync_id: null },
      });
    });

    it('should not modify anything when no deleted items exist', async () => {
      // Mock data: only active items
      const mockItems = [
        {
          id: 1,
          sync_id: mockSyncId,
          state: DataStates.ACTIVE,
        },
        {
          id: 2,
          sync_id: mockSyncId,
          state: DataStates.ACTIVE,
        },
      ];

      prismaClient[mockTable].findMany.mockResolvedValue(mockItems);

      await worker.ensureUniqueSyncId(mockTable, mockSyncId, mockAccountId);

      expect(prismaClient[mockTable].findMany).toHaveBeenCalled();

      expect(prismaClient[mockTable].update).not.toHaveBeenCalled();
    });

    it('should not modify anything when no items exist', async () => {
      // Mock data: empty array
      prismaClient[mockTable].findMany.mockResolvedValue([]);

      await worker.ensureUniqueSyncId(mockTable, mockSyncId, mockAccountId);

      expect(prismaClient[mockTable].findMany).toHaveBeenCalled();

      expect(prismaClient[mockTable].update).not.toHaveBeenCalled();
    });
  });
});
