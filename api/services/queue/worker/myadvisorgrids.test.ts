import { describe, it, expect, vi } from 'vitest';

import { MyAdvisorGridsWorker } from './myadvisorgrids';
import type { CarrierEffectiveDateList } from '@/services/myadvisorgrids/interface';
import { container } from '@/ioc';
import { CompensationType } from '@/types/enum/compensation-type.enum';

describe('MyAdvisorGridsWorker', () => {
  const worker = container.get(MyAdvisorGridsWorker);

  describe('generateDateRanges', () => {
    it('Should return empty array when no dates are provided', () => {
      const data: CarrierEffectiveDateList = {
        carrier_id: '123',
        carrier_effective_dates: [],
      };

      const result = worker.generateDateRanges(data);
      expect(result).toEqual([]);
    });

    it('Should handle a single effective date', () => {
      const data: CarrierEffectiveDateList = {
        carrier_id: '123',
        carrier_effective_dates: [
          {
            carrier_effective_date_id: 1,
            carrier_effective_date: '2024-01-01',
          },
        ],
      };

      const result = worker.generateDateRanges(data);
      expect(result).toHaveLength(1);
      expect(result[0].carrier_effective_date_id).toBe(1);
      expect(result[0].carrier_effective_date).toBe('2024-01-01');
      expect(result[0].start_date).toBeInstanceOf(Date);
      expect(result[0].end_date).toBeNull();
    });

    it('Should generate correct date ranges for multiple dates', () => {
      const data: CarrierEffectiveDateList = {
        carrier_id: '197',
        carrier_effective_dates: [
          {
            carrier_effective_date_id: 185,
            carrier_effective_date: '2024-01-01',
          },
          {
            carrier_effective_date_id: 186,
            carrier_effective_date: '2025-03-13',
          },
          {
            carrier_effective_date_id: 202,
            carrier_effective_date: '2025-03-28',
          },
        ],
      };

      const result = worker.generateDateRanges(data);
      expect(result).toHaveLength(3);

      // First range
      expect(result[0].carrier_effective_date_id).toBe(185);
      expect(result[0].carrier_effective_date).toBe('2024-01-01');
      expect(result[0].start_date).toBeInstanceOf(Date);
      expect(result[0].end_date).toBeInstanceOf(Date);

      // Second range
      expect(result[1].carrier_effective_date_id).toBe(186);
      expect(result[1].carrier_effective_date).toBe('2025-03-13');
      expect(result[1].start_date).toBeInstanceOf(Date);
      expect(result[1].end_date).toBeInstanceOf(Date);

      // Third range
      expect(result[2].carrier_effective_date_id).toBe(202);
      expect(result[2].carrier_effective_date).toBe('2025-03-28');
      expect(result[2].start_date).toBeInstanceOf(Date);
      expect(result[2].end_date).toBeNull();
    });

    it('Should sort dates chronologically before generating ranges', () => {
      const data: CarrierEffectiveDateList = {
        carrier_id: '197',
        carrier_effective_dates: [
          {
            carrier_effective_date_id: 202,
            carrier_effective_date: '2025-03-28',
          },
          {
            carrier_effective_date_id: 185,
            carrier_effective_date: '2024-01-01',
          },
          {
            carrier_effective_date_id: 186,
            carrier_effective_date: '2025-03-13',
          },
        ],
      };

      const result = worker.generateDateRanges(data);
      expect(result).toHaveLength(3);

      // Check that dates are sorted chronologically
      expect(result[0].carrier_effective_date_id).toBe(185);
      expect(result[1].carrier_effective_date_id).toBe(186);
      expect(result[2].carrier_effective_date_id).toBe(202);
    });
  });

  describe('getCompensationType', () => {
    it('Should return Override when no matches are found by default', () => {
      const data = {
        carrier_product_name: 'Test Product',
        carrier_product_sub_name: 'Test Sub Product',
        carrier_product_criteria: [],
      };

      const result = worker.getCompensationTypes(data);
      expect(result).toEqual([CompensationType.Override]);
    });

    it('Should return Renewal when the product policy year is empty or > 1', () => {
      const data = {
        carrier_product_name: 'Test Product',
        carrier_product_sub_name: 'Test Sub Product',
        carrier_product_criteria: [],
      };
      const result = worker.getCompensationTypes(data, 2);
      expect(result).toEqual([CompensationType.Renew]);
      expect(worker.getCompensationTypes(data, 2)).toEqual([
        CompensationType.Renew,
      ]);
    });

    it('Should return Override when the product policy year is empty', () => {
      const data = {
        carrier_product_name: 'Test Product',
        carrier_product_sub_name: 'Test Sub Product',
        carrier_product_criteria: [],
      };
      expect(worker.getCompensationTypes(data, null)).toEqual([
        CompensationType.Override,
      ]);
      expect(worker.getCompensationTypes(data, undefined)).toEqual([
        CompensationType.Override,
      ]);
    });

    it('Should return the correct compensation type', () => {
      expect(
        worker.getCompensationTypes({
          carrier_product_name: 'Renewal Test Product',
          carrier_product_sub_name: 'Test Sub Product',
          carrier_product_criteria: [],
        })
      ).toEqual([CompensationType.Renew]);
      expect(
        worker.getCompensationTypes({
          carrier_product_name: 'Renewals Excess Years 2-5',
          carrier_product_sub_name: undefined,
          carrier_product_criteria: [
            {
              criteria_id: '1',
              criteria_name: 'Excess Year',
              carrier_product_criteria_id: 2,
              carrier_product_criteria_data: [],
            },
          ],
        })
      ).toEqual([CompensationType.Renew, CompensationType.Excess]);
      expect(
        worker.getCompensationTypes({
          carrier_product_name: 'Renewals Test Product',
          carrier_product_sub_name: 'Test Sub Product',
          carrier_product_criteria: [
            {
              criteria_id: '1',
              criteria_name: 'Renewal Year',
              carrier_product_criteria_id: 2,
              carrier_product_criteria_data: [],
            },
          ],
        })
      ).toEqual([CompensationType.Renew]);
      expect(
        worker.getCompensationTypes({
          carrier_product_name: 'Override Test Product',
          carrier_product_sub_name: 'Test Sub Product',
          carrier_product_criteria: [],
        })
      ).toEqual([CompensationType.Override]);
      expect(
        worker.getCompensationTypes({
          carrier_product_name: null,
          carrier_product_sub_name: 'Excess',
          carrier_product_criteria: [
            {
              criteria_id: '1',
              criteria_name: 'Renewal Year',
              carrier_product_criteria_id: 2,
              carrier_product_criteria_data: [],
            },
            {
              criteria_id: '2',
              criteria_name: 'Excess Year',
              carrier_product_criteria_id: 3,
              carrier_product_criteria_data: [],
            },
          ],
        })
      ).toEqual([CompensationType.Excess]);
    });
  });

  describe('extractProductCarrierMapping', () => {
    it('Should return empty maps when no details are provided', () => {
      const details = [];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(0);
      expect(result.productIds).toEqual([]);
    });

    it('Should return empty maps when details have no carrier products', () => {
      const details = [
        {
          carrier_id: 1,
          carrier_product: [],
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any,
      ];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(0);
      expect(result.productIds).toEqual([]);
    });

    it('Should return empty maps when carrier products have no data', () => {
      const details = [
        {
          carrier_id: 1,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [],
            },
          ],
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any,
      ];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(0);
      expect(result.productIds).toEqual([]);
    });

    it('Should filter out products without product_ai_id', () => {
      const details = [
        {
          carrier_id: 1,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: null, // No AI ID
                  },
                },
                {
                  carrier_product_data_id: 'data2',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: undefined, // No AI ID
                  },
                },
                {
                  carrier_product_data_id: 'data3',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '', // Empty AI ID
                  },
                },
              ],
            },
          ],
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any,
      ];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(0);
      expect(result.productIds).toEqual([]);
    });

    it('Should correctly map single product to carrier', () => {
      const details = [
        {
          carrier_id: 123,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456',
                  },
                },
              ],
            },
          ],
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any,
      ];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(1);
      expect(result.productCarrierMap.get(456)).toBe(123);
      expect(result.productIds).toEqual([456]);
    });

    it('Should correctly map multiple products from same carrier', () => {
      const details = [
        {
          carrier_id: 123,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456',
                  },
                },
                {
                  carrier_product_data_id: 'data2',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '789',
                  },
                },
              ],
            },
          ],
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any,
      ];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(2);
      expect(result.productCarrierMap.get(456)).toBe(123);
      expect(result.productCarrierMap.get(789)).toBe(123);
      expect(result.productIds).toEqual([456, 789]);
    });

    it('Should correctly map products from multiple carriers', () => {
      const details = [
        {
          carrier_id: 123,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456',
                  },
                },
              ],
            },
          ],
        },
        {
          carrier_id: 789,
          carrier_product: [
            {
              carrier_product_id: 'prod2',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data2',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '101112',
                  },
                },
              ],
            },
          ],
        },
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ] as any[];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(2);
      expect(result.productCarrierMap.get(456)).toBe(123);
      expect(result.productCarrierMap.get(101112)).toBe(789);
      expect(result.productIds).toEqual([456, 101112]);
    });

    it('Should handle multiple carrier products per carrier', () => {
      const details = [
        {
          carrier_id: 123,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456',
                  },
                },
              ],
            },
            {
              carrier_product_id: 'prod2',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data2',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '789',
                  },
                },
              ],
            },
          ],
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any,
      ];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(2);
      expect(result.productCarrierMap.get(456)).toBe(123);
      expect(result.productCarrierMap.get(789)).toBe(123);
      expect(result.productIds).toEqual([456, 789]);
    });

    it('Should handle duplicate product IDs across carriers (last carrier wins)', () => {
      const details = [
        {
          carrier_id: 123,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456',
                  },
                },
              ],
            },
          ],
        },
        {
          carrier_id: 789,
          carrier_product: [
            {
              carrier_product_id: 'prod2',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data2',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456',
                  },
                },
              ],
            },
          ],
        },
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ] as any[];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(1);
      expect(result.productCarrierMap.get(456)).toBe(789); // Last carrier wins
      expect(result.productIds).toEqual([456, 456]); // Both instances recorded in array
    });

    it('Should convert string product IDs to numbers', () => {
      const details = [
        {
          carrier_id: 123,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456',
                  },
                },
              ],
            },
          ],
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } as any,
      ];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(1);
      expect(result.productCarrierMap.get(456)).toBe(123);
      expect(result.productIds).toEqual([456]);
    });

    it('Should handle complex nested structure with mixed valid and invalid data', () => {
      const details = [
        {
          carrier_id: 123,
          carrier_product: [
            {
              carrier_product_id: 'prod1',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data1',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '456', // Valid
                  },
                },
                {
                  carrier_product_data_id: 'data2',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: null, // Invalid
                  },
                },
                {
                  carrier_product_data_id: 'data3',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '789', // Valid
                  },
                },
              ],
            },
            {
              carrier_product_id: 'prod2',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data4',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '', // Invalid
                  },
                },
              ],
            },
          ],
        },
        {
          carrier_id: 999,
          carrier_product: [
            {
              carrier_product_id: 'prod3',
              carrier_product_data: [
                {
                  carrier_product_data_id: 'data5',
                  carrier_product_ai_mapping_data: {
                    product_ai_id: '101112', // Valid
                  },
                },
              ],
            },
          ],
        },
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      ] as any[];

      const result = worker.extractProductCarrierMapping(details);

      expect(result.productCarrierMap.size).toBe(3);
      expect(result.productCarrierMap.get(456)).toBe(123);
      expect(result.productCarrierMap.get(789)).toBe(123);
      expect(result.productCarrierMap.get(101112)).toBe(999);
      expect(result.productIds).toEqual([456, 789, 101112]);
    });
  });

  describe('generateCriteria', () => {
    it('Should return empty result with geo_states array when no criteria provided', () => {
      const data = {
        carrier_product_criteria: [],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result).toEqual({ geo_states: [] });
    });

    it('Should handle Issue Age criteria correctly', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Issue Age',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'From Age',
                criteria_field_value: '18',
              },
              {
                criteria_field_name: 'To Age',
                criteria_field_value: '65',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.issue_age_start).toBe(18);
      expect(result.issue_age_end).toBe(65);
      expect(result.geo_states).toEqual([]);
    });

    it('Should handle Policy Year criteria correctly', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Policy Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '1',
              },
              {
                criteria_field_name: 'End Year',
                criteria_field_value: '5',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.policy_year_start).toBe(1);
      expect(result.policy_year_end).toBe(5);
    });

    it('Should handle Excess Year criteria correctly', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Excess Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '6',
              },
              {
                criteria_field_name: 'End Year',
                criteria_field_option_value: 'Not Applicable',
                criteria_field_value: '10',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.policy_year_start).toBe(6);
      expect(result.policy_year_end).toBe(6); // Should be set to start year when 'Not Applicable'
    });

    it('Should handle Renewal Year criteria correctly', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Renewal Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '2',
              },
              {
                criteria_field_name: 'End Year',
                criteria_field_value: '10',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.policy_year_start).toBe(2);
      expect(result.policy_year_end).toBe(10);
    });

    it('Should handle State criteria correctly', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'State',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Select States',
                criteria_field_option_value: 'CA',
              },
              {
                criteria_field_name: 'Select States',
                criteria_field_option_value: 'NY',
              },
              {
                criteria_field_name: 'Select States',
                criteria_field_option_value: 'TX',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.geo_states).toEqual(['CA', 'NY', 'TX']);
    });

    it('Should handle Sub Type criteria correctly', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: 'Service Fees Years 11+',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.notes).toBe('Product Sub Type: Service Fees Years 11+');
      expect(result.geo_states).toEqual([]);
    });

    it('Should handle multiple criteria types together', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Issue Age',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'From Age',
                criteria_field_value: '25',
              },
              {
                criteria_field_name: 'To Age',
                criteria_field_value: '70',
              },
            ],
          },
          {
            criteria_name: 'Policy Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '1',
              },
              {
                criteria_field_name: 'End Year',
                criteria_field_value: '3',
              },
            ],
          },
          {
            criteria_name: 'State',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Select States',
                criteria_field_option_value: 'FL',
              },
            ],
          },
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: 'MYGA',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.issue_age_start).toBe(25);
      expect(result.issue_age_end).toBe(70);
      expect(result.policy_year_start).toBe(1);
      expect(result.policy_year_end).toBe(3);
      expect(result.geo_states).toEqual(['FL']);
      expect(result.notes).toBe('Product Sub Type: MYGA');
    });

    it('Should log warning and append to notes when conflicting start years found', () => {
      const loggerWarnSpy = vi.spyOn(worker.logger, 'warn');

      const data = {
        carrier_product_name: 'Test Product',
        carrier_product_data_id: 'test_id',
        carrier_product_criteria: [
          {
            criteria_name: 'Policy Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '1',
              },
            ],
          },
          {
            criteria_name: 'Renewal Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '2',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);

      expect(result.policy_year_start).toBe(2); // Last value wins
      expect(result.notes).toContain(
        'Different start years (1 !== 2) found for product Test Product (test_id)'
      );
      expect(loggerWarnSpy).toHaveBeenCalled();
    });

    it('Should log warning and append to notes when conflicting end years found', () => {
      const loggerWarnSpy = vi.spyOn(worker.logger, 'warn');

      const data = {
        carrier_product_name: 'Test Product',
        carrier_product_data_id: 'test_id',
        carrier_product_criteria: [
          {
            criteria_name: 'Policy Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'End Year',
                criteria_field_value: '5',
              },
            ],
          },
          {
            criteria_name: 'Renewal Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'End Year',
                criteria_field_value: '10',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);

      expect(result.policy_year_end).toBe(10); // Last value wins
      expect(result.notes).toContain(
        'Different end years (5 !== 10) found for product Test Product (test_id)'
      );
      expect(loggerWarnSpy).toHaveBeenCalled();
    });

    it('Should handle Sub Type correctly when notes already exist from conflicts', () => {
      const loggerWarnSpy = vi.spyOn(worker.logger, 'warn');

      const data = {
        carrier_product_name: 'Test Product',
        carrier_product_data_id: 'test_id',
        carrier_product_criteria: [
          {
            criteria_name: 'Policy Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '1',
              },
            ],
          },
          {
            criteria_name: 'Renewal Year',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Start Year',
                criteria_field_value: '2',
              },
            ],
          },
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: 'Life Insurance',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);

      expect(result.policy_year_start).toBe(2);
      expect(result.notes).toContain('Product Sub Type: Life Insurance');
      expect(loggerWarnSpy).toHaveBeenCalled();
    });

    it('Should update Product Sub Type when value changes', () => {
      // Simulate existing notes with different Product Sub Type
      const existingNotes = 'Product Sub Type: Old Value';
      const newProductSubType = 'New Value';
      const subTypeNote = `Product Sub Type: ${newProductSubType}`;
      const productSubTypePattern = /Product Sub Type: .*/;

      const updatedNotes = existingNotes.replace(
        productSubTypePattern,
        subTypeNote
      );

      expect(updatedNotes).toBe('Product Sub Type: New Value');
    });

    it('Should append Product Sub Type to existing non-SubType notes', () => {
      const existingNotes = 'Some other warning message';
      const productSubType = 'SPIA';
      const subTypeNote = `Product Sub Type: ${productSubType}`;
      const productSubTypePattern = /Product Sub Type: .*/;

      let notes = existingNotes;
      if (!productSubTypePattern.test(notes)) {
        notes = `${notes}\n${subTypeNote}`;
      }

      expect(notes).toBe('Some other warning message\nProduct Sub Type: SPIA');
    });

    it('Should ignore empty Product Sub Type values', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: '',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.notes).toBeUndefined();
      expect(result.geo_states).toEqual([]);
    });

    it('Should ignore null Product Sub Type values', () => {
      const data = {
        carrier_product_criteria: [
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: null,
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.generateCriteria(data);
      expect(result.notes).toBeUndefined();
      expect(result.geo_states).toEqual([]);
    });
  });

  describe('mergeNotes', () => {
    it('Should concatenate existing notes with new notes when both exist and new notes are not included', () => {
      const result = worker.mergeNotes('Existing note', 'New note');
      expect(result).toBe('Existing note\nNew note');
    });

    it('Should not concatenate when new notes are already included in existing notes', () => {
      const result = worker.mergeNotes('Existing note\nNew note', 'New note');
      expect(result).toBe('New note');
    });

    it('Should return new notes when no existing notes', () => {
      const result = worker.mergeNotes(undefined, 'New note');
      expect(result).toBe('New note');
    });

    it('Should return new notes when existing notes is empty string', () => {
      const result = worker.mergeNotes('', 'New note');
      expect(result).toBe('New note');
    });

    it('Should return existing notes when no new notes', () => {
      const result = worker.mergeNotes('Existing note', undefined);
      expect(result).toBe('Existing note');
    });

    it('Should return existing notes when new notes is empty string', () => {
      const result = worker.mergeNotes('Existing note', '');
      expect(result).toBe('Existing note');
    });

    it('Should return undefined when both notes are undefined', () => {
      const result = worker.mergeNotes(undefined, undefined);
      expect(result).toBeUndefined();
    });

    it('Should return undefined when both notes are empty strings', () => {
      const result = worker.mergeNotes('', '');
      expect(result).toBeUndefined();
    });

    it('Should handle partial matches correctly', () => {
      const result = worker.mergeNotes(
        'Product Sub Type: MYGA',
        'Product Sub Type: SPIA'
      );
      expect(result).toBe('Product Sub Type: MYGA\nProduct Sub Type: SPIA');
    });

    it('Should handle exact substring matches', () => {
      const result = worker.mergeNotes(
        'This is a long note with details',
        'long note'
      );
      expect(result).toBe('long note');
    });
  });

  describe('compCriteriaTransform', () => {
    it('Should concatenate existing notes with new notes when both exist and new notes are not included', () => {
      const mockDBData = {
        comp_grid_criteria: new Map([
          ['test_data_id', { notes: 'Existing note' }],
        ]),
        comp_grid_products: new Map([['123', { id: 1 }]]),
        comp_grids: new Map([['456', { id: 2 }]]),
        companies: new Map([['ai123', { id: 3 }]]),
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const testData = {
        carrier_product_data_id: 'test_data_id',
        carrier_id: '456',
        ai_id: 'ai123',
        product_id: '123',
        compensation_type: 'Override',
        carrier_product_criteria: [
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: 'MYGA',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.compCriteriaTransform(testData, mockDBData);

      expect(result.notes).toBe('Existing note\nProduct Sub Type: MYGA');
    });

    it('Should not concatenate when new notes are already included in existing notes', () => {
      const mockDBData = {
        comp_grid_criteria: new Map([
          ['test_data_id', { notes: 'Existing note\nProduct Sub Type: MYGA' }],
        ]),
        comp_grid_products: new Map([['123', { id: 1 }]]),
        comp_grids: new Map([['456', { id: 2 }]]),
        companies: new Map([['ai123', { id: 3 }]]),
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const testData = {
        carrier_product_data_id: 'test_data_id',
        carrier_id: '456',
        ai_id: 'ai123',
        product_id: '123',
        compensation_type: 'Override',
        carrier_product_criteria: [
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: 'MYGA',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.compCriteriaTransform(testData, mockDBData);

      expect(result.notes).toBe('Product Sub Type: MYGA');
    });

    it('Should use only new notes when no existing notes', () => {
      const mockDBData = {
        comp_grid_criteria: new Map([['test_data_id', {}]]),
        comp_grid_products: new Map([['123', { id: 1 }]]),
        comp_grids: new Map([['456', { id: 2 }]]),
        companies: new Map([['ai123', { id: 3 }]]),
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const testData = {
        carrier_product_data_id: 'test_data_id',
        carrier_id: '456',
        ai_id: 'ai123',
        product_id: '123',
        compensation_type: 'Override',
        carrier_product_criteria: [
          {
            criteria_name: 'Sub Type',
            carrier_product_criteria_data: [
              {
                criteria_field_name: 'Product Sub Type',
                criteria_field_value: 'SPIA',
              },
            ],
          },
        ],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.compCriteriaTransform(testData, mockDBData);

      expect(result.notes).toBe('Product Sub Type: SPIA');
    });

    it('Should use only existing notes when no new notes', () => {
      const mockDBData = {
        comp_grid_criteria: new Map([
          ['test_data_id', { notes: 'Existing note only' }],
        ]),
        comp_grid_products: new Map([['123', { id: 1 }]]),
        comp_grids: new Map([['456', { id: 2 }]]),
        companies: new Map([['ai123', { id: 3 }]]),
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const testData = {
        carrier_product_data_id: 'test_data_id',
        carrier_id: '456',
        ai_id: 'ai123',
        product_id: '123',
        compensation_type: 'Override',
        carrier_product_criteria: [],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.compCriteriaTransform(testData, mockDBData);

      expect(result.notes).toBe('Existing note only');
    });

    it('Should have undefined notes when neither existing nor new notes exist', () => {
      const mockDBData = {
        comp_grid_criteria: new Map([['test_data_id', {}]]),
        comp_grid_products: new Map([['123', { id: 1 }]]),
        comp_grids: new Map([['456', { id: 2 }]]),
        companies: new Map([['ai123', { id: 3 }]]),
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const testData = {
        carrier_product_data_id: 'test_data_id',
        carrier_id: '456',
        ai_id: 'ai123',
        product_id: '123',
        compensation_type: 'Override',
        carrier_product_criteria: [],
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      } as any;

      const result = worker.compCriteriaTransform(testData, mockDBData);

      expect(result.notes).toBeUndefined();
    });
  });
});
