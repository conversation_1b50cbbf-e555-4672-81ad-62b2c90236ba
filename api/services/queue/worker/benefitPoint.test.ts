import { describe, it, expect, beforeEach } from 'vitest';
import type { Prisma } from '@prisma/client';
import { BenefitPointWorker } from './benefitPoint';

describe('BenefitPointWorker', () => {
  let worker: BenefitPointWorker;

  beforeEach(() => {
    worker = new BenefitPointWorker();
  });

  describe('shouldUpdateMemberCount', () => {
    const basePolicy = {
      id: 1,
      sync_id: '123',
      config: {},
    };

    it.each([
      { count: null, description: 'null' },
      { count: undefined, description: 'undefined' },
    ])(
      'Given member count is $description, when checking if should update, should return false',
      ({ count }) => {
        const member_count = { count, period_date: '2023-01-01' };
        const policy = {
          ...basePolicy,
          config: { member_count: { count: 10, period_date: '2022-01-01' } },
        };
        expect(
          worker.shouldUpdateMemberCount(
            member_count as never,
            policy as Prisma.report_dataGetPayload<{
              select: { config: true; sync_id: true; id: true };
            }>
          )
        ).toBe(false);
      }
    );

    it('Given policy has no member count config, when checking if should update, should return true', () => {
      const member_count = { count: 10, period_date: '2023-01-01' };
      const policy = { ...basePolicy, config: {} };
      expect(
        worker.shouldUpdateMemberCount(
          member_count,
          policy as Prisma.report_dataGetPayload<{
            select: { config: true; sync_id: true; id: true };
          }>
        )
      ).toBe(true);
    });

    it('Given policy config is null, when checking if should update, should return true', () => {
      const member_count = { count: 10, period_date: '2023-01-01' };
      const policy = { ...basePolicy, config: null };
      expect(
        worker.shouldUpdateMemberCount(
          member_count,
          policy as Prisma.report_dataGetPayload<{
            select: { config: true; sync_id: true; id: true };
          }>
        )
      ).toBe(true);
    });

    it('Given new member count date is more recent, when checking if should update, should return true', () => {
      const member_count = { count: 10, period_date: '2023-01-01' };
      const policy = {
        ...basePolicy,
        config: { member_count: { count: 5, period_date: '2022-12-31' } },
      };
      expect(
        worker.shouldUpdateMemberCount(
          member_count,
          policy as Prisma.report_dataGetPayload<{
            select: { config: true; sync_id: true; id: true };
          }>
        )
      ).toBe(true);
    });

    it('Given new member count date is older, when checking if should update, should return false', () => {
      const member_count = { count: 10, period_date: '2022-01-01' };
      const policy = {
        ...basePolicy,
        config: { member_count: { count: 5, period_date: '2022-12-31' } },
      };
      expect(
        worker.shouldUpdateMemberCount(
          member_count,
          policy as Prisma.report_dataGetPayload<{
            select: { config: true; sync_id: true; id: true };
          }>
        )
      ).toBe(false);
    });

    it('Given new member count date is the same, when checking if should update, should return false', () => {
      const member_count = { count: 10, period_date: '2022-12-31' };
      const policy = {
        ...basePolicy,
        config: { member_count: { count: 5, period_date: '2022-12-31' } },
      };
      expect(
        worker.shouldUpdateMemberCount(
          member_count,
          policy as Prisma.report_dataGetPayload<{
            select: { config: true; sync_id: true; id: true };
          }>
        )
      ).toBe(false);
    });
  });

  describe('getPaymentMethod', () => {
    it('Should return the payment_method when provided', () => {
      const document = { payment_method: 'CreditCard' };
      expect(worker.getPaymentMethod(document)).toBe('CreditCard');
    });

    it('Should return "ACH" when payment_method is undefined', () => {
      const document = { payment_method: undefined };
      expect(worker.getPaymentMethod(document)).toBe('ACH');
    });

    it('Should return "ACH" when payment_method is null', () => {
      const document = { payment_method: null };
      expect(worker.getPaymentMethod(document)).toBe('ACH');
    });
    it('Should return "ACH" when payment_method is ""', () => {
      const document = { payment_method: '' };
      expect(worker.getPaymentMethod(document)).toBe('ACH');
    });

    it('Should return "ACH" when document is empty object', () => {
      const document = {};
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      expect(worker.getPaymentMethod(document as any)).toBe('ACH');
    });

    it('Should handle various payment method values', () => {
      expect(worker.getPaymentMethod({ payment_method: 'Wire' })).toBe('Wire');
      expect(worker.getPaymentMethod({ payment_method: 'Check' })).toBe(
        'Check'
      );
      expect(worker.getPaymentMethod({ payment_method: '' })).toBe('ACH');
    });
  });
});
