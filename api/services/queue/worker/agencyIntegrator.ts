import { AccountIds, WorkerNames } from 'common/constants';
import type { Entity, SyncParamsDTO } from 'common/dto/data_processing/sync';
import { inject, injectable } from 'inversify';
import { nanoid } from 'nanoid';
import * as Sentry from '@sentry/nextjs';

import dayjs from '@/lib/dayjs';
import { Cache } from '@/lib/decorators';
import { getValidDate, limitConcurrency } from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { AgencyIntegratorService } from '@/services/agencyIntegrator';
import {
  type CarrierPartySearch,
  type Holding,
  InquiryLevel,
  LineOfBusiness,
  OperationTypeCodes,
  type Party,
  type Phone,
  type Policy,
  type PolicyProduct,
  type Relation,
} from '@/services/agencyIntegrator/interfaces';
import { CloudTaskService } from '@/services/cloud-task';
import { AppLoggerService } from '@/services/logger/appLogger';
import { Queue } from '@/services/queue/types';
import { StatementService } from '@/services/statement';
import {
  DataProcessingStatuses,
  DataProcessingTypes,
  DataStates,
  type ExtAccountInfo,
} from '@/types';
import {
  type AgentDatum,
  BaseWorker,
  type CompanyDatum,
  type DBData,
  type IDataSyncWorker,
  type PolicyDatum,
  type ProductDatum,
} from './base';
import { ContactService } from '@/services/contact';

const BROKERS_ALLIANCE_CARRIER_IDS = [
  '2',
  '3',
  '4',
  '7',
  '9',
  '19',
  '23',
  '24',
  '26',
  '28',
  '33',
  '35',
  '37',
  '53',
  '55',
  '64',
  '77',
  '78',
  '89',
  '94',
  '110',
  '166',
  '168',
  '205',
  '209',
  '210',
  '244',
  '245',
  '246',
  '269',
  '271',
  '275',
  '276',
  '286',
  '288',
  '322',
  '327',
  '342',
  '346',
  '397',
  '407',
  '409',
  '516',
  '700',
  '752',
  '812',
  '822',
  '950',
  '1023',
  '1056',
  '1066',
  '1101',
  '1182',
  '1245',
  '1253',
  '1264',
  '1271',
  '1279',
  '1322',
  '1352',
  '1414',
  '1440',
  '1459',
  '1465',
  '1478',
  '1497',
  '1533',
  '1613',
  '1647',
  '1664',
  '1701',
  '1722',
  '1790',
  '1827',
  '1854',
  '1890',
  '1901',
  '1926',
  '1946',
  '1959',
  '1987',
  '2008',
  '2013',
  '2025',
  '2042',
  '2048',
  '2061',
];

@injectable()
export class AgencyIntegratorWorker
  extends BaseWorker
  implements IDataSyncWorker<SyncParamsDTO>
{
  name = WorkerNames.AgencyIntegratorWorker;
  @inject(AgencyIntegratorService)
  agencyIntegratorService: AgencyIntegratorService;

  @inject(CloudTaskService) private cloudTaskService: CloudTaskService;

  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  @inject(StatementService) private statementService: StatementService;

  @inject(ContactService) contactService: ContactService;

  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: {
      service: WorkerNames.AgencyIntegratorWorker,
    },
  });

  constructor() {
    super();
    this.bootstrap();
  }

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  groupRelations(relations: any[] = []) {
    const groups = [];
    let currentGroup = null;

    for (const relation of relations) {
      if (
        relation.OriginatingObjectType === 'Party' &&
        relation.RelatedObjectType === 'Party' &&
        relation.RelationRoleCode === 'Agent Of Agency'
      ) {
        if (currentGroup) {
          groups.push(currentGroup);
        }
        currentGroup = {
          agentOfAgency: relation,
          relations: [],
        };
      } else if (currentGroup) {
        currentGroup.relations.push(relation);
      }
    }

    if (currentGroup) {
      groups.push(currentGroup);
    }

    return groups;
  }

  async setup(account: ExtAccountInfo) {
    const config = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        LoginName: string;
        Password: string;
        FileControlID: number;
      }>
    >({ account_id: account.account_id, worker: this.name });

    this.agencyIntegratorService.loadConfig(config.value.credentials);
  }

  async bootstrap() {
    this.registerDataSource({
      policies: this.fetchPolicies.bind(this),
      carriersAndProducts: this.fetchCarriersAndProducts.bind(this),
      agents: this.fetchAgents.bind(this),
      policySplits: this.fetchPoliciesToSyncSplits.bind(this),
    });

    this.registerTransformer({
      companyTransform: {
        getData: (data: { carriers: CarrierPartySearch[] }) =>
          data.carriers || [],
      },
      productTransform: {
        getData: this.getProductData.bind(this),
      },
      policySplitTransform: {
        getData: this.getSplits.bind(this),
      },
    });
  }

  async fetchPoliciesByPolNumbers(polNumbers: string[]) {
    this.logger.info(
      `Fetching policies by policy numbers: ${JSON.stringify(polNumbers)}`
    );
    const data = await limitConcurrency<
      Awaited<ReturnType<typeof this.agencyIntegratorService.policySearch>>
    >(
      async (polNumber) => {
        return await this.agencyIntegratorService.policySearch({
          filters: [
            {
              ObjectType: { '@tc': 18, '#text': 'Policy' },
              PropertyName: 'PolNumber',
              PropertyValue: polNumber,
              Operation: { '@tc': OperationTypeCodes.Equal },
            },
          ],
          inquiryLevel: InquiryLevel.All,
        });
      },
      polNumbers,
      30
    );
    const result = data.filter(
      (d) => (d.Holding as Holding)?.Policy?.PolNumber
    );
    this.logger.info(
      `Fetched ${result.length} policies, ${result.map((r) => (r.Holding as Holding)?.Policy?.PolNumber).join(', ')}`
    );
    return result;
  }

  @Cache('VIBE_FETCH_POLICIES')
  async fetchPolicies() {
    if (this.task.payload?.policyNumbers?.length > 0) {
      return await this.fetchPoliciesByPolNumbers(
        this.task.payload.policyNumbers
      );
    }
    const lastSync = await prismaClient.data_processing.findFirst({
      where: {
        account_id: this.task.account.account_id,
        type: DataProcessingTypes.data_sync,
        status: DataProcessingStatuses.COMPLETED,
        worker: this.name,
      },
      orderBy: {
        id: 'desc',
      },
    });

    let filters = [];
    const failedTasks = [];
    if (this.task.payload.isFullSync || !lastSync) {
      // Split the time range into days
      const startDate = dayjs('2023-01-01').startOf('month');
      const endDate = dayjs().add(1, 'day');
      let currentDate = startDate.clone();

      while (currentDate.isBefore(endDate)) {
        filters.push([
          {
            ObjectType: { '@tc': 18, '#text': 'Policy' },
            PropertyName: 'ApplicationCreateDate',
            PropertyValue: currentDate.format('YYYY-MM-DD'),
            Operation: { '@tc': OperationTypeCodes.Equal },
          },
        ]);
        currentDate = currentDate.add(1, 'day');
      }
    } else {
      filters = [
        [
          {
            ObjectType: { '@tc': 18, '#text': 'Policy' },
            PropertyName: 'CaseLastChanged',
            PropertyValue: dayjs(lastSync.created_at)
              .subtract(7, 'day')
              .format('YYYY-MM-DD'),
            Operation: { '@tc': OperationTypeCodes.GreaterThanEqualTo },
          },
        ],
      ];
    }

    const searchTasks = filters.map((filter) => ({
      filters: [...filter],
      inquiryLevel: InquiryLevel.BasicWithRelations,
    }));

    const results = await limitConcurrency(
      async (searchParams) => {
        try {
          const data =
            await this.agencyIntegratorService.policySearch(searchParams);
          return (
            Array.isArray(data?.Holding) ? data?.Holding : [data?.Holding]
          )?.map((holding) => ({
            Holding: holding,
            Relation: [],
          }));
        } catch (err) {
          this.logger.error(`Error fetching policies: ${JSON.stringify(err)}`);
          Sentry.captureException(err);
          throw err;
        }
      },
      searchTasks,
      30,
      {
        onFail: ({ error, data }) => {
          this.logger.error(
            `Failed to fetch policies with filters: ${JSON.stringify(data)}`
          );
          failedTasks.push(data);
          Sentry.captureException(error);
        },
      }
    );

    const allResults = results.filter(Boolean).flat();
    this.logger.warn(`Failed tasks: ${JSON.stringify(failedTasks)}`);

    return allResults?.filter((r) => r.Holding?.Policy?.PolNumber);
  }

  async fetchPoliciesToSyncSplits() {
    if (this.task.payload?.policyNumbers?.length > 0) {
      return await this.fetchPoliciesByPolNumbers(
        this.task.payload.policyNumbers
      );
    }

    const data = await prismaClient.report_data.findMany({
      where: {
        account_id: this.task.account.account_id,
        state: {
          in: [DataStates.ACTIVE, DataStates.GROUPED, DataStates.DUPLICATE],
        },
      },
      select: {
        id: true,
        sync_id: true,
      },
    });

    if (data.length === 0) {
      this.logger.warn('No policies found to sync splits');
      return [];
    }

    const result = await this.fetchPoliciesByPolNumbers(
      data.map((r) => r.sync_id)
    );
    return result;
  }

  async getAgentsFromPolicies() {
    const agents = await prismaClient.report_data.findMany({
      where: {
        account_id: this.task.account.account_id,
        state: DataStates.ACTIVE,
        agent_name: { not: null },
      },
      select: {
        agent_name: true,
      },
    });
    return [...new Set(agents.map((agent) => agent.agent_name))];
  }

  async searchForSalesReps() {
    const data = await this.agencyIntegratorService.partySearch({
      inquiryLevel: InquiryLevel.Basic,
      filters: [
        {
          ObjectType: { '@tc': 8, '#text': 'Relation' },
          PropertyName: 'RelationRoleCode',
          PropertyValue: 11,
          Operation: { '@tc': OperationTypeCodes.Equal },
        },
        {
          ObjectType: { '@tc': 8, '#text': 'Person' },
          PropertyName: 'CRM_Guid',
          PropertyValue: '',
          Operation: { '@tc': OperationTypeCodes.NotEqual },
        },
      ],
    });
    return data;
  }
  async getCRMID(
    agent: { firstName: string; lastName: string },
    accountId: string
  ) {
    return await prismaClient.contacts.findFirst({
      where: {
        first_name: agent.firstName,
        last_name: agent.lastName,
        account_id: accountId,
        sync_id: { not: null },
      },
    });
  }
  async searchByName(name: string, propertyName: 'FirstName' | 'LastName') {
    const data = await this.agencyIntegratorService.partySearch({
      inquiryLevel: InquiryLevel.All,
      filters: [
        {
          ObjectType: { '@tc': 8, '#text': 'Relation' },
          PropertyName: 'RelationRoleCode',
          PropertyValue: 11,
          Operation: { '@tc': OperationTypeCodes.Equal },
        },
        {
          ObjectType: { '@tc': 115, '#text': 'Person' },
          PropertyName: propertyName,
          PropertyValue: name,
          Operation: { '@tc': OperationTypeCodes.Equal },
        },
      ],
    });
    return data;
  }
  async fetchAgents() {
    const agentNames = await this.getAgentsFromPolicies();
    const nameParts: string[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    agentNames.forEach((name) => {
      const parts = name.split(',');
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      parts.forEach((part) => {
        const trimmedPart = part.trim();
        if (trimmedPart) {
          nameParts.push(trimmedPart);
        }
      });
    });
    const crms = await this.searchForSalesReps();
    const crmByFirstName = ((crms?.Party as Party[]) || [])
      .filter((r) => r?.Person?.FirstName)
      .map((r) => r?.Person?.FirstName);
    const uniqueNameParts = [...new Set([...nameParts, ...crmByFirstName])];
    const results = await limitConcurrency(
      async (agentName) => {
        return await this.searchByName(agentName.split(',')[0], 'FirstName');
      },
      uniqueNameParts,
      100,
      {
        retries: 1,
      }
    );
    const resultsByLastName = await limitConcurrency(
      async (agentName) => {
        return await this.searchByName(agentName.split(',')[0], 'LastName');
      },
      uniqueNameParts,
      100,
      {
        retries: 1,
      }
    );
    const Parties: Party[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    [...results, ...resultsByLastName].filter(Boolean).forEach((result) => {
      const resultParties = Array.isArray(result?.Party)
        ? result.Party
        : [result?.Party];
      Parties.push(...resultParties.filter(Boolean));
    });
    // Get unique parties using Map and PersonKey
    const uniqueParties = new Map(
      Parties.map((party) => [party.Person?.PersonKey, party])
    );
    return Array.from(uniqueParties.values());
  }

  async fetchCarriersAndProducts() {
    let data = await this.agencyIntegratorService.getCarriers();
    if (this.task.account.account_id === AccountIds.BROKERS_ALLIANCE) {
      data = data.filter((r) =>
        BROKERS_ALLIANCE_CARRIER_IDS.includes(`${r.Carrier.CarrierCode}`)
      );
    }
    const lineOfBusinesses = [
      LineOfBusiness.Life,
      LineOfBusiness.Annuity,
      LineOfBusiness.Disability,
      LineOfBusiness.Health,
      LineOfBusiness.LongTermCare,
    ];
    const productDetails = await limitConcurrency<PolicyProduct>(
      async (lineOfBusiness) => {
        const data = await this.agencyIntegratorService.planSearch({
          filters: [
            {
              ObjectType: { '@tc': 35, '#text': 'PolicyProduct' },
              PropertyName: 'LineOfBusiness',
              PropertyValue: lineOfBusiness,
              Operation: { '@tc': OperationTypeCodes.Equal },
            },
          ],
        });
        return data?.PolicyProduct;
      },
      lineOfBusinesses,
      10
    );
    const products = productDetails.flat();
    return { carriers: data, products };
  }

  getProductData(
    data: Awaited<ReturnType<typeof this.fetchCarriersAndProducts>>
  ) {
    const productSet = new Set();
    const products: PolicyProduct[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.products.forEach((item) => {
      if (!productSet.has(item?.ProductCode) && item?.ProductCode) {
        productSet.add(item?.ProductCode);
        products.push(item);
      }
    });
    return products;
  }

  productTransform(data: PolicyProduct, dbData: DBData): ProductDatum {
    return {
      company_id: dbData.companies?.get(`${data.CarrierCode}`)?.id,
      product_name: data.PlanName,
      product_type: data.LineOfBusiness,
      sync_id: `${data.ProductCode}`,
    };
  }

  companyTransform(data: CarrierPartySearch): CompanyDatum {
    return {
      company_name: data.FullName,
      type: ['Carrier'],
      company_id: `${data.Carrier?.CarrierCode}`,
      sync_id: `${data.Carrier?.CarrierCode}`,
    };
  }

  // Overriding the base method to use the combination of agent's first name and last name and agentCode
  async getLinkingPolicyAgents(
    agents: { firstName: string; lastName: string; agentCode: string }[],
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    account_id: any
  ) {
    const contacts = await Promise.all(
      agents.map(async (agent) => {
        if (agent.agentCode) {
          const results = await this.contactService.getContacts({
            where: {
              account_id: account_id,
              agent_code: agent.agentCode,
              state: DataStates.ACTIVE,
            },
          });
          if (results.length <= 1) {
            return results;
          } else {
            const synced = results.find((r) => r.sync_id);
            return synced ? [synced] : results;
          }
        } else {
          return super.getLinkingPolicyAgents([agent], account_id);
        }
      })
    );
    return contacts.flat();
  }
  async populatePolicyFields(payload: PolicyDatum, account: ExtAccountInfo) {
    const data = await this.agencyIntegratorService.policySearch({
      inquiryLevel: InquiryLevel.All,
      filters: [
        {
          ObjectType: { '@tc': 1, '#text': 'Policy' },
          PropertyName: 'PolNumber',
          PropertyValue: payload.policy_id,
          Operation: { '@tc': OperationTypeCodes.Equal },
        },
      ],
    });

    const item = data?.Holding as Holding;
    if (!item?.Policy) {
      this.logger.warn(`${this.name}: no policy found for ${payload.sync_id}`);
      return;
    }

    const updateData = {
      policy_status: item.Policy.PolicyStatus,
      agent_name: data.Relation.find(
        (r) => r.RelationRoleCode === 'Writing Agent'
      )?.OLifEExtension?.RelationName,
      issue_age: item.Policy.OLifEExtension?.IssueAge,
      effective_date: getValidDate(item.Policy.EffDate),
      policy_date: getValidDate(
        item.Policy?.ApplicationInfo?.OLifEExtension?.PlacedDate
      ),
      notes: item.Policy.OLifEExtension?.CasePremiumNotes,
    };

    this.logger.info(
      `updating ${payload.sync_id} with ${JSON.stringify(updateData)}`,
      updateData
    );
    await prismaClient.report_data.updateMany({
      where: { sync_id: payload.sync_id, account_id: account?.account_id },
      data: updateData,
    });

    // Get all agent relations
    const agentRelations = data.Relation?.filter(
      (r) =>
        r.RelationRoleCode === 'Writing Agent' || r.RelationRoleCode === 'Agent'
    );

    if (agentRelations?.length > 0) {
      // Map of GovtID to agent details from relations
      const agentDetails = new Map(
        agentRelations.map((r) => [
          r.OLifEExtension.RelationGovtID,
          {
            name: r.OLifEExtension.RelationName,
            govtId: r.OLifEExtension.RelationGovtID,
          },
        ])
      );

      // Find all agent parties
      const agentParties = data?.Party?.filter(
        (p) =>
          p.PartyTypeCode === 'Person' && p.GovtID && agentDetails.has(p.GovtID)
      );

      // Find CRM Owner if exists
      const crmOwnerParty = data?.Party?.find(
        (p) =>
          p.PartyTypeCode === 'Person' && p.Person?.OLifEExtension?.CRMOwnerID
      );

      let agents = [];

      if (agentParties?.length > 0) {
        // Prepare agents data for linking
        agents = agentParties.map((party) => {
          const { LastName, FirstName } = party.Person || {};
          return {
            firstName: FirstName,
            lastName: LastName,
            agentCode: party.Person?.PersonKey?.toString(),
          };
        });
      }

      // Add CRM Owner to the agents list if found
      if (crmOwnerParty?.Person?.OLifEExtension) {
        const { CRMOwnerFirstName, CRMOwnerLastName } =
          crmOwnerParty.Person.OLifEExtension;
        const crm = await this.getCRMID(
          {
            firstName: CRMOwnerFirstName,
            lastName: CRMOwnerLastName,
          },
          account.account_id
        );
        if (CRMOwnerFirstName && CRMOwnerLastName && crm) {
          agents.push({
            firstName: CRMOwnerFirstName,
            lastName: CRMOwnerLastName,
            agentCode: crm.agent_code, // CRM Owner doesn't have an agent code
          });
        }
      }

      // Link all agents to the policy if we have any
      if (agents.length > 0) {
        await this.linkingPolicyAgents({
          syncId: payload.sync_id,
          agents,
          opts: { contactStatus: DataStates.ACTIVE },
          account_id: account?.account_id,
        });
      }
    }

    await this.syncAgentSplits(data, account);

    return data;
  }

  async getAgentSplits(
    data: Awaited<ReturnType<typeof this.agencyIntegratorService.policySearch>>,
    account: ExtAccountInfo
  ) {
    const agentRelations =
      data.Relation?.filter(
        (r) =>
          r.RelationRoleCode === 'Writing Agent' ||
          r.RelationRoleCode === 'Agent'
      ) || [];

    // Create a map of GovtID to splits
    const govtIdToSplits = new Map(
      agentRelations.map((r) => [
        r.OLifEExtension.RelationGovtID,
        r.InterestPercent,
      ])
    );

    // Create a map of GovtID to PartyKey
    const govtIdToPartyKey = new Map(
      data?.Party?.filter((p) => p.PartyTypeCode === 'Person' && p.GovtID).map(
        (p) => [p.GovtID, p.PartyKey?.toString()]
      )
    );

    const partyKeys = Array.from(govtIdToPartyKey.values()).filter(Boolean);
    const agents = await prismaClient.contacts.findMany({
      where: {
        account_id: account.account_id,
        state: DataStates.ACTIVE,
        sync_id: { in: partyKeys },
      },
    });

    const splits = agents.reduce((acc, agent) => {
      const govtId = Array.from(govtIdToPartyKey.entries()).find(
        ([_, partyKey]) => partyKey === agent.sync_id
      )?.[0];

      // Get the split percentage for this agent
      const split = govtId ? govtIdToSplits.get(govtId) : null;

      // biome-ignore lint/performance/noAccumulatingSpread: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      return split ? { ...acc, [agent.str_id]: split } : acc;
    }, {});
    return splits;
  }

  async getSplits(
    data: Awaited<ReturnType<typeof this.fetchPoliciesToSyncSplits>>
  ) {
    return await limitConcurrency<{
      sync_id: string;
      splits: { [agentStrId: string]: number };
    }>(
      async (
        policy: Awaited<
          ReturnType<typeof this.agencyIntegratorService.policySearch>
        >
      ) => {
        const split = await this.getAgentSplits(policy, this.task.account);
        return {
          sync_id: (policy?.Holding as Holding)?.Policy?.PolNumber?.toString(),
          splits: split,
        };
      },
      data,
      30
    );
  }

  policySplitTransform(
    data: { sync_id: string; splits: { [agentStrId: string]: number } },
    _dbData?: DBData
  ) {
    return {
      sync_id: data.sync_id,
      contacts_split: data.splits,
    };
  }

  async syncAgentSplits(
    data: Awaited<ReturnType<typeof this.agencyIntegratorService.policySearch>>,
    account: ExtAccountInfo
  ) {
    // Get all agent relations (both Writing Agent and Agent roles)
    const splits = await this.getAgentSplits(data, account);
    if (Object.keys(splits).length > 0) {
      const holding = data.Holding as Holding;
      this.logger.debug(
        `Updating ${holding.Policy.PolNumber} with splits: ${
          // biome-ignore lint/complexity/noCommaOperator: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          (JSON.stringify(splits), { splits })
        }`
      );
      await prismaClient.report_data.updateMany({
        where: {
          sync_id: holding?.Policy?.PolNumber?.toString(),
          account_id: account.account_id,
        },
        data: {
          contacts_split: splits,
        },
      });
    }
  }

  async afterSyncHook(context: {
    entity: Entity;
    data: unknown;
    dbData?: DBData;
    account: ExtAccountInfo;
    taskData?: unknown[];
  }) {
    const { entity, account, taskData } = context;
    const concurrency = 50;
    if (entity === 'policies') {
      await limitConcurrency(
        async (policy: PolicyDatum) => {
          return await this.cloudTaskService.createTask({
            account: undefined,
            task_id: nanoid(),
            type: DataProcessingTypes.data_sync,
            queue: Queue.AGENCY_INTEGRATOR_SYNC,
            payload: { item: policy, account },
          });
        },
        taskData as PolicyDatum[],
        concurrency
      );
    }
  }

  policyTransform(
    data: {
      Holding: Holding | Holding[];
      Relation?: Relation[];
      Party?: Party[];
    },
    dbData?: DBData
  ): PolicyDatum {
    const { Holding: holding = {} } = data;
    const { Policy: policy = {} as Policy } = holding as Holding;
    const company = dbData?.companies?.get(`${policy.CarrierCode}`);
    const result = {
      sync_id: `${policy.PolNumber}`,
      agent_name: undefined,
      writing_carrier_name:
        policy.OLifEExtension?.CaseCarrierName || company?.company_name,
      company_id: company?.id,
      product_type: policy.ProductType,
      product_name: policy.PlanName,
      customer_name: policy.OLifEExtension?.CaseFullName,
      issue_age: undefined,
      policy_date: undefined,
      payment_mode: policy.PaymentMode,
      premium_amount:
        policy?.Life?.FullyCommPremAnnualized ||
        policy?.OLifEExtension?.TargetPremium,
      effective_date: undefined,
      policy_id: `${policy.PolNumber}`,
      company_product_id: dbData?.company_products?.get(
        policy?.ProductCode?.toString()
      )?.id,
      policy_status: policy.PolicyStatus,
      notes: undefined,
      commissionable_premium_amount: policy.AnnualPaymentAmt,
      // The contacts will be populated in the afterSyncHook
      contacts: undefined,
    };
    if (this.task?.account?.account_id === AccountIds.BROKERS_ALLIANCE) {
      result.commissionable_premium_amount =
        policy?.Life?.FullyCommPremAnnualized ||
        policy?.OLifEExtension?.TargetPremium;
      result.premium_amount = policy.AnnualPaymentAmt;
    }
    return result;
  }

  formatPhone(phone: Phone) {
    if (!phone) return null;
    let formattedPhone = `${phone.AreaCode}-${phone.DialNumber}`;
    if (phone.Ext) {
      formattedPhone += ` ext ${phone.Ext}`;
    }
    return formattedPhone;
  }
  agentTransform(data: Party, _dbData?: DBData): AgentDatum {
    const address = Array.isArray(data?.Address)
      ? data?.Address[0]
      : data?.Address
        ? [data?.Address]
        : [];
    const phones = Array.isArray(data?.Phone)
      ? data?.Phone
      : data?.Phone
        ? [data?.Phone]
        : [];
    return {
      sync_id: `${data?.Person?.PersonKey}`,
      name: data?.FullName,
      birthday: getValidDate(data?.Person?.BirthDate),
      first_name: data?.Person?.FirstName,
      last_name: data?.Person?.LastName,
      middle_name: data?.Person?.MiddleName,
      agent_code: data?.Person?.PersonKey.toString() || null,
      gender: data?.Person?.Gender,
      email: data?.EMailAddress?.AddrLine,
      title: data?.Person?.Title ? `${data?.Person?.Title}` : null,
      zip: address?.Zip ? `${address?.Zip}` : null,
      city: address?.City ? `${address?.City}` : null,
      geo_state: address?.AddressState ? `${address?.AddressState}` : null,
      country: address?.AddressCountryTC
        ? `${address?.AddressCountryTC}`
        : null,
      status: data?.Producer?.OLifEExtension?.Status ?? null,
      phone: this.formatPhone(phones[0]),
      phone_type: phones[0]?.PhoneTypeCode ?? null,
      phone2: this.formatPhone(phones[1]),
      phone2_type: phones[1]?.PhoneTypeCode ?? null,
    };
  }
}
