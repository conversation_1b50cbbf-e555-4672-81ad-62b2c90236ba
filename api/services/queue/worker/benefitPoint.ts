import {
  type companies,
  customer_gender,
  customer_type,
  type Prisma,
  type report_data,
  type statement_data,
} from '@prisma/client';
import type { SyncParamsDTO } from 'common/dto/data_processing/sync';
import { inject, injectable } from 'inversify';
import { groupBy } from 'lodash-es';
import * as math from 'mathjs';
import { AccountIds, WorkerNames } from 'common/constants';
import pRetry from 'p-retry';
import type { AxiosError } from 'axios';

import { Cache } from '@/lib/decorators';
import { BusinessException } from '@/lib/exceptionHandler';
import { getValidDate, limitConcurrency, runInBatch } from '@/lib/helpers';
import { prismaClient } from '@/lib/prisma';
import { AccountProcessorConfigService } from '@/services/account-processor-config';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { BenefitPointService } from '@/services/benefitPoint';
import {
  type Account,
  AccountClassifications,
  type AccountList,
  AccountType,
  Gender,
  type ProductDetail,
  type ProductList,
  type UserDetail,
  UserRole,
} from '@/services/benefitPoint/interface';
import { CompaniesService } from '@/services/companies';
import { ContactService } from '@/services/contact';
import { DataProcessService } from '@/services/data_processing';
import { AppLoggerService } from '@/services/logger/appLogger';
import {
  BaseWorker,
  type CompanyDatum,
  type CustomerDatum,
  type DBData,
  type IDataSyncWorker,
  type PolicyDatum,
  type ProductDatum,
} from '@/services/queue/worker/base';
import { StatementService } from '@/services/statement';
import {
  DataProcessingStatuses,
  DataProcessingTypes,
  DataStates,
  type ExtAccountInfo,
} from '@/types';
import dayjs from '@/lib/dayjs';

export const updateRequiredFields = [
  'productID',
  'accountID',
  'name',
  'carrierID',
  'effectiveAsOf',
  'continuousPolicy',
  'unionProduct',
  'nonPayable',
  'nonRevenue',
  'renewalOn',
  'billingCarrierID',
  'officeID',
  'departmentID',
  'primarySalesLeadUserID',
  'primaryServiceLeadUserID',
];
const tz = 'America/Los_Angeles';

interface MemberCountConfig {
  count: number;
  period_date: string;
}
@injectable()
export class BenefitPointWorker
  extends BaseWorker
  implements IDataSyncWorker<SyncParamsDTO>
{
  name = WorkerNames.BenefitPointWorker;

  @inject(ContactService) contactService: ContactService;
  @inject(BenefitPointService) benefitPointService: BenefitPointService;
  @inject(AccountProcessorConfigService)
  configItemService: AccountProcessorConfigService;

  @inject(StatementService)
  private statementService: StatementService;

  @inject(CompaniesService)
  private companyService: CompaniesService;

  @inject(DataProcessService)
  private dataProcessService: DataProcessService;

  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: WorkerNames.BenefitPointWorker },
  });

  private readonly BENEFITS_GROUP_OFFICE_ID = 33218;

  constructor() {
    super();

    this.registerDataSource({
      policies: this.fetchPolicies.bind(this),
      carriersAndProducts: this.fetchPolicies.bind(this),
      customers: this.fetchCustomers.bind(this),
    });

    this.registerTransformer({
      companyTransform: {
        getData: this.getCompanyData.bind(this),
      },
      productTransform: {
        getData: this.getProductData.bind(this),
      },
    });
  }

  async setup(account: ExtAccountInfo) {
    const config = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        brokerageId: number;
        username: string;
        password: string;
      }>
    >({ account_id: account.account_id, worker: this.name });

    this.benefitPointService.loadConfig(config.value.credentials);
  }
  /**
   *  We cannot get carrier name from the search carrier accounts api,
   *  instead we get the carrier info from the search products api
   * */
  getCompanyData(data: ProductList[]) {
    const companySet = new Set();
    const companies: ProductList[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((item) => {
      if (!companySet.has(item.carrierID)) {
        companySet.add(item.carrierID);
        companies.push(item);
      }
    });
    return companies;
  }

  getProductData(data: ProductList[]) {
    const productSet = new Set();
    const products: ProductList[] = [];
    data.filter((item) => {
      if (!productSet.has(item.productSyncId)) {
        productSet.add(item.productSyncId);
        products.push(item);
      }
    });
    return products;
  }

  companyTransform(data: ProductList): CompanyDatum {
    return {
      sync_id: `${data.carrierID}`,
      company_name: data.carrierName,
      company_id: `${data.carrierID}`,
      type: ['Carrier'],
    };
  }

  productTransform(data: ProductList, dbData?: DBData): ProductDatum {
    return {
      sync_id: `${data.productSyncId}`,
      company_id: dbData.companies?.get(`${data.carrierID}`)?.id,
      product_type: data.productType,
      product_name: data.name,
    };
  }

  @Cache('AllAccounts')
  private async getAllAccounts() {
    const data = await this.benefitPointService.searchAccounts();
    return data;
  }

  async fetchCustomers() {
    const accounts = await this.benefitPointService.searchAccounts({
      accountTypes: AccountType.CLIENT,
    });
    const customers = (await limitConcurrency(
      async (account: AccountList) => {
        return await this.benefitPointService.queryAccount(account.accountID);
      },
      accounts,
      100
    )) as Account[];
    return customers;
  }

  getCustomerTypeFromAccountClassification(
    accountClassification: AccountClassifications
  ) {
    if (accountClassification === AccountClassifications.INDIVIDUAL) {
      return customer_type.individual;
    }
    if (accountClassification === AccountClassifications.GROUP) {
      return customer_type.group;
    }
    return null;
  }

  getGender(gender: Gender) {
    if (gender === Gender.MALE) {
      return customer_gender.male;
    }
    if (gender === Gender.FEMALE) {
      return customer_gender.female;
    }
    return null;
  }

  customerTransform(data: Account): CustomerDatum {
    const isIndividual =
      data?.accountClassification === AccountClassifications.INDIVIDUAL;
    const personInfo = data?.individualAccountInfo?.personInfo;
    const groupAccountInfo = data?.groupAccountInfo;
    return {
      sync_id: `${data.accountID}`,
      address: {
        street: data?.mainAddress?.street1,
        street2: data?.mainAddress?.street2,
        city: data?.mainAddress?.city,
        geo_state: data?.mainAddress?.state,
        zipcode: data?.mainAddress?.zip,
        country: data?.mainAddress?.country,
      },
      company_name: groupAccountInfo?.accountName,
      dob: isIndividual ? getValidDate(personInfo?.dateOfBirth) : null,
      type: this.getCustomerTypeFromAccountClassification(
        data?.accountClassification
      ),
      email: isIndividual ? data?.individualAccountInfo?.email : null,
      phone: isIndividual ? data?.individualAccountInfo?.phone?.number : null,
      gender: isIndividual ? this.getGender(personInfo?.gender) : null,
      first_name: isIndividual ? personInfo?.firstName : null,
      last_name: isIndividual ? personInfo?.lastName : null,
      middle_name: isIndividual ? personInfo?.middleName : null,
      nickname: isIndividual ? personInfo?.firstName : null,
    };
  }
  @Cache('AllPolicies')
  async fetchPolicies(_dbData, account: ExtAccountInfo) {
    const data = await this.getAllAccounts();

    const contacts = await this.contactService.getContacts({
      where: {
        state: DataStates.ACTIVE,
        account_id: account.account_id,
      },
    });
    const getName = ({ firstName, lastName }) =>
      [firstName?.trim(), lastName?.trim()].join(' ');
    const contactsMap = groupBy(contacts, (item) =>
      getName({ firstName: item.first_name, lastName: item.last_name })
    );
    const customers = data.filter(
      (r) =>
        r.accountType === AccountType.CLIENT ||
        r.accountType === AccountType.PROSPECT
    );

    const policies = (await runInBatch({
      items: customers,
      name: 'Fetch Account policies',
      batchSize: 100,
      onBatch: async (items: AccountList[]) => {
        return await Promise.all(
          items.map((item) =>
            pRetry(() =>
              this.benefitPointService
                .searchProducts({
                  accountId: item.accountID,
                })
                .catch((e) => {
                  this.logger.error(e);
                  return [];
                })
            )
          )
        );
      },
    })) as ProductList[];
    const policyDetailList: ProductDetail[] = await runInBatch({
      items: policies,
      name: 'Fetch policy detail',
      batchSize: 100,
      onBatch: async (items: ProductList[]) => {
        const details = await Promise.all(
          items.map((item) =>
            pRetry(
              () => this.benefitPointService.getProductDetail(item?.productID),
              {
                retries: 3,
              }
            ).catch((e) => {
              this.logger.error(e);
              return null;
            })
          )
        );
        return details.filter((r) => r);
      },
    });
    const agents = await this.benefitPointService.queryUsers({
      roles: [UserRole.BROKER_USER],
    });
    const agentMap = new Map(agents.map((r) => [r.id, r]));

    const policyDetailMap = new Map(
      policyDetailList.map((r: ProductDetail) => [`${r.productID}`, r])
    );

    const productTypes = await this.benefitPointService.getProductTypes();
    const productTypeMap = new Map(
      productTypes.map((r) => [r.productTypeID, r])
    );
    return policies.map((r) => {
      const agent = agentMap.get(
        policyDetailMap.get(`${r?.productID}`)?.primarySalesLeadUserID
      );
      const contacts = (
        agent ? (contactsMap[getName(agent)] ?? []) : []
      ).filter((r) => r);
      const productDetail = policyDetailMap.get(`${r?.productID}`);
      return {
        ...r,
        agent,
        accountID: productDetail?.accountID,
        contact_str_id: contacts?.length === 1 ? contacts[0].str_id : undefined,
        productSyncId: `${r?.carrierID}::${r.name?.trim()}`,
        productType: productTypeMap.get(r.productTypeID).longDescription,
      };
    });
  }

  isInEffectiveRange(params: {
    periodDate: Date;
    effectiveDate: Date;
    terms: number;
  }) {
    const { periodDate, effectiveDate, terms } = params;
    if (!terms) {
      return dayjs(periodDate)
        .startOf('day')
        .isSameOrAfter(dayjs(effectiveDate).startOf('day'));
    }
    return (
      dayjs(periodDate)
        .startOf('day')
        .isBefore(dayjs(effectiveDate).startOf('day').add(terms, 'month')) &&
      dayjs(periodDate)
        .startOf('day')
        .isSameOrAfter(dayjs(effectiveDate).startOf('day'))
    );
  }
  policyTransform(
    data: ProductList & {
      agent?: UserDetail;
      contact_str_id?: string;
      accountID: number;
    },
    dbData?: DBData
  ): PolicyDatum {
    return {
      sync_id: `${data.productID}`,
      policy_id: `${data.policyNumber}`,
      company_id: dbData.companies?.get(`${data.carrierID}`)?.id,
      internal_id: `${data.productID}`,
      writing_carrier_name: data.carrierName,
      product_name: data.name,
      product_type: `${data.productType}`,
      effective_date: getValidDate(data.effectiveAsOf),
      company_product_id: dbData.company_products?.get(`${data.productSyncId}`)
        ?.id,
      policy_term_months:
        data.renewalOn && data.effectiveAsOf
          ? dayjs
              .utc(data.renewalOn)
              .diff(dayjs.utc(data.effectiveAsOf), 'months')
          : null,
      policy_status: data.productStatus,
      customer_name: data.accountName,
      product_sub_type:
        this.task?.account?.account_id === AccountIds.RISK_TAG
          ? this.getRiskTagSubType(data.productType)
          : null,
      transaction_type: data.policyOriginationReason,
      notes: `
      Origination Reason: ${data.policyOriginationReason}
      Lose Reason: ${data.policyCancellationReason}
      `,
      customer_id: dbData.customers?.get(`${data.accountID}`)?.id,
      contacts: data.contact_str_id ? [data.contact_str_id] : [],
      agent_name: data?.agent
        ? `${data.agent.firstName} ${data.agent.lastName}`
        : '',
    };
  }

  getPaymentMethod(document: { payment_method: string }) {
    return document.payment_method || 'ACH';
  }

  diffHours(timestamp) {
    const tz = 'America/Los_Angeles';

    const utcTime = dayjs.utc(timestamp);
    const pstTime = utcTime.tz(tz, true);
    return utcTime.diff(pstTime, 'hours');
  }

  applyTimezoneOffset(datetime: Date) {
    return datetime ? dayjs.tz(datetime.toISOString(), tz) : null;
  }

  getRiskTagSubType(productType: string) {
    const productTypeMap = {
      'Health Reimbursement Arrangement': 'Medical',
      'HSA / HRA Admin': 'Medical',
      'Medical EPO (1-Tier)': 'Medical',
      'Medical EPO (2-Tier)': 'Medical',
      'Medical Gap': 'Medical',
      'Medical HMO': 'Medical',
      'Medical Indemnity': 'Medical',
      'Medical POS (2-Tier)': 'Medical',
      'Medical POS (3-Tier)': 'Medical',
      'Medical PPO': 'Medical',

      Accident: 'Ancillary',
      'AD&D': 'Ancillary',
      'Critical Illness': 'Ancillary',
      'Dental - Other': 'Ancillary',
      'Dental PPO': 'Ancillary',
      'Disability Plans - Other': 'Ancillary',
      'Employee Assistance Program (EAP)': 'Ancillary',
      'FMLA Administration': 'Ancillary',
      'FSA Admin': 'Ancillary',
      'Group Term Life': 'Ancillary',
      'Hospital Indemnity': 'Ancillary',
      'Hospitalization Only': 'Ancillary',
      'Life - Universal Life': 'Ancillary',
      'Life and AD&D': 'Ancillary',
      'Long Term Disability (LTD)': 'Ancillary',
      'New York DBL': 'Ancillary',
      'Permanent Life': 'Ancillary',
      'Pet Insurance': 'Ancillary',
      'Short Term Disability (STD)': 'Ancillary',
      Vision: 'Ancillary',
      'Voluntary Accident': 'Ancillary',
      'Voluntary AD&D': 'Ancillary',
      'Voluntary Critical Illness': 'Ancillary',
      'Voluntary Dental': 'Ancillary',
      'Voluntary Life': 'Ancillary',
      'Voluntary Life and AD&D': 'Ancillary',
      'Voluntary Life/AD&D': 'Ancillary',
      'Voluntary Long Term Disability (LTD)': 'Ancillary',
      'Voluntary Short Term Disability (STD)': 'Ancillary',
      'Voluntary Vision': 'Ancillary',

      'Medicare Advantage': 'Medicare',
      'Medicare Part D': 'Medicare',
      'Medicare Supplement': 'Medicare',

      'Benefits Administration': 'Other',
      'COBRA Administration': 'Other',
      Other: 'Other',

      'Prescription Drug (Carve-Out)': 'Self-Funded Medical',
      'Stop Loss': 'Self-Funded Medical',

      '401(k)': '401k',

      'IND - INDIV TERM LIFE (FCC)': 'Individual',
      'Individual Dental': 'Individual',
      'Individual Disability': 'Individual',
      'Individual Medical': 'Individual',
      'Individual Vision': 'Individual',
      'Whole Life - Individual': 'Individual',
    };

    return productTypeMap[productType] || null;
  }

  async syncBenefitPointStatements(data: {
    account: ExtAccountInfo;
    documentId: string;
  }) {
    const { account, documentId } = data;
    const startTime = Date.now();
    await this.setup(data.account);
    // This.benefitPointService.usePrviewEndpoint();

    const task = await this.dataProcessService.create({
      account: { connect: { str_id: account.account_id } },
      user: { connect: { uid: account.uid } },
      type: DataProcessingTypes.sync,
      params: JSON.stringify(data),
      status: DataProcessingStatuses.PENDING,
    });
    const stats = {
      update: [],
      create: [],
      invalid: [],
      alerts: [],
      actualCreated: 0,
      grouped: 0,
      statementId: null,
      company: {},
    };
    const invalidPaymentDateStatements = [];

    try {
      const statements = (await this.statementService.getStatementData(
        {
          account_id: account.account_id,
          document_id: documentId,
          state: DataStates.ACTIVE,
        },
        { report: true }
      )) as unknown as (statement_data & { report: report_data })[];

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      statements.forEach((statement) => {
        if (
          !this.isInEffectiveRange({
            periodDate: statement.period_date,
            effectiveDate: statement.report.effective_date,
            terms: statement.report.policy_term_months,
          })
        ) {
          invalidPaymentDateStatements.push(statement.id);
        }
      });

      if (invalidPaymentDateStatements.length) {
        throw BusinessException.from(
          `Payment dates must be within effective date range. statementIds with invalid data: ${invalidPaymentDateStatements.join(', ')}`
        );
      }
      const document = await prismaClient.documents.findFirst({
        where: {
          str_id: documentId,
          account_id: account.account_id,
        },
        include: { companies: true },
      });

      if (document.companies?.id && !document.companies.sync_id) {
        stats.alerts.push(
          `Company "${document.companies.company_name}" is not synced from BenefitPoint. Linked policy's company must be one synced from BenefitPoint to post statement.`
        );
      }
      // Find standard company synced from benefit point endpoint, we need to use the carrier id when posting a statement
      const company: companies = await this.companyService.queryOne({
        account_id: account.account_id,
        OR: [
          {
            id: document?.companies?.id,
          },
          {
            company_name: document?.companies?.company_name,
          },
          {
            id: {
              in: statements.map((r) => r?.report?.company_id).filter((r) => r),
            },
          },
        ],
        sync_id: { not: null },
      });
      if (!company) {
        throw BusinessException.from(
          `Can't find billiingCarrierId for company ${document?.companies?.company_name}`
        );
      }
      const reportCompanyIds = [
        ...new Set(
          statements.map((r) => r?.report?.company_id).filter((r) => r)
        ),
      ];
      if (reportCompanyIds.length > 1) {
        const companies = await this.companyService.queryAll({
          id: { in: reportCompanyIds },
        });

        // Group statements by company
        const statementsByCompany = statements.reduce((acc, statement) => {
          const companyId = statement.report?.company_id;
          if (!acc[companyId]) {
            acc[companyId] = [];
          }
          acc[companyId].push(statement.id);
          return acc;
        }, {});

        // Find the company with the most statements
        const companyCounts = Object.entries(statementsByCompany).map(
          ([companyId, statements]) => ({
            companyId,
            // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            count: (statements as any[]).length,
          })
        );
        const maxCount = Math.max(...companyCounts.map((c) => c.count));

        const errorMessage = companies
          .map((company) =>
            statementsByCompany[company.id].length < maxCount
              ? `The following statements belong to the company: ${company.company_name}.\nStatement IDs: ${statementsByCompany[company.id].join(', ')}`
              : `The majority of statements belong to the company: ${company.company_name}`
          )
          .join('\n');

        throw BusinessException.from(
          `Document "${documentId}" contains statements from multiple companies: ${companies.map((r) => r.company_name).join(', ')}.\nAll statements should belong to the same company.\n${errorMessage}`
        );
      }

      stats.company = {
        id: company.id,
        name: company.company_name,
        sync_id: company.sync_id,
      };
      if (document.sync_id) {
        try {
          const statement = await this.benefitPointService.getStatement(
            +document.sync_id
          );
          if (statement.statementStatus === 'CLOSED') {
            throw BusinessException.from(
              `Statement ${document.sync_id} is already closed, please create a new document to sync`
            );
          }
          if (statement) {
            await this.benefitPointService.deleteStatement(+document.sync_id);
          }
          // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        } catch (err: any) {
          if ((err as AxiosError).status === 404) {
            this.logger.warn(
              `Statement ${document.sync_id} not found, skipping delete`
            );
          } else if (err.response.data !== 'Statement not found') {
            this.logger.warn(
              `Error deleting statement: ${document.sync_id}`,
              err
            );
            throw BusinessException.from(err.response.data || err.message);
          }
        }
      }
      const today = this.applyTimezoneOffset(dayjs().toDate());
      const totalAmount = statements.reduce(
        (acc, r) =>
          math
            .chain(acc)
            .add(r.commission_amount || 0)
            .done(),
        0
      );
      await this.dataProcessService.updateTaskStatus({
        str_id: task.str_id,
        status: DataProcessingStatuses.PROCESSING,
      });

      const productDetails: ProductDetail[] = await runInBatch({
        name: 'getProductDetails',
        items: statements
          .map((r) => ({
            productId: r.report?.sync_id || r.report?.internal_id,
            report_id: r.report?.id,
            statement: r,
          }))
          .filter((r) => r.report_id),
        onBatch: async (
          items: {
            productId: number;
            report_id: number;
            statement: statement_data;
          }[]
        ) => {
          return Promise.all(
            items.map((item) => {
              if (!item.productId) {
                const alert = `Invalid statement - statementId: ${item.statement.id}, policyId: ${item.report_id}, policy number: ${item.statement.policy_id} with productId: ${item.productId}`;
                this.logger.warn(alert);
                stats.alerts.push(alert);
                return null;
              }
              return this.benefitPointService
                .getProductDetail(item.productId)
                .catch((err) => {
                  const alert =
                    err.status === 404
                      ? `${err.response.data} for statement - statementId: ${item.statement.id}, policyId: ${item.report_id}, policy number: ${item.statement.policy_id} with productId: ${item.productId}`
                      : `Invalid statement - statementId: ${item.statement.id}, policyId: ${item.report_id}, policy number: ${item.statement.policy_id} with productId: ${item.productId}`;
                  this.logger.warn(alert);
                  stats.alerts.push(alert);
                  return null;
                });
            })
          );
        },
        batchSize: 30,
      });

      const carrierList = await this.benefitPointService.getCarrierList();
      const carrierMap = new Map(
        (await carrierList).map((r) => [r.carrierId, r.carrierName])
      );

      // Product should be under the same carrier, if not then the linked report is invalid
      const productMap = new Map(
        productDetails.filter((r) => r).map((r) => [r.productID, r])
      );
      const validStatements = [];
      const invalidStatements = [];

      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let billingCarrierId;
      // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      let statementDate;
      const checkDate = this.applyTimezoneOffset(document.check_date)?.format();
      const depositDate = this.applyTimezoneOffset(
        document.deposit_date
      )?.format();

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      statements.forEach((r) => {
        const productId = +(r.report?.sync_id || r.report?.internal_id);
        const product = productMap.get(productId);
        if (!productId || !product) {
          // TODO: Figure out how to handle billingCarrierID vs carrierID discrepancies
          // if (!productId || product?.billingCarrierID !== +company.sync_id) {
          invalidStatements.push(r);
          let invalidReason = '';
          if (!productId) {
            invalidReason = `Invalid productId: ${productId}`;
          } else if (!product) {
            invalidReason = `Invalid product: ${productId}, unable to find product in BenefitPoint`;
          } else if (product?.billingCarrierID !== +company.sync_id) {
            invalidReason = `Invalid billingCarrierID: ${product?.billingCarrierID} vs ${company.sync_id}`;
          }
          stats.invalid.push({
            statement: r.id,
            productId: productId,
            report_id: r.report?.id,
            invalidReason,
          });
        } else {
          if (!billingCarrierId) {
            billingCarrierId = product.billingCarrierID;
          } else if (billingCarrierId !== product.billingCarrierID) {
            throw BusinessException.from(
              'Statement contains multiple billing carriers'
            );
          }
          if (!statementDate) {
            statementDate = this.applyTimezoneOffset(r.payment_date);
          } else if (
            r.payment_date &&
            statementDate?.toDate().getTime() !==
              this.applyTimezoneOffset(r.payment_date).toDate().getTime()
          ) {
            throw BusinessException.from(
              'Statement contains multiple statement dates (payment dates)'
            );
          }
          validStatements.push(r);
          stats.create.push(r.id);
        }
      });

      const taskData = validStatements.map(
        (statement: statement_data & { report: report_data }) => ({
          productID:
            +statement.report?.sync_id || +statement.report?.internal_id,
          revenueAmount: statement.commission_amount || 0,
          premiumAmount: statement.premium_amount || 0,
          applyToDate: this.applyTimezoneOffset(statement.period_date).format(),
          posted: true,
          splitColumnType: 'COMMISSION',
          override: false,
          numOfLIves: null,
          sagittaTransactionCode: null,
          activityLogRecordID: null,
          statementSplitID: null,
        })
      );
      // Group taskData by productId and create a new array with productId as key  and accumulated revenueAmount and premiumAmount
      const taskDataByProductId = taskData.reduce((acc, r) => {
        const productId = r.productID;
        if (!acc[productId]) {
          acc[productId] = { ...r, revenueAmount: 0, premiumAmount: 0 };
        }
        // Use mathjs to add revenueAmount
        acc[productId].revenueAmount = math
          .chain(acc[productId].revenueAmount)
          .add(r.revenueAmount)
          .done()
          .toString();
        return acc;
      }, {});
      const taskDataByProductIdArray = Object.values(taskDataByProductId);
      stats.grouped = taskDataByProductIdArray.length;
      const result = await this.benefitPointService.createStatement({
        billingCarrierID: +billingCarrierId,
        ams360GLDate: null,
        statementStatus: null,
        statementTotal: Number(totalAmount),
        paymentMethod: this.getPaymentMethod(document),
        entryDate: today.format(),
        statementDate: statementDate.format(),
        accountingMonth: depositDate || checkDate || statementDate.format(),
        officeID: this.BENEFITS_GROUP_OFFICE_ID,
        check: {
          // CheckNumber: 'Unknown',
          checkDate,
          depositDate,
          // PayableTo: '',
          issuedBy: carrierMap.get(+billingCarrierId),
          amount: Number(totalAmount),
        },
        notes: null,
        overrideOnly: false,
        overridePayeeID: null,
        rangeStartOn: undefined,
        rangeEndOn: undefined,
        useEstimatedPremium: false,
        lastPostedOn: today.format(),
        voidedOn: null,
        createdByUserID: undefined,
        // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        statementEntries: taskDataByProductIdArray as any,
      });
      this.logger.info(
        `Synced statement: ${result.id} for document: ${documentId}`,
        result
      );
      const statementId = result.id;

      stats.statementId = statementId;

      await prismaClient.documents.update({
        where: {
          str_id: documentId,
        },
        data: {
          sync_id: `${statementId}`,
        },
      });

      const record = await this.benefitPointService.getStatement(statementId);
      stats.actualCreated = record.statementEntries?.length;
      const productStatementMap = new Map(
        record.statementEntries.map((r) => [r.productID, r.statementEntryID])
      );

      // Update statementID in statement_data
      await runInBatch({
        items: validStatements.filter((r) => !r.sync_id),
        onBatch: async (
          statements: (statement_data & { report: report_data })[]
        ) => {
          return await Promise.all(
            statements.map(async (statement) => {
              const statementEntryId = productStatementMap.get(
                +statement.report?.internal_id || +statement.report?.sync_id
              );
              if (!statementEntryId) {
                return;
              }
              this.logger.debug(
                `Updating statement: ${statement.id} with statementEntryId: ${statementEntryId}`,
                {
                  id: statement.id,
                  statementEntryId,
                }
              );
              return await prismaClient.statement_data.update({
                where: { id: statement.id },
                data: {
                  sync_id: `${statementEntryId}`,
                },
              });
            })
          );
        },
      });

      await this.dataProcessService.updateTaskStatus({
        str_id: task.str_id,
        status: DataProcessingStatuses.COMPLETED,
        duration: Date.now() - startTime,
        stats: stats,
      });
      return stats;
    } catch (err) {
      this.logger.error('Error syncing statement', err);
      await this.dataProcessService.updateTaskStatus({
        str_id: task.str_id,
        status: DataProcessingStatuses.ERROR,
        duration: Date.now() - startTime,
        stats: { ...stats, error: err.message },
      });
      throw err;
    } finally {
      this.logger.debug('Statement syncing stats', stats);
    }
  }

  shouldUpdateMemberCount(
    member_count: MemberCountConfig,
    policy: Prisma.report_dataGetPayload<{
      select: { config: true; sync_id: true; id: true };
    }>
  ) {
    const config = policy.config as unknown as {
      member_count: MemberCountConfig;
    };
    if (member_count.count === null || member_count.count === undefined) {
      return false;
    }
    return (
      !config?.member_count ||
      member_count.period_date > config.member_count.period_date
    );
  }

  async syncMemberCount({
    id,
    member_count,
  }: {
    id: number;
    member_count: MemberCountConfig;
  }) {
    if (!member_count) {
      return;
    }

    const policy = await prismaClient.report_data.findUnique({
      where: { id },
      select: { config: true, sync_id: true, id: true },
    });

    if (!policy) {
      return;
    }

    const productDetail = await this.benefitPointService.getProductDetail(
      +policy.sync_id
    );

    if (!productDetail) {
      return;
    }

    if (this.shouldUpdateMemberCount(member_count, policy)) {
      const payload = {
        ...productDetail,
        numberOfEligibleEmployees: member_count.count,
      };
      this.logger.info(
        `Updating product: ${productDetail.productID} with member count: ${member_count.count}`,
        {
          origin: productDetail,
          new: payload,
        }
      );

      const ret = await this.benefitPointService.updateProductDetail(payload);

      if (ret.success) {
        await prismaClient.report_data.update({
          where: { id },
          data: {
            config: {
              ...((policy.config as unknown as {
                member_count: MemberCountConfig;
              }) ?? {}),
              member_count: member_count,
            },
          },
        });
      }
    }
  }

  async syncBenefitPointMemberCount(documentId: string) {
    const statements = await prismaClient.statement_data.findMany({
      where: {
        document_id: documentId,
        report_data_id: { not: null },
        state: { in: [DataStates.ACTIVE, DataStates.GROUPED] },
      },
    });
    const groupedStatements = groupBy(statements, 'report_data_id');

    const stats = {
      updated: 0,
      ignored: 0,
      failed: 0,
      failedTasks: [],
    };

    await limitConcurrency(
      async ([report_data_id, statements]) => {
        const finalActiveMemberCount = this.getFinalMemberCount(statements);

        const policy = await prismaClient.report_data.findUnique({
          where: { id: +report_data_id },
          select: { config: true, sync_id: true, id: true },
        });

        if (
          !policy ||
          !this.shouldUpdateMemberCount(finalActiveMemberCount, policy)
        ) {
          stats.ignored++;
          return;
        }

        await this.syncMemberCount({
          id: +report_data_id,
          member_count: {
            period_date: finalActiveMemberCount.period_date,
            count: finalActiveMemberCount.count,
          },
        });
        stats.updated++;
      },
      Object.entries(groupedStatements),
      10,
      {
        retries: 1,
        onFail: (context: { error; data: [number, statement_data[]] }) => {
          const { error, data } = context;
          stats.failed++;
          stats.failedTasks.push({
            report_data_id: data[0],
            policy_id: data[1][0].policy_id,
            error: Array.isArray(error?.response?.data)
              ? error.response.data.join(',')
              : error.message,
          });
          this.logger.error(
            `Failed to sync member count for report_data_id: ${data[0]}`,
            error
          );
        },
      }
    );

    this.logger.info('Member count sync completed', {
      documentId,
      stats,
    });

    return stats;
  }

  getFinalMemberCount(statements: statement_data[]): MemberCountConfig {
    if (statements.length === 0) {
      return null;
    }
    const sortedStatements = [...statements].sort(
      (a, b) =>
        new Date(b.period_date).getTime() - new Date(a.period_date).getTime()
    );
    return {
      period_date: dayjs(sortedStatements[0].period_date).format('YYYY-MM-DD'),
      count: sortedStatements[0].member_count,
    };
  }
}
