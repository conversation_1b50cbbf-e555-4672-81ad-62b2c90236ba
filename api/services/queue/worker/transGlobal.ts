import { inject, injectable } from 'inversify';
import type { SyncParamsDTO } from 'common/dto/data_processing/sync';
import { WorkerNames } from 'common/constants';
import dayjs from 'dayjs';

import Formatter from '@/lib/Formatter';
import { arrayToObj<PERSON>y<PERSON>ey, getValidDate } from '@/lib/helpers';
import { DataProcessService } from '@/services/data_processing';
import type {
  IAgentLevel,
  IAgentLevelDatum,
  IHierarchy,
  IHierarchyDatum,
} from '@/services/data_processing/interfaces';
import { BaseWorker, type DBData, type IDataSyncWorker } from './base';
import { DataProcessingStatuses, DataProcessingTypes } from '@/types';
import { AppLoggerService } from '@/services/logger/appLogger';
import { TransGlobalService } from '@/services/transglobal';

const agentStatusMap = {
  C: 'Current',
  D: 'Inactive',
  M: 'Manager',
};

interface ICarrierProduct {
  carrier_id: number;
  product_id: number;
  carrier_name: string;
  plan_name: string;
  product_name: string;
  product_type: string;
  product_channel: string;
  datalake_start_ts: string;
}

export interface IPolicy {
  id: number;
  agent_id: number;
  carrier_id: number;
  product_id: number;
  policy_number: string;
  policy_holder: string;
  policy_date: string;
  policy_state: string;
  signed_date: string;
  issue_age: number;
  status: string;
  mode: string;
  target_premium: number;
  custom_field_referring_agent: string;
  created_timestamp: string;
  commit_timestamp: string;
}

const agentTransformer = (data) => ({
  agent_code: data.agent_code,
  birthday: data.birthday ? new Date(data.birthday) : null,
  city: data.city,
  company_name: data.corpname,
  country: data.country,
  email: data.email,
  first_name: data.fstname,
  gender: data.gender,
  geo_state: data.state,
  sync_id: `${data.agent_id}`,
  last_name: data.lstname,
  middle_name: data.midinit,
  name: data.name,
  nickname: data.nickname,
  level: undefined,
  phone:
    `${data.voice_area ?? ''} ${data.voice_phone ?? ''} ${data.voice_phoneext ? `x${data.voice_phoneext}` : ''}`.trim(),
  phone_type: 'voice',
  phone2:
    `${data.cell_area ?? ''} ${data.cell_phone ?? ''} ${data.cell_phoneext ? `x${data.cell_phoneext}` : ''}`.trim(),
  phone2_type: 'cell',
  status: data.status in agentStatusMap ? agentStatusMap[data.status] : null,
  title: data.title,
  zip: data.zip,
  notes: data.uni ? `Uni: ${data.custom_field_agent_director}` : '',
});

const agentHierarchyTransformer = (
  data: IHierarchy,
  dbData: DBData
): IHierarchyDatum => {
  return {
    sync_id: `${data.id}`,
    contact_id: dbData.contacts?.get(`${data.agent_id}`)?.id,
    parent_id:
      dbData.contacts instanceof Map && data.parent_agent_id
        ? dbData.contacts.get(`${data.parent_agent_id}`)?.id
        : null,
    start_date: getValidDate(data.start_date),
    end_date: getValidDate(data.end_date),
    hierarchy_master_id: data.hierarchy_master_id,
  };
};

const agentLevelTransformer = (
  data: IAgentLevel,
  dbData: DBData
): IAgentLevelDatum => ({
  sync_id: `${data.agent_id}`,
  level: data.level !== null ? `${data.level}` : null,
  level_label: data.level !== null ? data.level_label : null,
  contact_id: dbData.contacts?.get(`${data.agent_id}`)?.id,
  loa: data.loa,
  start_date: getValidDate(data.effective_date),
  end_date: getValidDate(data.expiration_date),
});

const companyTransformer = (data, _dbData: DBData = {} as DBData) => ({
  sync_id: `${data.carrier_id}`,
  company_name: data.carrier_name,
  type: ['Carrier'],
});

const productTransformer = (
  data: ICarrierProduct,
  dbData: DBData = {} as DBData
) => ({
  company_id:
    dbData.companies instanceof Map
      ? dbData.companies.get(`${data.carrier_id}`)?.id
      : null,
  product_name: data.plan_name,
  product_type: data.product_channel,
  product_sub_type: data.product_name,
  sync_id: `${data.product_id}`,
});

const policyTransformer = (data: IPolicy, dbData: DBData = {} as DBData) => {
  const policyContacts = dbData?.report_data?.get(`${data.id}`)?.contacts || [];
  const currentAgent =
    dbData?.contacts instanceof Map && dbData.contacts.get(`${data.agent_id}`);
  const contacts = [
    ...new Set(
      currentAgent ? [...policyContacts, currentAgent?.str_id] : policyContacts
    ),
  ];

  return {
    agent_name: currentAgent ? Formatter.contact(currentAgent) : null,
    writing_carrier_name:
      dbData.companies instanceof Map &&
      dbData.companies.get(`${data.carrier_id}`)
        ? dbData.companies.get(`${data.carrier_id}`)?.company_name
        : null,
    company_id:
      dbData.companies instanceof Map &&
      dbData.companies.get(`${data.carrier_id}`)
        ? dbData.companies.get(`${data.carrier_id}`)?.id
        : null,
    contacts: contacts,
    // Contacts_split:
    //   dbData.contacts instanceof Map && dbData.contacts.get(`${data.agent_id}`)
    //     ? { [dbData.contacts.get(`${data.agent_id}`)?.str_id]: '100' }
    //     : {},
    customer_name: data.policy_holder,
    sync_id: `${data.id}`,
    issue_age: data.issue_age,
    payment_mode: data.mode,
    signed_date: getValidDate(data.signed_date),
    // If db data's effective_date is not null, then don't update effective_date
    effective_date: dbData?.report_data?.get(`${data.id}`)?.effective_date
      ? undefined
      : getValidDate(data.policy_date),
    geo_state: data?.policy_state,
    policy_date: getValidDate(data.policy_date),
    policy_id: data.policy_number,
    product_name:
      dbData.company_products instanceof Map &&
      dbData.company_products.get(`${data.product_id}`)
        ? dbData.company_products.get(`${data.product_id}`)?.product_name
        : null,
    product_type:
      dbData.company_products instanceof Map &&
      dbData.company_products.get(`${data.product_id}`)
        ? dbData.company_products.get(`${data.product_id}`)?.product_type
        : null,
    product_sub_type:
      dbData.company_products instanceof Map &&
      dbData.company_products.get(`${data.product_id}`)
        ? dbData.company_products.get(`${data.product_id}`)?.product_sub_type
        : null,
    company_product_id:
      dbData.company_products instanceof Map &&
      dbData.company_products.get(`${data.product_id}`)
        ? dbData.company_products.get(`${data.product_id}`)?.id
        : null,
    policy_status: data.status,
    commissionable_premium_amount: data.target_premium,
  };
};

const policySplitTransformer = (data, dbData: DBData = {} as DBData) => {
  const policyContacts =
    dbData?.report_data?.get(`${data.policy_id}`)?.contacts || [];
  const currentAgents = Object.keys(data.splits ?? {})
    .map((agentId) =>
      dbData.contacts instanceof Map && dbData.contacts.get(agentId)
        ? dbData.contacts.get(agentId)?.str_id
        : null
    )
    .filter((id) => id);
  const contacts = [...new Set([...policyContacts, ...currentAgents])];

  return {
    sync_id:
      dbData.report_data instanceof Map &&
      dbData.report_data.get(`${data.policy_id}`)
        ? dbData.report_data.get(`${data.policy_id}`)?.sync_id
        : null,
    contacts: contacts?.length ? contacts : null,
    geo_state: data?.policy_state,
    contacts_split: Object.fromEntries(
      Object.entries(data.splits ?? {}).map(([k, v]) => [
        dbData.contacts instanceof Map && dbData.contacts.get(k)
          ? dbData.contacts.get(k)?.str_id
          : null,
        v,
      ])
    ),
  };
};

@injectable()
export class TransGlobalWorker
  extends BaseWorker
  implements IDataSyncWorker<SyncParamsDTO>
{
  name = WorkerNames.TransGlobalWorker;
  @inject(DataProcessService) private dataProcessingService: DataProcessService;
  @inject(TransGlobalService) private transGlobalService: TransGlobalService;
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private duration: number;
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private disableFilter: boolean = false;
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  private resetMode: boolean = false;
  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: { service: WorkerNames.TransGlobalWorker },
  });

  policySplitTransform = policySplitTransformer;
  agentTransform = agentTransformer;
  agentHierarchyTransform = agentHierarchyTransformer;
  agentLevelTransform = agentLevelTransformer;
  companyTransform = companyTransformer;
  productTransform = productTransformer;
  policyTransform = policyTransformer;

  constructor() {
    super();
    this.autoLoadTransformers();
    this.registerDataSource({
      policies: this.fetchPolicies.bind(this),
      carriersAndProducts: this.fetchCarriersAndProducts.bind(this),
      agents: this.fetchContacts.bind(this),
      agentHierarchy: this.fetchAgentHierarchy.bind(this),
      policySplits: this.fetchPolicySplits.bind(this),
      agentLevel: this.fetchAgentLevel.bind(this),
    });
    this.registerTransformer({
      agentLevelTransform: {
        getData: this.getAgentLevelData,
      },
      companyTransform: {
        getData: this.getCompanyData,
      },
      policySplitTransform: {
        getData: this.getPolicySplitsData,
      },
    });
  }

  async fetchPolicies() {
    const lastSync = await this.dataProcessingService.getLastTask({
      account_id: this.task.account.account_id,
      type: DataProcessingTypes.data_sync,
      status: DataProcessingStatuses.COMPLETED,
      worker: this.name,
      entity: 'policies',
    });
    // This endpoint will throw error if we try to fetch policies that commit_timestamp is before 2025-01-29
    return this.transGlobalService.fetchPolicies({
      after_commit_timestamp:
        lastSync && !this.task.payload.isFullSync
          ? dayjs(lastSync?.created_at)
              .subtract(1, 'month')
              .startOf('day')
              .format('YYYY-MM-DDTHH:mm:ss')
          : undefined,
    });
  }

  async fetchCarriersAndProducts() {
    return this.transGlobalService.fetchCarriersAndProducts();
  }

  getCompanyData = (data) => {
    const dataUnique = new Map();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data
      .filter((d) => d.carrier_id)
      .forEach((d) => {
        dataUnique.set(d.carrier_id, d);
      });
    return Array.from(dataUnique.values());
  };

  async fetchContacts() {
    return this.transGlobalService.fetchContacts();
  }

  async fetchAgentHierarchy() {
    return this.transGlobalService.fetchAgentHierarchy();
  }

  async fetchPolicySplits() {
    const lastSync = await this.dataProcessingService.getLastTask({
      account_id: this.task.account.account_id,
      type: DataProcessingTypes.data_sync,
      status: DataProcessingStatuses.COMPLETED,
      worker: this.name,
      entity: 'policySplits',
    });
    return this.transGlobalService.fetchPolicySplits({
      after_policy_date:
        lastSync && !this.task.payload.isFullSync
          ? dayjs(lastSync?.created_at)
              .subtract(3, 'month')
              .format('YYYY-MM-DD')
          : undefined,
    });
  }

  getPolicySplitsData = (data) => {
    const keyByPolicyId = arrayToObjByKey(data, 'policy_id');
    const policySplits = Object.entries(keyByPolicyId).map(
      ([policyId, splits]) => ({
        policy_id: policyId,
        splits: (Array.isArray(splits) ? splits : []).reduce(
          (acc, cur) => ({
            // biome-ignore lint/performance/noAccumulatingSpread: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
            ...acc,
            [cur.agent_id]: `${cur.split_percentage * 100}`,
          }),
          {}
        ),
      })
    );
    return policySplits;
  };

  async fetchAgentLevel() {
    return this.transGlobalService.fetchAgentLevel();
  }

  getAgentLevelData = (data, dbData: DBData) => {
    const dataUnique = new Map();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((d) => {
      if (!dbData.contact_levels?.get(`${d.agent_id}`)) {
        return;
      }
      if (
        !dataUnique.has(d.agent_id) ||
        new Date(dataUnique.get(d.agent_id).effective_date) <
          new Date(d.effective_date)
      ) {
        dataUnique.set(d.agent_id, d);
      }
    });
    return Array.from(dataUnique.values());
  };

  // Async process(
  //   task: QueueTask<SyncParamsDTO>
  // ): Promise<{ stats: DataSyncStats; duration: number }> {
  //   this.logger.info('syncData() started');
  //   console.time('syncData()');
  //   const body = task.payload;
  //   const account = task.account;
  //   const results = [];
  //   const startTime = Date.now();
  //   this.disableFilter = body.disableFilter;
  //   this.resetMode = body.resetMode;
  //   this.logger.info(
  //     `Sync Filter Feature: ${this.disableFilter ? 'Disabled' : 'Enabled'}`
  //   );
  //   this.logger.info(
  //     `Sync Reset Mode: ${this.resetMode ? 'Enabled' : 'Disabled'}`
  //   );
  //   try {
  //     const entitiesToSync = Object.fromEntries(
  //       Object.entries(entities).filter(
  //         ([k, v]) => body.entities?.includes(k as Entity) ?? true
  //       )
  //     );

  //     const lastSync = this.disableFilter
  //       ? null
  //       : await this.dataProcessingService.getLastTask({
  //           account_id: account.account_id,
  //           type: DataProcessingTypes.data_sync,
  //           status: DataProcessingStatuses.COMPLETED,
  //           worker: this.name,
  //         });
  //     const filterState = this.resetMode
  //       ? [DataStates.ACTIVE, DataStates.DELETED]
  //       : [DataStates.ACTIVE];
  //     for await (const [k, v] of Object.entries(entitiesToSync)) {
  //       this.logger.info(`Syncing ${k} started`);
  //       console.group();
  //       console.time(`Syncing ${k}`);
  //       const dbData = await this.getDBData(account.account_id, filterState);
  //       const curStartTime = Date.now();
  //       const urlpath =
  //         typeof v.path === 'function'
  //           ? v.path({
  //               after_commit_timestamp: lastSync
  //                 ? dayjs(lastSync?.created_at).format('YYYY-MM-DDTHH:mm:ss')
  //                 : undefined,
  //             })
  //           : v.path;
  //       const endpoint = new URL(urlpath, config.api);

  //       this.logger.info(`Fetching data from: ${endpoint.toString()}`);
  //       console.time(`Fetch ${k}`);
  //       const res = await fetch(endpoint.toString(), {
  //         method: 'GET',
  //         headers: {
  //           authorization: `Bearer ${process.env.TRANSGLOBAL_KEY}`,
  //         },
  //       });
  //       console.timeEnd(`Fetch ${k}`);
  //       if (res.status !== 200) {
  //         results.push({
  //           status: res.status,
  //           statusText: res.statusText,
  //           entity: k,
  //         });
  //         this.logger.error(`Failed to sync ${k}`);
  //         throw new Error(`Failed to sync ${k}`);
  //       }
  //       const data = await res.json();
  //       this.logger.info(`Fetched ${data.length} records`);
  //       const updatedCounts = {};

  //       for await (const mapper of v.mappers) {
  //         this.logger.info(`Applying mapper for table: ${mapper.table}`);
  //         console.time(`Mapper for table: ${mapper.table}`);
  //         const _data =
  //           typeof mapper.getData === 'function' ? mapper.getData(data) : data;

  //         const pendingTasks = _data
  //           .map((d) => {
  //             const datum = mapper.transformer(d, dbData);
  //             if (!datum || !datum.sync_id || datum.sync_id === 'null') {
  //               this.logger.warn(
  //                 `Received update for "${JSON.stringify(d)}", but could not be found.`
  //               );
  //               return;
  //             }
  //             if (
  //               mapper.fields &&
  //               !this.isDiff(
  //                 mapper.fields,
  //                 datum,
  //                 dbData[mapper.table].get(datum.sync_id)
  //               ) &&
  //               !this.resetMode
  //             ) {
  //               return;
  //             }
  //             return datum;
  //           })
  //           .filter((d) => d)
  //           .filter((d) => {
  //             return mapper.table === 'company_products' ? d?.company_id : d;
  //           })
  //           .map((d) =>
  //             this.overrideValues({ datum: d, dbData, table: mapper.table })
  //           )
  //           .map((d) => {
  //             return {
  //               ...d,
  //               sync_worker: this.name,
  //             };
  //           });
  //         const handler = async (datum) => {
  //           if (
  //             ['contact_levels', 'contact_hierarchy'].includes(mapper.table)
  //           ) {
  //             const contact =
  //               mapper.table === 'contact_hierarchy'
  //                 ? await prismaClient.contacts.findFirst({
  //                     where: {
  //                       id: (datum as IHierarchyDatum).contact_id,
  //                     },
  //                   })
  //                 : await this.contactsService.findContactBySyncId(
  //                     `${datum.contact_id}`
  //                   );
  //             if (!contact) {
  //               return;
  //             }
  //             await this.ensureUniqueSyncId(
  //               mapper.table,
  //               datum.sync_id,
  //               account.account_id
  //             );
  //             // Maxium two records, one active, on deleted
  //             const records = await prismaClient[mapper.table].findMany({
  //               where: {
  //                 sync_id: datum.sync_id,
  //                 account_id: account.account_id,
  //                 state: {
  //                   in: [
  //                     DataStates.ACTIVE,
  //                     DataStates.DELETED,
  //                     DataStates.GROUPED,
  //                     DataStates.DUPLICATE,
  //                   ],
  //                 },
  //               },
  //             });
  //             const states = records?.map((r) => r.state);
  //             const ret = await prismaClient[mapper.table].upsert({
  //               where: {
  //                 account_id_sync_id_state: {
  //                   sync_id: datum.sync_id,
  //                   account_id: account.account_id,
  //                   state:
  //                     states.length > 1
  //                       ? DataStates.ACTIVE
  //                       : states[0] || DataStates.ACTIVE,
  //                 },
  //               },
  //               update: {
  //                 ...datum,
  //                 updated_at: new Date(),
  //                 updated_by: account.uid,
  //                 contact_id: contact.id,
  //                 state: undefined,
  //                 updated_proxied_by: account.ouid,
  //               },
  //               create: {
  //                 ...datum,
  //                 account_id: account.account_id,
  //                 contact_id: contact?.id,
  //                 created_by: account.uid,
  //                 created_proxied_by: account.ouid,
  //               },
  //             });
  //             if (mapper.table === 'contact_hierarchy') {
  //               return await prismaClient.contacts.update({
  //                 where: { id: contact.id },
  //                 data: {
  //                   parent_relationships: {
  //                     connect: {
  //                       id: ret.id,
  //                     },
  //                   },
  //                 },
  //               });
  //             }
  //           } else {
  //             await this.ensureUniqueSyncId(
  //               mapper.table,
  //               datum.sync_id,
  //               account.account_id
  //             );
  //             // Maxium two records, one active, on deleted
  //             const records = await prismaClient[mapper.table].findMany({
  //               where: {
  //                 sync_id: datum.sync_id,
  //                 account_id: account.account_id,
  //                 state: {
  //                   in: [
  //                     DataStates.ACTIVE,
  //                     DataStates.DELETED,
  //                     DataStates.GROUPED,
  //                     DataStates.DUPLICATE,
  //                   ],
  //                 },
  //               },
  //             });
  //             const states = records?.map((r) => r.state);
  //             return await prismaClient[mapper.table].upsert({
  //               where: {
  //                 account_id_sync_id_state: {
  //                   sync_id: datum.sync_id,
  //                   account_id: account.account_id,
  //                   state:
  //                     states.length > 1
  //                       ? DataStates.ACTIVE
  //                       : states[0] || DataStates.ACTIVE,
  //                 },
  //               },
  //               update: {
  //                 ...datum,
  //                 updated_at: new Date(),
  //                 updated_by: account.uid,
  //                 // Do not re-activiate deleted item
  //                 state: undefined,
  //                 updated_proxied_by: account.ouid,
  //               },
  //               create: {
  //                 ...datum,
  //                 account_id: account.account_id,
  //                 uid: account.uid,
  //                 str_id: nanoid(),
  //                 created_by: account.uid,
  //                 created_proxied_by: account.ouid,
  //               },
  //             });
  //           }
  //         };

  //         updatedCounts[mapper.table] = pendingTasks.length;
  //         this.logger.info(`Updating ${pendingTasks.length} records`);
  //         await limitConcurrency(handler, pendingTasks, 10);
  //         console.timeEnd(`Mapper for table: ${mapper.table}`);
  //       }
  //       console.timeEnd(`Syncing ${k}`);
  //       const curEndTime = Date.now();
  //       this.stats[k] = {
  //         totalCount: data.length,
  //         updatedCounts: updatedCounts,
  //         duration: curEndTime - curStartTime,
  //       };
  //       console.groupEnd();
  //     }
  //     const endTime = Date.now();
  //     this.duration = endTime - startTime;
  //   } finally {
  //     console.timeEnd('syncData()');
  //   }
  //   return { stats: this.stats, duration: this.duration };
  // }
}
