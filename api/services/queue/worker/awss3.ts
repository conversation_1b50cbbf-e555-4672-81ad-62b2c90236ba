import type { SyncParamsDTO } from 'common/dto/data_processing/sync';
import { inject, injectable } from 'inversify';
import { AccountIds, WorkerNames } from 'common/constants';

import {
  BaseWorker,
  type DocumentDatum,
  type IDataSyncWorker,
} from '@/services/queue/worker/base';
import { AwsStorageService } from '@/services/aws-storage';
import { storage } from '@/lib/firebase-admin';
import type { ExtAccountInfo } from '@/types';
import { AccountProcessorConfigService } from '@/services/account-processor-config';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';

@injectable()
export class AwsS3Worker
  extends BaseWorker
  implements IDataSyncWorker<SyncParamsDTO>
{
  name = WorkerNames.AwsS3Worker;

  @inject(AwsStorageService)
  readonly awsStorageService: AwsStorageService;
  @inject(AccountProcessorConfigService)
  readonly configItemService: AccountProcessorConfigService;

  private serviceConfig: ConfigItemValueForDataSync<{
    accessKeyId: string;
    secretAccessKey: string;
    region: string;
    bucket: string;
  }>;

  constructor() {
    super();
    this.registerDataSource({
      documents: this.fetchDocuments.bind(this),
    });
  }
  async setup(account: ExtAccountInfo) {
    const config = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        accessKeyId: string;
        secretAccessKey: string;
        region: string;
        bucket: string;
      }>
    >({ account_id: account.account_id, worker: this.name });

    this.serviceConfig = config.value;
    this.awsStorageService.loadConfig(config.value.credentials);
  }

  async fetchDocuments() {
    const objects = await this.awsStorageService.listObjects({
      Bucket: this.serviceConfig.credentials.bucket,
    });
    // Save to our firebase storage
    const results = (
      await Promise.all(
        objects
          .filter((r) => r.Size)
          .map(async (object) => {
            if (object.Key.includes('_7DaysData')) {
              const data = await this.awsStorageService.getObject({
                Bucket: this.serviceConfig.credentials.bucket,
                Key: object.Key,
              });
              const filePath = await this.saveToFirebaseStorage(
                data,
                object.Key
              );
              return {
                key: object.Key,
                filePath,
              };
            }
          })
      )
    ).filter(Boolean);
    return results;
  }

  async saveToFirebaseStorage(data: string, key: string) {
    if (!this.task?.account?.account_id) {
      throw new Error('account_id is required');
    }
    const filePath = `uploads/${this.task?.account?.account_id}/${key}`;
    await storage.file(filePath).save(Buffer.from(data));
    return filePath;
  }

  documentTransform(data: { key: string; filePath: string }): DocumentDatum {
    // Hard code company_str_id for Transglobal
    const company_str_id =
      this.task?.account?.account_id === AccountIds.TRANSGLOBAL
        ? 'XxLGDVQ3UdGkw6l-W2vEv'
        : null;
    return {
      sync_id: data.key,
      filename: data.key?.split('/').pop(),
      file_path: data.filePath,
      company_str_id,
    };
  }
}
