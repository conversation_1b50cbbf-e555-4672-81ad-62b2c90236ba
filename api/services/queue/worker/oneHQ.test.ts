import { describe, expect, it } from 'vitest';

import { container } from '@/ioc';
import { OneHQWorker } from '@/services/queue/worker/oneHQ';

describe('OneHQ Worker', () => {
  describe('getProductSubType', () => {
    it('should return the product sub type', () => {
      const cases = [
        { name: 'MYGA', output: 'MYGA' },
        { name: 'MYG', output: 'MYGA' },
        { name: 'MYG s', output: 'MYGA' },
        { name: 'MYGga', output: '' },
        { name: 'some MYG', output: 'MYGA' },
        { name: 'some SPIA', output: 'SPIA' },
        { name: 'some sPIA', output: 'SPIA' },
        { name: 'FIA', output: 'FIA' },
        { name: 'FiA', output: 'FIA' },
        { name: '', output: '' },
      ];
      const service = container.get<OneHQWorker>(OneHQWorker);
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      cases.forEach((c) => {
        expect(service.getProductSubType(c.name)).toEqual(c.output);
      });
    });
  });

  describe('getAgeOfBusiness', () => {
    it('should return the number of the product name', () => {
      const cases = [
        { product: 'Summit Bonus Index', year: undefined },
        { product: 'Teton 7 Year', year: '7' },
        { product: 'Impact 10', year: '10' },
        { product: 'Retirement Plus Multiplier 5 - Year', year: '5' },
        { product: 'Personal Choice  - 5 Year', year: '5' },
        { product: 'Safe Haven Bonus Guarantee 3 Year', year: '3' },
        { product: '10 Year Indexed Annuity', year: '10' },
        { product: 'Personal Choice  - 5 Year', year: '5' },
        { product: '10 Year Indexed Annuity', year: '10' },
        { product: '3 Year MYGA', year: '3' },
        // Abnormal years
        { product: '360', year: undefined },
        { product: 'Income 150+ Se', year: undefined },
      ];
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      cases.forEach((c) => {
        const service = container.get<OneHQWorker>(OneHQWorker);
        expect(service.getAgeOfBusiness(c.product)).toEqual(c.year);
      });
    });
  });
});
