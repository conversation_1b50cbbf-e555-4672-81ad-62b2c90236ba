import type {
  agent_commission_schedule_profiles,
  agent_commission_schedule_profiles_sets,
  comp_grid_criteria,
  comp_grid_levels,
  comp_grid_products,
  comp_grid_rates,
  comp_grids,
  companies,
  company_products,
  contact_hierarchy,
  contact_levels,
  contacts,
  contacts_agent_commission_schedule_profiles_sets,
  customers,
  date_ranges,
  DateRangesTypes,
  documents,
  report_data,
} from '@prisma/client';
import { inject, injectable } from 'inversify';
import { groupBy, isEqual } from 'lodash-es';
import type { Entity, SyncParamsDTO } from 'common/dto/data_processing/sync';
import pRetry from 'p-retry';

import { prismaClient } from '@/lib/prisma';
import type { QueueTask } from '../types';
import { DataStates, type ExtAccountInfo } from '@/types';
import { limitConcurrency } from '@/lib/helpers';
import type { IHierarchyDatum } from '@/services/data_processing/interfaces';
import { ContactsService } from '@/services/contacts';
import { AccountProcessorConfigService } from '@/services/account-processor-config';
import { AppLoggerService } from '@/services/logger/appLogger';
import { ReportService } from '@/services/report';
import { ContactService } from '@/services/contact';

export interface CompanyDatum {
  company_name: string;
  type: string[];
  sync_id: string;
  company_id?: string;
  sync_worker?: string;
}

export interface CompGridDatum {
  sync_id: string;
  company_id?: number;
  name?: string;
  notes?: string;
}

export interface PolicyDatum {
  sync_id: string;
  agent_name: string;
  writing_carrier_name: string;
  customer_name: string;
  effective_date: Date;
  policy_id: string;
  policy_status: string;

  contacts?: string[];
  contacts_split?: Record<string, number>;
  company_id?: number;
  product_type?: string;
  product_sub_type?: string;
  company_product_id?: number;
  issue_age?: number;
  payment_mode?: string;
  premium_amount?: number;
  commissionable_premium_amount?: number;
  product_name?: string;
  notes?: string;
  geo_state?: string;
  group_id?: string;
  group_name?: string;
  policy_term_months?: number;
  internal_id?: string;
  transaction_type?: string;
  signed_date?: Date;
  policy_date?: Date;
  customer_id?: number;
}

export interface ProductDatum {
  sync_id: string;
  product_type?: string;
  product_name: string;
  company_id: number;
}

export interface AgentHierarchyDatum {
  sync_id: string;
  contact_id: number;
  parent_id?: number;
  start_date?: Date;
  end_date?: Date;
  hierarchy_master_id?: number;
}

export interface AgentLevelDatum {
  sync_id: string;
  level?: string;
  level_label: string;
  // This is the sync_id of the contact in case of transglobal
  contact_id: string | number;
  loa?: boolean;
  start_date?: Date;
  end_date?: Date;
  company_id?: number;
}

export interface AgentDatum {
  agent_code?: string;
  birthday?: Date;
  city?: string;
  company_name?: string;
  country?: string;
  email?: string;
  first_name?: string;
  gender?: string;
  geo_state?: string;
  sync_id: string;
  last_name?: string;
  level?: string;
  middle_name?: string;
  name: string;
  nickname?: string;
  phone?: string;
  phone_type?: string;
  phone2?: string;
  phone2_type?: string;
  status?: string;
  title?: string;
  zip?: string;
  type?: string[] | string;
  notes?: string;
}

export interface CompGridLevelDatum {
  sync_id: string;
  name?: string;
  comp_grid_id?: number;
  type?: 'Agency level';
}
export interface CompGridProductDatum {
  sync_id: string;
  name?: string;
  type?: string;
  notes?: string;
  company_products?: { connect: { id: number } | { id: number }[] };
  comp_grid_id?: number;
}
export interface CompGridRateDatum {
  sync_id: string;
  rate?: number;
  carrier_rate?: number;
  comp_grid_level_id?: number;
  comp_grid_level?: { connect: { id: number } };
  comp_grid_criterion_id?: number;
  comp_grid_criterion?: { connect: { id: number } };
  date_ranges?: { connect: { id: number } | { id: number }[] };
}
export interface CompGridCriteriaDatum {
  sync_id: string;
  company_id?: number;
  company?: {
    connect: {
      id: number;
    };
  };
  name?: string;
  comp_grid_id?: number;
  grid_product_id?: number;
  comp_grid_product?: {
    connect: {
      id: number;
    };
  };
  comp_grid?: {
    connect: {
      id: number;
    };
  };
  issue_age_start?: number;
  issue_age_end?: number;
  policy_year_start?: number;
  policy_year_end?: number;
  premium_min?: number;
  premium_max?: number;
  compensation_type?: string;
  notes?: string;
  geo_states?: string[];
}
export interface CompProfilesDatum {
  sync_id: string;
  name?: string;
  company_id?: number;
  comp_grid_id?: number;
}
export interface CompProfileSetsDatum {
  sync_id: string;
  name?: string;
  notes?: string;
  // Contacts_agent_commission_schedule_profiles: number[];
  commission_profiles?: {
    connect: {
      id: number;
    }[];
  };
}
export interface CompAgentProfileDatum {
  sync_id: string;
  name?: string;
  notes?: string;
  comp_grid_id?: number;
  company_id?: number;
  contact_id?: number;
  agent_commission_schedule_profiles_sets?: {
    connect: {
      id: number;
    };
  };
  contact?: {
    connect: {
      id: number;
    };
  };
  agent_commission_schedule_profile_set_id?: number;
}

export interface CustomerDatum {
  sync_id: string;
  address?: {
    street?: string;
    street2?: string;
    city?: string;
    geo_state?: string;
    zipcode?: string;
    country?: string;
  };
  company_name?: string;
  dob?: Date;
  email?: string;
  end_date?: Date;
  first_name?: string;
  gender?: string;
  group_id?: string;
  last_name?: string;
  middle_name?: string;
  nickname?: string;
  phone?: string;
  start_date?: Date;
  status?: string;
  type?: string;
  website?: string;
}
export type TransformerFunc = (data: unknown, dbData?: DBData) => unknown;
export interface IDataSyncWorker<T> {
  name: string;

  task: QueueTask<T>;

  setup?(account: ExtAccountInfo): Promise<void>;
  process(
    task: QueueTask<T>
  ): Promise<{ stats: DataSyncStats; duration: number }>;
  policyTransform?(
    data: unknown,
    dbData?: DBData
  ): PolicyDatum | Promise<PolicyDatum>;
  companyTransform?(data: unknown, dbData?: DBData): CompanyDatum;
  productTransform?(data: unknown, dbData?: DBData): ProductDatum;
  compGridTransform?(data: unknown, dbData?: DBData): CompGridDatum;
  compGridLevelTransform?(data: unknown, dbData?: DBData): CompGridLevelDatum;
  compGridProductTransform?(
    data: unknown,
    dbData?: DBData
  ): CompGridProductDatum;
  compGridRateTransform?(data: unknown, dbData?: DBData): CompGridRateDatum;
  compCriteriaTransform?(data: unknown, dbData?: DBData): CompGridCriteriaDatum;
  compProfilesTransform?(data: unknown, dbData?: DBData): CompProfilesDatum;
  dateRangeTransform?(data: unknown, dbData?: DBData): DateRangeDatum;
  compProfileSetsTransform?(
    data: unknown,
    dbData?: DBData
  ): CompProfileSetsDatum;
  compAgentProfileTransform?(
    data: unknown,
    dbData?: DBData
  ): CompAgentProfileDatum;
  // This will be called after each time a entity is synced
  afterSyncHook?(context: {
    entity: Entity;
    data: unknown;
    taskData?: unknown[];
    dbData?: DBData;
  }): Promise<void>;
  linkingPolicyAgents(data: {
    syncId: string;
    agents: { firstName: string; lastName: string }[];
    account_id: string;
  }): Promise<void>;
  policySplitTransform?(data: unknown, dbData?: DBData): unknown;
  agentTransform?(data: unknown, dbData?: DBData): AgentDatum;
  agentLevelTransform?(data: unknown, dbData?: DBData): AgentLevelDatum;
  agentHierarchyTransform?(data: unknown, dbData?: DBData): AgentHierarchyDatum;
  documentTransform?(data: unknown, dbData?: DBData): DocumentDatum;
  customerTransform?(data: unknown, dbData?: DBData): CustomerDatum;
  getAvailableTransformers?(): {
    table: string;
    transformer: TransformerFunc;
  }[];
  getAvailableEntities(): string[];
}

export interface IDocumentProcessingWorker<T> {
  name: string;
  setup(account: ExtAccountInfo): Promise<void>;
  process(task: QueueTask<T>): Promise<{
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    stats: any;
    duration: number;
  }>;
}

export interface DocumentDatum {
  sync_id: string;
  filename?: string;
  file_path?: string;
  company_str_id?: string;
}
type TransfomerKey = Exclude<keyof IDataSyncWorker<unknown>, 'process'>;
export interface DataSyncEntityStats {
  totalCount: number;
  updatedCounts: updateCount;
  duration: number;
}

export interface DateRangeDatum {
  sync_id: string;
  start_date?: Date;
  end_date?: Date;
  type?: DateRangesTypes;
  name?: string;
  notes?: string;
}

export interface updateCount {
  [table: string]: number;
}
export type DataSyncStats = {
  [key in Entity]?: DataSyncEntityStats;
};

export type DBData = Awaited<ReturnType<typeof BaseWorker.prototype.getDBData>>;
@injectable()
export abstract class BaseWorker implements IDataSyncWorker<unknown> {
  name = 'BaseWorker';

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  task: QueueTask<any>;

  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: {
      service: 'DataSyncWorker',
    },
  });

  entityConfig: {
    [key in Entity]: {
      mappers: {
        table: string;
        fields: {
          key: string;
          type?: string;
        }[];
        transformerKey?: TransfomerKey;
        transformer?: (data: unknown, dbData: DBData) => unknown;
        getData?: (
          data: unknown,
          dbData: DBData
        ) => Promise<unknown[]> | unknown[];
      }[];
    };
  } = {
    documents: {
      mappers: [
        {
          table: 'documents',
          fields: [{ key: 'sync_id' }, { key: 'name' }],
          transformerKey: 'documentTransform',
        },
      ],
    },
    agents: {
      mappers: [
        {
          table: 'contacts',
          fields: [
            { key: 'agent_code' },
            { key: 'birthday', type: 'date' },
            { key: 'city' },
            { key: 'company_name' },
            { key: 'country' },
            { key: 'email' },
            { key: 'first_name' },
            { key: 'gender' },
            { key: 'geo_state' },
            { key: 'sync_id' },
            { key: 'last_name' },
            { key: 'log', type: 'json' },
            { key: 'middle_name' },
            { key: 'name' },
            { key: 'nickname' },
            { key: 'type' },
            { key: 'phone' },
            { key: 'phone_type' },
            { key: 'phone2' },
            { key: 'phone2_type' },
            { key: 'status' },
            { key: 'title' },
            { key: 'zip' },
            { key: 'notes' },
          ],
          transformerKey: 'agentTransform',
        },
      ],
    },
    compGrids: {
      // Order matters, the latter task depends on the former one
      mappers: [
        {
          table: 'comp_grids',
          fields: [
            { key: 'name' },
            { key: 'notes' },
            { key: 'company_id', type: 'number' },
          ],
          transformerKey: 'compGridTransform',
        },
        {
          table: 'comp_grid_levels',
          fields: [{ key: 'name' }, { key: 'comp_grid_id', type: 'number' }],
          transformerKey: 'compGridLevelTransform',
        },
        {
          table: 'comp_grid_products',
          fields: [
            { key: 'name' },
            { key: 'type' },
            { key: 'notes' },
            { key: 'comp_grid_id', type: 'number' },
          ],
          transformerKey: 'compGridProductTransform',
        },
        {
          table: 'comp_grid_criteria',
          fields: [
            { key: 'issue_age_start', type: 'date' },
            { key: 'issue_age_end', type: 'date' },
            { key: 'comp_grid_id', type: 'number' },
            { key: 'geo_states', type: 'json' },
            { key: 'notes', type: 'string' },
            { key: 'policy_year_start', type: 'date' },
            { key: 'policy_year_end', type: 'date' },
            { key: 'premium_min', type: 'number' },
            { key: 'premium_max', type: 'number' },
            { key: 'grid_product_id', type: 'number' },
            { key: 'comp_grid_product', type: 'json' },
            { key: 'compensation_type', type: 'string' },
            { key: 'comp_grid', type: 'json' },
            { key: 'company', type: 'json' },
          ],
          transformerKey: 'compCriteriaTransform',
        },
        {
          table: 'date_ranges',
          fields: [
            { key: 'start_date', type: 'date' },
            { key: 'end_date', type: 'date' },
            { key: 'type', type: 'json' },
            { key: 'name', type: 'string' },
            { key: 'notes', type: 'string' },
          ],
          transformerKey: 'dateRangeTransform',
        },
        {
          table: 'comp_grid_rates',
          fields: [
            { key: 'rate', type: 'number' },
            { key: 'carrier_rate', type: 'number' },
            { key: 'comp_grid_level_id', type: 'number' },
            { key: 'comp_grid_criterion_id', type: 'number' },
            { key: 'comp_grid_level', type: 'json' },
            { key: 'comp_grid_criterion', type: 'json' },
            { key: 'date_ranges', type: 'json' },
          ],
          transformerKey: 'compGridRateTransform',
        },
      ],
    },
    compProfiles: {
      mappers: [
        {
          table: 'agent_commission_schedule_profiles',
          fields: [
            { key: 'sync_id' },
            { key: 'name' },
            { key: 'comp_grid_id', type: 'number' },
            { key: 'company_id', type: 'number' },
          ],
          transformerKey: 'compProfilesTransform',
        },
        {
          table: 'agent_commission_schedule_profiles_sets',
          fields: [{ key: 'name' }, { key: 'notes', type: 'string' }],
          transformerKey: 'compProfileSetsTransform',
        },
        {
          table: 'contacts_agent_commission_schedule_profiles_sets',
          fields: [
            { key: 'sync_id' },
            {
              key: 'agent_commission_schedule_profile_set_id',
              type: 'number',
            },
            { key: 'contact_id', type: 'number' },
          ],
          transformerKey: 'compAgentProfileTransform',
        },
      ],
    },
    carriersAndProducts: {
      mappers: [
        {
          table: 'companies',
          fields: [{ key: 'company_name' }, { key: 'notes' }],
          transformerKey: 'companyTransform',
        },
        {
          table: 'company_products',
          fields: [
            { key: 'product_name' },
            { key: 'product_type' },
            { key: 'company_id', type: 'number' },
            { key: 'notes' },
          ],
          transformerKey: 'productTransform',
        },
      ],
    },

    policies: {
      mappers: [
        {
          table: 'report_data',
          fields: [
            { key: 'commissionable_premium_amount', type: 'number' },
            { key: 'company_id', type: 'number' },
            { key: 'company_product_id', type: 'number' },
            { key: 'agent_name' },
            { key: 'contacts_split', type: 'json' },
            { key: 'notes' },
            { key: 'product_name' },
            { key: 'policy_term_months', type: 'number' },
            { key: 'transaction_type' },
            { key: 'group_id' },
            { key: 'customer_name' },
            { key: 'effective_date', type: 'date' },
            { key: 'issue_age', type: 'number' },
            { key: 'payment_mode' },
            { key: 'policy_id' },
            { key: 'policy_status' },
            { key: 'product_sub_type' },
            { key: 'customer_id', type: 'number' },
            { key: 'contacts', type: 'json' },
            { key: 'premium_amount', type: 'number' },
          ],
          transformerKey: 'policyTransform',
        },
      ],
    },
    policySplits: {
      mappers: [
        {
          table: 'report_data',
          fields: [
            { key: 'contacts', type: 'json' },
            { key: 'contacts_split', type: 'json' },
          ],
          transformerKey: 'policySplitTransform',
        },
      ],
    },

    agentLevel: {
      mappers: [
        {
          table: 'contact_levels',
          fields: [
            { key: 'level' },
            { key: 'level_label' },
            { key: 'type', type: 'json' },
            { key: 'start_date', type: 'date' },
            { key: 'end_date', type: 'date' },
            { key: 'company_id', type: 'number' },
          ],
          transformerKey: 'agentLevelTransform',
        },
      ],
    },
    agentHierarchy: {
      mappers: [
        {
          table: 'contact_hierarchy',
          fields: [
            { key: 'parent_id', type: 'number' },
            { key: 'contact_id', type: 'number' },
            { key: 'hierarchy_master_id', type: 'number' },
            { key: 'start_date', type: 'date' },
            { key: 'end_date', type: 'date' },
          ],
          transformerKey: 'agentHierarchyTransform',
        },
      ],
    },
    customers: {
      mappers: [
        {
          table: 'customers',
          fields: [
            { key: 'customer_name' },
            { key: 'address', type: 'json' },
            { key: 'company_name' },
            { key: 'dob', type: 'date' },
            { key: 'email' },
            { key: 'end_date', type: 'date' },
            { key: 'first_name' },
            { key: 'gender' },
            { key: 'group_id' },
            { key: 'last_name' },
            { key: 'middle_name' },
            { key: 'nickname' },
            { key: 'phone' },
            { key: 'start_date', type: 'date' },
            { key: 'status' },
            { key: 'type' },
            { key: 'website' },
          ],
          transformerKey: 'customerTransform',
        },
      ],
    },
  };

  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  dataSources: { [key in Entity]?: (...args) => Promise<any> } = {};
  stats: DataSyncStats = {};

  constructor() {
    this.autoLoadTransformers();
  }

  @inject(ContactsService) protected readonly contactsService: ContactsService;
  @inject(ContactService) protected readonly contactService: ContactService;
  @inject(AccountProcessorConfigService)
  configItemService: AccountProcessorConfigService;
  @inject(ReportService) protected readonly reportService: ReportService;

  registerDataSource(
    opts: {
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      [entity in Entity]?: (...args) => Promise<any>;
    }
  ) {
    this.dataSources = { ...this.dataSources, ...opts };
  }

  registerTransformer(
    params: {
      [key in TransfomerKey]?: {
        transformer?: (data: unknown, dbData?: DBData) => unknown;
        getData?: (
          data: unknown,
          dbData?: DBData
        ) => Promise<unknown[]> | unknown[];
      };
    }
  ) {
    for (const [_entity, config] of Object.entries(this.entityConfig)) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      config.mappers.forEach((mapper) => {
        if (params[mapper.transformerKey]) {
          mapper.transformer =
            params[mapper.transformerKey].transformer || mapper.transformer;
          mapper.getData =
            params[mapper.transformerKey].getData || mapper.getData;
        }
      });
    }
  }

  getAvailableEntities(): string[] {
    return Object.keys(this.dataSources);
  }

  autoLoadTransformers() {
    for (const [_entity, config] of Object.entries(this.entityConfig)) {
      for (const mapper of config.mappers) {
        if (mapper.transformerKey) {
          const transformer = this[mapper.transformerKey as string];
          if (transformer && typeof transformer === 'function') {
            mapper.transformer = transformer.bind(this);
          }
        }
      }
    }
  }

  async getDBData(account_id: string, state?: DataStates | DataStates[]) {
    const isSyncingCompGrids = this.task.payload.entities.includes('compGrids');
    const isSyncingCompProfiles =
      this.task.payload.entities.includes('compProfiles');
    const stateFiler = state
      ? { state: Array.isArray(state) ? { in: state } : state }
      : { state: DataStates.ACTIVE };
    const contacts = await prismaClient.contacts.findMany({
      where: { account_id: account_id, ...stateFiler },
    });
    const companies = await prismaClient.companies.findMany({
      where: { account_id: account_id, ...stateFiler },
    });
    const products = await prismaClient.company_products.findMany({
      where: { account_id: account_id, ...stateFiler },
    });
    const policies = await prismaClient.report_data.findMany({
      where: { account_id: account_id, ...stateFiler },
      select: {
        commissionable_premium_amount: true,
        company_id: true,
        company_product_id: true,
        contacts: true,
        contacts_split: true,
        customer_name: true,
        effective_date: true,
        policy_date: true,
        signed_date: true,
        issue_age: true,
        payment_mode: true,
        policy_id: true,
        policy_status: true,
        sync_id: true,
        config: true,
      },
    });
    const levels = await prismaClient.contact_levels.findMany({
      where: { account_id: account_id, ...stateFiler },
    });
    const hierarchy = await prismaClient.contact_hierarchy.findMany({
      where: { account_id: account_id, ...stateFiler },
    });
    const compGridLevels = isSyncingCompGrids
      ? await prismaClient.comp_grid_levels.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];
    const compGrids = isSyncingCompGrids
      ? await prismaClient.comp_grids.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];

    const dateRanges = isSyncingCompGrids
      ? await prismaClient.date_ranges.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];

    const compGridRates = isSyncingCompGrids
      ? await prismaClient.comp_grid_rates.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];

    const compGridProducts = isSyncingCompGrids
      ? await prismaClient.comp_grid_products.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];

    const compGridCriterias = isSyncingCompGrids
      ? await prismaClient.comp_grid_criteria.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];
    const compProfiles = isSyncingCompProfiles
      ? await prismaClient.agent_commission_schedule_profiles.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];
    const compProfileSets = isSyncingCompProfiles
      ? await prismaClient.agent_commission_schedule_profiles_sets.findMany({
          where: { account_id: account_id, state: DataStates.ACTIVE },
        })
      : [];

    const compAgentProfiles = isSyncingCompProfiles
      ? await prismaClient.contacts_agent_commission_schedule_profiles_sets.findMany(
          {
            where: { account_id: account_id, state: DataStates.ACTIVE },
          }
        )
      : [];
    const documents = await prismaClient.documents.findMany({
      where: { account_id: account_id, ...stateFiler },
    });
    const customers = await prismaClient.customers.findMany({
      where: { account_id: account_id, ...stateFiler },
    });
    const customerMap = new Map<string, customers>(
      customers.map((c) => [c.sync_id, c])
    );
    const contactsMap = new Map<string, contacts>(
      contacts.map((c) => [c.sync_id, c])
    );
    const companiesMap = new Map<string, companies>(
      companies.map((c) => [c.sync_id, c])
    );
    const productsMap = new Map<string, company_products>(
      products.map((p) => [p.sync_id, p])
    );
    const policiesMap = new Map<string, Partial<report_data>>(
      policies.map((p) => [p.sync_id, p])
    );
    const levelsMap = new Map<string, contact_levels>(
      levels.map((l) => [l.sync_id, l])
    );
    const hierarchyMap = new Map<string, contact_hierarchy>(
      hierarchy.map((h) => [h.sync_id, h])
    );
    const compGridLevelsMap = new Map<string, comp_grid_levels>(
      compGridLevels.map((c) => [c.sync_id, c])
    );
    const compGridsMap = new Map<string, comp_grids>(
      compGrids.map((c) => [c.sync_id, c])
    );
    const dateRangesMap = new Map<string, date_ranges>(
      dateRanges.map((d) => [d.sync_id, d])
    );
    const compGridRatesMap = new Map<string, comp_grid_rates>(
      compGridRates.map((c) => [c.sync_id, c])
    );
    const compGridProductsMap = new Map<string, comp_grid_products>(
      compGridProducts.map((c) => [c.sync_id, c])
    );
    const compGridCriteriasMap = new Map<string, comp_grid_criteria>(
      compGridCriterias.map((c) => [c.sync_id, c])
    );
    const compProfilesMap = new Map<string, agent_commission_schedule_profiles>(
      compProfiles.map((c) => [c.sync_id, c])
    );
    const compProfileSetsMap = new Map<
      string,
      agent_commission_schedule_profiles_sets
    >(compProfileSets.map((c) => [c.sync_id, c]));
    const compAgentProfilesMap = new Map<
      string,
      contacts_agent_commission_schedule_profiles_sets
    >(compAgentProfiles.map((c) => [c.sync_id, c]));
    const documentsMap = new Map<string, documents>(
      documents.map((d) => [d.sync_id, d])
    );

    return {
      contacts: contactsMap,
      contact_levels: levelsMap,
      companies: companiesMap,
      company_products: productsMap,
      report_data: policiesMap,
      contact_hierarchy: hierarchyMap,
      comp_grid_levels: compGridLevelsMap,
      comp_grids: compGridsMap,
      comp_grid_rates: compGridRatesMap,
      comp_grid_products: compGridProductsMap,
      comp_grid_criteria: compGridCriteriasMap,
      agent_commission_schedule_profiles: compProfilesMap,
      agent_commission_schedule_profiles_sets: compProfileSetsMap,
      contacts_agent_commission_schedule_profiles_sets: compAgentProfilesMap,
      documents: documentsMap,
      customers: customerMap,
      date_ranges: dateRangesMap,
    };
  }

  async ensureUniqueSyncId(table: string, sync_id: string, account_id: string) {
    const items = await prismaClient[table].findMany({
      where: {
        sync_id,
        account_id,
        state: {
          in: [
            DataStates.ACTIVE,
            DataStates.GROUPED,
            DataStates.DELETED,
            DataStates.DUPLICATE,
          ],
        },
      },
    });
    const deletedItem = items.find((i) => i.state === DataStates.DELETED);

    // If there's only one item and it's deleted, reactivate it
    if (items.length === 1 && deletedItem) {
      await prismaClient[table].update({
        where: { id: deletedItem.id },
        data: { state: DataStates.ACTIVE },
      });
    }
    // If there are more than one item, and one of them is deleted, set the sync_id to null for the deleted one
    else if (items.length > 1 && deletedItem) {
      await prismaClient[table].update({
        where: { id: deletedItem.id },
        data: { sync_id: null },
      });
    }
  }

  getAvailableTransformers() {
    const transformers = [];
    for (const [_entity, config] of Object.entries(this.entityConfig)) {
      for (const mapper of config.mappers) {
        if (mapper.transformer) {
          transformers.push({
            table: mapper.table,
            transformer: this[mapper.transformerKey].bind(this),
          });
        }
      }
    }
    return transformers;
  }

  isDiff(fields, syncDatum, dbDatum) {
    let diff = false;
    if (!dbDatum) {
      return true;
    }
    for (const field of fields) {
      if (field.type === 'number') {
        if (
          (syncDatum?.[field.key] || dbDatum?.[field.key]) &&
          +syncDatum?.[field.key] !== +dbDatum?.[field.key]
        ) {
          diff = true;
          break;
        }
      } else if (field.type === 'json') {
        if (
          (syncDatum?.[field.key] || dbDatum?.[field.key]) &&
          !isEqual(syncDatum?.[field.key], dbDatum?.[field.key])
        ) {
          diff = true;
          break;
        }
      } else if (field.type === 'date') {
        if (
          syncDatum?.[field.key] > dbDatum?.[field.key] ||
          syncDatum?.[field.key] < dbDatum?.[field.key]
        ) {
          diff = true;
          break;
        }
      } else if (
        Object.hasOwn(syncDatum, field.key) &&
        syncDatum?.[field.key] !== dbDatum?.[field.key]
      ) {
        diff = true;
        break;
      }
    }
    return diff;
  }

  async upsert(
    table: string,
    data: {
      where: { sync_id: string; account_id: string; state: DataStates };
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      update: any;
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      create: any;
    }
  ) {
    const records = await prismaClient[table].findMany({
      where: {
        ...data.where,
        state: { not: 'active' },
      },
    });

    if (records.length) {
      await prismaClient[table].updateMany({
        where: { id: { in: records.map((r) => r.id) } },
        data: { ...data.update, state: undefined },
      });
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      return (records as any[])[0];
    } else {
      if (table === 'comp_grid_levels') {
        const existing = await prismaClient.comp_grid_levels.findFirst({
          where: {
            account_id: data.where.account_id,
            sync_id: data.where.sync_id,
            state: DataStates.ACTIVE,
          },
        });
        if (existing) {
          await prismaClient.comp_grid_levels.update({
            where: { id: existing.id },
            data: { ...data.update, state: undefined },
          });
        } else {
          await prismaClient.comp_grid_levels.create({
            data: { ...data.create, state: DataStates.ACTIVE },
          });
        }
      } else {
        return await prismaClient[table].upsert({
          where: {
            account_id_sync_id_state: {
              ...data.where,
            },
            state: DataStates.ACTIVE,
          },
          update: data.update,
          create: data.create,
        });
      }
    }
  }

  // Prevent override fields from being updated
  overrideValues(params: {
    datum;
    dbData?: DBData;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    originDatum?: any;
    table: string;
  }) {
    const { datum, dbData, table, originDatum } = params;
    const originData = originDatum || dbData[table].get(datum.sync_id);
    // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    if (originData && originData.config?.overrideFields) {
      for (const field of originData.config.overrideFields) {
        datum[field] = undefined;
      }
    }
    return datum;
  }

  async process(
    task: QueueTask<SyncParamsDTO>
  ): Promise<{ stats: DataSyncStats; duration: number }> {
    const startTime = Date.now();
    const { account, payload } = task;
    this.task = task;

    for (const entity of payload.entities) {
      const startTime = Date.now();

      for (const [_, mappers] of Object.entries(this.entityConfig[entity])) {
        for (const mapper of mappers) {
          const dbData = await this.getDBData(account.account_id);

          console.time(`Fetch data for ${entity} mapper: ${mapper.table}`);
          const data = await this.dataSources[entity](dbData, account);
          console.timeEnd(`Fetch data for ${entity} mapper: ${mapper.table}`);

          const fields = mapper.fields;
          const _data = mapper.getData
            ? await mapper.getData(data, dbData)
            : data;
          const transformedData = mapper.transformer
            ? _data.map((d) => mapper.transformer(d, dbData))
            : _data;
          const taskData = transformedData
            .filter((r) => {
              return this.isDiff(
                fields,
                r,
                dbData[mapper.table].get(r.sync_id)
              );
            })
            .map((r) =>
              this.overrideValues({ datum: r, dbData, table: mapper.table })
            )
            .filter((r) => {
              return ['company_products', 'comp_grids'].includes(mapper.table)
                ? r?.company_id
                : r;
            })
            .filter((r) => r.sync_id)
            .map((r) => {
              return {
                ...r,
                sync_worker: r.sync_worker || this.name,
              };
            });

          await limitConcurrency(
            async (datum) => {
              this.logger.info(
                `Syncing ${mapper.table} with sync_id: ${datum.sync_id}`
              );
              if (['contact_hierarchy'].includes(mapper.table)) {
                const contact =
                  mapper.table === 'contact_hierarchy'
                    ? await prismaClient.contacts.findFirst({
                        where: {
                          id: (datum as IHierarchyDatum).contact_id,
                        },
                        accountInject: false,
                      })
                    : await this.contactsService.findContactBySyncId(
                        `${datum.contact_id}`
                      );
                if (!contact) {
                  return;
                }

                const ret = await pRetry(
                  async () => {
                    await this.ensureUniqueSyncId(
                      mapper.table,
                      datum.sync_id,
                      account.account_id
                    );
                    return this.upsert(mapper.table, {
                      where: {
                        sync_id: datum.sync_id,
                        account_id: account.account_id,
                        state: DataStates.ACTIVE,
                      },
                      update: {
                        ...datum,
                        updated_at: new Date(),
                        updated_by: account.uid,
                        contact_id: contact.id,
                        state: DataStates.ACTIVE,
                        updated_proxied_by: account.ouid,
                      },
                      create: {
                        ...datum,
                        account_id: account.account_id,
                        contact_id: contact?.id,
                        created_by: account.uid,
                        created_proxied_by: account.ouid,
                      },
                    });
                  },
                  { retries: 3, minTimeout: 100 }
                );
                if (mapper.table === 'contact_hierarchy' && ret.id) {
                  return await prismaClient.contacts.update({
                    where: { id: contact.id },
                    data: {
                      parent_relationships: {
                        connect: {
                          id: ret.id,
                        },
                      },
                    },
                  });
                }
              } else {
                return await pRetry(
                  async () => {
                    await this.ensureUniqueSyncId(
                      mapper.table,
                      datum.sync_id,
                      account.account_id
                    );
                    return this.upsert(mapper.table, {
                      where: {
                        account_id: account.account_id,
                        sync_id: datum.sync_id,
                        state: DataStates.ACTIVE,
                      },
                      update: {
                        ...datum,
                        updated_by: account.uid,
                        updated_at: new Date(),
                        state: 'active',
                        updated_proxied_by: account.ouid,
                      },
                      create: {
                        ...datum,
                        account_id: account.account_id,
                        ...(['customers', 'date_ranges'].includes(mapper.table)
                          ? {}
                          : { uid: account.uid }),
                        created_by: account.uid,
                        created_proxied_by: account.ouid,
                      },
                    });
                  },
                  { retries: 3, minTimeout: 100 }
                );
              }
            },
            taskData,
            50,
            {
              retries: 0,
            }
          );

          // Don't await here, since we want it to run async in the background, and continue with the next entity
          this?.afterSyncHook({
            entity: entity,
            data: data,
            dbData: dbData,
            taskData: taskData,
            account,
          });

          this.stats[entity] = {
            totalCount: transformedData.length,
            updatedCounts: {
              ...(this.stats[entity]?.updatedCounts || {}),
              [mapper.table]: taskData.length,
            },
            duration: Date.now() - startTime,
          };
        }
      }
    }
    return { stats: this.stats, duration: Date.now() - startTime };
  }
  async afterSyncHook(_context: {
    entity: Entity;
    data: unknown;
    dbData: DBData;
    taskData?: unknown[];
    account: ExtAccountInfo;
  }): Promise<void> {
    return;
  }

  async getLinkingPolicyAgents(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    agents: { firstName: string; lastName: string; [key: string]: any }[],
    account_id,
    opts?: {
      contactStatus?: string;
    }
  ) {
    const nameCondition: {
      name?: string;
      first_name?: string;
      last_name?: string;
    }[] = (agents || [])?.map((agent) => ({
      first_name: agent.firstName,
      last_name: agent.lastName,
    }));

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    agents.forEach((agent) => {
      nameCondition.push({ name: this.getName(agent) });
    });

    const contacts = await this.contactService.getContacts({
      where: {
        OR: nameCondition,
        state: DataStates.ACTIVE,
        status: opts?.contactStatus ?? undefined,
        account_id,
      },
    });
    return contacts;
  }

  getName({
    firstName,
    lastName,
    agentCode,
  }: {
    firstName: string;
    lastName: string;
    agentCode?: string;
  }) {
    if (agentCode) {
      return agentCode;
    }
    return [firstName?.trim(), lastName?.trim()].join(' ');
  }
  async linkingPolicyAgents(data: {
    syncId: string;
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    agents: { firstName: string; lastName: string; [key: string]: any }[];
    opts?: { contactStatus?: string };
    account_id: string;
  }) {
    const { syncId, agents, account_id, opts } = data;
    const policies = await this.reportService.getReportData(
      {
        sync_id: syncId,
      },
      false
    );

    const contacts = await this.getLinkingPolicyAgents(
      agents,
      account_id,
      opts
    );
    const contactsMap = groupBy(contacts, (item) =>
      this.getName({
        firstName: item.first_name,
        lastName: item.last_name,
        agentCode: item.agent_code,
      })
    );

    const multipleContacts = [];
    for (const [name, contacts] of Object.entries(contactsMap)) {
      if (contacts.length > 1) {
        multipleContacts.push(name);
      }
    }

    if (multipleContacts.length) {
      this.logger.warn(
        `Multiple contacts found for agents: ${multipleContacts}`
      );
      return;
    }

    const agentStrIds = agents
      ?.map((agent) => contactsMap[this.getName(agent)])
      .filter((r) => r)
      .map((r) => r[0].str_id);

    for (const policy of policies) {
      if (
        (
          policy?.config as { overrideFields: string[] }
        )?.overrideFields?.includes('contacts')
      ) {
        this.logger.info(
          `Skipping policy ${policy.id}, policy_id: ${policy.policy_id} because it has overrideFields for contacts`
        );
        continue;
      }
      if (!isEqual(policy?.contacts?.sort(), agentStrIds?.sort())) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.info(
          `Update contacts for policy ${policy.id}, policy_id: ${policy.policy_id}: from ${policy.contacts} to ${agentStrIds}`
        );
        await prismaClient.report_data.update({
          where: { id: policy.id },
          data: { contacts: [...new Set(agentStrIds)] },
        });
      }
    }
  }
}
