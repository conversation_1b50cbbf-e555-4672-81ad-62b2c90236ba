import type { DataProcessingTypes, ExtAccountInfo } from '@/types';

export enum Queue {
  DATA_PROCESSING = 'data-processing',
  NOWCERTS_SYNC = 'nowcerts-sync-processing',
  AGENT_COMMISSION_CALC = 'agent-commission-calc',
  RECONCILIATION = 'reconciliation',
  GROUP_DEDUPE = 'group-dedupe',
  AGENCY_INTEGRATOR_SYNC = 'agency-integrator-sync-processing',
  DOCUMENT_PROCESSING = 'document-processing',
}

export class QueueTask<T> {
  queue?: Queue;
  task_id?: string;
  url?: string;
  type: DataProcessingTypes;
  payload: T;
  account?: ExtAccountInfo;
}
