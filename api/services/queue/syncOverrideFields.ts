import { prismaClient } from '@/lib/prisma';

export class SyncOverrideFieldsService {
  async updateOverrideFields({
    table,
    fields,
    id,
  }: {
    table?: string;
    id?: number;
    fields?: string[];
  }) {
    const record = await prismaClient[table].findOne({
      where: {
        id,
      },
      select: {
        config: true,
      },
    });
    await prismaClient[table].update({
      where: {
        id,
      },
      data: {
        config: {
          ...(record?.config || {}),
          overrideFields: fields,
        },
      },
    });
  }
}
