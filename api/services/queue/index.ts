import { CloudTasksClient } from '@google-cloud/tasks';
import type { Prisma, PrismaClient } from '@prisma/client/extension';
import * as Sentry from '@sentry/nextjs';
import { WorkerNames } from 'common/constants';
import type {
  EntityType,
  SyncParamsDTO,
} from 'common/dto/data_processing/sync';
import { inject, injectable } from 'inversify';
import omitBy from 'lodash-es/omitBy';
import { nanoid } from 'nanoid';

import { SERVICE_TYPES } from '@/constants';
import { BusinessException } from '@/lib/exceptionHandler';
import { prismaClient } from '@/lib/prisma';
import { prismaTransactionHandler } from '@/lib/prismaUtils';
import { AccountService } from '@/services/account';
import { AccountProcessorConfigService } from '@/services/account-processor-config';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { CloudTaskService } from '@/services/cloud-task';
import { ConfigService } from '@/services/config';
import { DataProcessService } from '@/services/data_processing';
import type { GChatService } from '@/services/notification/gchat';
import { AgencyIntegratorWorker } from '@/services/queue/worker/agencyIntegrator';
import { AwsS3Worker } from '@/services/queue/worker/awss3';
import type {
  IDataSyncWorker,
  IDocumentProcessingWorker,
} from '@/services/queue/worker/base';
import { BenefitPointWorker } from '@/services/queue/worker/benefitPoint';
import { DocumentProcessingWorker } from '@/services/queue/worker/documentProcessing';
import { MyAdvisorGridsWorker } from '@/services/queue/worker/myadvisorgrids';
import { NowCertsWorker } from '@/services/queue/worker/nowCerts';
import { OneHQWorker } from '@/services/queue/worker/oneHQ';
import { SmartOfficeWorker } from '@/services/queue/worker/smartOffice';
import {
  DataProcessingStatuses,
  DataProcessingTypes,
  type ExtAccountInfo,
} from '@/types';
import { Queue, type QueueTask } from './types';
import { TransGlobalWorker } from './worker/transGlobal';
import { AppLoggerService } from '@/services/logger/appLogger';

@injectable()
export class QueueService {
  private client: CloudTasksClient;

  public dataSyncWorkers: IDataSyncWorker<unknown>[] = [];
  private documentProcessingWorkers: IDocumentProcessingWorker<unknown>[] = [];
  private logger = new AppLoggerService({
    defaultMeta: {
      service: 'QueueService',
    },
  });

  @inject(CloudTaskService) private cloudTaskService: CloudTaskService;
  @inject(DataProcessService) private dataProcessingService: DataProcessService;
  @inject(AccountProcessorConfigService)
  private configItemService: AccountProcessorConfigService;
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  @inject(ConfigService) private configService: ConfigService;
  @inject(AccountService) private accountService: AccountService;
  @inject(SERVICE_TYPES.GChatServiceFactory) private gchatServiceFactory: (
    endpoint: string
  ) => GChatService;

  constructor(
    @inject(AgencyIntegratorWorker)
    agencyIntegratorWorker: AgencyIntegratorWorker,
    @inject(TransGlobalWorker) transGlobalWorker: TransGlobalWorker,
    @inject(BenefitPointWorker) benefitPointWorker: BenefitPointWorker,
    @inject(NowCertsWorker) nowCertsWorker: NowCertsWorker,
    @inject(OneHQWorker) oneHQWorker: OneHQWorker,
    @inject(MyAdvisorGridsWorker) myAdvisorGridsWorker: MyAdvisorGridsWorker,
    @inject(SmartOfficeWorker) smartOfficeWorker: SmartOfficeWorker,
    @inject(AwsS3Worker) awsS3Worker: AwsS3Worker,
    @inject(DocumentProcessingWorker)
    documentProcessingWorker: DocumentProcessingWorker
  ) {
    const data = JSON.parse(process.env.CLOUD_TASKS_JSON);
    this.client = new CloudTasksClient({
      credentials: {
        ...data,
        private_key: data.private_key.replace(/\\n/gm, '\n'),
      },
    });
    this.dataSyncWorkers = [
      agencyIntegratorWorker,
      transGlobalWorker,
      benefitPointWorker,
      nowCertsWorker,
      oneHQWorker,
      myAdvisorGridsWorker,
      smartOfficeWorker,
      awsS3Worker,
    ];
    this.documentProcessingWorkers = [documentProcessingWorker];
  }

  async getWorker(task: QueueTask<SyncParamsDTO>) {
    // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    let configs;
    if (task.type === DataProcessingTypes.data_sync) {
      if (!task.payload.worker) {
        configs = await this.configItemService.getAccountConfigByType<
          ConfigItemValueForDataSync<unknown>
        >({
          type: 'dataSync',
          account_id: task.account.account_id,
        });
      }
      // If worker is specified in the task payload, use it, otherwise use the worker specified in the config, mainly for backward compatibility purpose
      return this.dataSyncWorkers.find((worker) =>
        task.payload.worker
          ? worker.name === task.payload.worker
          : worker.name === configs?.[0]?.value?.worker
      );
    } else if (task.type === DataProcessingTypes.document_processing) {
      return this.documentProcessingWorkers.find(
        (worker) => worker.name === WorkerNames.DocumentProcessingWorker
      );
    }
  }

  async createTask<T extends { worker?: string }>(
    data: QueueTask<T>,
    opts: { disableTask?: boolean } = { disableTask: false },
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    const { account } = data;
    const queue = data?.queue || Queue.DATA_PROCESSING;

    const taskId = nanoid();
    const taskName = this.cloudTaskService.getTaskName(queue, taskId);

    try {
      return await prismaTransactionHandler(prisma, async (transaction) => {
        if (data.type === DataProcessingTypes.data_sync) {
          const isDataSyncEnabled = await this.accountService.isDataSyncEnabled(
            account.account_id,
            transaction
          );
          if (!isDataSyncEnabled) {
            throw BusinessException.from(
              `Data sync is not enabled for this account: ${account.account_id}`
            );
          }
        }
        // Only lock non-data-sync tasks
        if (
          data.type !== DataProcessingTypes.data_sync &&
          data.type !== DataProcessingTypes.document_processing
        ) {
          const isLocked = await this.dataProcessingService.lockSyncTask(
            { account, type: data.type },
            transaction
          );
          if (!isLocked) {
            throw BusinessException.from(
              `${data.type} task is running, please try again later`
            );
          }
        }
        await this.dataProcessingService.create(
          {
            account: account?.account_id
              ? { connect: { str_id: account?.account_id } }
              : undefined,
            user: account?.uid ? { connect: { uid: account?.uid } } : undefined,
            proxy_user: account?.ouid
              ? { connect: { uid: account?.ouid } }
              : undefined,
            str_id: taskId,
            params: JSON.stringify({
              // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              ...(omitBy(data?.payload ?? {}, (val: any) => {
                if ([null, undefined, ''].includes(val)) return true;
                if (Array.isArray(val)) return val.length === 0;
                if (typeof val === 'object' && !(val instanceof Date))
                  return Object.keys(val).length === 0;
                return false;
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              }) as Record<string, any>),
              task_id: taskId,
              queue,
            }),
            type: data.type,
            status: DataProcessingStatuses.PENDING,
            worker: data.payload?.worker,
          },
          transaction
        );

        if (!opts.disableTask) {
          await this.cloudTaskService.createTask({
            ...data,
            queue,
            task_id: taskId,
          });
        }
        return taskId;
      });
    } catch (err) {
      this.logger.error('Error creating task', err);
      // Incase of error, delete the task, ignore if task not exists
      if (!opts.disableTask) {
        // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        await this.client.deleteTask({ name: taskName }).catch(() => {});
      }
      throw err;
    }
  }

  async createAutoTask(params: {
    entities: EntityType;
    worker: string;
    account_id: string;
  }) {
    const { entities, worker, account_id } = params;
    return await this.createTask<SyncParamsDTO>({
      type: DataProcessingTypes.data_sync,
      payload: {
        entities: entities,
        worker: worker,
      },
      account: {
        account_id: account_id,
        // Make these values empty means a cron driven data-sync task
        uid: undefined,
        ouid: undefined,
        role_id: undefined,
      } as ExtAccountInfo,
    }).catch((err) => {
      this.logger.error('Error creating auto task', err);
      Sentry.captureException(err);
    });
  }

  async autoSync() {
    const configs = await this.configItemService.listConfigs();
    for (const config of configs) {
      for (const entity of config.value.entities) {
        await this.createAutoTask({
          entities: [entity],
          worker: config.value.worker,
          account_id: config.account_id,
        });
      }
    }
    return true;
  }

  async process<T>(task: QueueTask<T>) {
    const stats = {};
    const taskId = task.task_id;
    const startTime = Date.now();
    const notifyService = this.gchatServiceFactory(
      'https://chat.googleapis.com/v1/spaces/AAAAH8qsFjQ/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=QppxPDGJzWgFegcShdwNGNozqVq2dMXhIz90Gt4oi58'
    );
    let worker: IDataSyncWorker<unknown> | IDocumentProcessingWorker<unknown>;
    try {
      const datum = await this.dataProcessingService.findByTaskId(taskId);
      if (!datum) {
        return;
      }
      if (
        [
          DataProcessingStatuses.COMPLETED,
          DataProcessingStatuses.ERROR,
        ].includes(datum.status as DataProcessingStatuses)
      ) {
        return;
      }

      this.logger.info(
        `Update data_proxessing status from ${datum.status} to processing`
      );
      await this.dataProcessingService.updateTaskStatus({
        str_id: taskId,
        status: DataProcessingStatuses.PROCESSING,
      });

      this.logger.info('Start processing task', {
        task,
      });
      worker = await this.getWorker(task as QueueTask<SyncParamsDTO>);
      if (!worker) {
        throw new Error('Worker not found');
      }

      this.logger.info(`Worker For ${task.type}: ${worker.name}`);

      if (worker.setup) {
        await worker.setup(task.account);
      }

      const { stats, duration } = await worker.process(
        task as QueueTask<SyncParamsDTO>
      );

      this.logger.info('Task completed', {
        stats,
      });
      await this.dataProcessingService.updateTaskStatus({
        str_id: taskId,
        status: DataProcessingStatuses.COMPLETED,
        duration: duration || Date.now() - startTime,
        stats: stats,
      });
    } catch (error) {
      this.logger.error('Task processing error:', error);
      const account = await prismaClient.accounts.findFirst({
        where: {
          str_id: task.account.account_id,
        },
        accountInject: false,
      });
      if (process.env.NODE_ENV === 'production') {
        notifyService.notify(
          `
          DataSync task failed
          Worker: ${worker?.name}
          Account: ${account.name} 
          AccountId: ${account?.str_id}
          SyncEntities: ${(task.payload as SyncParamsDTO).entities}
          Error: ${error.message}
          Stats: ${JSON.stringify(stats, null, 2)}
          Env: ${process.env.NODE_ENV}
        `
        );
      }
      await this.dataProcessingService.updateTaskStatus({
        str_id: taskId,
        status: DataProcessingStatuses.ERROR,
        stats: { ...stats, error: error.message },
        duration: Date.now() - startTime,
      });
      throw error;
    }
    return stats;
  }
}
