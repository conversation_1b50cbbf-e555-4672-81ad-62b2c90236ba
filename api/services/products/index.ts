import type { Prisma } from '@prisma/client';
import type { PrismaClient } from '@prisma/client/extension';
import { injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';

@injectable()
export class ProductService {
  async queryAll(
    query: { where: Prisma.company_productsWhereInput },
    prisma: PrismaClient | Prisma.TransactionClient = prismaClient
  ) {
    if (!query?.where) {
      return [];
    }
    return await prisma.company_products.findMany(query);
  }

  async getProductById(id: number) {
    if (!id) {
      return null;
    }
    return await prismaClient.company_products.findFirst({
      where: {
        id,
      },
      accountInject: false,
    });
  }
}
