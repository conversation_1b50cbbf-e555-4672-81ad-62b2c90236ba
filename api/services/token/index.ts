import { AccountIds } from 'common/constants';
import { TRANSGLOBAL_ACCOUNTID } from 'common/customer/customer.constants';
import { inject, injectable } from 'inversify';
import jwt from 'jsonwebtoken';

import { Config } from '@/lib/decorators';
import { ConfigService } from '@/services/config';
import { ContactService } from '@/services/contact';
import { UserService } from '@/services/user';

@injectable()
export class TokenService implements ITokenService {
  @inject(ConfigService) configService: ConfigService;
  @inject(ContactService) contactService: ContactService;
  @inject(UserService) userService: UserService;
  @Config('TRANSGLOBAL_SECRET')
  secret: string;

  decodeAndVerifyJWT = async (token: string) => {
    try {
      // Verify the token's signature
      const decoded = jwt.verify(token, this.secret, {
        algorithms: ['HS256'],
      });
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      const { email, ipipelineid } = decoded as any;
      // Check if the contact exists
      const transglobalAccountId = AccountIds.TRANSGLOBAL;
      const contact = await this.contactService.getContactByEmailOrSyncId(
        email,
        ipipelineid,
        transglobalAccountId
      );
      if (!contact) {
        const accountAdmin = await this.userService.getAccountAdminByEmail(
          email,
          TRANSGLOBAL_ACCOUNTID
        );
        if (accountAdmin) {
          return { decodedToken: decoded, uid: accountAdmin.uid };
        }

        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.error('Contact not found:', email, ipipelineid);
        return null;
      }
      return { decodedToken: decoded, contact_id: contact.id };
    } catch (err) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Token verification failed:', err.message);
      return null;
    }
  };
}

export interface ITokenService {
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  decodeAndVerifyJWT(token: string): any;
}
