import { Decimal } from '@prisma/client/runtime/library';
import dayjs from 'dayjs';
import { Container } from 'inversify';
import { beforeEach, describe, expect, test, vi } from 'vitest';

import { REPOSITORY_TYPES } from '@/constants';
import type ICommissionScheduleRepo from '@/persistence/commission-schedules/ICommissionScheduleRepo';
import type ICompGridCriteriaRepo from '@/persistence/comp-grid-criteria/ICompGridCriteriaRepo';
import InMemCompGridCriteriaRepo from '@/persistence/comp-grid-criteria/InMemCompGridCriteriaRepo';
import type ICompGridProductsRepo from '@/persistence/comp-grid-products/ICompGridProductsRepo';
import InMemCompGridProductsRepo from '@/persistence/comp-grid-products/InMemCompGridProductsRepo';
import type ICompProfilesRepo from '@/persistence/comp-profiles/ICompProfilesRepo';
import InMemCompProfilesRepo from '@/persistence/comp-profiles/InMemCompProfilesRepo';
import type ICompanyRepository from '@/persistence/companies/ICompanyRepository';
import type ICompanyProductOptionsRepo from '@/persistence/company-product-options/ICompanyProductOptionsRepo';
import InMemCompanyProductOptionsRepo from '@/persistence/company-product-options/InMemCompanyProductOptionsRepo';
import type ICompanyProductsRepo from '@/persistence/company-products/ICompanyProductsRepo';
import InMemCompanyProductsRepo from '@/persistence/company-products/InMemCompanyProductsRepo';
import type IContactsRepo from '@/persistence/contacts/IContactsRepo';
import type IReconciliationDataRepo from '@/persistence/reconciliation-data/IReconciliationDataRepo';
import type IReportDataRepo from '@/persistence/report-data/IReportDataRepo';
import type ReportDataRecord from '@/persistence/report-data/ReportDataRecord';
import CommissionSchedulesService from '@/services/commission-schedules/CommissionSchedulesService';
import CompGridsService from '@/services/comp-grids/CompGridsService';
import CompProfilesService from '@/services/comp-profiles/CompProfilesService';
import { CompaniesService } from '@/services/companies';
import { ContactsService } from '@/services/contacts';
import { ReconciliationService } from '@/services/reconciliation';
import { ReportService } from '@/services/report';
import InMemCommissionScheduleRepo from '../../persistence/commission-schedules/InMemCommissionScheduleRepo';
import InMemCompanyRepository from '../../persistence/companies/InMemCompanyRepository';
import InMemContactsRepo from '../../persistence/contacts/InMemContactsRepo';
import InMemReconciliationDataRepo from '../../persistence/reconciliation-data/InMemReconciliationDataRepo';
import InMemReportDataRepo from '../../persistence/report-data/InMemReportDataRepo';
import type {
  CommissionBalanceMonthly,
  CommissionExpectedMonthly,
} from '../reconciliation/types';
import ReceivableCalcService from './ReceivableCalcService';

describe('ReceivableCalcService', () => {
  let container: Container;

  // Prettier-ignore
  beforeEach(() => {
    container = new Container({ defaultScope: 'Singleton' });
    container
      .bind<ICompanyRepository>(REPOSITORY_TYPES.CompanyRepository)
      .to(InMemCompanyRepository);
    container
      .bind<ICompanyProductsRepo>(REPOSITORY_TYPES.CompanyProductsRepository)
      .to(InMemCompanyProductsRepo);
    container
      .bind<ICompanyProductOptionsRepo>(
        REPOSITORY_TYPES.CompanyProductOptionsRepository
      )
      .to(InMemCompanyProductOptionsRepo);
    container
      .bind<ICommissionScheduleRepo>(
        REPOSITORY_TYPES.CommissionScheduleRepository
      )
      .to(InMemCommissionScheduleRepo);
    container
      .bind<ICompGridCriteriaRepo>(REPOSITORY_TYPES.CompGridRepository)
      .to(InMemCompGridCriteriaRepo);
    container
      .bind<ICompGridCriteriaRepo>(REPOSITORY_TYPES.CompGridCriteriaRepository)
      .to(InMemCompGridCriteriaRepo);
    container
      .bind<ICompGridProductsRepo>(REPOSITORY_TYPES.CompGridProductsRepository)
      .to(InMemCompGridProductsRepo);
    container
      .bind<ICompProfilesRepo>(REPOSITORY_TYPES.CompProfilesRepository)
      .to(InMemCompProfilesRepo);
    container
      .bind<IContactsRepo>(REPOSITORY_TYPES.ContactsRepository)
      .to(InMemContactsRepo);
    container
      .bind<IReconciliationDataRepo>(
        REPOSITORY_TYPES.ReconciliationDataRepository
      )
      .to(InMemReconciliationDataRepo);
    container
      .bind<IReportDataRepo>(REPOSITORY_TYPES.ReportDataRepository)
      .to(InMemReportDataRepo);
    container.bind<CompaniesService>(CompaniesService).toSelf();
    container
      .bind<CommissionSchedulesService>(CommissionSchedulesService)
      .toSelf();
    container.bind<CompGridsService>(CompGridsService).toSelf();
    container.bind<CompProfilesService>(CompProfilesService).toSelf();
    container.bind<ContactsService>(ContactsService).toSelf();
    container.bind<ReconciliationService>(ReconciliationService).toSelf();
    container.bind<ReportService>(ReportService).toSelf();
    container.bind<ReceivableCalcService>(ReceivableCalcService).toSelf();
  });

  describe('resolveReportCompProfiles()', () => {
    test('matches a comp schedule to a report', async () => {
      const companyRepo = container.get<ICompanyRepository>(
        REPOSITORY_TYPES.CompanyRepository
      );
      const compProfileRepo = container.get<ICompProfilesRepo>(
        REPOSITORY_TYPES.CompProfilesRepository
      );
      const productsRepo = container.get<ICompanyProductsRepo>(
        REPOSITORY_TYPES.CompanyProductsRepository
      );
      const reportRepo = container.get<IReportDataRepo>(
        REPOSITORY_TYPES.ReportDataRepository
      );
      const svc = container.get<ReceivableCalcService>(ReceivableCalcService);

      const account_id = '3dc8f9c0';
      const uid = 'f27feda5';

      // Setup entities
      const { id: companyPkId } = await companyRepo.create({
        account_id,
        uid,
        company_name: 'SecureLife',
      });

      const { id: productPkId } = await productsRepo.create({
        account_id,
        uid,
        company_id: companyPkId,
        product_name: 'SecureLife UL',
        product_type: 'UL',
      });

      const { str_id: compProfileStrId } = await compProfileRepo.create({
        account_id,
        uid,
        name: 'Test Comp Profile',
        schedules: [
          {
            product_id: [productPkId],
            issue_age_max: '1',
            issue_age_min: '0',
            limit_to_received: false,
          },
        ],
      });

      const { str_id: reportDataStrId } = await reportRepo.create({
        account_id,
        uid,
        state: 'active',
        company_id: companyPkId,
        product_type: 'UL',
        product_name: 'SecureLife UL',
      });

      // Act
      await svc.resolveReportCompProfiles({
        accountId: account_id,
        useCompGrids: false,
      });

      // Verify
      const [reportRecord] = await reportRepo.getByStrId([reportDataStrId]);
      expect(reportRecord).toMatchObject<Partial<ReportDataRecord>>({
        str_id: reportDataStrId,
        commission_profile_id: compProfileStrId,
      });
    });
  });

  describe('calculateCommissionSchedules()', () => {
    test('calculates a receivable schedule', async () => {
      vi.useFakeTimers();
      vi.setSystemTime(new Date('2025-06-01T00:00:00.000Z'));

      const companyRepo = container.get<ICompanyRepository>(
        REPOSITORY_TYPES.CompanyRepository
      );
      const commissionScheduleRepo = container.get<ICommissionScheduleRepo>(
        REPOSITORY_TYPES.CommissionScheduleRepository
      );
      const reportRepo = container.get<IReportDataRepo>(
        REPOSITORY_TYPES.ReportDataRepository
      );
      const reconciliationDataRepo = container.get<IReconciliationDataRepo>(
        REPOSITORY_TYPES.ReconciliationDataRepository
      );
      const svc = container.get<ReceivableCalcService>(ReceivableCalcService);

      const accountId = '330347bc';
      const uid = '27ea132b';
      const companyStrId = '4b02ac51';
      const companyName = 'Test Company 03c4108a';
      const productType = 'IUL';
      const productName = 'SecureIndex UL';

      // Setup entities
      const { id: companyPkId } = await companyRepo.create({
        account_id: accountId,
        uid,
        company_id: companyStrId,
        company_name: companyName,
        email: '<EMAIL>',
      });

      await commissionScheduleRepo.create({
        name: 'Commission Sched 001',
        account_id: accountId,
        uid: uid,
        carrier_company_id: companyPkId,
        start_date: dayjs('2024-01-01T00:00:00.000Z').toDate(),
        end_date: dayjs('2025-01-01T00:00:00.000Z').toDate(),
        product_type: productType,
        product_name: productName,
        commission_schedule: [
          {
            rate: '1',
            year: '1',
            breakdown: [
              '25',
              '75',
              '0',
              '0',
              '0',
              '0',
              '0',
              '0',
              '0',
              '0',
              '0',
              '0',
            ],
          },
        ],
      });

      const { id: reportDataPkId, str_id: reportDataStrId } =
        await reportRepo.create({
          account_id: accountId,
          uid,
          state: 'active',
          company_id: companyPkId,
          product_type: productType,
          product_name: productName,
        });

      const statementAPkId = 9620;
      const statementAStrId = '427e4ad8';
      const statementBPkId = 1581;
      const statementBStrId = '6ed2cf23';

      const { str_id: reconciliationDataStrId } =
        await reconciliationDataRepo.create({
          account_id: accountId,
          uid,
          product_type: productType,
          product_name: productName,
          commission_amount_monthly: {
            '06/01/2024': { commission_amount_monthly: 14727.72 },
            '07/01/2024': { commission_amount_monthly: 584.43 },
          },
          commissionable_premium_amount: new Decimal(5640.51),
          effective_date: dayjs('2024-04-04T16:00:00.000Z').toDate(),
          policy_id: '5888285',
          report_id: `${reportDataPkId}`,
          report_str_id: reportDataStrId,
          statement_ids: [statementAPkId, statementBPkId],
          statement_str_ids: [statementAStrId, statementBStrId],
          writing_carrier_name: companyName,
        });

      // Act
      await svc.calculateCommissionSchedules({
        account_id: accountId,
      });

      // Verify
      const finalRecDataRecords = await reconciliationDataRepo.getByStrId([
        reconciliationDataStrId,
      ]);
      const [finalRecData] = finalRecDataRecords;
      expect(finalRecData.balance.toNumber()).toBeCloseTo(112.81, 2);
      expect(finalRecData.commissions_expected.toNumber()).toBeCloseTo(
        112.81,
        2
      );
      expect(
        finalRecData.commission_expected_monthly
      ).toEqual<CommissionExpectedMonthly>({
        '04/01/2024': expect.closeTo(14.1, 2),
        '05/01/2024': expect.closeTo(42.3, 2),
        '06/01/2024': expect.closeTo(0, 2),
        '07/01/2024': expect.closeTo(0, 2),
        '08/01/2024': expect.closeTo(0, 2),
        '09/01/2024': expect.closeTo(0, 2),
        '10/01/2024': expect.closeTo(0, 2),
        '11/01/2024': expect.closeTo(0, 2),
        '12/01/2024': expect.closeTo(0, 2),
        '01/01/2025': expect.closeTo(0, 2),
        '02/01/2025': expect.closeTo(0, 2),
        '03/01/2025': expect.closeTo(0, 2),
        '04/01/2025': expect.closeTo(14.1, 2),
        '05/01/2025': expect.closeTo(42.3, 2),
        '06/01/2025': expect.closeTo(0, 2),
        '07/01/2025': expect.closeTo(0, 2),
      });
      expect(
        finalRecData.commission_balance_monthly
      ).toEqual<CommissionBalanceMonthly>({
        '04/01/2024': expect.closeTo(14.1, 2),
        '05/01/2024': expect.closeTo(56.41, 2),
        '06/01/2024': expect.closeTo(56.41, 2),
        '07/01/2024': expect.closeTo(56.41, 2),
        '08/01/2024': expect.closeTo(56.41, 2),
        '09/01/2024': expect.closeTo(56.41, 2),
        '10/01/2024': expect.closeTo(56.41, 2),
        '11/01/2024': expect.closeTo(56.41, 2),
        '12/01/2024': expect.closeTo(56.41, 2),
        '01/01/2025': expect.closeTo(56.41, 2),
        '02/01/2025': expect.closeTo(56.41, 2),
        '03/01/2025': expect.closeTo(56.41, 2),
        '04/01/2025': expect.closeTo(70.51, 2),
        '05/01/2025': expect.closeTo(112.81, 2),
        '06/01/2025': expect.closeTo(112.81, 2),
        '07/01/2025': expect.closeTo(112.81, 2),
      });

      vi.useRealTimers();
    });
  });
});
