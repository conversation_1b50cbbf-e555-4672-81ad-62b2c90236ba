import * as Sentry from '@sentry/nextjs';
import dayjs from 'dayjs';
import { inject, injectable } from 'inversify';
import * as math from 'mathjs';

import {
  getApplicableCommissionSchedule,
  validateCommissionSchedule,
} from '@/lib/commissions';
import { isNill } from '@/lib/helpers';
import CommissionSchedulesService from '@/services/commission-schedules/CommissionSchedulesService';
import CompGridsService from '@/services/comp-grids/CompGridsService';
import CompProfilesService from '@/services/comp-profiles/CompProfilesService';
import type { CompProfile } from '@/services/comp-profiles/types';
import { CompaniesService } from '@/services/companies';
import type { Company } from '@/services/companies/types';
import { ContactsService } from '@/services/contacts';
import type { Contact } from '@/services/contacts/types';
import { ReconciliationService } from '@/services/reconciliation';
import type {
  CommissionBalanceMonthly,
  CommissionExpectedMonthly,
  ReconciliationData,
} from '@/services/reconciliation/types';
import { ReportService } from '@/services/report';
import type { ReportData } from '@/services/report/types';
import type { CommissionSchedule } from '@/types';
import CompProfileConditionBuilder, {
  type ReportMatchCondition,
} from './matching/CompProfileConditionBuilder';

export interface CalculateCommissionSchedulesInput {
  account_id: string;
}

export type CalculateCommissionSchedulesOutput = Record<string, never>;

export interface ResolveReportCompProfilesInput {
  accountId: string;
  useCompGrids: boolean;
}

@injectable()
export default class ReceivableCalcService {
  @inject(CompaniesService)
  private readonly companiesSvc: CompaniesService;
  @inject(CommissionSchedulesService)
  private readonly commissionSchedulesSvc: CommissionSchedulesService;
  @inject(CompGridsService)
  private readonly compGridSvc: CompGridsService;
  @inject(CompProfilesService)
  private readonly compProfilesSvc: CompProfilesService;
  @inject(ContactsService)
  private readonly contactsSvc: ContactsService;
  @inject(ReconciliationService)
  private readonly reconciliationSvc: ReconciliationService;
  @inject(ReportService)
  private readonly reportsSvc: ReportService;

  private async buildCompProfileConditions(
    accountId: string,
    compProfiles: CompProfile[],
    useCompGrids: boolean
  ): Promise<Record<string, ReportMatchCondition>> {
    const conditionBuilder = new CompProfileConditionBuilder(
      this.companiesSvc,
      this.compGridSvc
    );

    const asyncCompProfileConditions = compProfiles.map<
      Promise<[string, ReportMatchCondition]>
    >(async (compProfile) => {
      const conditions = await conditionBuilder.resolveCompProfileConditions(
        accountId,
        compProfile,
        useCompGrids
      );

      return [compProfile.str_id, conditions];
    });

    const conditionsByCompProfileIdEntries: [string, ReportMatchCondition][] =
      await Promise.all(asyncCompProfileConditions);

    return Object.fromEntries(conditionsByCompProfileIdEntries);
  }

  async resolveReportCompProfiles(
    input: ResolveReportCompProfilesInput
  ): Promise<void> {
    const { accountId, useCompGrids } = input;

    const { comp_profiles } =
      await this.compProfilesSvc.findCompProfilesByAccount({
        account_id: accountId,
      });
    if (comp_profiles.length === 0) {
      // Early exit: nothing to do.
      return;
    }

    const { report_data } = await this.reportsSvc.findReportDatabyAccount({
      account_id: accountId,
    });
    if (report_data.length === 0) {
      // Early exit: nothing to do.
      return;
    }

    const compProfileConditions = await this.buildCompProfileConditions(
      accountId,
      comp_profiles,
      useCompGrids
    );

    // Sort comp profiles by weight, giving priority to highest weight
    const sortedConditionEntries = Object.entries(compProfileConditions).sort(
      ([, a], [, b]) => b.weight - a.weight
    );

    // Find the first (highest-weighted) comp profile that applies to each report
    const compProfileStrIdsByReportDataStrId = report_data.map<
      [string, string | null]
    >((report) => {
      const compProfile = sortedConditionEntries.find(([, condition]) =>
        condition.test(report)
      );

      let compProfileStrId: string | null = null;
      if (compProfile) {
        const [matchedCompProfileStrId] = compProfile;
        compProfileStrId = matchedCompProfileStrId;
      }

      return [report.str_id, compProfileStrId];
    });

    // Update report_data records with matched comp policy keys.
    const asyncUpdates = compProfileStrIdsByReportDataStrId
      .filter(([, compProfileStrId]) => compProfileStrId !== null)
      .map(async ([reportDataStrId, compProfileStrId]) => {
        // Set comp profile key on report data record
        return this.reportsSvc.updateReportCompProfile({
          account_id: accountId,
          report_data_str_id: reportDataStrId,
          comp_profile_str_id: compProfileStrId,
        });
      });

    await Promise.all(asyncUpdates);
  }

  async calculateCommissionSchedules(
    input: CalculateCommissionSchedulesInput
  ): Promise<CalculateCommissionSchedulesOutput> {
    try {
      const { account_id } = input;

      const asyncFindCompanies = this.companiesSvc.findCompaniesByAccount({
        account_id,
      });

      const asyncFindCommissionSchedules =
        this.commissionSchedulesSvc.findSchedulesByAccount({ account_id });

      const asyncFindContacts = this.contactsSvc.findContactsByAccount({
        account_id,
      });

      const asyncReconciliationData =
        this.reconciliationSvc.findReconciliationDataByAccount({ account_id });

      const [
        { companies },
        { commission_schedules: commissionSchedules },
        { contacts },
        { reconciliation_data: processedData },
      ] = await Promise.all([
        asyncFindCompanies,
        asyncFindCommissionSchedules,
        asyncFindContacts,
        asyncReconciliationData,
      ]);

      const companiesById = companies.reduce<Map<number, Company>>(
        (acc, company) => acc.set(company.id, company),
        new Map()
      );

      const reportDataIds = processedData.reduce<Set<string>>(
        (acc, { report_id }) => (report_id ? acc.add(report_id) : acc),
        new Set()
      );
      const { report_data: reportData } =
        await this.reportsSvc.getReportDataById({
          account_id,
          str_ids: Array.from(reportDataIds),
        });
      const reportDataMap = reportData.reduce<
        Record<string | number, ReportData>
      >((acc, cur) => {
        acc[cur.id] = cur;
        acc[cur.str_id] = cur;
        return acc;
      }, {});

      const contactsMap = contacts.reduce<Record<string, Contact>>(
        (acc, cur) => {
          acc[cur.str_id] = cur;
          return acc;
        },
        {}
      );

      console.time('reconcileData() = commissionSchedules');
      // If a commission_schedule applies to this record, use it to fill in commission data
      const listOfCompaniesWithCommissionSchedules = new Set(
        commissionSchedules.map(
          (e) => companiesById.get(e.carrier_company_id)?.company_name
        )
      );

      interface ReconciliationDataUpdates {
        str_id: string;
        balance?: number;
        commissions_expected?: number;
        commission_expected_monthly?: CommissionExpectedMonthly;
        commission_balance_monthly?: CommissionBalanceMonthly;
        log?: string[];
      }

      const recDataUpdates: ReconciliationDataUpdates[] = [];

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      Object.values(processedData).forEach(
        (rec_data_row: ReconciliationData) => {
          const rowUpdates: ReconciliationDataUpdates = {
            str_id: rec_data_row.str_id,
          };
          recDataUpdates.push(rowUpdates);

          // TODO: Should this just be based on policy's carrier name?
          // TODO: HANDLE NOW THAT WE HAVE BOTH CARRIER AND PAYING ENTITY
          // Build map so no need to do lookup
          let schedules =
            listOfCompaniesWithCommissionSchedules.has(
              rec_data_row.writing_carrier_name
            ) &&
            commissionSchedules.filter(
              (e) =>
                companiesById.get(e.carrier_company_id)?.company_name ===
                rec_data_row.writing_carrier_name
            );
          // TODO(toby): This condition is false (w/ schedules.length === undefined) if rec_data_row.writing_carrier_name is null or not in listOfCompanies
          if (schedules.length === 0) {
            // Check carrier_name if not writing_carrier_name
            schedules =
              listOfCompaniesWithCommissionSchedules.has(
                rec_data_row.carrier_name
              ) &&
              commissionSchedules.filter(
                (e) =>
                  companiesById.get(e.carrier_company_id)?.company_name ===
                  rec_data_row.carrier_name
              );
          }

          if (schedules.length > 0) {
            // TODO: Add config for which value to use? Currently use commissionable if available
            const policyPremium =
              rec_data_row.commissionable_premium_amount ||
              rec_data_row.premium_amount;
            const schedulesMatchingFilter = schedules.filter((schedule) => {
              // Check if start/end date is in range and product type / product name match
              const startDate = dayjs(schedule.start_date);
              const endDate = dayjs(schedule.end_date);
              const policyEffectiveDate = dayjs(rec_data_row.effective_date);

              const isDateCriteriaMet =
                policyEffectiveDate.isAfter(startDate) &&
                policyEffectiveDate.isBefore(endDate);
              const isProductTypeCriteriaMet =
                !schedule.product_type ||
                schedule.product_type.toLowerCase() ===
                  rec_data_row?.product_type?.toLowerCase();
              const isProductNameCriteriaMet =
                !schedule.product_name ||
                schedule.product_name.toLowerCase() ===
                  rec_data_row?.product_name?.toLowerCase();
              const isIssueAgeStartCriteriaMet =
                [null, undefined].includes(schedule.issue_age_start) ||
                schedule.issue_age_start <= rec_data_row.issue_age;
              const isIssueAgeEndCriteriaMet =
                [null, undefined].includes(schedule.issue_age_end) ||
                schedule.issue_age_end <= rec_data_row.issue_age;
              const isIssueAgeCriteriaMet =
                isIssueAgeStartCriteriaMet && isIssueAgeEndCriteriaMet;
              const isPremiumMinCriteriaMet =
                [null, undefined].includes(schedule.premium_min) ||
                schedule.premium_min <= rec_data_row.premium_amount;
              const isPremiumMaxCriteriaMet =
                [null, undefined].includes(schedule.premium_max) ||
                schedule.premium_max <= rec_data_row.premium_amount;
              const isPremiumCriteriaMet =
                isPremiumMinCriteriaMet && isPremiumMaxCriteriaMet;
              // TODO(toby): This lookup won't hit in cases where `row.report_id` is the PK for report_data.
              const report = reportDataMap[rec_data_row.report_id];
              const agentStrIds = Object.keys(report?.contacts_split ?? {});
              const agents = agentStrIds.map((str_id) => contactsMap[str_id]);
              // TODO: What do we do since agent levels can differ?
              // Checking any for now
              const isAgentGridLevelCriteriaMet =
                !schedule.agent_grid_level ||
                agents.length === 0 ||
                agents.some(
                  (agent) => agent?.grid_level === schedule.agent_grid_level
                );
              const result: boolean =
                isDateCriteriaMet &&
                isProductTypeCriteriaMet &&
                isProductNameCriteriaMet &&
                isIssueAgeCriteriaMet &&
                isPremiumCriteriaMet &&
                isAgentGridLevelCriteriaMet;
              return result;
            });
            if (schedulesMatchingFilter.length > 0) {
              // TODO: Use first one until we figure out how to prioritize
              const scheduleConfig = schedulesMatchingFilter[0];
              const {
                isValid: isValidCommissionSchedule,
                errors: scheduleValidationErrors,
              } = validateCommissionSchedule(scheduleConfig);
              if (!isValidCommissionSchedule)
                throw Error(
                  `Invalid commission schedule (${scheduleValidationErrors.join(
                    ','
                  )})`
                );

              const policyCancellationDate = rec_data_row.cancellation_date
                ? dayjs(rec_data_row.cancellation_date)
                : null;
              const policyEffectiveDate = dayjs(rec_data_row.effective_date);
              const policyEffectiveDateStartMOnth =
                policyEffectiveDate.startOf('month');
              const monthlyDueData = {};
              const monthlyBalanceData = {};
              let done = false;
              let cancelled = false;
              const numRate = isNill(scheduleConfig.commission_schedule[0].rate)
                ? null
                : parseFloat(scheduleConfig.commission_schedule[0].rate);
              let currentRate = numRate / 100;
              let currentBreakdown =
                scheduleConfig.commission_schedule[0].breakdown;
              const numMaxCommission = isNill(
                scheduleConfig.commission_schedule[0].max_commission
              )
                ? null
                : parseFloat(
                    scheduleConfig.commission_schedule[0].max_commission
                  );
              let currentMaxCommission = numMaxCommission;
              // Calculate monthly expected commission for up to 5 years after effective and until this month
              const commissionExpectedForYear = currentRate * policyPremium;
              let commissionExpectedTotal = 0;
              let commissionPaidTotal = 0;

              // Start commissionPaidTotal with any amount that's been paid before we start the commission schedule calculations
              // Look back six months
              const startMonth = policyEffectiveDateStartMOnth.add(
                (scheduleConfig?.delay ?? 0) - 1,
                'month'
              );
              let commissionsPaidBeforeEffectiveDateNotes = '';
              for (let i = 0; i < 6; i++) {
                const currentMonth = startMonth.subtract(i, 'month');
                const currentMonthKey = currentMonth.format('MM/DD/YYYY');
                const paidThisMonth =
                  rec_data_row?.commission_amount_monthly?.[currentMonthKey]
                    ?.commissionAmountMonthly ?? 0;
                if (paidThisMonth > 0) {
                  commissionsPaidBeforeEffectiveDateNotes += `  ${currentMonthKey}: ${paidThisMonth}\n`;
                }
                commissionPaidTotal += paidThisMonth;
              }
              if (commissionPaidTotal > 0) {
                rowUpdates.log = [
                  ...(rec_data_row.log ?? []),
                  `Commissions received before effective date\n${commissionsPaidBeforeEffectiveDateNotes}`,
                ];
              }

              // Year i
              for (let i = 0; i < 5; i++) {
                const delayInMonths = scheduleConfig?.delay ?? 0;
                // Get the applicable commission schedule for this year
                const currentSchedule: CommissionSchedule =
                  getApplicableCommissionSchedule(scheduleConfig, i);
                if (!currentSchedule) {
                  // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                  console.warn('No applicable schedule found.');
                  break;
                }
                currentRate = Number.parseFloat(currentSchedule.rate) / 100;
                currentMaxCommission = [undefined, null].includes(
                  currentSchedule.max_commission
                )
                  ? Number.POSITIVE_INFINITY
                  : Number.parseFloat(currentSchedule.max_commission);
                currentBreakdown = currentSchedule.breakdown;
                let commissionExpectedThisYear = 0;
                for (let j = 0; j < 12; j++) {
                  const currentMonth = policyEffectiveDateStartMOnth
                    .add(i, 'year')
                    .add(j, 'month')
                    .add(delayInMonths, 'month');
                  const currentMonthKey = currentMonth.format('MM/DD/YYYY');
                  const paidThisMonth =
                    rec_data_row?.commission_amount_monthly?.[currentMonthKey]
                      ?.commissionAmountMonthly ?? 0;
                  commissionPaidTotal += paidThisMonth;
                  let commissionExpected = 0;
                  if (
                    // biome-ignore lint/complexity/useOptionalChain: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
                    policyCancellationDate &&
                    policyCancellationDate.isAfter(currentMonth) &&
                    policyCancellationDate.isBefore(
                      currentMonth.add(1, 'month')
                    )
                  ) {
                    // Policy cancelled this month
                    // Calculate expected refund based on previous commission pro-rated by number of months
                    const durationInMonths = policyCancellationDate.diff(
                      policyEffectiveDate,
                      'months'
                    );
                    const pctOfYear = durationInMonths / 12;
                    const commissionToBeCollected =
                      pctOfYear * commissionExpectedForYear;
                    const commissionActuallyCollected = commissionExpectedTotal;
                    const refund =
                      commissionToBeCollected - commissionActuallyCollected;
                    commissionExpected = refund;
                    cancelled = true;
                  } else {
                    let monthShare = math.number(
                      math.fraction(currentBreakdown[j])
                    );
                    if (monthShare > 1) monthShare = monthShare / 100; // Only if the breakdown was entered as percentage convert it to decimal
                    commissionExpected = cancelled
                      ? 0
                      : policyPremium * currentRate * monthShare;
                  }
                  // const maxCommissionMet =
                  //   commissionExpected + commissionExpectedTotal >
                  //   currentMaxCommission;

                  if (
                    commissionExpectedThisYear + commissionExpected >
                    currentMaxCommission
                  ) {
                    commissionExpected =
                      currentMaxCommission - commissionExpectedThisYear;
                    commissionExpectedThisYear = currentMaxCommission;
                  } else {
                    commissionExpectedThisYear += commissionExpected;
                  }
                  commissionExpectedTotal += commissionExpected;
                  // These totals are cumulative (up until the current month)
                  monthlyBalanceData[currentMonthKey] =
                    commissionExpectedTotal - commissionPaidTotal;
                  monthlyDueData[currentMonthKey] = commissionExpected;
                  if (currentMonth.isAfter(dayjs())) {
                    done = true;
                    break;
                  }
                }
                if (done) break;
              }
              // Only set when there's a schedule?
              // if (accountSettings.commissions_expected_source === 'Schedule') {
              rowUpdates.commissions_expected = commissionExpectedTotal;
              // }
              rowUpdates.commission_expected_monthly = monthlyDueData;
              rowUpdates.commission_balance_monthly = monthlyBalanceData;
              rowUpdates.balance =
                commissionExpectedTotal - commissionPaidTotal;
            }
          }
        }
      );
      console.timeEnd('reconcileData() = commissionSchedules');

      const asyncUpdates = recDataUpdates.map(
        ({
          str_id,
          balance,
          commission_balance_monthly,
          commission_expected_monthly,
          commissions_expected,
          log,
        }) =>
          this.reconciliationSvc.updateReceivableSchedule({
            str_id,
            balance,
            commission_balance_monthly,
            commission_expected_monthly,
            commissions_expected,
            log,
          })
      );

      await Promise.all(asyncUpdates);

      return {};
    } catch (err) {
      // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      console.error('Error in calculateCommissionSchedules', err);
      Sentry.captureException(err);
      throw err as Error;
    }
  }
}
