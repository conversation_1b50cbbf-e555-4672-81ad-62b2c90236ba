export interface MatchCondition<TRecord> {
  get weight(): number;

  test(record: TRecord): boolean;
}

export class Or<TRecord> implements MatchCondition<TRecord> {
  constructor(private readonly conditions: MatchCondition<TRecord>[]) {
    if (conditions.length === 0) {
      throw new Error(`Or-matcher requires at least one nested condition.`);
    }
  }

  get weight(): number {
    // Use maximum weight amongst conditions.
    return this.conditions.reduce(
      (acc, condition) => Math.max(acc, condition.weight),
      0
    );
  }

  test(record: TRecord): boolean {
    return this.conditions.some((condition) => condition.test(record));
  }
}

export class And<TRecord> implements MatchCondition<TRecord> {
  constructor(private readonly conditions: MatchCondition<TRecord>[]) {
    if (conditions.length === 0) {
      throw new Error(`And-matcher requires at least one nested condition.`);
    }
  }

  get weight(): number {
    // Use sum of all weights amongst conditions
    return this.conditions.reduce(
      (acc, condition) => acc + condition.weight,
      0
    );
  }

  test(record: TRecord): boolean {
    return this.conditions.every((condition) => condition.test(record));
  }
}

/** Special matcher for cases where data is unavailable */
export class Never implements MatchCondition<unknown> {
  get weight(): number {
    // Prevent this matcher from being used when there's any other option available.
    return Number.NEGATIVE_INFINITY;
  }

  test(_report: unknown): boolean {
    return false;
  }

  static NEVER = new Never();
}
