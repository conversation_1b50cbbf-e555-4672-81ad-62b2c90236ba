import { DEFAULT_FILTER } from 'common/constants';

import { isNill } from '@/lib/helpers';
import { filterMatcher } from '@/lib/matcher';
import type { MatchCondition } from './conditions';
import type {
  Company,
  CompanyProduct,
  CompanyProductOption,
} from '@/services/companies/types';
import type { ReportData } from '@/services/report/types';

const testText = (
  expected: string,
  actual: string | null,
  collapseWhitespace = false
): boolean => {
  let normalizedExpected = expected.trim().toLowerCase();
  let normalizedActual = actual?.trim().toLowerCase();

  if (collapseWhitespace) {
    normalizedExpected = normalizedExpected.replaceAll(' ', '');
    normalizedActual = normalizedActual.replaceAll(' ', '');
  }

  if (isNill(expected)) {
    return true;
  }

  if (expected === DEFAULT_FILTER.BLANK_OPTION) {
    return isNill(actual);
  }

  return normalizedExpected === normalizedActual;
};

export class CompanyCondition implements MatchCondition<ReportData> {
  private readonly companyPkId: number;
  private readonly companyName: string | null;

  constructor(company: Company) {
    this.companyPkId = company.id;
    this.companyName = company.company_name;
  }

  get weight(): number {
    return 1;
  }

  test({ company_id, writing_carrier_name }: ReportData): boolean {
    return (
      company_id === this.companyPkId ||
      (!isNill(this.companyName) && writing_carrier_name === this.companyName)
    );
  }
}

export class ProductCondition implements MatchCondition<ReportData> {
  private readonly productName: string;

  constructor(product: CompanyProduct) {
    this.productName = product.product_name;
  }

  get weight(): number {
    return 1;
  }

  test({ product_name }: ReportData): boolean {
    if (isNill(product_name)) {
      return true;
    }

    return testText(this.productName, product_name, true);
  }
}

export class ProductOptionCondition implements MatchCondition<ReportData> {
  private readonly productOptionName: string;

  constructor(productOption: CompanyProductOption) {
    this.productOptionName = productOption.name;
  }

  get weight(): number {
    return 1;
  }

  test({ product_option_name }: ReportData): boolean {
    if (isNill(product_option_name)) {
      return true;
    }

    return testText(this.productOptionName, product_option_name, true);
  }
}

export class EffectiveDateWithinRange implements MatchCondition<ReportData> {
  constructor(
    private readonly startDate: Date | null,
    private readonly endDate: Date | null
  ) {}

  get weight(): number {
    const startWeight = this.startDate ? 1 : 0;
    const endWeight = this.endDate ? 1 : 0;

    return startWeight + endWeight;
  }

  test({ effective_date }: ReportData): boolean {
    if (!effective_date) {
      return true;
    }

    if (this.startDate && effective_date < this.startDate) {
      return false;
    }

    if (this.endDate && effective_date > this.endDate) {
      return false;
    }

    return true;
  }
}

export class IssueAgeWithinRange implements MatchCondition<ReportData> {
  constructor(
    private readonly minIssueAge: number | null,
    private readonly maxIssueAge: number | null
  ) {}

  get weight(): number {
    const minAgeWeight = this.minIssueAge ? 1 : 0;
    const maxAgeWeight = this.maxIssueAge ? 1 : 0;
    return minAgeWeight + maxAgeWeight;
  }

  test({ issue_age }: ReportData): boolean {
    if (isNill(issue_age)) {
      return true;
    }

    if (!isNill(this.minIssueAge) && issue_age < this.minIssueAge) {
      return false;
    }

    if (!isNill(this.maxIssueAge) && issue_age > this.maxIssueAge) {
      return false;
    }

    return true;
  }
}

export class CompensationTypeCondition implements MatchCondition<ReportData> {
  // biome-ignore lint/correctness/noUnusedPrivateClassMembers: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  constructor(private readonly compensationType: string) {}

  get weight(): number {
    // TODO: Update to a positive weight once the condition is functional.
    return 0;
  }

  test(_report: ReportData): boolean {
    // Compensation_type field does not exist on report
    return false;
  }
}

export class TransactionTypeCondition implements MatchCondition<ReportData> {
  constructor(private readonly transactionType: string) {}

  get weight(): number {
    return 1;
  }

  test({ transaction_type }: ReportData): boolean {
    return testText(this.transactionType, transaction_type);
  }
}

export class PaymentModeCondition implements MatchCondition<ReportData> {
  constructor(private readonly paymentMode: string) {}

  get weight(): number {
    return 1;
  }

  test({ payment_mode }: ReportData): boolean {
    return testText(this.paymentMode, payment_mode);
  }
}

export class FilterCriteriaCondition implements MatchCondition<ReportData> {
  constructor(private readonly filterCriteria: unknown) {}

  get weight(): number {
    // Giving half point for additional filters
    return 0.5;
  }

  test(report: ReportData): boolean {
    return filterMatcher(this.filterCriteria)(report);
  }
}
