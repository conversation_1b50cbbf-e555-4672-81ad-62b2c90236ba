import { isNill } from '@/lib/helpers';
import type CompGridsService from '@/services/comp-grids/CompGridsService';
import type { CompGridCriteria } from '@/services/comp-grids/types';
import type {
  CompProfile,
  CompProfileSchedule,
} from '@/services/comp-profiles/types';
import type { CompaniesService } from '@/services/companies';
import type { ReportData } from '@/services/report/types';
import { And, type MatchCondition, Never, Or } from './conditions';
import {
  CompanyCondition,
  CompensationTypeCondition,
  FilterCriteriaCondition,
  IssueAgeWithinRange,
  PaymentModeCondition,
  ProductCondition,
  ProductOptionCondition,
  TransactionTypeCondition,
} from './report-conditions';

export type ReportMatchCondition = MatchCondition<ReportData>;

export default class CompProfileConditionBuilder {
  constructor(
    private readonly companiesSvc: CompaniesService,
    private readonly compGridSvc: CompGridsService
  ) {}

  private async resolveCompGridCriterionConditions(
    accountId: string,
    criterion: CompGridCriteria
  ): Promise<ReportMatchCondition> {
    const conditions: ReportMatchCondition[] = [];

    if (
      !isNill(criterion.issue_age_start) ||
      !isNill(criterion.issue_age_end)
    ) {
      conditions.push(
        new IssueAgeWithinRange(
          criterion.issue_age_start ?? null,
          criterion.issue_age_end ?? null
        )
      );
    }

    if (!isNill(criterion.compensation_type)) {
      conditions.push(
        new CompensationTypeCondition(criterion.compensation_type)
      );
    }

    if (!isNill(criterion.transaction_type)) {
      conditions.push(new TransactionTypeCondition(criterion.transaction_type));
    }

    if (!isNill(criterion.payment_mode)) {
      conditions.push(new PaymentModeCondition(criterion.payment_mode));
    }

    if (criterion.grid_product_id) {
      // TODO(toby): Avoid this N+1 query by lifting this DB query to parseCompProfileConditions()
      const { company_product_pk_ids } =
        await this.compGridSvc.findCompanyProductForCompGridProduct({
          account_id: accountId,
          comp_grid_product_pk_id: criterion.grid_product_id,
        });

      const { products } = await this.companiesSvc.getProductsByPkId({
        account_id: accountId,
        product_pk_ids: company_product_pk_ids,
      });

      const productConditions = products.map(
        (record) => new ProductCondition(record)
      );

      // Match at least one product
      conditions.push(new Or(productConditions));
    }

    // Require all conditions to match
    return new And(conditions);
  }

  private async resolveScheduleRuleConditions(
    accountId: string,
    useCompGrids: boolean,
    rule: CompProfileSchedule
  ): Promise<ReportMatchCondition> {
    const conditions: ReportMatchCondition[] = [];

    if (!useCompGrids) {
      if (!isNill(rule.issue_age_min) || !isNill(rule.issue_age_max)) {
        conditions.push(
          new IssueAgeWithinRange(rule.issue_age_min, rule.issue_age_max)
        );
      }

      if (!isNill(rule.compensation_type)) {
        conditions.push(new CompensationTypeCondition(rule.compensation_type));
      }

      if (!isNill(rule.transaction_type)) {
        conditions.push(new TransactionTypeCondition(rule.transaction_type));
      }

      if (rule.product_id) {
        const { products } = await this.companiesSvc.getProductsByPkId({
          account_id: accountId,
          // Rule.product_id is an array of IDs
          product_pk_ids: rule.product_id,
        });

        const productConditions = products.map(
          (product) => new ProductCondition(product)
        );

        // Match any one with Or-condition
        conditions.push(new Or(productConditions));
      }

      if (rule.product_option_id) {
        const { product_options } =
          await this.companiesSvc.getProductOptionsByPkId({
            account_id: accountId,
            // Rule.product_option_id is an array of IDs
            product_option_pk_ids: rule.product_option_id,
          });

        const productOptionConditions = product_options.map(
          (productOption) => new ProductOptionCondition(productOption)
        );

        // Match any one with Or-condition
        conditions.push(new Or(productOptionConditions));
      }
    } else {
      const { comp_grid_criteria } = await this.compGridSvc.getCriteriaByPkId({
        account_id: accountId,
        // Rule.comp_grid_criteria_id is an array of IDs
        criteria_pk_ids: rule.comp_grid_criteria_id,
      });

      const asyncCompGridCriteriaConditions = comp_grid_criteria.map(
        async (criteria) =>
          this.resolveCompGridCriterionConditions(accountId, criteria)
      );

      const compGridCriteriaConditions = await Promise.all(
        asyncCompGridCriteriaConditions
      );

      // Require at least one criteria set match
      conditions.push(new Or(compGridCriteriaConditions));
    }

    if (rule.match_criteria) {
      const matchCriteriaConditions = rule.match_criteria.map(
        (filterCriteria) => new FilterCriteriaCondition(filterCriteria)
      );

      // Require all criteria match
      conditions.push(new And(matchCriteriaConditions));
    }

    // Require all conditions to match
    return new And(conditions);
  }

  async resolveCompProfileConditions(
    accountId: string,
    compProfile: CompProfile,
    useCompGrids: boolean
  ): Promise<ReportMatchCondition> {
    const { company_id } = compProfile;

    const conditions: ReportMatchCondition[] = [];

    if (!isNill(company_id)) {
      const {
        companies: [company],
      } = await this.companiesSvc.getCompaniesByPkId({
        account_id: accountId,
        company_pk_ids: [company_id],
      });

      if (!company) {
        // biome-ignore lint/suspicious/noConsole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        console.warn(
          `[CompProfileMatcher] Cannot find company with id: ${company_id}`
        );
        return Never.NEVER;
      }

      conditions.push(new CompanyCondition(company));
    }

    // TODO(toby): Add start and end date condition (EffectiveDateWithinRange) once comp profile sets are supported

    const asyncScheduleRuleConditions = compProfile.schedules.map(
      async (rule) =>
        this.resolveScheduleRuleConditions(accountId, useCompGrids, rule)
    );

    const scheduleRuleConditions = await Promise.all(
      asyncScheduleRuleConditions
    );

    // Allow any one rule to match
    conditions.push(new Or(scheduleRuleConditions));

    // Require all conditions to match
    return new And(conditions);
  }
}
