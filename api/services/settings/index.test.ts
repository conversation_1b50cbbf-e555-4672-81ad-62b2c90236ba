import { describe, it, expect, vi, beforeEach } from 'vitest';
import { customViewDefault } from 'common/constants/account_role_settings';

import { prismaClient } from '@/lib/prisma';
import { SettingsService } from '@/services/settings/index';
import { Roles } from '@/types';
import { BusinessException } from '@/lib/exceptionHandler';

vi.mock('@/lib/prisma', () => ({
  prismaClient: {
    account_role_settings: {
      findUnique: vi.fn(),
    },
  },
}));

describe('SettingsService.getAgentCommissionsDownlineDataAccess', () => {
  let settingsService: SettingsService;

  beforeEach(() => {
    settingsService = new SettingsService();
    vi.resetAllMocks();
  });

  it('Should return false for both access types when no record is found', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue(
      null
    );

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(prismaClient.account_role_settings.findUnique).toHaveBeenCalledWith({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: 'account123',
          role_id: Roles.PRODUCER,
          custom_view_name: customViewDefault,
        },
      },
      select: {
        agent_settings: true,
      },
    });

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: false,
      agentCommissionsExtendedDownlineDataAccess: false,
    });
  });

  it('Should return false for both access types when agent_settings is null', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      agent_settings: null,
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: false,
      agentCommissionsExtendedDownlineDataAccess: false,
    });
  });

  it('Should return false for both access types when directDownlineDataAccess and extendedDownlineDataAccess are undefined', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      agent_settings: {},
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: false,
      agentCommissionsExtendedDownlineDataAccess: false,
    });
  });

  it('Should return true for direct access when directDownlineDataAccess.commissionsConfig is "Yes"', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      agent_settings: {
        directDownlineDataAccess: {
          commissionsConfig: 'Yes',
        },
        extendedDownlineDataAccess: {
          commissionsConfig: 'No',
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: true,
      agentCommissionsExtendedDownlineDataAccess: false,
    });
  });

  it('Should return true for extended access when extendedDownlineDataAccess.commissionsConfig is "Yes"', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      agent_settings: {
        directDownlineDataAccess: {
          commissionsConfig: 'No',
        },
        extendedDownlineDataAccess: {
          commissionsConfig: 'Yes',
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: false,
      agentCommissionsExtendedDownlineDataAccess: true,
    });
  });

  it('Should return true for both access types when both configs are set to "Yes"', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      agent_settings: {
        directDownlineDataAccess: {
          commissionsConfig: 'Yes',
        },
        extendedDownlineDataAccess: {
          commissionsConfig: 'Yes',
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: true,
      agentCommissionsExtendedDownlineDataAccess: true,
    });
  });

  it('Should handle non-Yes values as false', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      agent_settings: {
        directDownlineDataAccess: {
          commissionsConfig: 'yes',
        },
        extendedDownlineDataAccess: {
          commissionsConfig: 'TRUE',
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: false, // Should be false as it's not exactly "Yes"
      agentCommissionsExtendedDownlineDataAccess: false, // Should be false as it's not exactly "Yes"
    });
  });

  it('Should handle commissionsConfig being undefined', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      agent_settings: {
        directDownlineDataAccess: {},
        extendedDownlineDataAccess: {},
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result =
      await settingsService.getAgentCommissionsDownlineDataAccess('account123');

    expect(result).toEqual({
      agentCommissionsDirectDownlineDataAccess: false,
      agentCommissionsExtendedDownlineDataAccess: false,
    });
  });
});

describe('SettingsService.getCommissionFieldsByAccountAndRole', () => {
  let settingsService: SettingsService;

  beforeEach(() => {
    settingsService = new SettingsService();
    vi.resetAllMocks();
  });

  it('should throw BusinessException when no record is found', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue(
      null
    );

    await expect(
      settingsService.getCommissionFieldsByAccountAndRole(
        'account123',
        Roles.ACCOUNT_ADMIN
      )
    ).rejects.toThrow(BusinessException);

    await expect(
      settingsService.getCommissionFieldsByAccountAndRole(
        'account123',
        Roles.ACCOUNT_ADMIN
      )
    ).rejects.toThrow(
      `No settings found for account account123 and role ${Roles.ACCOUNT_ADMIN}`
    );

    expect(prismaClient.account_role_settings.findUnique).toHaveBeenCalledWith({
      where: {
        account_id_role_id_custom_view_name: {
          account_id: 'account123',
          role_id: Roles.ACCOUNT_ADMIN,
          custom_view_name: customViewDefault,
        },
      },
      select: {
        pages_settings: true,
      },
    });
  });

  it('Should return empty fields when pages_settings has no commissions fields', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      pages_settings: {
        // No commissions field
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result = await settingsService.getCommissionFieldsByAccountAndRole(
      'account123',
      Roles.ACCOUNT_ADMIN
    );

    expect(result).toEqual({});
  });

  it('Should return only regular fields when no comp_calc fields are included', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      pages_settings: {
        commissions: {
          fields: ['agent_name', 'commission_amount', 'carrier_name'],
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result = await settingsService.getCommissionFieldsByAccountAndRole(
      'account123',
      Roles.ACCOUNT_ADMIN
    );

    expect(result).toEqual({
      agent_name: true,
      commission_amount: true,
      carrier_name: true,
    });
  });

  it('Should handle comp_calc fields by adding accounting_transaction_details relation', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      pages_settings: {
        commissions: {
          fields: ['agent_name', 'comp_calc_status', 'commission_amount'],
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result = await settingsService.getCommissionFieldsByAccountAndRole(
      'account123',
      Roles.ACCOUNT_ADMIN
    );

    expect(result).toEqual({
      agent_name: true,
      commission_amount: true,
      accounting_transaction_details: {
        where: { state: 'active' },
        select: {
          status: true,
          contact: {
            select: {
              id: true,
              email: true,
              first_name: true,
              last_name: true,
              str_id: true,
            },
          },
        },
      },
    });
  });

  it('Should handle all comp_calc field types correctly', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      pages_settings: {
        commissions: {
          fields: [
            'agent_name',
            'comp_calc_status',
            'comp_calc',
            'comp_calc_log',
            'commission_amount',
          ],
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result = await settingsService.getCommissionFieldsByAccountAndRole(
      'account123',
      Roles.ACCOUNT_ADMIN
    );

    expect(result).toEqual({
      agent_name: true,
      commission_amount: true,
      accounting_transaction_details: {
        where: { state: 'active' },
        select: {
          status: true,
          amount: true,
          logs: true,
          contact: {
            select: {
              id: true,
              email: true,
              first_name: true,
              last_name: true,
              str_id: true,
            },
          },
        },
      },
    });
  });

  it('Should not include accounting_transaction_details when no comp fields are configured', async () => {
    vi.mocked(prismaClient.account_role_settings.findUnique).mockResolvedValue({
      pages_settings: {
        commissions: {
          fields: ['agent_name', 'commission_amount', 'carrier_name'],
        },
      },
      // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    } as any);

    const result = await settingsService.getCommissionFieldsByAccountAndRole(
      'account123',
      Roles.ACCOUNT_ADMIN
    );

    expect(result).toHaveProperty('agent_name');
    expect(result).not.toHaveProperty('accounting_transaction_details');
  });
});
