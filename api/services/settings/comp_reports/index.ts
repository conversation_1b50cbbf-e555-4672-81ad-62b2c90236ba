import { injectable } from 'inversify';

import { prismaClient } from '@/lib/prisma';
import { DataStates } from '@/types';

@injectable()
export class CompReportSettingsService implements ICompReportSettingsService {
  async getCompReportSettings(
    accountId: string
  ): Promise<CompReportSettings | null> {
    const result = await prismaClient.compensation_reports_config.findUnique({
      where: {
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      select: {
        id: true,
        str_id: true,
        account_id: true,
        enable_custom_terms: true,
        custom_terms_text: true,
      },
    });

    return result;
  }

  async upsertCompReportSettings(
    accountId: string,
    enableCustomTerms: boolean,
    customTermsText: string
  ): Promise<CompReportSettings | null> {
    const result = await prismaClient.compensation_reports_config.upsert({
      where: {
        account_id: accountId,
        state: DataStates.ACTIVE,
      },
      update: {
        enable_custom_terms: enableCustomTerms,
        custom_terms_text: customTermsText,
      },
      create: {
        account_id: accountId,
        enable_custom_terms: enableCustomTerms,
        custom_terms_text: customTermsText,
      },
      select: {
        id: true,
        str_id: true,
        account_id: true,
        enable_custom_terms: true,
        custom_terms_text: true,
      },
    });

    return result;
  }
}

export interface ICompReportSettingsService {
  getCompReportSettings(accountId: string): Promise<CompReportSettings | null>;
  upsertCompReportSettings(
    accountId: string,
    enableCustomTerms: boolean,
    customTermsText: string
  ): Promise<CompReportSettings | null>;
}

export interface CompReportSettings {
  id: number;
  str_id: string;
  account_id: string;
  enable_custom_terms: boolean | null;
  custom_terms_text: string | null;
}
