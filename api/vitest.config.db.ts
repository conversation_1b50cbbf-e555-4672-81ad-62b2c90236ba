import { defineConfig } from 'vitest/config';
// biome-ignore lint/style/useNodejsImportProtocol: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import path from 'path';
// biome-ignore lint/correctness/noUndeclaredDependencies: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
import dotenv from 'dotenv';

dotenv.config({ path: path.resolve(__dirname, '.env.test') });

export default defineConfig({
  test: {
    env: {
      NODE_ENV: 'test',
    },
    fileParallelism: false,
    setupFiles: ['test.setup.ts'],
    include: [
      'pages/api/**/*.db.test.ts',
      'pages/api/**/**/*.db.test.ts',
      'services/**/*.db.test.ts',
      'lib/**/*.db.test.ts',
    ],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, ''),
    },
  },
});
