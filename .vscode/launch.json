{"version": "0.2.0", "configurations": [{"name": "LOCAL: run seed script debug", "type": "node-terminal", "request": "launch", "command": "npm run local:db-seed-local-database-from-dev", "cwd": "${workspaceFolder}/api"}, {"name": "PROD: Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npx cross-env DEBUG=local npm run prod", "cwd": "${workspaceFolder}/api", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/api/node_modules/**", "**/node_modules/**", "${workspaceFolder}/api/.next/**", "**/.next/**"]}, {"name": "LOCAL: Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npx cross-env DEBUG=local npm run dev-local", "cwd": "${workspaceFolder}/api", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/api/node_modules/**", "**/node_modules/**", "${workspaceFolder}/api/.next/**", "**/.next/**"]}, {"name": "INTEGRATION: Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npx cross-env DEBUG=local npm run integration", "cwd": "${workspaceFolder}/api", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/api/node_modules/**", "**/node_modules/**", "${workspaceFolder}/api/.next/**", "**/.next/**"]}, {"name": "DEV: Next.js: debug server-side", "type": "node-terminal", "request": "launch", "command": "npx cross-env DEBUG=local npm run dev", "cwd": "${workspaceFolder}/api", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/api/node_modules/**", "**/node_modules/**", "${workspaceFolder}/api/.next/**", "**/.next/**"]}, {"name": "Test: debug server-side", "type": "node-terminal", "request": "launch", "command": "npx cross-env DEBUG=local npm run test:unit", "cwd": "${workspaceFolder}/api", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/api/node_modules/**", "**/node_modules/**", "${workspaceFolder}/api/.next/**", "**/.next/**"]}, {"name": "Next.js: debug client-side", "type": "chrome", "request": "launch", "url": "http://localhost:3001", "cwd": "${workspaceFolder}/web", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/web/node_modules/**", "**/node_modules/**", "${workspaceFolder}/web/.next/**", "**/.next/**"]}, {"name": "Next.js: debug full stack", "type": "node-terminal", "request": "launch", "command": "npx cross-env DEBUG=local npm run dev", "serverReadyAction": {"pattern": "started server on .+, url: (https?://.+)", "uriFormat": "%s", "action": "debugWithChrome"}, "cwd": "${workspaceFolder}/api", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/api/node_modules/**", "**/node_modules/**", "${workspaceFolder}/api/.next/**", "**/.next/**"]}, {"name": "Docker: Python - Flask", "type": "docker", "request": "launch", "preLaunchTask": "docker-run: debug", "python": {"pathMappings": [{"localRoot": "${workspaceFolder}", "remoteRoot": "/app"}], "projectType": "flask"}}]}