{"editor.formatOnSave": true, "editor.defaultFormatter": "biomejs.biome", "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "prettier.enable": false, "editor.tabSize": 2, "IDX.aI.enableInlineCompletion": true, "sqltools.connections": [{"previewLimit": 50, "server": "localhost", "port": 5432, "driver": "PostgreSQL", "name": "Fintary PROD", "database": "postgres", "username": "postgres"}, {"previewLimit": 50, "server": "localhost", "port": 5433, "driver": "PostgreSQL", "name": "Fintary DEV", "database": "postgres", "username": "dev"}, {"previewLimit": 50, "server": "localhost", "port": 5431, "driver": "PostgreSQL", "name": "Fintary LOCAL", "database": "postgres", "username": "postgres"}], "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "[jsonc]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "[json]": {"editor.defaultFormatter": "biomejs.biome"}}