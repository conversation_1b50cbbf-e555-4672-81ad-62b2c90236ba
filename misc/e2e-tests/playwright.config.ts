import { defineConfig, devices } from '@playwright/test';

const getWorkers = () => {
  const args = process.argv.join(' ');
  if (args.includes('--grep')) {
    return 1;
  }

  return 3;
};

export default defineConfig({
  fullyParallel: true,
  workers: getWorkers(),
  reportSlowTests: null,
  outputDir: 'google-chat-sender/test-results',
  reporter: [
    ['json', { outputFile: 'google-chat-sender/test-results/results.json' }],
    ['html', { open: 'never', outputFolder: 'playwright-report' }],
  ],
  use: {
    screenshot: 'only-on-failure',
  },
  timeout: 2 * 60 * 1000,

  projects: [
    {
      name: 'basic-tests',
      testDir: './tests/basic',
      use: { ...devices['Desktop Chrome'] },
      fullyParallel: false,
    },
    {
      name: 'admin-tests',
      testDir: './tests/admin',
      use: { ...devices['Desktop Chrome'] },
      fullyParallel: false,
    },
    {
      name: 'commission-tests',
      testDir: './tests/commission',
      use: { ...devices['Desktop Chrome'] },
      fullyParallel: false,
    },
  ],
});
