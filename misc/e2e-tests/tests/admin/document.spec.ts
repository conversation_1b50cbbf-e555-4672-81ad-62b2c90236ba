import {
  test,
  expect,
  type Page,
  Locator,
  BrowserContext,
} from '@playwright/test';
// Pages Objects
import DocumentPage from '@pages/document-page';
// Utils
import { getDocumentPath } from '@utils/get-path';
import { BASE_URL } from '@assets/data/authData';

let documentPage: DocumentPage;
let page: Page;

test.beforeAll(async ({ browser }) => {
  const context: BrowserContext = await browser.newContext();
  page = await context.newPage();

  // Create instance and initialize
  documentPage = new DocumentPage(page, context);
  await documentPage.setup('admin');
  await documentPage.autoRegister();
  page = documentPage.page;

  await page.goto(BASE_URL + '/documents');
  await page.waitForLoadState('networkidle');
});

test.beforeEach(async () => {});

test.afterAll(async () => {
  await page.close();
});

test.describe('Document Management Tests', () => {
  test.describe('Upload document test', () => {
    test(
      'Should allow user to create a new company, upload it, and cancel the operation.',
      { tag: ['@Admin'] },
      async () => {
        const {
          uploadButton,
          addButton,
          addCompanyButton,
          newCompanyInput,
          companySearchList,
          companySelect,
        } = documentPage.documentLocators;

        await uploadButton.click();
        await page.waitForLoadState('load');

        await addCompanyButton.click();
        await newCompanyInput.fill('Test company');
        await addButton.click();

        await companySelect.click();
        const option: Locator = companySearchList
          .locator('.MuiBox-root.css-1k31fij')
          .first();

        await option.click();
      }
    );
    test(
      'Should allow user to upload a file',
      { tag: ['@Admin'] },
      async () => {
        const { dialog, nextButton } = documentPage.commonLocators;
        const { documentSelect, documentUpload, uploadConfirmButton } =
          documentPage.documentLocators;

        await documentSelect.click();
        const option = page.getByTestId('statement');

        await option.click();

        // Upload file
        await documentUpload.setInputFiles(getDocumentPath());
        expect(dialog.getByText('Test-Document.xlsx')).toBeVisible();

        await nextButton.click();
        await page.waitForLoadState('load');
        await uploadConfirmButton.click();
        await page.waitForLoadState('load');
        await page.getByText('Preview & Upload').waitFor({ state: 'detached' });
      }
    );
  });

  test.describe('Document processor test', () => {
    test(
      'Should allow user open the data processer',
      { tag: ['@Admin'] },
      async () => {
        const { firstRow } = documentPage.commonLocators;

        await firstRow.waitFor({ state: 'visible' });
        await firstRow.hover();
        await page.waitForTimeout(3000);
        await firstRow.getByTestId('PlayArrowIcon').click();
        await page.waitForTimeout(3000);
      }
    );
  });
});
