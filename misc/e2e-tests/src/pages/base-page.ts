import { expect, Page, BrowserContext, Locator } from '@playwright/test';
// Types
import LocatorType from '@customTypes/locator-type';
import DataType from '@customTypes/data-type';
import PersonalInfo from '@customTypes/personal-info-type';
// Locators
import commonLocatorsGenerator from '@plugins/locators/common';
import accountLocatorsGenerator from '@plugins/locators/account/account';
// Plugins
import { auth, googleOAuth } from '@plugins/auth/auth';
import { signIn, signOut } from '@plugins/auth/user-session';
import deleteAccount from '@plugins/auth/delete-account';
import {
  uploadData as uploadDataAction,
  modifyFirstRowData as modifyFirstRowDataAction,
  deleteFirstRowData as deleteFirstRowDataAction,
  deleteFirstRowDataCust as deleteFirstRowDataCustAction,
  searchData as searchDataAction,
  dataProcessing,
} from '@plugins/action/table-action';
import {
  uploadD<PERSON><PERSON><PERSON><PERSON>,
  dataExisting<PERSON><PERSON><PERSON>,
  dataTotalNumber<PERSON>hecker,
} from '@plugins/action/data-checker';
import getIndexOfThead from '@utils/get-index-of-thead';
// Data
import { EMAILS, PASSWORD, OAUTH_EMAIL } from '@assets/data/authData';
import personalInfoDataJson from '@assets/data/personalInfoData.json';

const personalInfoData: PersonalInfo = personalInfoDataJson[0];
export default class BasePage {
  protected context: BrowserContext;
  public page: Page;
  public commonLocators: { [key: string]: Locator };
  public accountLocators: { [key: string]: Locator };

  constructor(page: Page, context: BrowserContext) {
    this.page = page;
    this.context = context;
    this.commonLocators = commonLocatorsGenerator(page);
    this.accountLocators = accountLocatorsGenerator(page);
  }

  async setup(account: string) {
    if (!this.page) {
      throw new Error('Page is not initialized');
    }
    switch (account) {
      case 'base':
        await auth(this.page, EMAILS['base'], PASSWORD);
        break;
      case 'commission':
        await auth(this.page, EMAILS['commission'], PASSWORD);
        break;
      case 'admin':
        await auth(this.page, EMAILS['admin'], PASSWORD);
        break;
    }
  }

  // Auth
  async OAuth() {
    if (!this.page) {
      throw new Error('Page is not initialized');
    }
    await googleOAuth(this.page, OAUTH_EMAIL, PASSWORD);
  }

  async signIn(email: string, passwd: string) {
    if (!this.page) {
      throw new Error('Page is not initialized');
    }
    await signIn(this.page, email, passwd);
  }

  async signOut() {
    if (!this.page) {
      throw new Error('Page is not initialized');
    }
    await signOut(this.page);
  }

  async autoRegister() {
    await this.page.waitForLoadState('load');
    const isRegister = await this.page.getByText("Let's get started").count();
    if (isRegister > 0) {
      const { saveButton } = this.commonLocators;
      const { firstName, lastName, phoneInput, nameInput, descriptionInput } =
        this.accountLocators;

      // Enter personal information and save
      await expect(saveButton).toBeDisabled();
      await expect(firstName).toHaveValue('');
      await expect(lastName).toHaveValue('');
      await expect(phoneInput).toHaveValue('');

      // Add max attempts to prevent an infinite loop
      const maxAttempts = 1000;
      let attempts = 0;

      while (true) {
        await this.page.waitForTimeout(3000);
        const firstNameValue = await firstName.inputValue();
        const lastNameValue = await lastName.inputValue();
        const phoneValue = await phoneInput.inputValue();
        if (
          (firstNameValue && lastNameValue && phoneValue) ||
          attempts >= maxAttempts
        ) {
          await saveButton.click();
          break;
        }
        await firstName.fill(personalInfoData.FIRST_NAME);
        await lastName.fill(personalInfoData.LAST_NAME);
        await phoneInput.fill(personalInfoData.PHONE);

        attempts++;
      }

      await expect(saveButton).toBeDisabled();
      await expect(nameInput).toHaveValue('');
      await expect(descriptionInput).toHaveValue('');

      // Enter account information and save
      while (true) {
        await this.page.waitForTimeout(3000);
        const nameValue = await nameInput.inputValue();
        const descriptionValue = await descriptionInput.inputValue();

        if ((nameValue && descriptionValue) || attempts >= maxAttempts) {
          await saveButton.click();
          break;
        }
        await nameInput.fill(personalInfoData.ACCOUNT_NAME);
        await descriptionInput.fill(personalInfoData.ACCOUNT_DESCRIPTION);

        attempts++;
      }

      if (attempts >= maxAttempts) {
        console.warn(
          'Max attempts reached, exiting the loop to avoid an infinite loop.'
        );
      }

      // Verify completion message
      await expect(
        this.page.getByText(
          "Congratulations! You're ready for start using Fintary."
        )
      ).toBeVisible();

      await this.page.reload();
      await this.page.waitForLoadState('networkidle');
    }
  }

  async deleteAccount(email: string) {
    try {
      deleteAccount(email);
    } catch (error) {
      console.error('Error during delete testing account:', error);
      throw error;
    }
  }

  // Data process
  async uploadData(locators: LocatorType[], data: DataType) {
    try {
      await uploadDataAction(this.page, locators, data);
    } catch (error) {
      console.error('Error during upload data:', error);
      throw error;
    }
  }

  async modifyFirstRowData(locators: LocatorType[], data: DataType) {
    try {
      await modifyFirstRowDataAction(this.page, locators, data);
    } catch (error) {
      console.error('Error during modify first-row data:', error);
      throw error;
    }
  }

  async deleteFirstRowData() {
    try {
      await deleteFirstRowDataAction(this.page);
    } catch (error) {
      console.error('Error during delete first-row data:', error);
      throw error;
    }
  }

  async groupDeleteData(url: string) {
    try {
      const { rows } = this.commonLocators;

      await this.page.goto(url);

      try {
        await this.page.waitForSelector('tbody tr', { timeout: 30000 });
      } catch {
        return;
      }

      const maxAttempts = 1000;
      let attempts = 0;

      while (true) {
        const count: number = await rows.count();
        if (count === 0 || attempts >= maxAttempts) {
          break;
        }
        await this.deleteFirstRowData();
        attempts++;
      }

      if (attempts >= maxAttempts) {
        console.warn(
          'Max attempts reached, exiting the loop to avoid an infinite loop.'
        );
      }
    } catch (error) {
      console.error('Error during delete group delete data:', error);
      throw error;
    }
  }

  async groupDeleteDataCust(url: string, deleteConfirm: string) {
    try {
      const { rows } = this.commonLocators;

      await this.page.goto(url);
      try {
        await this.page.waitForSelector('tbody tr', { timeout: 30000 });
      } catch {
        return;
      }

      // Add max attempts to prevent an infinite loop
      const maxAttempts = 1000;
      let attempts = 0;

      while (true) {
        const count: number = await rows.count();
        if (count === 0 || attempts >= maxAttempts) {
          break;
        }
        await deleteFirstRowDataCustAction(this.page, deleteConfirm);
        attempts++;
      }

      if (attempts >= maxAttempts) {
        console.warn(
          'Max attempts reached, exiting the loop to avoid an infinite loop.'
        );
      }
    } catch (error) {
      console.error('Error during delete group delete data:', error);
      throw error;
    }
  }

  async searchData(searchData: string) {
    try {
      await searchDataAction(this.page, searchData);
    } catch (error) {
      console.error('Error during search data:', error);
      throw error;
    }
  }

  async uploadDataChecker(data: DataType | any) {
    try {
      await uploadDataChecker(this.page, data);
    } catch (error) {
      console.error('Error during check data matches:', error);
      throw error;
    }
  }

  async dataExistingChecker(data: string) {
    try {
      return await dataExistingChecker(this.page, data);
    } catch (error) {
      console.error('Error during check existing data:', error);
      throw error;
    }
  }

  async dataTotalNumberChecker() {
    try {
      await dataTotalNumberChecker(this.page);
    } catch (error) {
      console.error('Error during check data total number:', error);
      throw error;
    }
  }

  async dataProcessing(locators: LocatorType[], data: DataType | any) {
    try {
      await dataProcessing(this.page, locators, data);
    } catch (error) {
      console.error('Error during data processing:', error);
      throw error;
    }
  }

  async getIndexOfThead(title: string) {
    try {
      return await getIndexOfThead(this.page, title);
    } catch (error) {
      console.error('Error during get index:', error);
      throw error;
    }
  }

  // Common test action
  async cancelAdditionTest(testLocator: Locator) {
    try {
      const { addButton, cancelButton } = this.commonLocators;

      await addButton.click();
      await this.page.waitForLoadState('load');
      await expect(testLocator).toBeVisible();

      // Click cancel button
      await cancelButton.click();
      await this.page.waitForLoadState('load');
      await expect(testLocator).not.toBeVisible();
    } catch (error) {
      console.error('Error during cancel addition test:', error);
      throw error;
    }
  }

  async cancelModifiedTest() {
    try {
      const { firstRow, addButton, cancelButton } = this.commonLocators;

      await this.page.waitForSelector('tbody tr');

      // Ensure add button is disabled during modification
      await firstRow.hover();
      await firstRow.locator('button').nth(-1).click();
      await this.page.waitForLoadState('load');
      await expect(addButton).toBeDisabled();

      // Click cancel button
      await cancelButton.click();
      await this.page.waitForLoadState('load');
      await expect(this.page.locator('tbody')).toBeVisible();
    } catch (error) {
      console.error('Error during cancel modified test:', error);
      throw error;
    }
  }

  async cancelCopyModifiedTest() {
    try {
      const { firstRow, copyButton, cancelButton } = this.commonLocators;

      await this.page.waitForSelector('tbody tr');
      await firstRow.hover();
      await firstRow.locator('button').nth(-1).click();
      await this.page.waitForLoadState('load');

      await copyButton.click();
      await this.page.waitForLoadState('load');
      await cancelButton.click();
      await this.page.waitForLoadState('load');
      await expect(this.page.locator('tbody')).toBeVisible();
    } catch (error) {
      console.error('Error during cancel copy modified test:', error);
      throw error;
    }
  }

  async searchTest(searchData: string) {
    try {
      const { firstRow } = this.commonLocators;

      await this.searchData(searchData);
      await expect(firstRow.getByText(searchData).nth(0)).toBeVisible();
    } catch (error) {
      console.error('Error during search test:', error);
      throw error;
    }
  }

  async pageChecker(pageButton: Locator, url: string) {
    try {
      await pageButton.click();
      await this.page.waitForLoadState('networkidle');
      expect(this.page.url()).toContain(url);
    } catch (error) {
      console.error('Error during search test:', error);
      throw error;
    }
  }

  async keyBoardChecker(hotkey: string[], url: string) {
    try {
      await this.page.keyboard.press('Escape');
      await this.page.keyboard.down(hotkey[0]);
      await this.page.keyboard.press(hotkey[1]);
      await this.page.keyboard.up(hotkey[0]);
      await this.page.waitForLoadState('networkidle');
      expect(this.page.url()).toContain(url);
    } catch (error) {
      console.error('Error during search test:', error);
      throw error;
    }
  }
}
