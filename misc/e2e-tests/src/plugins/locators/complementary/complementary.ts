import { type Page, Locator } from '@playwright/test';

const complementaryLocatorsGenerator = (page: Page) => {
  const menuButton: Locator = page.getByTestId('MenuIcon').locator('..');
  const mainTtitleButton: Locator = page
    .locator('a')
    .filter({ has: page.locator('img[alt="Logo"]') });
  const profileButton: Locator = page
    .locator('h6', { hasText: '<EMAIL>' })
    .locator('..');
  const helpButton: Locator = page.locator('li:has-text("Help")');
  const feedbackOpenButton: Locator = page.locator(
    'li:has-text("Open feedback")'
  );
  const feedbackCloseButton: Locator = page.locator(
    'li:has-text("Close feedback")'
  );
  const sendFeedbackButton: Locator = page.getByText('Send feedback');
  const notificationButton: Locator = page
    .getByTestId('NotificationsNoneIcon')
    .locator('..');

  const reconciliationButton: Locator = page.locator(
    'a:has-text("Reconciliation")'
  );
  const commissionsButton: Locator = page.locator('a[href="/commissions"]');
  const policiesButton: Locator = page.locator('a[href="/policies"]');

  const schedulesCollapseButton: Locator = page
    .locator('span:has-text("Schedules")')
    .locator('..');
  const compGridsButton: Locator = page.locator('a:has-text("Comp grids")');
  const compProfilesButton: Locator = page.locator(
    'a:has-text("Comp profiles")'
  );
  const compProfileSetsButton: Locator = page.locator(
    'a:has-text("Comp profile sets")'
  );
  const carrierButton: Locator = page.locator('a:has-text("Carrier")');

  const insightsButton: Locator = page
    .locator('span:has-text("Insights")')
    .locator('..');
  const businessInsightsButton: Locator = page.locator(
    'a:has-text("Business Insights")'
  );
  const policiesInsightsButton: Locator = page.locator(
    'a:has-text("Policies (Life clients)")'
  );
  const agentsInsightsButton: Locator = page.locator('a:has-text("Agents")');
  const agentGroupInsightsButton: Locator = page.locator(
    'a:has-text("Agent Group")'
  );
  const viewsButton: Locator = page.locator('a:has-text("Views")');
  const reportsCollapseButton: Locator = page.locator(
    'div[role="button"][aria-expanded="false"]:has-text("Reports")'
  );
  const summariesButton: Locator = page.locator('a:has-text("Summaries")');
  const reportsButton: Locator = page.locator('a:has-text("Reports")');

  const companiesCollapseButton: Locator = page.locator(
    'div[role="button"][aria-expanded="false"]:has-text("Companies")'
  );
  const companiesButton: Locator = page.locator('a:has-text("Companies")');

  const productsButton: Locator = page.locator('a:has-text("Products")');
  const optionsButton: Locator = page.locator('a:has-text("Options")');

  const agentsCollapseButton: Locator = page.locator(
    'div[role="button"][aria-expanded="false"]:has-text("Agents")'
  );
  const agentsButton: Locator = page.locator('a:has-text("Agents")');

  const groupsButton: Locator = page.locator('a:has-text("Groups")');
  const productionButton: Locator = page.locator('a:has-text("Production")');

  const documentsButton: Locator = page.locator('a:has-text("Documents")');
  const settingsButton: Locator = page.locator('a:has-text("Settings")');

  const complementaryLocators = {
    menuButton,
    mainTtitleButton,
    profileButton,
    helpButton,
    feedbackOpenButton,
    feedbackCloseButton,
    sendFeedbackButton,
    notificationButton,

    reconciliationButton,
    commissionsButton,
    policiesButton,

    schedulesCollapseButton,
    compGridsButton,
    compProfilesButton,
    compProfileSetsButton,
    carrierButton,

    insightsButton,
    businessInsightsButton,
    policiesInsightsButton,
    agentsInsightsButton,
    agentGroupInsightsButton,
    viewsButton,

    reportsCollapseButton,
    reportsButton,
    summariesButton,

    companiesCollapseButton,
    companiesButton,
    productsButton,
    optionsButton,

    agentsCollapseButton,
    agentsButton,
    groupsButton,
    productionButton,

    documentsButton,
    settingsButton,
  };

  return complementaryLocators;
};
export default complementaryLocatorsGenerator;
